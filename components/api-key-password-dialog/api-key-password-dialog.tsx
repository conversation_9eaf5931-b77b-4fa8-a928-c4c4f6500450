import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { makeStyles } from '@material-ui/core/styles';
import * as yup from 'yup';

import DialogComponent from '../dialog-component';
import FormSection from '../form-section';
import { useYupValidationResolver } from '../form-validation';
import Loading from '../loading';

const defaultProps = {};

const useStyles = makeStyles({
  text: {
    color: '#000000',
    opacity: 0.5,
  },
  formSection: {
    paddingBottom: '12px',
  },
});

const fields = [
  {
    apiName: 'password',
    name: 'password',
    type: 'text',
    updatable: true,
    creatable: true,
    required: true,
    xs: 12,
    subType: 'password',
  },
];

const ApiKeyPasswordDialog: React.FC<any> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const classes = useStyles();

  const { open, handleClose, handleSubmitResult, title, description, loading } = props;

  //@ts-ignore
  const validationSchema = yup.object({
    password: yup.string().required('Password is required'),
  });
  const resolver = useYupValidationResolver(validationSchema);
  //@ts-ignore
  const methods = useForm({ defaultValues: {}, resolver });
  const { getValues, handleSubmit, setError } = methods;

  const onSubmit = async () => {
    const values = getValues();
    handleSubmitResult(values, (message: string) => {
      setError('password', {
        type: 'custom',
        message: message,
      });
    });
  };

  return (
    <DialogComponent
      width="sm"
      open={open}
      title={title}
      handleClose={() => {
        handleClose();
      }}
      actions={{
        rightButtons: [
          {
            text: 'Confirm',
            type: 'submit',
            disabled: loading,
            onClick: () => {
              try {
                handleSubmit(onSubmit)();
              } catch (err) {
                console.log('err: ', err);
              }
            },
          },
        ],
        leftButtons: [
          {
            onClick: () => handleClose(),
            disabled: loading,
            text: 'Cancel',
          },
        ],
      }}
    >
      <p
        style={{
          fontSize: 14,
          fontWeight: 400,
          marginBottom: 45,
        }}
      >
        {description}
      </p>
      {loading ? (
        <Loading />
      ) : (
        <FormProvider {...methods}>
          <form className={classes.formSection} onSubmit={handleSubmit(onSubmit)}>
            <FormSection
              showDivider={false}
              //@ts-ignore
              fields={fields}
              values={{}}
            />
          </form>
        </FormProvider>
      )}
    </DialogComponent>
  );
};

export default ApiKeyPasswordDialog;

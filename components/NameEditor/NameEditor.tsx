import React, { useState } from 'react';

import TextField from '@material-ui/core/TextField';
import { makeStyles } from '@material-ui/core/styles';
import { Edit } from '@material-ui/icons';

import type { Props } from './interface';

const useStyles = makeStyles({
  headerContainer: {
    padding: '0 32px 50px 32px',
  },

  nameContainer: {
    display: 'flex',
    alignItems: 'center',
  },

  editIcon: {
    marginLeft: '5px',
  },

  name: {
    fontSize: '28px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    maxWidth: '300px',
  },

  underline: {
    '&::after': {
      borderBottom: '1px solid rgba(0, 0, 0, 0.42)',
    },
    '&::active': {
      borderBottom: 'none',
      color: 'red',
    },
    '&::hover': {
      borderBottom: 'none',
      color: 'red',
    },
  },
});

const NameEditor = (props: Props) => {
  const { defaultName, nameValue, handleChange, handleSubmit } = props;
  const [isEditable, setIsEditable] = useState(false);
  const classes = useStyles();
  const handleEditClick = () => {
    setIsEditable(true);
  };

  if (isEditable) {
    return (
      <div className="nameContainer">
        <TextField
          InputProps={{ classes: { underline: classes.underline } }}
          autoFocus={true}
          inputProps={{
            style: {
              fontSize: '20px',
              fontWeight: 'bold',
              fontFamily: '"AvenirNextRegular", "Arial", "sans-serif"',
              opacity: '.8',
            },
          }}
          placeholder={defaultName}
          value={nameValue}
          onChange={handleChange}
          onBlur={async (e) => {
            await handleSubmit(e);
            setIsEditable(true);
          }}
          onKeyPress={async (event) => {
            if (event.key === 'Enter') {
              await handleSubmit(event);
            }
          }}
          required={true}
        />
        <Edit className="editIcon" cursor="pointer" onClick={handleEditClick} />
      </div>
    );
  } else {
    return (
      <div className="nameContainer">
        <h1 className="name">{nameValue || defaultName}</h1>
        <Edit className="editIcon" cursor="pointer" onClick={handleEditClick} />
      </div>
    );
  }
};

export default NameEditor;

import { Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { DialogComponent, RadioGroupComponent, RubyCheckbox } from '@nue-apps/ruby-ui-component';
import React, { useState } from 'react';
import type { RadioItem } from '../radio-group-component';
import type { ExpressCheckoutDialogProps } from './interface';

const useStyles = makeStyles({
  root: {
    marginBottom: '12px',
    '& .Mui-disabled': {
      '& .Mui-checked': {
        color: '#6239eb',
      },
      color: 'inherit',
    },
  },
  checkbox: {
    marginLeft: '0px',
  },
});

const ExpressCheckoutDialog: React.FC<ExpressCheckoutDialogProps> = (props) => {
  const classes = useStyles();

  const { open, handleExpressCheckout, onClose } = props;

  const [orderStatusOption, setOrderStatusOption] = useState<string>('draft');
  const [includeChildren, setIncludeChildren] = useState<boolean>(false);

  const handleSubmit = async () => {
    handleExpressCheckout({ orderStatusOption, includeChildren });
  };

  const handleClose = () => {
    setOrderStatusOption('draft');
    onClose?.();
  };

  const radioOptions: RadioItem[] = [
    {
      label: 'as Draft orders',
      type: 'radio',
      radioSelection: {
        value: 'draft',
        enableCheckbox: false,
        showCheckbox: false,
      },
    },
    {
      label: 'as Active orders, which create or version subscriptions, assets or entitlements',
      type: 'radio',
      radioSelection: {
        value: 'active',
        enableCheckbox: false,
        showCheckbox: false,
      },
    },
  ];

  return (
    <DialogComponent
      open={open}
      title={'Express Checkout'}
      width={'md'}
      handleClose={handleClose}
      handleSubmit={handleSubmit}
      submitButtonText="Confirm"
      cancelButtonText="Cancel"
    >
      <div className={classes.root}>
        <Typography variant="body1" component="div" style={{ marginBottom: 12 }}>
          Checkout all changesets of this customer at once:
        </Typography>

        <RubyCheckbox
          label={'Create orders for each changeset automatically'}
          field={{
            apiName: 'orders',
            name: 'orders',
            type: 'boolean',
          }}
          readOnly={true}
          value={true}
          labelClassName={classes.checkbox}
        />
        <div style={{ marginLeft: '2.5rem' }}>
          <RadioGroupComponent
            handleSetRadioSelection={(newRadioSelection) => {
              setOrderStatusOption(newRadioSelection.value);
            }}
            handleUpdateOptions={() => {}}
            options={radioOptions}
            radioSelection={{ value: orderStatusOption }}
          />
        </div>
        <RubyCheckbox
          label={'Include all children accounts'}
          field={{
            apiName: 'includeChildren',
            name: 'includeChildren',
            type: 'boolean',
          }}
          value={includeChildren}
          handleInputChange={(newValue: boolean) => {
            setIncludeChildren(newValue);
          }}
          labelClassName={classes.checkbox}
        />
      </div>
    </DialogComponent>
  );
};

export default ExpressCheckoutDialog;

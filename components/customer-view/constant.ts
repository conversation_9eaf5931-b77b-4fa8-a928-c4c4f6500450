import type { ListViewMetadata, RubyObject } from '@nue-apps/ruby-ui-component';

export const revenueContractMetadata: RubyObject = {
  apiName: 'RevenueContracts',
  name: 'Revenue Contracts',
  fields: [
    {
      apiName: 'name',
      name: 'Name',
      type: 'id',
      creatable: false,
      updatable: false,
      filterable: true,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: true,
    },
    {
      apiName: 'customer',
      name: 'Customer',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'value',
      name: 'Contract Value',
      type: 'currency',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'stage',
      name: 'Stage',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'billed',
      name: 'Billed',
      type: 'currency',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'recognized',
      name: 'Recognized',
      type: 'currency',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'scheduled',
      name: 'Scheduled',
      type: 'currency',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'created_period',
      name: 'Created Period',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'modified_period',
      name: 'Modified Period',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'version',
      name: 'Version',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'isonhold',
      name: 'Is On Hold',
      type: 'boolean',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'amendment_version',
      name: 'Amendment Version',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'prior_rc_version',
      name: 'Prior Version',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'created_date',
      name: 'Created Date',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'modified_date',
      name: 'Modified Date',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'id',
      name: 'Id',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: true,
    },
    {
      apiName: 'mje_id',
      name: 'MJE Id',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'allocation_type',
      name: 'Allocation Type',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'doc_type',
      name: 'Doc Type',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'transactional_currency_code',
      name: 'Transactional Currency Code',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
    {
      apiName: 'ca_cl_position',
      name: 'CA CL Position',
      type: 'text',
      creatable: false,
      updatable: false,
      filterable: false,
      sortable: false,
      customField: false,
      encrypted: false,
      required: false,
      unique: false,
    },
  ],
};

export const revenueContractListMetadata: ListViewMetadata = {
  columns: [
    { apiName: 'name', name: 'Name', type: 'id' },
    { apiName: 'customer', name: 'Customer', type: 'text' },
    { apiName: 'value', name: 'Contract Value', type: 'currency' },
    { apiName: 'stage', name: 'Stage', type: 'text' },
    { apiName: 'billed', name: 'Billed', type: 'currency', style: { color: '#959595' } },
    { apiName: 'recognized', name: 'Recognized', type: 'currency', style: { color: '#0EB3D3' } },
    { apiName: 'scheduled', name: 'Scheduled', type: 'currency', style: { color: '#A33CEF' } },
    { apiName: 'created_period', name: 'Created Period', type: 'text' },
    { apiName: 'modified_period', name: 'Modified Period', type: 'text' },
    { apiName: 'version', name: 'Version', type: 'text' },
    { apiName: 'isonhold', name: 'Is On Hold', type: 'boolean', hidden: true },
    { apiName: 'allocation_type', name: 'Allocation Type', type: 'text', hidden: true },
    { apiName: 'amendment_version', name: 'Amendment Version', type: 'text', hidden: true },
    { apiName: 'ca_cl_position', name: 'CA CL Position', type: 'text', hidden: true },
    { apiName: 'created_date', name: 'Created Date', type: 'text', hidden: true },
    { apiName: 'doc_type', name: 'Doc Type', type: 'text', hidden: true },
    { apiName: 'mje_id', name: 'MJE Id', type: 'text', hidden: true },
    { apiName: 'id', name: 'Id', type: 'text', hidden: true },
    { apiName: 'modified_date', name: 'Modified Date', type: 'text', hidden: true },
    { apiName: 'prior_rc_version', name: 'Prior Version', type: 'text', hidden: true },
    {
      apiName: 'transactional_currency_code',
      name: 'Transactional Currency Code',
      type: 'text',
      hidden: true,
    },
  ],
};

export const time = new Date().toString().split(' ')[5];
export const timezone = time.slice(3).slice(0, 3) + ':' + time.slice(3).slice(3);

export const transactionHubProps = {
  showLaunch: true,
  quickBookRedirectUrl: '',
  salesforceRedirectUrl: '',
};

export const defaultDateFormat = 'YYYY-MM-DD';

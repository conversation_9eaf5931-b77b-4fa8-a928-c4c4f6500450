import React from 'react';

import { Card, Grid, Tooltip, Typography, makeStyles } from '@material-ui/core';
import { Theme, withStyles } from '@material-ui/core/styles';
import InfoIcon from '@material-ui/icons/Info';

import { InfoCardProps } from './interface';

const defaultProps = {
  subInfoColor: 'black',
};

const StyledCard = withStyles((theme: Theme) => ({
  root: {
    border: '0.5px solid #DEDEDE',
    borderRadius: 'inherit',
    boxShadow: '0 4px 8px -2px rgba(0,0,0,0.08)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    padding: '32px',
    width: '100%',
  },
}))(Card);

const useStyles = makeStyles({
  title: {
    opacity: '0.4',
    fontSize: '12px',
    fontWeight: 'bold',
    textTransform: 'uppercase',
    whiteSpace: 'nowrap',
  },
  infoWrapper: {
    display: 'flex',
    alignItems: 'flex-start',
    margin: '10px 0 5px',
    position: 'relative',
  },
  info: {
    fontSize: '32px',
    fontWeight: 'bold',
  },
  infoSymbol: {
    fontSize: '12px',
    marginRight: '10px',
    position: 'absolute',
    top: '25px',
    left: '-30px',
  },
  subInfo: {
    fontSize: '16px',
    whiteSpace: 'nowrap',
  },
  subInfoWrapper: {
    display: 'flex',
    alignItems: 'center',
  },
  green: {
    color: '#40D07E',
  },
  red: {
    color: '#D04040',
  },
  upTriangle: {
    width: '0',
    height: '0',
    borderWidth: '0 6px 8px 6px',
    borderColor: 'transparent transparent #40D07E transparent',
    borderStyle: 'solid',
    marginRight: '10px',
  },
  downTriangle: {
    width: '0',
    height: '0',
    borderWidth: '8px 6px 0 6px',
    borderColor: '#D04040 transparent transparent transparent',
    borderStyle: 'solid',
    marginRight: '10px',
  },
  infoIcon: {
    fontSize: '.875rem',
    color: '#6239EB',
    cursor: 'pointer',
  },
});

const InfoCard: React.FC<InfoCardProps> = (userProps) => {
  const classes = useStyles();

  const props = { ...defaultProps, ...userProps };

  const { title, info, infoSymbol, infoColor, subInfo, subInfoColor, tipTitle } = props;

  const getInfoClasses = (infoClass: string, infoColor?: string) => {
    if (infoColor === 'green') {
      return `${classes.green} ${infoClass}`;
    } else if (infoColor === 'red') {
      return `${classes.red} ${infoClass}`;
    } else {
      return infoClass;
    }
  };

  return (
    <StyledCard>
      {tipTitle ? (
        <Grid item container spacing={2} style={{ justifyContent: 'center', marginTop: '6px' }}>
          <Grid item style={{ fontSize: '14px', color: '#999999', height: '20px' }}>
            {title}
          </Grid>
          <Grid item style={{ marginTop: '1px', marginLeft: '-10px' }}>
            <Tooltip title={tipTitle} arrow placement="top">
              <InfoIcon className={classes.infoIcon} />
            </Tooltip>
          </Grid>
        </Grid>
      ) : (
        <Typography className={classes.title}>{title}</Typography>
      )}
      <div className={classes.infoWrapper} role="info">
        {infoSymbol && <span className={classes.infoSymbol}>{infoSymbol}</span>}
        <Typography className={getInfoClasses(classes.info, infoColor)}>{info}</Typography>
      </div>
      <div className={getInfoClasses(classes.subInfoWrapper, subInfoColor)} role="subInfo">
        {subInfoColor && subInfoColor !== 'black' && (
          <div className={subInfoColor === 'green' ? classes.upTriangle : classes.downTriangle} />
        )}
        {subInfo && <Typography className={classes.subInfo}>{subInfo} &nbsp; </Typography>}
      </div>
    </StyledCard>
  );
};

InfoCard.displayName = 'InfoCard';

export default InfoCard;

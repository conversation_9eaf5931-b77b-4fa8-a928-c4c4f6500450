{"name": "@nue-apps/ruby-ui-component", "version": "2505.0.3", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "lib/index.js", "module": "esm/index.js", "typings": "lib/index.d.ts", "files": ["lib", "esm", "static"], "scripts": {"build": "npm run clean && npm run build:types && gulp", "build-storybook": "build-storybook", "build:doc": "rimraf doc-site && docz build", "build:types": "tsc -p tsconfig.build.json && cpr lib esm", "clean": "<PERSON><PERSON>f lib esm dist", "commit": "git-cz", "copy2nue": "cp -r ./esm ../ruby-ui/node_modules/@nue-apps/ruby-ui-component && cp -r ./lib ../ruby-ui/node_modules/@nue-apps/ruby-ui-component && cp -r ./static ../ruby-ui/node_modules/@nue-apps/ruby-ui-component", "deploy2nue": "yarn build && yarn copy2nue", "copy2revenue-ui": "cp -r ./esm ../revenue-builder-ui/node_modules/@nue-apps/ruby-ui-component && cp -r ./lib ../revenue-builder-ui/node_modules/@nue-apps/ruby-ui-component && cp -r ./static ../revenue-builder-ui/node_modules/@nue-apps/ruby-ui-component", "deploy2revenue-ui": "yarn build && yarn copy2revenue-ui", "copy2sf": "cp -r ./esm ../ruby-revenue-builder-sf/node_modules/@nue-apps/ruby-ui-component && cp -r ./lib ../ruby-revenue-builder-sf/node_modules/@nue-apps/ruby-ui-component && cp -r ./static ../ruby-revenue-builder-sf/node_modules/@nue-apps/ruby-ui-component && cd ../ruby-revenue-builder-sf && npm run deploy", "deploy2sf": "yarn build && yarn copy2sf", "deploy-all": "yarn build && yarn copy2nue && yarn copy2revenue-ui && yarn copy2sf", "dev": "docz dev", "new": "plop --plopfile ./scripts/plopfile.ts", "preview:doc": "docz serve", "release": "ts-node ./scripts/release.ts", "start": "npm run dev", "storybook": "start-storybook -p 6006 -c .storybook watch-css -s ./.storybook", "test": "jest --silent", "test:ci": "jest --silent --config ./jest.config.js --ci --reporters=default --reporters=jest-junit --watchAll=false"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"components/**/*.ts?(x)": ["prettier --write", "git add"], "components/**/*.less": ["stylelint --syntax less --fix", "git add"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "resolutions": {"**/ts-node": "^10.8.1", "@types/react": "^17.0.3", "@types/react-dom": "^17.0.3", "csstype": "^2.5.2", "execa": "^2", "node-fetch": "2.6.9", "react": "^16.13.1", "react-dom": "^16.13.1", "tough-cookie": "4.1.3", "express": "4.20.0", "dompurify": "2.5.4", "fast-loops": "1.1.4"}, "dependencies": {"@babel/plugin-proposal-private-methods": "^7.14.5", "@babel/runtime": "^7.11.2", "@date-io/core": "^1.3.13", "@date-io/date-fns": "^1.3.13", "@devexpress/dx-core": "^2.5.1", "@devexpress/dx-react-core": "^2.5.1", "@devexpress/dx-react-grid": "^2.5.1", "@devexpress/dx-react-grid-material-ui": "^2.5.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@juggle/resize-observer": "3.4.0", "@material-ui/core": "^4.11.3", "@material-ui/icons": "^4.11.2", "@material-ui/lab": "^4.0.0-alpha.57", "@material-ui/pickers": "3.3.10", "@microsoft/fetch-event-source": "^2.0.1", "@neutrixs/colorthief": "^2.5.0", "@nue-apps/ruby-pricing-calculator": "0.16.12", "@popperjs/core": "^2.4.4", "@types/estree": "^0.0.48", "@types/html2canvas": "^1.0.0", "@types/inquirer": "^7.3.0", "@types/jspdf": "^2.0.0", "@types/numeral": "^2.0.5", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.12", "@types/react-grid-layout": "1.3.2", "@types/semver": "^7.3.1", "@types/uuid": "^8.3.0", "array-move": "^3.0.1", "babel-plugin-async-to-promises": "^1.0.5", "bignumber.js": "^9.0.1", "css-mediaquery": "^0.1.2", "date-fns": "2.29.3", "dayjs": "^1.9.6", "echarts": "5.2.2", "eslint-plugin-jest": "^24.4.2", "format-graphql": "^1.4.0", "get-pixels": "^3.3.3", "html2canvas": "1.4.1", "immer": "^9.0.6", "inquirer": "^7.3.3", "jspdf": "^2.5.1", "localforage": "^1.9.0", "lodash": "^4.17.21", "material-ui-popup-state": "^1.6.1", "numbro": "^2.5.0", "numeral": "^2.0.6", "papaparse": "^5.3.2", "pdf-lib": "^1.17.1", "prop-types": "^15.7.2", "qrcode": "^1.5.3", "quantize": "^1.0.2", "react": "^16.13.1", "react-beautiful-dnd": "^13.1.1", "react-code-input": "^3.10.1", "react-color": "^2.19.3", "react-draggable": "4.4.6", "react-grid-layout": "1.3.4", "react-hook-form": "^6.7.0", "react-jss": "^10.10.0", "react-markdown": "^8.0.7", "react-number-format": "^4.4.1", "react-popper": "^2.2.3", "react-resize-detector": "^7.0.0", "react-sortable-hoc": "^1.11.0", "remark-gfm": "^3.0.1", "semver": "^7.3.2", "short-uuid": "^4.1.0", "uuid": "^8.3.0", "yarn": "^1.22.5", "yup": "^0.29.3"}, "devDependencies": {"@babel/core": "7.14.3", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-transform-runtime": "^7.11.0", "@babel/preset-env": "^7.11.0", "@babel/preset-react": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@commitlint/cli": "^9.1.1", "@commitlint/config-conventional": "^9.1.1", "@storybook/addon-actions": "^6.1.15", "@storybook/addon-essentials": "^6.1.15", "@storybook/addon-links": "^6.1.15", "@storybook/react": "^6.1.15", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.1.0", "@testing-library/user-event": "^14.6.1", "@trivago/prettier-plugin-sort-imports": "^4.0.0", "@types/css-mediaquery": "^0.1.1", "@types/enzyme": "^3.10.8", "@types/estree": "^0.0.48", "@types/inquirer": "^7.3.0", "@types/jest": "^27.0.0", "@types/qrcode": "^1.5.5", "@types/react": "^17.0.3", "@types/react-dom": "^17.0.3", "@types/semver": "^7.3.1", "@types/vfile-message": "^2.0.0", "@types/yup": "^0.29.6", "@umijs/fabric": "^2.2.2", "babel-loader": "^8.2.2", "commitizen": "^4.1.2", "cpr": "^3.0.1", "cz-conventional-changelog": "^3.2.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.5", "gatsby-plugin-less": "^4.0.3", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "husky": "^4.2.5", "inquirer": "^7.3.3", "jest": "^27.2.0", "jest-canvas-mock": "^2.3.1", "jest-junit": "^16.0.0", "lint-staged": "^10.2.11", "plop": "^2.7.4", "prettier": "^3.3.3", "prismjs": "^1.21.0", "react": "^16.13.1", "react-copy-to-clipboard": "^5.0.2", "react-dom": "^16.13.1", "react-feather": "^2.0.8", "react-simple-code-editor": "^0.11.0", "react-tooltip": "^4.2.8", "react-use": "^15.3.3", "semver": "^7.3.2", "styled-components": "^5.1.1", "ts-jest": "^27.0.5", "typescript": "^4.7.4"}, "overrides": {"fast-loops": "^1.1.4"}, "engines": {"node": ">=16.0.0 <17.0.0"}, "publishConfig": {"registry": "https://nexus.nuedev.click/repository/npm-private/"}}
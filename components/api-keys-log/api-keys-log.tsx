import React, { useEffect, useState } from 'react';

import { Grid, InputAdornment, InputLabel, Paper, TextField, makeStyles } from '@material-ui/core';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import debounce from 'lodash/debounce';

import { DateInput, Loading, ModalAppBar, PickList, SearchIcon, TextInput } from '../index';
import { useDebounce } from '../webhook-connection/webhook-connection-hook';
import ApiKeysEventDetail from './api-keys-event-detail';
import ApiKeysEventLog from './api-keys-event-log';
import ApiKeysLogEmpty from './api-keys-log-empty';

const PAGE_COUNT = 20;

const useStyle = makeStyles({
  header: {
    display: 'flex',
    alignItems: 'center',
  },
  status: {
    fontSize: 18,
    marginLeft: 20,
    padding: 5,
  },
  url: {
    fontSize: 15,
    marginBottom: 15,
    display: 'flex',
    alignItems: 'flex-start',
  },
  events: {},
  eventLogs: {
    marginTop: 15,
    marginBottom: 15,
    fontSize: '1.3rem',
  },
  container: {
    padding: 0,
    boxShadow: 'none',
    border: '1px solid rgba(0,0,0,0.2)',
    maxHeight: '75vh',
    minHeight: '40vh',
    overflow: 'hidden',
  },
  logContainer: {
    borderRight: '1px solid lightgray',
    maxHeight: '75vh',
    minHeight: '40vh',
    overflowY: 'scroll',
  },
  detailContainer: {
    maxHeight: '75vh',
    minHeight: '40vh',
    overflowY: 'scroll',
  },
  dialogTitle: {
    padding: 0,
  },
  dialogContent: {},
  titleContainer: {
    marginLeft: 32,
    display: 'flex',
    alignItems: 'center',
  },
  iconContainer: {
    color: '#8b66f4',
    height: 24,
    marginRight: 4,
  },
  highlightText: {
    color: '#8b66f4',
    marginRight: 8,
  },
  root: {
    backgroundColor: '#0094ff',
  },
  filterContainer: {
    marginBottom: 15,
  },
  eventsNumber: {
    color: '#6239eb',
    fontSize: '0.8rem',
    cursor: 'pointer',
    marginLeft: '10px',
    marginTop: '1px',
  },
  menuContainer: {
    maxHeight: '160px',
  },
  searchBar: {
    borderRightColor: 'whitesmoke',
    borderRadius: '4px 0px 0px 4px !important',
    fontSize: '.875rem',
    height: '100%',
    marginTop: '0 !important',
    backgroundColor: '#f1f0f2 !important',
    border: 'none',
    minHeight: '22px',
    paddingLeft: 0,
    paddingTop: 10,
    paddingBottom: 10,
  },
  searchBarRoot: {
    overflow: 'hidden',
    borderRadius: '4px 0 0 4px !important',
    backgroundColor: '#f1f0f2 !important',
  },
});

export interface EvnetLog {
  id: string;
  eventId: string;
  eventType: string;
  registryId: string;
  status: 'Queued' | 'Posted' | 'Failed' | 'PartialPosted' | 'Ignored';
  target: string;
  request?: any;
  response?: any;
  createdAt: string;
}

interface Props {
  loadApiKeyLog: (
    apiKeyIds: string[],
    page: number,
    pageSize: number,
    statusCode?: number,
    accessDate?: string,
    endPoint?: string,
    method?: string,
  ) => any;
  selectedRow: any;
  onClose: () => void;
  open: boolean;
  getEventRequestDetails: (id: string) => any;
}

const ApiKeysLog = ({
  loadApiKeyLog,
  selectedRow,
  onClose,
  open,
  getEventRequestDetails,
}: Props) => {
  const classes = useStyle();
  const [eventLogs, setEventLogs] = useState<any>({});
  const [activeLog, setActiveLog] = useState<any>();
  const [isEventLogsLoading, setIsEventLogsLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pageKey, setPageKey] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [filter, setFilter] = useState({
    endPoint: undefined,
    statusCode: undefined,
    httpMethod: undefined,
    accessDate: undefined,
  });

  const checkHasMore = (data: any) => {
    if (!data) {
      return false;
    }
    const values = Object.values(data);
    if (values.length >= PAGE_COUNT) {
      return true;
    }
    let total = 0;
    values.forEach((innerValues: any) => {
      if (innerValues?.length) {
        total += innerValues.length;
      }
    });

    return total >= PAGE_COUNT;
  };

  const setUp = async () => {
    try {
      setIsEventLogsLoading(true);
      const result = await loadApiKeyLog([selectedRow.rootId], 0, PAGE_COUNT);
      if (result?.data && Object.keys(result?.data).length) {
        setEventLogs(result.data);
        const selected: any = Object.values(result.data);
        setActiveLog(selected?.[0]?.[0]);
        setIsEventLogsLoading(false);
        if (checkHasMore(result?.data)) {
          setHasMore(true);
        }
      } else {
        setIsEventLogsLoading(false);
      }
    } catch {
      setIsEventLogsLoading(false);
    }
  };

  const debounceHandleSearch = useDebounce(async (updatedFilter: any) => {
    setIsEventLogsLoading(true);
    const result = await loadApiKeyLog(
      [selectedRow.rootId],
      0,
      PAGE_COUNT,
      updatedFilter.statusCode,
      updatedFilter.accessDate,
      updatedFilter.endPoint,
      updatedFilter.httpMethod,
    );
    setPageKey(0);

    if (result?.data && Object.keys(result?.data).length) {
      setEventLogs(result.data);
      const selected: any = Object.values(result.data);
      setActiveLog(selected?.[0]?.[0]);
      setHasMore(checkHasMore(result?.data));
      setIsEventLogsLoading(false);
    } else {
      setEventLogs([]);
      setHasMore(false);
      setIsEventLogsLoading(false);
    }
  }, 1000);

  const triggerRef = React.useRef(null);

  const _handleEntry = async (entry: any) => {
    const boundingRect = entry.boundingClientRect;
    const intersectionRect = entry.intersectionRect;
    if (
      hasMore &&
      !loadingMore &&
      entry.isIntersecting &&
      intersectionRect.bottom - boundingRect.bottom <= 5
    ) {
      setLoadingMore(true);
      const result = await loadApiKeyLog([selectedRow.rootId], pageKey + 1, PAGE_COUNT);
      setPageKey(pageKey + 1);

      if (result?.data && Object.keys(result?.data).length) {
        const newEventLogs = { ...eventLogs };
        Object.keys(result.data).forEach((key) => {
          if (newEventLogs[key]) {
            newEventLogs[key] = [...newEventLogs[key], ...result.data[key]];
          } else {
            newEventLogs[key] = result.data[key];
          }
        });

        setEventLogs(newEventLogs);
        setHasMore(checkHasMore(result?.data));
        setLoadingMore(false);
      } else {
        setHasMore(false);
        setLoadingMore(false);
      }
    }
  };

  const handleEntry = debounce(_handleEntry, 500);

  const onIntersect = React.useCallback(
    (entries) => {
      handleEntry(entries[0]);
    },
    [handleEntry],
  );

  useEffect(() => {
    if (triggerRef.current && hasMore) {
      const container = triggerRef.current;
      const observer = new IntersectionObserver(onIntersect);

      observer.observe(container);

      return () => {
        observer.disconnect();
      };
    }
  }, [triggerRef, onIntersect, hasMore]);

  useEffect(() => {
    setUp();
  }, []);

  return (
    <>
      <Dialog
        open={open}
        fullWidth
        fullScreen
        PaperProps={{
          style: {
            backgroundColor: '#F9F8FA',
          },
        }}
      >
        <DialogTitle className={classes.dialogTitle}>
          <ModalAppBar
            title={''}
            handleClose={() => {
              onClose();
            }}
          />
        </DialogTitle>
        <DialogContent className={classes.dialogContent}>
          <div style={{ paddingLeft: 20, paddingRight: 20, paddingBottom: 16 }}>
            <h3>Request Logs</h3>
            <div className={classes.header}>
              <h2>{selectedRow.name}</h2>
            </div>
            <Grid container xs={12} style={{ marginBottom: 8 }} spacing={2}>
              <Grid item xs={2}>
                <PickList
                  readOnly={false}
                  value={filter.statusCode}
                  field={{
                    apiName: 'statusCode',
                    type: 'text',
                    name: 'HTTP Status Code',
                    defaultValue: 'ANY',
                  }}
                  label={''}
                  handleInputChange={(newValue) => {
                    const newFilter = {
                      ...filter,
                      statusCode: newValue === 'ANY' ? undefined : newValue,
                    };
                    debounceHandleSearch(newFilter);
                    setFilter(newFilter);
                  }}
                  options={[
                    {
                      name: 'ANY',
                      value: 'ANY',
                    },
                    {
                      name: '200',
                      value: '200',
                    },
                    {
                      name: '400',
                      value: '400',
                    },
                    {
                      name: '403',
                      value: '403',
                    },
                    {
                      name: '404',
                      value: '404',
                    },
                    {
                      name: '500',
                      value: '500',
                    },
                    {
                      name: '502',
                      value: '502',
                    },
                  ]}
                />
              </Grid>
              <Grid item xs={2}>
                <PickList
                  readOnly={false}
                  value={filter.httpMethod}
                  field={{
                    apiName: 'requestMethod',
                    type: 'text',
                    name: 'Request Method',
                    defaultValue: 'ANY',
                  }}
                  label={''}
                  handleInputChange={(newValue) => {
                    const newFilter = {
                      ...filter,
                      httpMethod: newValue === 'ANY' ? undefined : newValue,
                    };
                    debounceHandleSearch(newFilter);
                    setFilter(newFilter);
                  }}
                  options={[
                    {
                      name: 'ANY',
                      value: 'ANY',
                    },
                    {
                      name: 'GET',
                      value: 'GET',
                    },
                    {
                      name: 'POST',
                      value: 'POST',
                    },
                    {
                      name: 'PUT',
                      value: 'PUT',
                    },
                    {
                      name: 'DELETE',
                      value: 'DELETE',
                    },
                    {
                      name: 'PATCH',
                      value: 'PATCH',
                    },
                  ]}
                />
              </Grid>
              <Grid item xs={2}>
                <DateInput
                  required={false}
                  value={filter.accessDate}
                  field={{
                    apiName: 'date',
                    required: true,
                    name: 'Date',
                    type: 'date',
                  }}
                  clearable={true}
                  name="date-input"
                  handleInputChange={(newValue) => {
                    const newFilter = { ...filter, accessDate: newValue };
                    debounceHandleSearch(newFilter);
                    setFilter(newFilter);
                  }}
                />
              </Grid>
              <Grid item xs={4}>
                <TextInput
                  label={'Endpoint'}
                  applyBottomPadding
                  classes={{
                    input: classes.searchBar,
                    root: classes.searchBarRoot,
                  }}
                  placeholder="Search Endpoint"
                  startAdornment={
                    <InputAdornment position="start">
                      <SearchIcon viewBox={'-2 -2 21 21'} style={{ paddingLeft: 4 }} />
                    </InputAdornment>
                  }
                  handleInputChange={(newValue) => {
                    const newFilter = { ...filter, endpoint: newValue };
                    debounceHandleSearch(newFilter);
                    setFilter(newFilter);
                  }}
                  field={{
                    apiName: 'search',
                    type: 'text',
                    name: 'Search',
                  }}
                  value={filter.endPoint}
                />
              </Grid>
            </Grid>

            <Paper
              className={classes.container}
              style={{
                border:
                  Object.entries(eventLogs).length === 0 ? 'none' : '1px solid rgba(0,0,0,0.2)',
              }}
            >
              {Object.entries(eventLogs).length > 0 ? (
                <Grid xs={12}>
                  <Grid container>
                    <Grid xs={6} item className={classes.logContainer}>
                      {isEventLogsLoading ? (
                        <Loading />
                      ) : (
                        <>
                          {Object.entries(eventLogs).map(([key, value], index) => {
                            return (
                              <ApiKeysEventLog
                                dateKey={key}
                                event={value}
                                key={index}
                                setActiveLog={setActiveLog}
                                activeLog={activeLog}
                              />
                            );
                          })}
                          {hasMore && (
                            <div
                              ref={triggerRef}
                              style={{
                                paddingBottom: 8,
                                visibility: loadingMore ? 'visible' : 'hidden',
                              }}
                            >
                              <Loading />
                            </div>
                          )}
                        </>
                      )}
                    </Grid>
                    <Grid xs={6} item className={classes.detailContainer}>
                      <ApiKeysEventDetail
                        activeLog={activeLog}
                        getEventRequestDetails={getEventRequestDetails}
                      />
                    </Grid>
                  </Grid>
                </Grid>
              ) : isEventLogsLoading ? (
                <Grid xs={12}>
                  <Grid container style={{ minHeight: '75vh', alignItems: 'center' }}>
                    <Loading />
                  </Grid>
                </Grid>
              ) : (
                <ApiKeysLogEmpty />
              )}
            </Paper>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ApiKeysLog;

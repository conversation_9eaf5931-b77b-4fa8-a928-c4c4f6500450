import { <PERSON><PERSON><PERSON>, RubyFilter, RubyObject } from '@nue-apps/ruby-ui-component';
import { InvokeObject } from '../filter-builder';
import { SelectedObject, SelectedObjectStyleDecider } from '../multi-selection-search-bar';
import QueryExecutor from '../query-executor';

export interface CustomerProps {
  open: boolean;
  setOpen: Function;
  title: string;
  activeTabIndex: number;
  setActiveTabIndex: Function;
  executeQuery: QueryExecutor;
  objectMetadata: RubyObject;
  searchFields: RubyField[];
  selectedValues: SelectedObject[];
  setSelectedValues: Function;
  selectedObjectStyleDecider: SelectedObjectStyleDecider;
  disabled?: boolean;
  customerBillCycleDay: string;
  setCustomerBillCycleDay: Function;
  billCycleDayOptions: string[];
  customerBillCycleDaySelected: string;
  setCustomerBillCycleDaySelected: Function;
  isWebCreated: boolean;
  customerFilter: string;
  setCustomerFilter: Function;
  customerInvokeObject: InvokeObject;
  savedFilters: Array<RubyFilter>;
  associateSubsidiaries: boolean;
  setAssociateSubsidiaries: Function;
  associateBillingAccounts: boolean;
  setAssociateBillingAccounts: Function;
}

export interface ProductProps {
  open: boolean;
  setOpen: Function;
  title: string;
  objectMetadata: RubyObject;
  activeTabIndex: number;
  setActiveTabIndex: Function;
  disabled?: boolean;
  revenueModel: RevenueModel;
  setRevenueModel: Function;
  isWebCreated: boolean;
  productFilter: string;
  productInvokeObject: InvokeObject;
  savedFilters: Array<RubyFilter>;
}

export interface RevenueModel {
  oneTime: boolean;
  recurring: boolean;
  usage: boolean;
  creditBurndown: boolean;
  credit: boolean;
}

export interface OrderProps {
  open: boolean;
  setOpen: Function;
  title: string;
  activeTabIndex: number;
  setActiveTabIndex: Function;
  executeQuery: QueryExecutor;
  objectMetadata: RubyObject;
  selectedValues: SelectedObject[];
  setSelectedValues: Function;
  disabled?: boolean;
  isWebCreated: boolean;
  orderFilter: string;
  orderInvokeObject: InvokeObject;
  savedFilters: Array<RubyFilter>;
}

import type { CellRendererRowProps } from '../grid-actions';
import type { Card<PERSON><PERSON>, GridViewMetadata, RubyField, RubyObject } from '../metadata/interface';
import type { RubySettings } from '../ruby-settings';

export interface Props {
  fieldSetMetadata: RubyField[];
  gridViewMetadata: GridViewMetadata;
  object: any;
  currencyIsoCode: string;
  objectMetadata: RubyObject;
  handleCardClick?: (object: any) => void;
  orderGridViewMetadata?: GridViewMetadata | null;
  orderFieldSetMetadata?: RubyField[] | null;
  subscriptionPricingFieldSet?: RubyField[] | null;
  actionEventHandler: (argument: {
    object: any;
    objectMetadata: RubyObject;
    action: CardAction;
    assetHierarchy?: any;
    ref?: string;
  }) => void | Promise<void>;
  getFieldSetMetadata?: (fieldSetApiName: string, objectApiName: string) => Promise<RubyField[]>;
  showSubmitBtn?: boolean;
  onShowDetails?: (object: any) => void;
  showDetails?: boolean;
  isPopup?: boolean;
  rubySettings?: RubySettings;
  locale?: string;
  summaryView?: boolean;
  addTransactionHub?: () => {
    apiName: string;
    type: string;
    name: string;
    title: string;
    cellRenderer: ({ row }: CellRendererRowProps) => JSX.Element | null;
  };
}

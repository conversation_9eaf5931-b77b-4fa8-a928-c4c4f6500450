import { Typography, Grid } from '@material-ui/core';
import Button from '@material-ui/core/Button';
import Popover from '@material-ui/core/Popover';
import IconButton from '@material-ui/core/IconButton';
import Tooltip from '@material-ui/core/Tooltip';
import {
  BallotOutlined,
  CheckCircleOutlined,
  PowerSettingsNewOutlined,
  CreditCard,
} from '@material-ui/icons';
import StarIcon from '@material-ui/icons/Star';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import ChatOutlinedIcon from '@material-ui/icons/ChatOutlined';
import FindInPageIcon from '@material-ui/icons/FindInPage';
import MonetizationOnOutlinedIcon from '@material-ui/icons/MonetizationOnOutlined';
import type {
  Breadcrumb,
  CellRendererRowProps,
  ChangeGroup,
  ChangeItem,
  GridViewMetadata,
  ListViewLocalStateService,
  OrderByField,
  RightRevIntegration,
  RubyField,
  RubyFilter,
  RubyObject,
  TabFilter,
  TaxDetail,
} from '@nue-apps/ruby-ui-component';
import {
  AccountViewItem,
  BalanceIcon,
  BreadcrumbContext,
  ChangeCart,
  DialogComponent,
  GraphqlQueryConstructor,
  GridActions,
  GridAndListView,
  GridViewItem,
  GridViewMetadataApi,
  LoadingScreenTimeout,
  MultiCurrencyContext,
  NetSuiteLogoIcon,
  QuickbookLogoIconForTrans,
  StripeLogoIcon,
  RubyButton,
  RubyListViewLocalStateContext,
  TaxBreakdownModal,
  UserContext,
  ViewIcon,
  toTaxLines,
  useCustomLabel,
  useRubySnackbarContext,
} from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import localforage from 'localforage';
import _, { result } from 'lodash';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import type { BatchAction } from '../batch-action-bar';
import type { Milestone } from '../create-milestone';
import CreditPoolSelector from '../credit-pool-selector/credit-pool-selector';
import { CustomerViewContext } from '../customer-view-context/customer-view-context';
import type { AccountHierarchy } from '../customer-view-page';
import creditFiledSetMetadata from '../field-set-metadata-api/credit-field-set-metadata';
import usageFiledSetMetadata from '../field-set-metadata-api/usage-field-set-metadata';
import { GeneralInformationSummaryView } from '../general-information-summary-view/general-information-summary-view';
import {
  DeleteButton,
  GridButton,
  MuiGridButton,
  PopoverButton,
  ViewButton,
} from '../grid-actions/grid-actions';
import InvoiceCommentPopupContent from '../invoice-comment-popup/invoice-comment-popup';
import InvoiceListSelector from '../invoice-periods-selector/invoice-periods-selector';
import { formatNumberForDisplay } from '../list/list';
import ManageMilestoneDialog from '../manage-milestone-dialog/manage-milestone-dialog';
import type {
  CustomerViewPageMode,
  ListViewMetadata,
  SelectedSubscription,
  User,
} from '../metadata';
import type { TabMetadata, TabMetadataState } from '../panel-with-tabs';
import { PreviewInvoiceDialog } from '../preview-invoice/preview-invoice-dialog';
import { PreviewInvoiceGridDialog } from '../preview-invoice/preview-invoice-grid-dialog';
import type { CreditStatistics, InvoicePeriods } from '../revenue-builder-types';
import type { StatsBarData } from '../ruby-list-view';
import {
  constructDataItems,
  constructDateGroups,
  getPredefinedFilters,
  sortByDate,
} from '../ruby-list-view';
import type { CustomerViewPageTab } from '../ruby-list-view-local-state-context';
import { CurrencySelector } from '../ruby-list-view/ruby-list-view';
import type { CreditTypes } from '../ruby-settings';
import type { StripeTaxConfig } from '../stripe-connection';
import SubscriptionActionDialog from '../subscription-action-dialog';
import SubscriptionCard, { getSubscriptionChangeValidationSchema } from '../subscription-card';
import { ActivateInvoiceDialog } from '../subscription-card/activate-invoice-dialog/activate-invoice-dialog';
import { AdjustPriceDialog } from '../subscription-card/adjust-price-dialog/adjust-price-dialog';
import { BulkRenewDialog } from '../subscription-card/bulk-renew-dialog/bulk-renew-dialog';
import { CancelInvoiceDialog } from '../subscription-card/cancel-invoice-dialog/cancel-invoice-dialog';
import { CashOutDialog } from '../subscription-card/cash-out-dialog/cash-out-dialog';
import type {
  ChangeModalProps,
  ManageMilestoneDialogProps,
  UpdateDialogProps,
} from '../subscription-card/interface';
import { SubscriptionBulkCancelForm } from '../subscription-card/subscription-bulk-cancel-form';
import {
  getDefaultValues,
  getFields,
  getLatestSubscriptionEndDateOfAllSubscriptions,
} from '../subscription-card/subscription-card-util';
import type { TransactionDetail } from '../transaction-dialog';
import UpdateQuantityActionDialog from '../update-quantity-action-dialog/update-quantity-action-dialog';
import type { UsageChartDataItem } from '../usage-chart';
import { UsageCreditSelector, UsageDashboard } from '../usage-chart';
import { ActionDialog } from './action-dialog';
import { getOriginalSubscriptionNumbers, setupSubscriptionFilters } from './customer-view-helper';
import type { Props, RevenueContractsResult, TabMode } from './interface';
import { SubscriptionBulkActions } from '../modals';
import { useStyles } from './styles';
import { revenueContractMetadata, timezone, transactionHubProps } from './constant';
import {
  buildSubscriptionHierarchy,
  extractErrorMessage,
  getAddMultipleGroupsToCartResult,
  loadChildernAndBillingAccountsByIds,
  loadTransactions,
  showTransactionHub,
  createTransactionHubPopoverButton,
} from './utils/utils';
import InvoiceBarChart from './components/InvoiceBarChart';
import CreditMemosBarChart from './components/CreditMemosBarChart';
import CreditsBarChart from './components/CreditsBarChart';
import RevenueContractsStatsBar from './components/RevenueContractsStatsBar';

const defaultProps = {};

export const CustomerView: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const classes = useStyles();

  const { customerViewService, showChildrenAccounts, setShowChildrenBtn } =
    useContext(CustomerViewContext);
  const { breadcrumbService } = useContext(BreadcrumbContext);

  const { userContextService } = useContext(UserContext);
  const { defaultCurrency, isMultiCurrencyEnabled, availableCurrencies } =
    useContext(MultiCurrencyContext);
  const snackbarService = useRubySnackbarContext();

  if (!userContextService) {
    throw Error(
      'CustomerView component requires userContextService to be declared in context provider',
    );
  }

  if (!customerViewService) {
    throw Error(
      'CustomerView component requires customerViewService to be declared in context provider',
    );
  }

  const [activeTabDetail, setActiveTabDetails] = useState<CustomerViewPageTab>({
    tabIndex: 0,
    tabMode: 'All',
  });
  const [selectedSubscriptions, setSelectedSubscriptions] = useState<SelectedSubscription[]>([]);
  const [customerViewPageMode, setCustomerViewPageMode] = useState<
    CustomerViewPageMode | undefined
  >();
  const [showPreviewInvoiceDialog, setShowPreviewInvoiceDialog] = useState(false);
  const [commentDetails, setCommentDetails] = useState<any | null>(null);
  const [objects, setObjects] = useState<any[] | null>(null);
  const { searchConfigs, customerViewConfigs, customer } =
    customerViewService.getCustomerViewConfig();
  const user = userContextService.getUserConfig().user as User;
  const [gridViewMetadata, setGridViewMetadata] = useState<GridViewMetadata | null>(null);
  const [fieldSetMetadata, setFieldSetMetadata] = useState<RubyField[] | null>(null);

  const [orderGridViewMetadata, setOrderGridViewMetadata] = useState<GridViewMetadata | null>(null);
  const [orderFieldSetMetadata, setOrderFieldSetMetadata] = useState<RubyField[] | null>(null);
  const [subscriptionPricingFieldSet, setSubscriptionPricingFieldSet] = useState<
    RubyField[] | null
  >(null);
  const [refreshDataFlag, setRefreshDataFlag] = useState(0);
  const [listViewMetadata, setListViewMetadata] = useState<RubyField[] | null>(null);
  const [openChangeModal, setOpenChangeModal] = useState<boolean>(false);
  const [cotermDate, setCotermDate] = useState<string>('');
  const [changeModalProps, setChangeModalProps] = useState<ChangeModalProps | null>(null);
  const [updateDialogProps, setUpdateDialogProps] = useState<UpdateDialogProps>();
  const [manageMilestoneDialogProps, setManageMilestoneDialogProps] =
    useState<ManageMilestoneDialogProps>();
  const [selectedObjectMetadata, setSelectedObjectMetadata] = useState<RubyObject | undefined>(
    undefined,
  );
  const [invoicePeriods, setInvoicePeriods] = useState<InvoicePeriods | null>(null);
  const [creditsStatistics, setCreditsStatistics] = useState<CreditStatistics | null>(null);

  const [selectedRowData, setSelectedRowData] = useState<any | null>();
  const [selectedRowType, setSelectedRowType] = useState<'Invoice' | 'CreditMemo'>('Invoice');
  const [showDialogForActivating, setShowDialogForActivating] = useState(false);
  const [showDialogForCanceling, setShowDialogForCanceling] = useState(false);
  const [showDialogForDeleting, setShowDialogForDeleting] = useState(false);

  const [revenueIntegration, setRevenueIntegration] = useState<RightRevIntegration | null>(null);
  const [statsBarData, setStatsBarData] = useState<StatsBarData | undefined>(undefined);
  const [usageChartData, setUsageChartData] = useState<any[] | undefined>(undefined);
  const [creditTypes, setCreditTypes] = useState<CreditTypes[] | null>(null);

  const [selectedCredit, setSelectedCredit] = useState<string>('');
  const [activeFilters, setActiveFilters] = useState<RubyFilter[]>();

  const [relatedBillingAccountsIds, setRelatedBillingAccountsIds] = useState<string[]>([]);
  const [childrenAccountIds, setChildrenAccountIds] = useState<string[]>([]);
  const [currentAccountIdANdChildrenAccountIds, setCurrentAccountIdANdChildrenAccountIds] =
    useState<string[]>([]);

  const [selectedCreditPoolId, setSelectedCreditPoolId] = useState<string>('');
  const [selectedObject, setSelectedObject] = useState(null);

  const [currency, setCurrency] = useState(defaultCurrency);

  const [taxBreakdown, setTaxBreakdown] = useState<TaxDetail | null>(null);
  const [showTaxBreakdown, setShowTaxBreakdown] = useState(false);
  const [showCashOutDialog, setShowCashOutDialog] = useState(false);

  const { getLabel } = useCustomLabel();
  const locale = user.locale;

  const {
    executeQuery,
    customerMetadata,
    quoteMetadata,
    orderMetadata,
    orderItemMetadata,
    subscriptionMetadata,
    assetMetadata,
    currencyIsoCode,
    opportunityMetadata,
    transactionHubMetadata,
    handleRemoveChangesFromCart,
    handleRemoveChangeItems,
    handleRemoveMultipleGroupsFromCart,
    changeGroups,
    handleOpenChangeCart,
    openChangeCart,
    getFieldSetMetadata,
    navigateToQuoteBuilder,
    navigateToCustomerViewPage,
    personPlaceholderImage,
    tabFilters: overrideTabFilters,
    rubySettings,
    entitlementMetadata,
    handleAddToCart,
    invoiceMetadata,
    loadInvoicePeriods,
    executeGetCountQuery,
    loadUsageMetadata,
    loadCreditMetadata,
    getPanelStatistics,
    getInvoiceIdsForAsset,
    getInvoicesByOrderItemIds,
    getSwapUpgradeDowngradeOptions,
    UpdateMileStonesStartDate,
    getOrderProductIdsByAssetId,
    loadCreditStatistics,
    creditMemosMetaData,
    paymentMethodsMetaData,
    getRevenueContracts,
    getRevenueIntegration,
    getCreditTypes,
    getRelatedBillingAccountsIdOfSalesAccount,
    handleAddMultipleGroupsToCart,
    loadInvoicesPreviewData,
    handleSaveInvoicePreview,
    loadSubscriptionStreamData,
    loadChildernAndBillingAccounts,
    loadStripeCardInfo,
    getAccountHierarchy,
    getTaxBreakdown,
    taxConfig,
    creditCashOut,
    integrations,
    sync,
    checkOut,
  } = customerViewConfigs;

  const [previewInvoiceGridData, setPreviewInvoiceGridData] = useState({
    open: false,
    values: {},
  });

  const [isPreview, setIsPreview] = useState(false);
  const [usageCurrencyIsoCode, setUsageCurrencyIsoCode] = useState(currencyIsoCode);

  const handlePreviewDialogClicked = async (values: any) => {
    setPreviewInvoiceGridData({
      open: true,
      values: values,
    });
  };

  const handleShowCashOutDialog = (status: boolean) => {
    setShowCashOutDialog(status);
  };

  const handleBulkActions = (values: any) => {
    if (!changeModalProps) return;

    const { cartItems, shouldCloseChangeDialog } = getAddMultipleGroupsToCartResult(
      values,
      changeModalProps,
      subscriptionMetadata,
      cotermDate,
    );

    if (handleAddMultipleGroupsToCart) {
      handleAddMultipleGroupsToCart(cartItems);
    }
    if (shouldCloseChangeDialog) {
      setChangeModalProps(null);
    }
  };

  const onLoadChildernAndBillingAccountsByIds = async (
    ids: string[],
    billingAccountIds: any,
    childAccountIds: any,
    accountDetails: any,
  ) => {
    return await loadChildernAndBillingAccountsByIds(
      ids,
      billingAccountIds,
      childAccountIds,
      accountDetails,
      executeQuery,
      customerMetadata,
      transactionHubMetadata,
    );
  };

  const handleTransfer = async (row: any, platform: string, transactionType: string) => {
    if (!sync) {
      return;
    }
    try {
      const result = await sync({
        itemId: row.nueId || row.Ruby__NueId__c || row.id,
        itemType: transactionType,
      });

      if (result?.success) {
        snackbarService.showSnackbar(
          'confirm',
          'Success',
          `The ${transactionType.replace(/([A-Z])/g, ' $1').toLowerCase()} ${row.name} is transferring to ${platform}`,
        );
      } else {
        snackbarService.showSnackbar(
          'error',
          'Error',
          `The ${transactionType.replace(/([A-Z])/g, ' $1').toLowerCase()} ${row.name} transfer to ${platform} failed`,
        );
      }
    } catch (err) {
      snackbarService.showSnackbar(
        'error',
        'Error',
        `The ${transactionType.replace(/([A-Z])/g, ' $1').toLowerCase()} ${row.name} transfer to ${platform} failed`,
      );
    }
  };

  const TransactionHubPopoverButton = (config: any) => {
    const { iconType, ...rest } = config;
    let Icon;

    if (iconType === 'NetSuite') {
      Icon = NetSuiteLogoIcon;
    } else if (iconType === 'QuickBooks') {
      Icon = QuickbookLogoIconForTrans;
    }

    return PopoverButton({
      ...rest,
      Icon,
    });
  };

  const tabFilters: TabFilter[] = [
    {
      id: '1',
      name: 'General Information',
      displayName: 'General Information',
      objectMetadata: customerMetadata,
      gridViewMetadataName: 'account-grid-view-metadata',
      pageKey: 'customer__customer_view',
      GridViewItem: AccountViewItem,
      fieldSetApiName: 'CustomerFields',
      filter: [
        {
          id: `objectFilter-1`,
          name: `Object Filter`,
          conditions: [
            {
              apiName: 'id',
              name: 'Id',
              operand: ' _or ',
              operator: ' _eq ',
              type: 'text',
              value: `"${customer.id}"`,
            },
            {
              apiName: 'parentCustomerId',
              name: 'ParentId',
              operand: ' _or ',
              operator: ' _eq ',
              type: 'text',
              value: `"${customer.id}"`,
            },
            {
              apiName: 'salesAccountId',
              name: 'SalesAccountId',
              operand: ' _or ',
              operator: ' _eq ',
              type: 'text',
              value: `"${customer.id}"`,
            },
          ],
          isApplied: true,
        },
      ],
      renderSummaryView: (values: any) => {
        return (
          <GeneralInformationSummaryView
            {...values}
            loadSubscriptionStreamData={loadSubscriptionStreamData}
            loadChildernAndBillingAccounts={loadChildernAndBillingAccounts}
            loadChildernAndBillingAccountsByIds={onLoadChildernAndBillingAccountsByIds}
            addTransactionHubColumn={tabFilters[activeTabDetail.tabIndex].addTransactionHubColumn}
          />
        );
      },
      addTransactionHubColumn: showTransactionHub(integrations)
        ? () => {
            return {
              name: 'transactionHub',
              title: 'Transaction Hub',
              type: 'action',
              apiName: '_transactionhub',
              width: 150,
              cellRenderer: ({ row }: CellRendererRowProps) => {
                if (
                  !integrations ||
                  integrations.length === 0 ||
                  !integrations.some((x) => x.status === 'Connected')
                ) {
                  return null;
                }

                const netsuite = TransactionHubPopoverButton(
                  createTransactionHubPopoverButton({
                    row,
                    id: row.id,
                    system: 'NetSuite',
                    transactionType: 'Customer',
                    transactionHubProps,
                    handleTransfer,
                    netsuiteRedirectUrl: 'app.netsuite.com/app/common/entity/custjob.nl',
                    snackbarService,
                  }),
                );

                const quickbooks = TransactionHubPopoverButton(
                  createTransactionHubPopoverButton({
                    row,
                    id: row.id,
                    system: 'QuickBooks',
                    transactionType: 'Customer',
                    transactionHubProps,
                    handleTransfer,
                    snackbarService,
                  }),
                );

                return <GridActions actions={[netsuite, quickbooks]} actionHelpers={{ row }} />;
              },
            };
          }
        : undefined,
    },
  ];

  const customerIds = showChildrenAccounts ? [customer.id, ...childrenAccountIds] : [customer.id];
  if (rubySettings?.showOrderOnCustomerLifecycle) {
    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'Orders',
      displayName: 'Orders',
      objectMetadata: orderMetadata,
      gridViewMetadataName: 'order-grid-view-metadata',
      extraRelationFields: 'owner { id name imageSignedUrl } billingAccount { id name }',
      pageKey: 'orders__customer_view',
      GridViewItem: GridViewItem,
      fieldSetApiName: 'OrderFields',
      filter: [
        {
          id: `objectFilter-2`,
          name: `Object Filter`,
          conditions: [
            {
              apiName: 'customerId',
              name: 'Customer Id',
              operand: ' _or ',
              operator: ' _in ',
              type: 'Picklist',
              value: JSON.stringify(customerIds),
              nestedConditions: [
                {
                  apiName: 'billingAccountId',
                  name: 'BillingAccount',
                  operand: ' _or ',
                  operator: ' _eq ',
                  type: 'text',
                  value: `"${customer.id}"`,
                },
              ],
            },
          ],
          isApplied: true,
        },
      ],
      predefinedFilters: [
        {
          isApplied: true,
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '"Activated"',
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'Active orders',
          isExclusive: true,
          id: '1',
        },
        {
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '"Draft"',
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'Draft orders',
          isExclusive: true,
          id: '2',
        },
        {
          conditions: [
            {
              apiName: 'ownerId',
              name: 'Order Owner',
              type: 'bLookup',
              relation: 'owner',
              // TODO: get user info user id
              value: `"${user.id}"`,
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'My orders',
          isExclusive: true,
          id: '3',
        },
        {
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '"Canceled"',
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'Canceled orders',
          isExclusive: true,
          id: '4',
        },
      ],
      showListViewMultiSelect: false,
      addTransactionHubColumn: showTransactionHub(integrations)
        ? () => {
            return {
              name: 'transactionHub',
              title: 'Transaction Hub',
              type: 'action',
              apiName: '_transactionhub',
              width: 150,
              cellRenderer: ({ row }: CellRendererRowProps) => {
                if (
                  !integrations ||
                  integrations.length === 0 ||
                  !integrations.some((x) => x.status === 'Connected')
                ) {
                  return null;
                }

                const netsuite = TransactionHubPopoverButton(
                  createTransactionHubPopoverButton({
                    row,
                    id: row.id,
                    system: 'NetSuite',
                    transactionType: 'Order',
                    transactionHubProps,
                    handleTransfer,
                    netsuiteRedirectUrl: 'app.netsuite.com/app/accounting/transactions/salesord.nl',
                    snackbarService,
                  }),
                );

                const quickbooks = TransactionHubPopoverButton(
                  createTransactionHubPopoverButton({
                    row,
                    id: row.id,
                    system: 'QuickBooks',
                    transactionType: 'Order',
                    transactionHubProps,
                    handleTransfer,
                    snackbarService,
                  }),
                );

                return <GridActions actions={[netsuite, quickbooks]} actionHelpers={{ row }} />;
              },
            };
          }
        : undefined,
    });
  }

  if (rubySettings?.showSubscriptionOnCustomerLifecycle) {
    const batchActions: BatchAction[] = [];
    if (!rubySettings.coTermOption || rubySettings.coTermOption != 'doNotCoterm') {
      batchActions.push({ apiName: 'bulkCoterm', name: 'Co-Term' });
    }

    if (rubySettings.renew) {
      batchActions.push({ apiName: 'bulkRenew', name: 'Renew' });
    }
    if (rubySettings.cancel) {
      batchActions.push({ apiName: 'bulkCancel', name: 'Cancel' });
    }
    if (rubySettings.adjustPrice) {
      batchActions.push({ apiName: 'adjustPrice', name: 'Adjust Price' });
    }
    if (rubySettings.upgradeDowngradeSwap) {
      batchActions.push({ apiName: 'bulkUpgradeDowngradeSwap', name: 'Upgrade/Downgrade/Swap' });
    }
    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'Subscriptions',
      displayName: 'Subscriptions',
      objectMetadata: subscriptionMetadata,
      gridViewMetadataName: 'subscription-grid-view-metadata',
      pageKey: 'subscription__customer_view',
      extraRelationFields:
        'product { name imageSignedUrl configurable priceModel defaultSubscriptionTerm defaultRenewalTerm defaultUom { id name termDimension quantityDimension} } billingAccount { id name } uom { name quantityDimension termDimension } customer { name id } priceBook { id name } orderProduct { orderId productOptionId productOption { bundled optionType required } } entity {id name} owner {id name}',
      GridViewItem: SubscriptionCard,
      fieldSetApiName: 'SubscriptionFields',
      filter: [
        {
          id: `objectFilter-3`,
          name: `Object Filter`,
          isApplied: true,
          conditions: [
            {
              apiName: 'customerId',
              name: 'Customer Id',
              operand: ' _and ',
              operator: ' _in ',
              type: 'Picklist',
              value: JSON.stringify(customerIds),
              nestedConditions: [
                {
                  apiName: 'billingAccountId',
                  name: 'BillingAccount',
                  operand: ' _or ',
                  operator: ' _in ',
                  type: 'Picklist',
                  value: JSON.stringify([customer.id]),
                },
              ],
            },
            {
              apiName: 'originalSubscriptionId',
              name: 'Original Subscription Id',
              operand: ' _and ',
              operator: ' _eq ',
              type: 'text',
              value: null,
            },
            {
              apiName: 'lastVersionedSubscriptionId',
              name: 'Last Versioned Subscription Id',
              operand: ' _and ',
              operator: ' _eq ',
              type: 'text',
              value: null,
            },
          ],
        },
      ],
      predefinedFilters: [
        {
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '["Draft", "Active", "Cancelled", "ScheduledForCancel", "Ended"]',
              operator: ' _in ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'All',
          isExclusive: true,
          id: '0',
        },
        {
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '"Active"',
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'Active subscriptions',
          isExclusive: true,
          id: '1',
        },
        {
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '"Cancelled"',
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'Cancelled subscriptions',
          isExclusive: true,
          id: '2',
        },
        {
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '"Active"',
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
            {
              apiName: 'subscriptionEndDate',
              name: 'Subscription End Date',
              type: 'date',
              value: `"${dayjs().add(30, 'day').format('YYYY-MM-DD')}"`,
              operator: ' _lte ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'Subscriptions up for renewal',
          isExclusive: true,
          id: '3',
        },
      ],
      showListViewMultiSelect: true,
      batchActions: batchActions,
      handleSelectBatchAction: (action: BatchAction, rows: any[]) => {
        setOpenChangeModal(true);
        if (action.apiName === 'bulkCoterm') {
          const latestEndDate = getLatestSubscriptionEndDateOfAllSubscriptions(rows);
          setCotermDate(latestEndDate);
        }

        setChangeModalProps({
          cardAction: {
            id: action.apiName,
            name: action.name,
            description: '',
          },
          initValues: {},
          defaultValues: getDefaultValues(action.apiName),
          validationSchema: getSubscriptionChangeValidationSchema(action.apiName),
          objectMetadata: { ...subscriptionMetadata, fields: getFields(action.apiName) },
          handleOpen: (newOpen: boolean) => setOpenChangeModal(newOpen),
          subscriptions: rows,
        });
      },
      showBatchActionBar: true,
    });
  }

  if (rubySettings?.showAssetOnCustomerLifecycle) {
    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'Assets',
      displayName: 'Assets',
      showDetails: false,
      gridViewMetadataName: 'asset-grid-view-metadata',
      extraRelationFields:
        'product { name imageSignedUrl configurable } billingAccount { id name } unitOfMeasure { name quantityDimension termDimension } priceBook { id name } customer { id name} entity {id name}',
      objectMetadata: assetMetadata,
      pageKey: 'asset__customer_view',
      GridViewItem: GridViewItem,
      predefinedFilters: [
        {
          isApplied: true,
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '"Active"',
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'Active assets',
          isExclusive: true,
          id: '1',
        },
      ],
      fieldSetApiName: 'AssetFields',
      filter: [
        {
          id: `objectFilter-4`,
          name: `Object Filter`,
          conditions: [
            {
              apiName: 'customerId',
              name: 'Customer Id',
              operand: ' _or ',
              operator: ' _in ',
              type: 'Picklist',
              value: JSON.stringify(customerIds),
              nestedConditions: [
                {
                  apiName: 'billingAccountId',
                  name: 'BillingAccount',
                  operand: ' _or ',
                  operator: ' _eq ',
                  type: 'Picklist',
                  value: `"${customer.id}"`,
                },
              ],
            },
          ],
          isApplied: true,
        },
      ],
      showListViewMultiSelect: false,
    });
  }

  if (rubySettings?.showEntitlementOnCustomerLifecycle) {
    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'Entitlements',
      displayName: 'Entitlements',
      showDetails: false,
      gridViewMetadataName: 'entitlement-grid-view-metadata',
      extraRelationFields:
        'product { name imageSignedUrl configurable } billingAccount { id name } unitOfMeasure { name quantityDimension termDimension } priceBook { id name } customer { id name} entity {id name}',
      objectMetadata: entitlementMetadata,
      pageKey: 'entitlement__customer_view',
      GridViewItem: (gridItemProps) => {
        const entitlement = gridItemProps.object;
        const entitlementFieldSetMetadata = _.cloneDeep(gridItemProps.fieldSetMetadata);
        if (
          entitlement.status === 'Canceled' &&
          entitlementFieldSetMetadata.findIndex((field) => field.apiName === 'cancellationDate') ===
            -1
        ) {
          entitlementFieldSetMetadata.push({
            apiName: 'cancellationDate',
            name: 'Cancellation Date',
            type: 'date',
          });
        }
        return <GridViewItem {...gridItemProps} fieldSetMetadata={entitlementFieldSetMetadata} />;
      },
      predefinedFilters: [
        {
          isApplied: true,
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '"Active"',
              operator: ' _eq ',
              operand: ' _or ',
              nestedConditions: [],
            },
          ],
          name: 'Active entitlements',
          isExclusive: true,
          id: '1',
        },
      ],
      fieldSetApiName: 'EntitlementFields',
      filter: [
        {
          id: `objectFilter-5`,
          name: `Object Filter`,
          conditions: [
            {
              apiName: 'customerId',
              name: 'Customer Id',
              operand: ' _or ',
              operator: ' _in ',
              type: 'Picklist',
              value: JSON.stringify(customerIds),
              nestedConditions: [
                {
                  apiName: 'billingAccountId',
                  name: 'BillingAccount',
                  operand: ' _or ',
                  operator: ' _eq ',
                  type: 'Picklist',
                  value: `"${customer.id}"`,
                },
              ],
            },
          ],
          isApplied: true,
        },
      ],
      showListViewMultiSelect: false,
    });
  }

  if (rubySettings?.showInvoiceOnCustomerLifecycle) {
    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'Invoices',
      displayName: 'Invoices',
      gridViewMetadataName: 'invoice-grid-view-metadata',
      extraRelationFields:
        'account { id name } salesAccountId { id name } Ruby__PreviewOrder__c { id name } Ruby__PreviewQuote__c { id name } owner {id name}',
      objectMetadata: invoiceMetadata,
      pageKey: 'invoice__customer_view',
      getSubTitle: () => {
        return (
          <Button
            color="primary"
            className={classes.btn}
            startIcon={<FindInPageIcon />}
            onClick={() => {
              setShowPreviewInvoiceDialog(true);
            }}
          >
            Preview Future Invoices
          </Button>
        );
      },
      GridViewItem: () => {
        return <></>;
      },
      predefinedFilters: [
        {
          conditions: [
            {
              apiName: 'status',
              name: 'Status',
              type: 'pickList',
              value: '["Draft", "Active", "Cancelled"]',
              operator: ' _in ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'All',
          isExclusive: true,
          id: '0',
        },
      ],
      fieldSetApiName: 'InvoiceFields',
      filter: [
        {
          id: `objectFilter-6`,
          name: `Object Filter`,
          conditions: [
            {
              apiName: 'account',
              name: 'Account',
              operand: ' _and ',
              operator: ' _eq ',
              type: 'text',
              value: `"${customer.id}"`,
              nestedConditions: [
                {
                  apiName: 'SalesAccountId__c',
                  name: 'SalesAccount',
                  operand: ' _or ',
                  operator: ' _in ',
                  type: 'Picklist',
                  value: JSON.stringify(customerIds),
                },
              ],
            },
          ],
          isApplied: true,
        },
      ],
      showListViewMultiSelect: false,
      isListViewOnly: true,
      renderExtraHeader: (objects: any[] | null) => {
        if (!objects || !objects.length) return <></>;
        const balance = objects
          .filter((x: any) => {
            if (isPreview) {
              return x.status === 'Preview';
            }
            return x.status === 'Active';
          })
          .reduce((x: number, y: any) => x + Number(y.balance), 0);

        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <BalanceIcon className={classes.balanceIcon} />
            <Typography variant="h6" style={{ width: 90 }}>
              Balance:
            </Typography>
            <span
              style={{
                fontSize: '30px',
                color: '#FF7D00',
                letterSpacing: '0.94px',
                fontWeight: 600,
              }}
            >
              {formatNumberForDisplay(balance, currencyIsoCode, true, false, locale)}
            </span>
          </div>
        );
      },
      addActionColumn: () => {
        return {
          apiName: 'actions',
          type: 'action',
          name: 'actions',
          title: 'Actions',
          cellRenderer: ({ row }: CellRendererRowProps) => {
            const iconStyles = {
              color: '#6239eb',
              opacity: 1,
              height: '18px',
              width: '18px',
            };
            const viewBox = '0 0 24 24';
            const actions = [];

            actions.push(
              ViewButton({
                tooltipText: `View Invoice`,
                onClickHandler: async () => {
                  await customerViewService.actionEventHandler({
                    object: row,
                    objectMetadata: invoiceMetadata,
                    action: {
                      id: 'details',
                      name: 'Details Click',
                      description: 'Details Title',
                    },
                  });
                },
              }),
            );

            if (
              (row.status === 'Draft' ||
                row.status === 'PendingActivation' ||
                row.status === 'E-Invoicing') &&
              user.permissions?.activateInvoice
            ) {
              actions.push(
                MuiGridButton({
                  id: 'activate',
                  tooltipText: 'Activate Invoice',
                  Icon: CheckCircleOutlined,
                  onClickHandler: async () => {
                    setSelectedRowData(row);
                    setSelectedRowType('Invoice');
                    setShowDialogForActivating(true);
                  },
                }),
              );
            }

            if (
              (row.status === 'Draft' ||
                row.status === 'Active' ||
                row.status === 'PendingActivation' ||
                row.status === 'E-Invoicing') &&
              user.permissions?.cancelInvoice
            ) {
              actions.push(
                MuiGridButton({
                  id: 'cancel',
                  tooltipText: 'Cancel Invoice',
                  Icon: PowerSettingsNewOutlined,
                  onClickHandler: async () => {
                    setSelectedRowData(row);
                    setSelectedRowType('Invoice');
                    setShowDialogForCanceling(true);
                  },
                }),
              );
            }

            if (row.status === 'Canceled' && user.permissions?.deleteInvoice) {
              actions.push(
                DeleteButton({
                  tooltipText: 'Delete Invoice',
                  onClickHandler: async () => {
                    setSelectedRowData(row);
                    setSelectedRowType('Invoice');
                    setShowDialogForDeleting(true);
                  },
                }),
              );
            }
            if (row.comments) {
              actions.push(
                MuiGridButton({
                  id: 'comment_invoice_' + row.id,
                  tooltipText: 'Click to show comments',
                  Icon: ChatOutlinedIcon,
                  iconAlt: 'view-icon',
                  onClickHandler: () => {
                    setCommentDetails({
                      invoice: row,
                      show: true,
                      domID: 'comment_invoice_' + row.id,
                    });
                  },
                }),
              );
            }

            if (
              (row.status === 'Draft' ||
                row.status === 'Active' ||
                row.status === 'PendingActivation' ||
                row.status === 'E-Invoicing' ||
                row.status === 'Canceled') &&
              taxConfig?.integrationType?.toLowerCase() === 'stripe' &&
              (taxConfig?.config as StripeTaxConfig)?.enableTaxInvoiceCollection
            ) {
              actions.push(
                MuiGridButton({
                  tooltipText: 'View Tax Breakdown',
                  Icon: BallotOutlined,
                  iconAlt: 'description-icon',
                  onClickHandler: () => {
                    getLineTaxBreakdown?.(row.externalId);
                  },
                }),
              );
            }

            return <GridActions actionHelpers={{ iconStyles, viewBox }} actions={actions} />;
          },
        };
      },
      addTransactionHubColumn: showTransactionHub(integrations)
        ? () => {
            return {
              name: 'transactionHub',
              title: 'Transaction Hub',
              type: 'action',
              apiName: '_transactionhub',
              width: 150,
              cellRenderer: ({ row }: CellRendererRowProps) => {
                if (
                  !integrations ||
                  integrations.length === 0 ||
                  !integrations.some((x) => x.status === 'Connected')
                ) {
                  return null;
                }

                const netsuite = TransactionHubPopoverButton(
                  createTransactionHubPopoverButton({
                    row,
                    id: row.id,
                    system: 'NetSuite',
                    transactionType: 'Invoice',
                    transactionHubProps,
                    handleTransfer,
                    netsuiteRedirectUrl: 'app.netsuite.com/app/accounting/transactions/custinvc.nl',
                    snackbarService,
                  }),
                );

                const quickbooks = TransactionHubPopoverButton(
                  createTransactionHubPopoverButton({
                    row,
                    id: row.id,
                    system: 'QuickBooks',
                    transactionType: 'Invoice',
                    transactionHubProps,
                    handleTransfer,
                    snackbarService,
                  }),
                );

                return <GridActions actions={[netsuite, quickbooks]} actionHelpers={{ row }} />;
              },
            };
          }
        : undefined,
      renderExtraContents: (
        objects: any[] | null,
        handleSearch: (extraFilters: RubyFilter[]) => void,
      ) => {
        const totalAmount =
          objects && objects.length > 0
            ? objects
                .filter((x: any) => {
                  if (isPreview) {
                    return x.status === 'Preview';
                  } else {
                    return x.status !== 'Canceled' && x.status !== 'Preview';
                  }
                })
                .reduce((x: number, y: any) => x + Number(y.amount), 0)
            : 0;

        return (
          <>
            <div className={classes.selectorContainer}>
              {invoicePeriods && (
                <div className={classes.selectorContainer}>
                  <InvoiceListSelector
                    label="BILLING PERIODS"
                    invoicePeriods={invoicePeriods}
                    onSelectPeriod={(startDate?: string, endDate?: string, id?: string) => {
                      setIsPreview(!!id);
                      if (id) {
                        handleSearch([
                          {
                            conditions: [
                              {
                                apiName: 'startDate',
                                error: '',
                                name: 'Start Date',
                                operand: ' _and ',
                                operator: ' _gte ',
                                relation: null,
                                type: 'date',
                                value: startDate || '',
                              },
                              {
                                apiName: 'endDate',
                                error: '',
                                name: 'End Date',
                                operand: ' _and ',
                                operator: ' _lte ',
                                relation: null,
                                type: 'date',
                                value: endDate || '',
                              },
                            ],
                            id: 'extraFilter1',
                            isApplied: true,
                            name: 'Extra Filter 1',
                          },
                        ]);
                        return;
                      }
                      if (!startDate && !endDate) {
                        handleSearch([]);
                      } else {
                        handleSearch([
                          {
                            conditions: [
                              {
                                apiName: 'invoiceDate',
                                error: '',
                                name: 'Invoice Date',
                                operand: ' _and ',
                                operator: ' _gte ',
                                relation: null,
                                type: 'date',
                                value: startDate || '',
                              },
                              {
                                apiName: 'invoiceDate',
                                error: '',
                                name: 'Invoice Date',
                                operand: ' _and ',
                                operator: ' _lte ',
                                relation: null,
                                type: 'date',
                                value: endDate || '',
                              },
                            ],
                            id: 'extraFilter1',
                            isApplied: true,
                            name: 'Extra Filter 1',
                          },
                        ]);
                      }
                    }}
                  />
                  <span className={classes.fieldLabel} role="label">
                    Total Invoice Amount
                  </span>
                  <span className={classes.amountLabel} role="label">
                    {formatNumberForDisplay(totalAmount, currencyIsoCode, true, false, locale)}
                  </span>
                </div>
              )}
            </div>
            {!isPreview && (
              <InvoiceBarChart
                currencyIsoCode={currencyIsoCode}
                locale={locale}
                objects={objects}
                rubySettings={rubySettings}
              />
            )}
          </>
        );
      },
      orderByFilters: [
        {
          columnName: 'name',
          direction: 'desc',
        },
      ],
    });
  }

  if (rubySettings?.showCreditMemoOnCustomerLifecycle) {
    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'CreditMemos',
      displayName: 'Credit Memos',
      gridViewMetadataName: 'credit-memos-grid-view-metadata',
      extraRelationFields: 'account { id name } salesAccountId { id name }',
      objectMetadata: creditMemosMetaData,
      pageKey: 'credit-memos__customer_view',
      GridViewItem: () => {
        return <></>;
      },
      predefinedFilters: [],
      fieldSetApiName: 'CreditMemoFields',
      filter: [
        {
          id: `objectFilter-${tabFilters.length + 1}`,
          name: `Object Filter`,
          conditions: [
            {
              apiName: 'account',
              name: 'Account',
              operand: ' _or ',
              operator: ' _eq ',
              type: 'text',
              value: `"${customer.id}"`,
              nestedConditions: [
                {
                  apiName: 'SalesAccountId__c',
                  name: 'SalesAccount',
                  operand: ' _or ',
                  operator: ' _in ',
                  type: 'Picklist',
                  value: JSON.stringify(customerIds),
                },
              ],
            },
          ],
          isApplied: true,
        },
      ],
      showListViewMultiSelect: false,
      isListViewOnly: true,
      addActionColumn: () => {
        return {
          apiName: 'actions',
          type: 'action',
          name: 'actions',
          title: 'Actions',
          cellRenderer: ({ row }: CellRendererRowProps) => {
            const iconStyles = {
              color: '#6239eb',
              opacity: 1,
              height: '18px',
              width: '18px',
            };
            const viewBox = '0 0 24 24';
            const actions = [];

            actions.push(
              ViewButton({
                tooltipText: `View Credit Memo`,
                onClickHandler: async () => {
                  await customerViewService.actionEventHandler({
                    object: row,
                    objectMetadata: creditMemosMetaData,
                    action: {
                      id: 'details',
                      name: 'Details Click',
                      description: 'Details Title',
                    },
                  });
                },
              }),
            );

            if (
              (row.status === 'Draft' ||
                row.status === 'PendingActivation' ||
                row.status === 'E-Invoicing') &&
              user.permissions?.activateCreditMemos
            ) {
              actions.push(
                MuiGridButton({
                  id: 'activate',
                  tooltipText: 'Activate Credit memo',
                  Icon: CheckCircleOutlined,
                  onClickHandler: async () => {
                    setSelectedRowData(row);
                    setSelectedRowType('CreditMemo');
                    setShowDialogForActivating(true);
                  },
                }),
              );
            }

            if (
              (row.status === 'Draft' ||
                row.status === 'Active' ||
                row.status === 'PendingActivation' ||
                row.status === 'E-Invoicing') &&
              user.permissions?.cancelCreditMemos
            ) {
              actions.push(
                MuiGridButton({
                  id: 'cancel',
                  tooltipText: 'Cancel Credit memo',
                  Icon: PowerSettingsNewOutlined,
                  onClickHandler: async () => {
                    setSelectedRowData(row);
                    setSelectedRowType('CreditMemo');
                    setShowDialogForCanceling(true);
                  },
                }),
              );
            }

            if (row.comments) {
              actions.push(
                MuiGridButton({
                  id: 'comment_credit_' + row.id,
                  tooltipText: 'Click to show comments',
                  Icon: ChatOutlinedIcon,
                  iconAlt: 'view-icon',
                  onClickHandler: () => {
                    setCommentDetails({
                      invoice: row,
                      show: true,
                      domID: 'comment_credit_' + row.id,
                    });
                  },
                }),
              );
            }

            if (
              (row.status === 'Draft' ||
                row.status === 'Active' ||
                row.status === 'PendingActivation' ||
                row.status === 'E-Invoicing' ||
                row.status === 'Canceled') &&
              taxConfig?.integrationType?.toLowerCase() === 'stripe' &&
              (taxConfig?.config as StripeTaxConfig)?.enableTaxInvoiceCollection
            ) {
              actions.push(
                MuiGridButton({
                  tooltipText: 'View Tax Breakdown',
                  Icon: BallotOutlined,
                  iconAlt: 'description-icon',
                  onClickHandler: () => {
                    getLineTaxBreakdown?.(row.externalId);
                  },
                }),
              );
            }

            return <GridActions actionHelpers={{ iconStyles, viewBox }} actions={actions} />;
          },
        };
      },
      addTransactionHubColumn: showTransactionHub(integrations)
        ? () => {
            return {
              name: 'transactionHub',
              title: 'Transaction Hub',
              type: 'action',
              apiName: '_transactionhub',
              width: 150,
              cellRenderer: ({ row }: CellRendererRowProps) => {
                if (
                  !integrations ||
                  integrations.length === 0 ||
                  !integrations.some((x) => x.status === 'Connected')
                ) {
                  return null;
                }

                const netsuite = TransactionHubPopoverButton(
                  createTransactionHubPopoverButton({
                    row,
                    id: row.id,
                    system: 'NetSuite',
                    transactionType: 'CreditMemo',
                    transactionHubProps,
                    handleTransfer,
                    netsuiteRedirectUrl: 'app.netsuite.com/app/accounting/transactions/custcred.nl',
                    snackbarService,
                  }),
                );

                const quickbooks = TransactionHubPopoverButton(
                  createTransactionHubPopoverButton({
                    row,
                    id: row.id,
                    system: 'QuickBooks',
                    transactionType: 'CreditMemo',
                    transactionHubProps,
                    handleTransfer,
                    snackbarService,
                  }),
                );

                return <GridActions actions={[netsuite, quickbooks]} actionHelpers={{ row }} />;
              },
            };
          }
        : undefined,
      renderExtraHeader: (objects: any[] | null) => {
        if (!objects || !objects.length) return <></>;
        const balance = objects
          .filter((x: any) => x.status === 'Active')
          .reduce((x: number, y: any) => x + Number(y.balance), 0);
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <BalanceIcon className={classes.balanceIcon} />
            <Typography variant="h6" style={{ width: 90 }}>
              Balance:
            </Typography>
            <span
              style={{
                fontSize: '30px',
                color: '#FF7D00',
                letterSpacing: '0.94px',
                fontWeight: 600,
              }}
            >
              {formatNumberForDisplay(balance, currencyIsoCode, true, false, locale)}
            </span>
          </div>
        );
      },
      renderExtraContents: (
        objects: any[] | null,
        handleSearch: (extraFilters: RubyFilter[]) => void,
      ) => {
        const totalAmount =
          objects && objects.length > 0
            ? objects
                .filter((x: any) => x.status !== 'Canceled')
                .reduce((x: number, y: any) => x + Number(y.amount), 0)
            : 0;

        return (
          <>
            <div className={classes.selectorContainer}>
              {invoicePeriods && (
                <div className={classes.selectorContainer}>
                  <InvoiceListSelector
                    label="BILLING PERIODS"
                    invoicePeriods={invoicePeriods}
                    onSelectPeriod={(startDate?: string, endDate?: string) => {
                      if (!startDate && !endDate) {
                        handleSearch([]);
                      } else {
                        handleSearch([
                          {
                            conditions: [
                              {
                                apiName: 'creditMemoDate',
                                error: '',
                                name: 'Credit memo Date',
                                operand: ' _and ',
                                operator: ' _gte ',
                                relation: null,
                                type: 'date',
                                value: startDate || '',
                              },
                              {
                                apiName: 'creditMemoDate',
                                error: '',
                                name: 'Credit memo Date',
                                operand: ' _and ',
                                operator: ' _lte ',
                                relation: null,
                                type: 'date',
                                value: endDate || '',
                              },
                            ],
                            id: 'extraFilter1',
                            isApplied: true,
                            name: 'Extra Filter 1',
                          },
                        ]);
                      }
                    }}
                  />
                  <span className={classes.fieldLabel} role="label">
                    Total Credit Memo Amount
                  </span>
                  <span className={classes.amountLabel} role="label">
                    {formatNumberForDisplay(totalAmount, currencyIsoCode, true, false, locale)}
                  </span>
                </div>
              )}
            </div>
            <CreditMemosBarChart
              currencyIsoCode={currencyIsoCode}
              locale={locale}
              objects={objects}
            />
          </>
        );
      },
      orderByFilters: [
        {
          columnName: 'name',
          direction: 'desc',
        },
      ],
    });
  }

  if (rubySettings?.showPaymentMethodOnCustomerLifecycle) {
    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'PaymentMethods',
      displayName: 'Payment Methods',
      gridViewMetadataName: 'payment-methods-grid-view-metadata',
      extraRelationFields: '',
      objectMetadata: paymentMethodsMetaData,
      pageKey: 'payment-methods__customer_view',
      GridViewItem: () => {
        return <></>;
      },
      predefinedFilters: getPredefinedFilters(timezone),
      fieldSetApiName: 'PaymentMethodFields',
      showListViewMultiSelect: false,
      isListViewOnly: true,
      filter: [
        {
          id: `objectFilter-${tabFilters.length + 1}`,
          name: `Object Filter`,
          conditions: [
            {
              apiName: 'customerId',
              name: 'Account id',
              operand: ' _or ',
              operator: ' _in ',
              type: 'Picklist',
              value: JSON.stringify(customerIds),
            },
          ],
          isApplied: true,
        },
      ],
      addActionColumn: () => {
        return {
          apiName: 'actions',
          type: 'action',
          name: 'actions',
          title: 'Actions',
          cellRenderer: ({ row }: CellRendererRowProps) => {
            const iconStyles = {
              color: '#6239eb',
              opacity: 1,
              height: '18px',
              width: '18px',
            };
            const viewBox = '0 0 24 24';
            const actions = [];
            actions.push(
              MuiGridButton({
                id: 'autoCharge',
                tooltipText: row?.autoChargeEnabled ? 'Disable Auto-charge' : 'Enable Auto-charge',
                Icon: row?.autoChargeEnabled ? StarIcon : StarBorderIcon,
                onClickHandler: async () => {
                  const res: any = await customerViewService.actionEventHandler({
                    object: row,
                    objectMetadata: paymentMethodsMetaData,
                    action: {
                      id: 'update',
                      name: 'Update Payment Method',
                      description: 'Update Payment Method',
                    },
                  });
                  const newObject = res?.upsertPaymentMethods?.[0];
                  const newObjects = objects ? [...objects] : [];
                  const index = newObjects.findIndex(
                    (item) => item.Ruby__NueId__c === newObject.id,
                  );
                  if (index !== -1) {
                    newObjects[index].autoChargeEnabled = newObject.autoChargeEnabled;
                  }
                  setObjects(newObjects);
                },
              }),
            );
            actions.push(
              PopoverButton({
                id: `view-card_${row.id}`,
                tooltipText: 'View Card Information',
                Icon: () => <ViewIcon style={{ width: 20, height: 20 }} />,
                iconAlt: 'View',
                PopoverComponent: (props) => {
                  const { cardInfo } = props;
                  return (
                    <Popover
                      {...props}
                      anchorOrigin={{
                        vertical: 'center',
                        horizontal: 'right',
                      }}
                      transformOrigin={{
                        vertical: 'center',
                        horizontal: 'left',
                      }}
                    >
                      <div className={classes.cardInfoCard}>
                        <div className={classes.cardInfoTitle}>
                          <CreditCard fontSize="medium" style={{ color: '#6239eb' }} />
                          <div>{cardInfo?.name}</div>
                        </div>
                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <span className={classes.cardInfoLabel}>Last 4 digits:</span>
                            <br />
                            {cardInfo?.lastFourDigits}
                          </Grid>
                          <Grid item xs={6}>
                            <span className={classes.cardInfoLabel}>Expiration Date:</span>
                            <br />
                            {cardInfo?.expMonth}/{cardInfo?.expYear}
                          </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <span className={classes.cardInfoLabel}>Brand:</span>
                            <br />
                            {cardInfo?.brand}
                          </Grid>
                          <Grid item xs={6}>
                            <span className={classes.cardInfoLabel}>Type:</span>
                            <br />
                            {cardInfo?.type}
                          </Grid>
                        </Grid>
                      </div>
                    </Popover>
                  );
                },
                popoverArgs: {
                  cardInfo: row,
                },
              }),
            );
            return <GridActions actionHelpers={{ iconStyles, viewBox }} actions={actions} />;
          },
        };
      },
      addTransactionHubColumn: () => {
        return {
          name: 'transactionHub',
          title: 'Transaction Hub',
          type: 'action',
          apiName: '_transactionhub',
          width: 150,
          cellRenderer: ({ row }: CellRendererRowProps) => {
            if (
              !integrations ||
              integrations.length === 0 ||
              !integrations.some((x) => x.status === 'Connected')
            ) {
              return null;
            }

            return (
              <Tooltip title={'Stripe'} placement="bottom" arrow>
                <IconButton className={classes.gridIcon} id={`view-Stripe_${row.id}`}>
                  <StripeLogoIcon />
                </IconButton>
              </Tooltip>
            );
          },
        };
      },
      orderByFilters: [
        {
          columnName: 'name',
          direction: 'asc',
        },
      ],
    });
  }

  const getTabIndex = (name: string): number => {
    return tabFilters.findIndex((x) => x.name === name);
  };

  const getUsageChartData = async (usageMetadata: RubyObject, extraFilters: RubyFilter[] = []) => {
    const filterConditions =
      GraphqlQueryConstructor.construct().conditionsFromFilters(extraFilters);
    const orderByConditions = GraphqlQueryConstructor.construct().orderByFields(
      tabFilters[activeTabDetail.tabIndex].orderByFilters!,
    );
    const query = GraphqlQueryConstructor.construct().queryWithWhereCondition(
      usageMetadata,
      `( where: ${filterConditions} ${orderByConditions})`,
      '',
    );
    const queryProps: [
      query: string,
      objectMetadata: RubyObject,
      filters: RubyFilter[],
      extraRelationFields?: string | undefined,
      orderByFields?: OrderByField[] | undefined,
    ] = [
      query,
      usageMetadata,
      [...tabFilters[activeTabDetail.tabIndex].filter!],
      undefined,
      tabFilters[activeTabDetail.tabIndex].orderByFilters,
    ];
    setUsageChartData(await executeQuery(...queryProps));
  };

  if (rubySettings?.showUsageOnCustomerLifecycle) {
    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'Usages',
      displayName: 'Usage',
      gridViewMetadataName: 'usage-grid-view-metadata',
      extraRelationFields: '',
      loadObjectMetadata: loadUsageMetadata,
      pageKey: 'usage__customer_view',
      GridViewItem: () => {
        return <></>;
      },
      predefinedFilters: getPredefinedFilters(timezone),
      fieldSetApiName: 'UsageFields',
      enablePagination: true,
      defaultPageSize: 20,
      showListViewMultiSelect: false,
      isListViewOnly: true,
      remotePaging: true,
      searchForBigObj: true,
      filter: [
        {
          id: `objectFilter-${tabFilters.length + 1}`,
          name: `Object Filter`,
          conditions:
            customer.uid && customer.uid !== customer.id
              ? [
                  {
                    apiName: 'customerId',
                    name: 'Customer id',
                    operand: ' _or ',
                    operator: ' _eq ',
                    type: 'bLookup',
                    relation: 'customer',
                    value: `"${customer.uid}"`,
                    lookupRelation: {
                      referenceField: 'id',
                      referenceTo: 'Customer',
                      relationLabel: 'Customer',
                      relationName: 'customer',
                    },
                    nestedConditions: [],
                  },
                ]
              : [
                  {
                    apiName: 'customerId',
                    name: 'Customer id',
                    operand: ' _or ',
                    operator: ' _eq ',
                    type: 'text',
                    value: `"${customer.id}"`,
                    nestedConditions: [
                      {
                        apiName: 'salesAccountId',
                        name: 'SalesAccount',
                        operand: ' _or ',
                        operator: ' _in ',
                        type: 'Picklist',
                        value: JSON.stringify(customerIds),
                      },
                    ],
                  },
                ],
          isApplied: true,
        },
        {
          name: 'isCash Filter',
          conditions: [
            {
              apiName: 'isCash',
              name: 'IsCash',
              type: 'boolean',
              value: `${selectedCredit === 'Cash'}`,
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          id: 'isCash',
          isSystemField: true,
          isExclusive: true,
          isApplied: true,
        },
        ...(isMultiCurrencyEnabled
          ? ([
              {
                name: 'currencyIsoCode Filter',
                conditions: [
                  {
                    apiName: 'currencyIsoCode',
                    name: 'Currency Iso Code',
                    operand: ' _and ',
                    operator: ' _eq ',
                    type: 'string',
                    value: `"${usageCurrencyIsoCode}"`,
                  },
                ],
                id: 'currencyFilter',
                isSystemField: true,
                isExclusive: false,
                isApplied: true,
              },
            ] as RubyFilter[])
          : []),
      ],
      defaultFieldSetMetaData: usageFiledSetMetadata as RubyField[],
      orderByFilters: [
        {
          columnName: 'startTime',
          direction: 'desc',
        },
      ],
      renderExtraContents: (
        _objects: any[] | null,
        handleSearch: (extraFilters: RubyFilter[]) => void,
      ) => {
        if (usageChartData) {
          const systemFilters: RubyFilter[] = activeFilters?.filter((_) => _.isSystemField) || [];
          const dataItems: UsageChartDataItem[] = constructDataItems(usageChartData, systemFilters);
          const billDate: string[] = dataItems?.[0]?.data?.map((_) => _.startTime) || [];
          const unbillDate: string[] = dataItems?.[1]?.data?.map((_) => _.startTime) || [];
          const billableDate = [...billDate, ...unbillDate];
          const usageDateGroup = constructDateGroups(billableDate, systemFilters);
          const chartDataResult = {
            dataItems: dataItems,
            dateGroups: sortByDate(usageDateGroup),
            hasFilter: true,
          };

          return (
            <>
              {creditTypes?.some(
                (creditType) =>
                  creditType.creditType === 'Credit' && creditType.status === 'Active',
              ) && (
                <UsageCreditSelector
                  usageCredit={selectedCredit}
                  usageCreditOptions={['Cash', 'Credit']}
                  onusageCreditChange={(v) => {
                    setSelectedCredit(v);
                    handleSearch([
                      {
                        name: 'isCash Filter',
                        conditions: [
                          {
                            apiName: 'isCash',
                            name: 'IsCash',
                            type: 'boolean',
                            value: `${v === 'Cash'}`,
                            operator: ' _eq ',
                            operand: ' _and ',
                            nestedConditions: [],
                          },
                        ],
                        id: 'isCash',
                        isSystemField: true,
                        isExclusive: false,
                        isApplied: true,
                      },
                    ]);
                  }}
                />
              )}
              {isMultiCurrencyEnabled && (
                <CurrencySelector
                  value={usageCurrencyIsoCode != null ? usageCurrencyIsoCode : currency}
                  onChange={(event: any) => {
                    const newCurrency = event.target.value;
                    setCurrency?.(newCurrency);
                    setUsageCurrencyIsoCode(newCurrency);
                    handleSearch([
                      {
                        name: 'currencyIsoCode Filter',
                        conditions: [
                          {
                            apiName: 'currencyIsoCode',
                            name: 'Currency Iso Code',
                            operand: ' _and ',
                            operator: ' _eq ',
                            type: 'string',
                            value: `"${newCurrency || currencyIsoCode}"`,
                          },
                        ],
                        id: 'currencyFilter',
                        isSystemField: true,
                        isExclusive: false,
                        isApplied: true,
                      },
                    ]);
                  }}
                  selectedCredit={selectedCredit}
                  userMultiCurrencyInfo={{
                    isMultiCurrencyEnabled,
                    orgDefaultCurrencyIsoCode: defaultCurrency,
                    availableCurrencyIsocodes: availableCurrencies,
                  }}
                />
              )}
              <UsageDashboard
                usageChartData={chartDataResult}
                currencyIsoCode={currency || currencyIsoCode}
                locale={locale || 'en-US'}
                isCurrency={selectedCredit && selectedCredit === 'Credit' ? false : true}
              />
            </>
          );
        }
        return <></>;
      },
      executeQuery: async (...queryProps) => {
        getUsageChartData(queryProps?.[1], queryProps?.[2]);
        return executeQuery(...queryProps);
      },
    });
  }

  if (rubySettings?.showCreditOnCustomerLifecycle) {
    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'Credits',
      displayName: getLabel('lifecycleManager_tab_credits_title', 'Credits'),
      gridViewMetadataName: 'credit-grid-view-metadata',
      extraRelationFields: '',
      loadObjectMetadata: loadCreditMetadata,
      pageKey: 'credits__customer_view',
      getCustomTitle: () => {
        if (!creditsStatistics) return '';
        return (
          creditsStatistics.creditTypeLabel ||
          getLabel('lifecycleManager_tab_credits_title', 'Credits')
        );
      },
      GridViewItem: () => {
        return <></>;
      },
      predefinedFilters: [
        {
          conditions: [
            {
              apiName: 'recordType',
              name: 'Record Type',
              type: 'pickList',
              value: '"Inflow"',
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'Credit Inflows',
          isExclusive: true,
          id: '0',
        },
        {
          conditions: [
            {
              apiName: 'recordType',
              name: 'Record Type',
              type: 'pickList',
              value: '"Outflow"',
              operator: ' _eq ',
              operand: ' _and ',
              nestedConditions: [],
            },
          ],
          name: 'Credit Outflows',
          isExclusive: true,
          id: '1',
        },
      ],
      fieldSetApiName: 'CreditsFields',
      enablePagination: true,
      defaultPageSize: 20,
      showListViewMultiSelect: false,
      isListViewOnly: true,
      remotePaging: true,
      searchForBigObj: true,
      renderExtraHeader: () => {
        if (!creditsStatistics) return <></>;
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <BalanceIcon className={classes.balanceIcon} />
            <Typography variant="h6" style={{ width: 90 }}>
              Balance:
            </Typography>
            <span
              style={{
                fontSize: '30px',
                color: '#FF7D00',
                letterSpacing: '0.94px',
                fontWeight: 600,
              }}
            >
              {formatNumberForDisplay(
                creditsStatistics.accountBalance,
                creditsStatistics.currencyIsoCode != null && creditsStatistics.currencyIsoCode != ''
                  ? creditsStatistics.currencyIsoCode
                  : currencyIsoCode,
                true,
                !creditsStatistics.isCash,
                locale,
              )}
            </span>
          </div>
        );
      },
      renderExtraContents: (
        objects: any[] | null,
        handleSearch: (extraFilters: RubyFilter[]) => void,
      ) => {
        if (!creditsStatistics) return <></>;
        return (
          <>
            <div className={classes.selectorContainer}>
              {creditsStatistics && (
                <div className={classes.selectorContainer}>
                  <CreditPoolSelector
                    label="CREDIT POOL"
                    creditPool={creditsStatistics.creditPool}
                    onSelectCreditPool={async (id: string) => {
                      setSelectedCreditPoolId(id);
                      handleSearch([
                        {
                          name: 'Credit pool Filter',
                          conditions:
                            currentAccountIdANdChildrenAccountIds &&
                            currentAccountIdANdChildrenAccountIds.length > 0
                              ? [
                                  {
                                    apiName: 'accountPoolId',
                                    name: 'Account Pool Id',
                                    type: 'text',
                                    value: `"${id}"`,
                                    operator: ' _eq ',
                                    operand: ' _and ',
                                    nestedConditions: [
                                      {
                                        apiName: 'customerId',
                                        name: 'Customer Id',
                                        type: 'text',
                                        value: `"${customer.id}"`,
                                        operator: ' _eq ',
                                        operand: ' _and ',
                                        nestedConditions: [],
                                      },
                                      {
                                        apiName: 'salesAccountId',
                                        name: 'Sales Account Id',
                                        type: 'text',
                                        value: JSON.stringify(
                                          currentAccountIdANdChildrenAccountIds,
                                        ),
                                        operator: ' _in ',
                                        operand: ' _or ',
                                        nestedConditions: [],
                                      },
                                    ],
                                  },
                                ]
                              : [
                                  {
                                    apiName: 'accountPoolId',
                                    name: 'Account Pool Id',
                                    type: 'text',
                                    value: `"${id}"`,
                                    operator: ' _eq ',
                                    operand: ' _and ',
                                    nestedConditions: [],
                                  },
                                  {
                                    apiName: 'customerId',
                                    name: 'Customer Id',
                                    type: 'text',
                                    value: `"${customer.id}"`,
                                    operator: ' _eq ',
                                    operand: ' _and ',
                                    nestedConditions: [],
                                  },
                                ],
                          id: 'creditPool',
                          isSystemField: true,
                          isExclusive: false,
                          isApplied: true,
                        },
                      ]);
                      const res = await getPanelStatistics({
                        AccountCreditPoolId: id,
                      });
                      setCreditsStatistics({
                        ...JSON.parse(res)?.data,
                        creditPool: creditsStatistics.creditPool,
                      });
                    }}
                  />
                  <span className={classes.fieldLabel} role="label">
                    Total Grant Credits
                  </span>
                  <span className={classes.amountLabel} role="label">
                    {formatNumberForDisplay(
                      creditsStatistics.totalGrantCredits,
                      creditsStatistics.currencyIsoCode != null &&
                        creditsStatistics.currencyIsoCode != ''
                        ? creditsStatistics.currencyIsoCode
                        : currencyIsoCode,
                      true,
                      !creditsStatistics.isCash,
                      locale,
                    )}
                  </span>
                  {creditsStatistics.isCash &&
                    creditsStatistics.creditPool.length > 0 &&
                    !!creditsStatistics.creditPool[0].enabledCreditMemo && (
                      <div
                        style={{
                          flex: 1,
                          display: 'flex',
                          justifyContent: 'flex-end',
                        }}
                      >
                        <RubyButton
                          icon={<MonetizationOnOutlinedIcon />}
                          text="Cash Out"
                          classNames={classes.cashOutButton}
                          onClick={() => {
                            setShowCashOutDialog(true);
                          }}
                        />
                      </div>
                    )}
                </div>
              )}
            </div>
            <CreditsBarChart
              creditsStatistics={creditsStatistics}
              locale={locale}
              currencyIsoCode={currencyIsoCode}
            />
          </>
        );
      },
      filter: [
        {
          id: `objectFilter-${tabFilters.length + 1}`,
          name: `Object Filter`,
          conditions: [],
          isApplied: true,
        },
      ],
      defaultFieldSetMetaData: creditFiledSetMetadata as RubyField[],
      orderByFilters: [
        {
          columnName: 'name',
          direction: 'desc',
        },
      ],
    });
  }

  if (rubySettings?.showRevContractsOnCustomerLifecycle) {
    const processStatsBarData = (data: RevenueContractsResult): StatsBarData | undefined => {
      if (!data) return undefined;
      return {
        items: [
          { value: data.total_billed_revenue, label: 'Billed', color: '#959595' },
          { value: data.total_recognized_revenue, label: 'Recognized', color: '#0EB3D3' },
          { value: data.total_scheduled_revenue, label: 'Scheduled', color: '#A33CEF' },
        ],
        total: { value: data.total_revenue, label: 'Total Revenue' },
      };
    };

    tabFilters.push({
      id: `${tabFilters.length + 1}`,
      name: 'Revenue Contracts',
      displayName: getLabel('lifecycleManager_tab_revenue_contracts_title', 'Revenue Contracts'),
      gridViewMetadataName: 'credit-grid-view-metadata',
      pageKey: 'revenue_contracts_customer_view',
      GridViewItem: () => {
        return <></>;
      },
      fieldSetApiName: 'RevenueContractsFields',
      isListViewOnly: true,
      hideFilters: true,
      hideSearch: true,
      addActionColumn: () => {
        return {
          name: 'actions',
          title: 'Actions',
          apiName: '',
          type: '',
          width: 105,
          cellRenderer: ({ row, helper }) => {
            const viewButton = GridButton({
              itemProps: { xs: 2 },
              tooltipText: 'View',
              Icon: ViewIcon,
              iconAlt: 'view-icon',
              onClickHandler: ({ row: _row }: any) => {
                if (revenueIntegration) {
                  window.open(
                    `https://${revenueIntegration.realm}.${
                      revenueIntegration.sandbox ? 'sdx02' : 'prd'
                    }.rightrev.cloud/appui#/RevenueWorkbench/${_row.name}`,
                  );
                }
              },
            });
            return (
              <GridActions
                actionHelpers={{
                  ...helper,
                  row,
                }}
                actions={[viewButton]}
              />
            );
          },
        };
      },
      executeQuery: async (query, objectMetadata, filters) => {
        const search = customer.id;
        const filter =
          filters.find((filtr) => filtr.name !== 'Search Filter')?.conditions[0].value || null;
        const data = await getRevenueContracts(search, filter);
        setStatsBarData(processStatsBarData(data));
        return [...data.data_items]
          .sort((a, b) => a.name.localeCompare(b.name))
          .map((item) => {
            return { ...item, id: item.name };
          });
      },
      predefinedFilters: [
        {
          conditions: [
            {
              value: null,
              apiName: 'name',
              name: 'Name',
              type: 'text',
            },
          ],
          name: 'All',
          isSystemField: true,
          isExclusive: true,
          id: '0',
        },
        {
          conditions: [
            {
              value: 'recently_created',
              apiName: 'name',
              name: 'Name',
              type: 'text',
            },
          ],
          name: 'Recently Created',
          isSystemField: true,
          isExclusive: true,
          id: '1',
        },
        {
          conditions: [
            {
              value: 'recently_modified',
              apiName: 'name',
              name: 'Name',
              type: 'text',
            },
          ],
          name: 'Recently Modified',
          isSystemField: true,
          isExclusive: true,
          id: '2',
        },
        {
          conditions: [
            {
              value: 'recently_viewed',
              apiName: 'name',
              name: 'Name',
              type: 'text',
            },
          ],
          name: 'Recently Viewed',
          isSystemField: true,
          isExclusive: true,
          id: '3',
        },
        {
          conditions: [
            {
              value: 'most_valued',
              apiName: 'name',
              name: 'Name',
              type: 'text',
            },
          ],
          name: 'Most Valued',
          isSystemField: true,
          isExclusive: true,
          id: '4',
        },
      ],
      renderExtraContents: () => (
        <RevenueContractsStatsBar
          statsBarData={statsBarData}
          currencyIsoCode={currencyIsoCode}
          locale={locale}
        />
      ),
      objectMetadata: revenueContractMetadata,
      searchForBigObj: true,
      defaultFieldSetMetaData: revenueContractMetadata.fields,
    });
  }

  if (overrideTabFilters && overrideTabFilters.length > 0) {
    for (let i = 0; i < overrideTabFilters.length; i++) {
      const currentOverrideTabFilter = overrideTabFilters[i];
      if (currentOverrideTabFilter) {
        const tabIndex = getTabIndex(currentOverrideTabFilter.name || '');
        if (tabIndex >= 0) {
          tabFilters[tabIndex] = Object.assign({}, tabFilters[tabIndex], currentOverrideTabFilter);
        }
      }
    }
  }

  const loadMetetada = React.useCallback(async () => {
    let newData = undefined;
    const state = await localforage.getItem<TabMetadataState | null>('tab-metadatas');
    const today = dayjs();
    if (
      state &&
      state.metadatas?.length &&
      dayjs(state.validUnitl).isAfter(today) &&
      state.metadatas.find((x: TabMetadata) => x.tabIndex === activeTabDetail.tabIndex)
    ) {
      newData = state.metadatas.find(
        (x: TabMetadata) => x.tabIndex === activeTabDetail.tabIndex,
      )?.data;
    } else {
      const data = await tabFilters[activeTabDetail.tabIndex]?.loadObjectMetadata?.();
      if (data) {
        const newLoadedObjectMetadata = [
          ...(state?.metadatas || []),
          {
            tabIndex: activeTabDetail.tabIndex,
            data: data,
          },
        ];
        localforage.setItem('tab-metadatas', {
          validUnitl: dayjs().add(30, 'day').format('YYYY-MM-DD'),
          metadatas: newLoadedObjectMetadata,
        });
        newData = data;
      }
    }

    return newData;
  }, [activeTabDetail.tabIndex, tabFilters]);

  const setupSelectedObjectMetadata = React.useCallback(async () => {
    try {
      let data: RubyObject | undefined = undefined;
      if (tabFilters[activeTabDetail.tabIndex].objectMetadata) {
        data = tabFilters[activeTabDetail.tabIndex].objectMetadata;
      } else if (tabFilters[activeTabDetail.tabIndex].loadObjectMetadata) {
        data = await loadMetetada();
      }
      setSelectedObjectMetadata(data);
    } catch (error) {
      snackbarService.showSnackbar(
        'error',
        'Error',
        extractErrorMessage(error, 'Metadata could not be loaded'),
      );
      console.error('Error getting object metadata', error.message);
    }
  }, [activeTabDetail.tabIndex, loadMetetada, tabFilters]);

  const handleUpdateObjects = async (newObjects: any[] | null, searchResult: string) => {
    if (newObjects === null) {
      setObjects(null);
      return;
    }

    if (selectedObjectMetadata?.apiName === 'Order') {
      setObjects(
        newObjects
          ? newObjects.map((object) => ({
              ...object,
              owner: {
                ...object.owner,
                imageSignedUrl: object.owner.imageSignedUrl || personPlaceholderImage,
              },
            }))
          : null,
      );
      // for subscription search, we will manually apply the objects being filtered
    } else if (selectedObjectMetadata?.name === 'Subscription') {
      const rootItems = buildSubscriptionHierarchy(newObjects, searchResult);
      setObjects(rootItems);
    } else if (selectedObjectMetadata?.apiName === 'Account') {
      // sort the accounts hierarchy
      const accountsList: any[] = [];
      const billingAccountList: any[] = [];

      // Find IDs of accounts with child sales accounts
      const accountsWithChildSalesAccounts = newObjects.reduce((accountIds, currentAccount) => {
        if (currentAccount.parentCustomerId) accountIds.add(currentAccount.parentCustomerId);
        return accountIds;
      }, new Set());

      // Find IDs of accounts with child billing accounts
      const accountsWithChildBillingAccounts = newObjects.reduce((accountIds, currentAccount) => {
        if (currentAccount.salesAccountId) accountIds.add(currentAccount.salesAccountId);
        return accountIds;
      }, new Set());

      newObjects.forEach((object) => {
        object.hasChildSalesAccounts = accountsWithChildSalesAccounts.has(object.id);
        object.hasChildBillingAccounts = accountsWithChildBillingAccounts.has(object.id);

        if (!object.salesAccountId && !object.parentCustomerId && !object.type) {
          // account
          accountsList.push(object);
          object.type = 'Account';
        }
        if (!object.salesAccountId && object.parentCustomerId) {
          // sales account
          accountsList.push(object);
          object.type = 'Subsidiary';
        }
        if (
          (object.salesAccountId && !object.parentCustomerId) ||
          object.type === 'BillingAccount'
        ) {
          // billing account
          billingAccountList.push(object);
          object.type = 'BillingAccount';
        }
        if (object.id === customer.id) {
          object.type = 'Account';
        }
      });

      if (!accountsList.length && !billingAccountList.length) {
        setObjects(newObjects);
      } else {
        setObjects(accountsList.concat(billingAccountList));
      }
    } else if (selectedObjectMetadata?.apiName === 'Credits') {
      if (!selectedCreditPoolId) {
        setObjects(null);
      } else {
        setObjects(newObjects);
      }
    } else if (selectedObjectMetadata?.name === 'Payment Method') {
      if (loadStripeCardInfo) {
        const stripeCardInfos = await loadStripeCardInfo(customerIds);
        const paymentMethods = stripeCardInfos?.paymentMethods || [];
        const newObjectsCopy = [...newObjects];
        paymentMethods.forEach((paymentMethod: any) => {
          const updatedItem = newObjectsCopy.find(
            (item) => item.externalPaymentMethodId === paymentMethod.paymentMethodId,
          );
          if (updatedItem) {
            updatedItem.lastFourDigits = paymentMethod.lastFourDigits;
            updatedItem.brand = paymentMethod.brand;
            updatedItem.expMonth = paymentMethod.expMonth;
            updatedItem.expYear = paymentMethod.expYear;
            updatedItem.type = paymentMethod.type;
          }
        });
        console.log(paymentMethods);
        console.log(newObjectsCopy);
        setObjects(newObjectsCopy);
      }
    } else {
      setObjects(newObjects);
    }
  };

  const setupGridViewMetadata = (
    tabIndex: number,
    setGridViewMetadata: (gridViewMetadata: GridViewMetadata) => void,
  ) => {
    try {
      const gridViewMetadata = GridViewMetadataApi.getGridViewMetadata(
        tabFilters[tabIndex].gridViewMetadataName,
      );
      setGridViewMetadata(gridViewMetadata);
    } catch (error) {
      snackbarService.showSnackbar(
        'error',
        'Error',
        extractErrorMessage(error, 'Grid view metadata could not be retrieved'),
      );
      console.error('Error getting grid view metadata', error.message);
    }
  };

  const setupFieldSetMetadata = async (
    tabIndex: number,
    setFieldSetMetadata: (fieldSetMetadata: RubyField[]) => void,
  ) => {
    let fieldSetMetadata: RubyField[] = [];
    try {
      fieldSetMetadata = await getFieldSetMetadata(
        tabFilters[tabIndex].fieldSetApiName,
        tabFilters[tabIndex].objectMetadata?.apiName || selectedObjectMetadata?.apiName || '',
      );
    } catch (error) {
      snackbarService.showSnackbar(
        'error',
        'Error',
        extractErrorMessage(error, 'Field set metadata could not be loaded'),
      );
      console.error('Error getting field set metadata', error.message);
    }

    setFieldSetMetadata(fieldSetMetadata);
    return fieldSetMetadata;
  };

  const getFieldSetMetadataResult = async (
    tabIndex: number,
    setFieldSetMetadata: (fieldSetMetadata: RubyField[]) => void,
  ) => {
    if (tabFilters[activeTabDetail.tabIndex].searchForBigObj) {
      const metadata = tabFilters[activeTabDetail.tabIndex].defaultFieldSetMetaData || [];
      setFieldSetMetadata(metadata);
      return metadata;
    }
    return await setupFieldSetMetadata(tabIndex, setFieldSetMetadata);
  };

  const getDefaultSubscriptionFields = () => {
    return (
      subscriptionMetadata?.fields?.filter((field) => {
        if (
          field.apiName === 'name' ||
          field.apiName === 'subscriptionVersion' ||
          field.apiName === 'productName' ||
          field.apiName === 'status'
        ) {
          return true;
        }
        return false;
      }) || []
    );
  };

  const findFieldsToDisplay = (result: Map<string, RubyField>, fields: RubyField[]) => {
    if (!fields) {
      return;
    }

    for (let i = 0; i < fields.length; i++) {
      if (result.get(fields[i].apiName)) {
        continue;
      }
      result.set(fields[i].apiName, fields[i]);
    }
  };

  const getDefaultSubscriptionFieldSetMetadata = (
    defaultSubscriptionFields: RubyField[],
    subscriptionFieldSetFields: RubyField[],
    subscriptionPricingFields: RubyField[],
  ) => {
    const fieldsToDisplay = new Map();
    findFieldsToDisplay(fieldsToDisplay, defaultSubscriptionFields);
    findFieldsToDisplay(fieldsToDisplay, subscriptionFieldSetFields);
    findFieldsToDisplay(fieldsToDisplay, subscriptionPricingFields);
    return [...fieldsToDisplay.values()];
  };

  const setupDefaultSubscriptionListViewMetadata = async (
    subscriptionPricingFields: RubyField[],
  ) => {
    const defaultSubscriptionFields = getDefaultSubscriptionFields();
    const subscriptionFieldSetFields = await getFieldSetMetadata(
      tabFilters[activeTabDetail.tabIndex].fieldSetApiName,
      subscriptionMetadata?.apiName || '',
    );
    const subscriptionFields = getDefaultSubscriptionFieldSetMetadata(
      defaultSubscriptionFields,
      subscriptionFieldSetFields,
      subscriptionPricingFields,
    );
    return subscriptionFields;
  };

  const setupSubscriptionOrderViewMetadata = async () => {
    if (orderGridViewMetadata === null) {
      setupGridViewMetadata(1, setOrderGridViewMetadata);
    }
    if (orderFieldSetMetadata === null) {
      await setupFieldSetMetadata(1, setOrderFieldSetMetadata);
    }
  };

  const setupSubscriptionListViewMetadata = async () => {
    // TODO: Re-add this try-catch block but right now it is causing an issue with the subscription list view
    // Which is causing the subscription card to not show the TCV
    let listViewMetadata = undefined;

    if (subscriptionPricingFieldSet == null) {
      const subscriptionPricingFields = await getFieldSetMetadata(
        'SubscriptionPricingFields',
        subscriptionMetadata?.apiName || '',
      );

      setSubscriptionPricingFieldSet(subscriptionPricingFields);

      listViewMetadata = await setupDefaultSubscriptionListViewMetadata(subscriptionPricingFields);
    } else {
      listViewMetadata = await setupDefaultSubscriptionListViewMetadata(
        subscriptionPricingFieldSet,
      );
    }

    setListViewMetadata(listViewMetadata);
  };

  const key = `customer-view-grid-and-list-view-tab-${tabFilters[activeTabDetail.tabIndex].name}`;
  const listViewLocalStateProvider: ListViewLocalStateService = {
    getLocalState: async () => {
      console.debug('getLocalState: ' + key);
      return await localforage.getItem(key);
    },
    getLocalStateByKey: async (key) => {
      return await localforage.getItem(key);
    },
    setLocalStateByKey: async (key, listViewLocalState) => {
      console.debug('setLocalStateByKey', listViewLocalState);
      await localforage.setItem(key, listViewLocalState);
    },
    setLocalState: async (listViewLocalState) => {
      console.debug('setLocalState', listViewLocalState);
      await localforage.setItem(key, listViewLocalState);
    },
  };

  const setupListViewMetadata = async () => {
    const fieldSetMetadata = await getFieldSetMetadataResult(
      activeTabDetail.tabIndex,
      setFieldSetMetadata,
    );
    if (tabFilters[activeTabDetail.tabIndex].name !== 'Subscriptions') {
      setListViewMetadata(fieldSetMetadata);
    } else {
      await setupSubscriptionListViewMetadata();
    }
  };

  const getInvoicePeriods = async () => {
    const result = await loadInvoicePeriods();
    setInvoicePeriods(result);
  };

  const getCreditsStatistics = async () => {
    if (!customer) return;
    const result = await loadCreditStatistics(customer.id);
    if (result && result.creditPool?.length > 0) {
      setSelectedCreditPoolId(result.creditPool[0].id);
    }
    setCreditsStatistics(result);
  };

  React.useEffect(() => {
    if (selectedObjectMetadata) {
      try {
        setupListViewMetadata();
      } catch (error) {
        snackbarService?.showSnackbar(
          'error',
          'Error',
          extractErrorMessage(error, 'Failed to set up list view metadata'),
        );
        console.error('Error setting up list view metadata', error.message);
      }
    }
  }, [selectedObjectMetadata]);

  const getReleatedBillingAccountsIds = async () => {
    const ids = await getRelatedBillingAccountsIdOfSalesAccount({
      salesAccountId: customer.id,
    });
    setRelatedBillingAccountsIds(ids);
    return ids;
  };

  const getChildrenAccounts = async () => {
    if (typeof getAccountHierarchy !== 'function') {
      return;
    }
    const accountHierarchy = await getAccountHierarchy(customer.id);
    const stack: AccountHierarchy[] = [];
    const allChildrenIds: string[] = [];
    if (accountHierarchy?.children?.length) {
      if (setShowChildrenBtn) {
        setShowChildrenBtn(true);
      }
      stack.push(...accountHierarchy.children);
      while (stack.length) {
        const item = stack.pop();
        if (item) {
          const children = item?.children || [];
          allChildrenIds.push(item.id);
          for (let i = children.length - 1; i >= 0; i--) {
            stack.push(children[i]);
          }
        }
      }
    }
    setChildrenAccountIds(allChildrenIds);
    const tempArr: string[] = allChildrenIds.slice();
    tempArr.push(customer.id);
    setCurrentAccountIdANdChildrenAccountIds(tempArr);
    return tempArr;
  };

  const setup = async () => {
    setupGridViewMetadata(activeTabDetail.tabIndex, setGridViewMetadata);
    if (tabFilters[activeTabDetail.tabIndex].name === 'Subscriptions') {
      await setupSubscriptionOrderViewMetadata();
    }
    if (
      (tabFilters[activeTabDetail.tabIndex].name === 'Invoices' ||
        tabFilters[activeTabDetail.tabIndex].name === 'CreditMemos') &&
      !invoicePeriods
    ) {
      await getInvoicePeriods();
    }
    if (tabFilters[activeTabDetail.tabIndex].name === 'Credits') {
      await getCreditsStatistics();
    }
    await setupSelectedObjectMetadata();

    if (tabFilters[activeTabDetail.tabIndex].name === 'Revenue Contracts') {
      if (!revenueIntegration) {
        setRevenueIntegration(await getRevenueIntegration());
      }
    }

    if (tabFilters[activeTabDetail.tabIndex].name === 'Usages' && getCreditTypes) {
      if (!creditTypes) {
        const _creditTypes = await getCreditTypes();
        setCreditTypes(_creditTypes);
        setSelectedCredit(
          _creditTypes?.some(
            (_creditType) => _creditType.creditType === 'Cash' && _creditType.status === 'Active',
          ) || _creditTypes?.every((_creditType) => _creditType.status === 'Inactive')
            ? 'Cash'
            : 'Credit',
        );
      }
    }

    const _childrenAccountIds = (await getChildrenAccounts()) || [];
    const _relatedBillingAccountsIds = await getReleatedBillingAccountsIds();
  };

  useEffect(() => {
    setGridViewMetadata(null);
    setFieldSetMetadata(null);
    setListViewMetadata(null);
    setObjects(null);
    setup();
  }, [activeTabDetail.tabIndex]);

  useEffect(() => {
    setRefreshDataFlag(refreshDataFlag + 1);
  }, [showChildrenAccounts]);

  useEffect(() => {
    async function fetchData() {
      if (customerViewConfigs.defaultTab && customerViewConfigs.defaultTab === 'order') {
        setActiveTabDetails({
          ...activeTabDetail,
          tabIndex: 1,
        });
      } else if (
        customerViewConfigs.defaultTab &&
        customerViewConfigs.defaultTab === 'subscription'
      ) {
        setActiveTabDetails({
          ...activeTabDetail,
          tabIndex: 2,
        });
      } else if (customerViewConfigs.orderId && listViewLocalStateProvider) {
        // go to subscriptions directly
        const subscriptionNumbers = await getOriginalSubscriptionNumbers(
          executeQuery,
          customerViewConfigs.orderId || '',
          customer.id,
          subscriptionMetadata,
        );
        await setupSubscriptionFilters(listViewLocalStateProvider, subscriptionNumbers);
        setActiveTabDetails({
          ...activeTabDetail,
          tabIndex: 2,
        });
      }
    }
    fetchData();
  }, []);

  const isFirstTimeLoadingSubscriptionTab = () => {
    return (
      tabFilters[activeTabDetail.tabIndex].name === 'Subscriptions' &&
      !orderGridViewMetadata &&
      !orderFieldSetMetadata &&
      !subscriptionPricingFieldSet
    );
  };

  const handleUpdateAutoRenew = (object: any) => {
    if (objects === null) return;
    const newObjects = [...objects].map((x: any) => {
      if (x.id === object.id) {
        return {
          ...x,
          autoRenew: object.autoRenew,
        };
      }
      return x;
    });
    setObjects(newObjects);
  };

  const handleCardClick = async (object: any) => {
    if (!customerViewService.actionEventHandler) {
      return;
    }
    if (
      tabFilters[activeTabDetail.tabIndex].name === 'Assets' ||
      tabFilters[activeTabDetail.tabIndex].name === 'Entitlements'
    ) {
      await customerViewService.actionEventHandler({
        object,
        //@ts-ignore
        objectMetadata: tabFilters[activeTabDetail.tabIndex].objectMetadata,
        action: {
          id: 'details',
          name: 'Title Click',
          description: 'Click Title',
        },
      });
    }
    if (tabFilters[activeTabDetail.tabIndex].name === 'General Information') {
      await navigateToCustomerViewPage('customer', object.id);
    }
  };

  const updateBreadCrumb = (newBreadcrumb: Breadcrumb[]) => {
    const config = breadcrumbService?.getBreadcrumbConfig();
    if (config) {
      //@ts-ignore
      config.updateBreadcrumbs(newBreadcrumb);
    }
  };

  const checkAndUpdateBreadcrumb = (mode: TabMode, name: string, researchCallBack: any) => {
    if (customerViewPageMode) {
      if (customerViewPageMode.mode === mode && customerViewPageMode.name === name) {
        return;
      }
    }
    setCustomerViewPageMode({
      mode: mode,
      name: name,
    });

    const newBreadCrumbs = [
      {
        action: () => {
          setActiveTabDetails({
            tabIndex: mode === 'Order' ? 1 : 2,
            tabMode: 'All',
            argument: null,
          });
          updateBreadCrumb([]);
          setCustomerViewPageMode(undefined);
          if (researchCallBack) {
            researchCallBack();
          }
        },
        label: 'Manage Customer Lifecycle',
      },
      {
        label: `${mode} ${name}`,
      },
    ];
    updateBreadCrumb(newBreadCrumbs);
  };

  const handleUpdateModeActions = (
    argument: any,
    mode: 'Subscription' | 'Order' | 'Entitlement' | 'Asset',
  ) => {
    let tabIndex = null;
    switch (argument.action.id) {
      case 'changeOrder':
        tabIndex = getTabIndex('Subscriptions');
        break;
      case 'viewInvoice':
        tabIndex = getTabIndex('Invoices');
        break;
      case 'viewUsage':
        tabIndex = getTabIndex('Usages');
        break;
      case 'viewOrder':
        tabIndex = getTabIndex('Orders');
        break;
      case 'viewAsset':
        tabIndex = getTabIndex('Assets');
        break;
      case 'viewEntitlement':
        tabIndex = getTabIndex('Entitlements');
        break;
      case 'viewCredits':
        tabIndex = getTabIndex('Credits');
        break;
      default:
        break;
    }
    if (tabIndex !== null) {
      setActiveTabDetails({
        tabIndex: tabIndex,
        tabMode: mode,
        argument: argument,
      });
      checkAndUpdateBreadcrumb(
        mode,
        mode === 'Subscription'
          ? argument.object.name
          : argument.object.number || argument.object.name,
        argument.callBack,
      );
      return true;
    }
    return false;
  };

  const getErrMsg = (action: string, invoiceName: string, e: any) => {
    return (
      'Sorry, failed to ' +
      action +
      ' invoice ' +
      invoiceName +
      ': ' +
      (e && e.response && e.response.data && e.response.data.errorMessage
        ? e.response.data.errorMessage
        : 'Unknown Reason')
    );
  };

  if (
    !gridViewMetadata ||
    !fieldSetMetadata ||
    !listViewMetadata ||
    !selectedObjectMetadata ||
    isFirstTimeLoadingSubscriptionTab() ||
    (selectedObjectMetadata.apiName === 'Usage' && !selectedCredit) ||
    !integrations
  ) {
    return <LoadingScreenTimeout />;
  }

  const dialogAction = selectedRowType === 'Invoice' ? 'Invoice' : 'Credit Memo';

  const onSaveAndClose = () => {
    setManageMilestoneDialogProps({
      open: false,
    });
    snackbarService.showSnackbar('confirm', 'Success', `Milestone Date is updated successfully.`);
  };

  const AddUpdateMilestoneToCart = async (row: Milestone) => {
    if (manageMilestoneDialogProps) {
      const changeGroup: ChangeGroup = {
        id: `${customer.id}-${manageMilestoneDialogProps.object.priceBook.id}-${manageMilestoneDialogProps.object.currencyIsoCode}`,
        customer: {
          name: customer.name,
          id: customer.id,
        },
        name: manageMilestoneDialogProps.object.name,
        priceBook: {
          name: manageMilestoneDialogProps.object.priceBook.name,
          id: manageMilestoneDialogProps.object.priceBook.id,
        },
        currencyIsoCode: manageMilestoneDialogProps.object.currencyIsoCode,
      };
      const currentTime = dayjs().format('MM/DD/YYYY hh:mm A');
      const changeDate = dayjs(row.startDate).format('YYYY-MM-DD');
      const changeItem: ChangeItem = {
        id: `${manageMilestoneDialogProps.object.id}-UpdateMilestone-${changeDate}-${
          row.milestones
        }-${row.id!}`,
        time: currentTime,
        // @ts-ignore
        asset: {
          name: manageMilestoneDialogProps.object.name,
          subscriptionStartDate: manageMilestoneDialogProps.object.startDate,
          uom: manageMilestoneDialogProps.object.uom,
          quantity: manageMilestoneDialogProps.object.quantity,
          customerId: customer.id,
          // @ts-ignore
          customer: {
            name: customer.name,
          },
          billingAccountId: manageMilestoneDialogProps.object.billingAccountId,
          milestoneIds: [row.id!],
        },
        cardAction: manageMilestoneDialogProps.action,
        subscriptionMetadata,
        requestWasEdited: false,
        parentSubscription: null,
        request: {
          assetName: manageMilestoneDialogProps.object.name,
          assetNumber:
            manageMilestoneDialogProps.apiName === 'Asset'
              ? manageMilestoneDialogProps.object.assetNumber
              : manageMilestoneDialogProps.object.entitlementNumber,
          changeType: 'UpdateMilestone',
          // @ts-ignore
          objectType: manageMilestoneDialogProps.apiName,
          label: manageMilestoneDialogProps.label,
          priceBookId: manageMilestoneDialogProps.object.priceBook.id,
          subscriptionStartDate: changeDate,
          milestoneIds: [row.id!],
          name: row.milestones || '',
        },
        objects: manageMilestoneDialogProps.object,
      };
      await handleAddToCart(changeGroup, [changeItem]);
    }
  };

  const getMilestonesOrdersForObject = async (argument: any) => {
    const orderProductIds = await getOrderProductIdsByAssetId({
      assetId: argument.object.id,
    });
    if (orderProductIds && orderProductIds.length > 0) {
      const orderProductFilter: RubyFilter = {
        isApplied: true,
        conditions: [
          {
            apiName: 'id',
            name: 'Id',
            operand: ' _and ',
            operator: ' _in ',
            type: 'Picklist',
            value: JSON.stringify(orderProductIds),
          },
        ],
        name: 'Order Product Filter',
        isExclusive: true,
        id: 'order-product-filter',
      };

      const query = GraphqlQueryConstructor.construct().query(
        orderItemMetadata,
        orderProductFilter.conditions || [],
      );
      let orders = await executeQuery(query, orderItemMetadata, [orderProductFilter]);
      if (orders && orders.length > 0) {
        const deletedMilestones = orders.filter(
          (o) => o.changeType === 'DeleteMilestone' && o.changeReferenceId,
        );
        if (deletedMilestones && deletedMilestones.length > 0) {
          deletedMilestones.forEach((d) => {
            const originalMilestoneIndex = orders.findIndex((o) => o.id === d.changeReferenceId);
            if (originalMilestoneIndex !== -1) {
              orders.splice(originalMilestoneIndex, 1);
            }
          });
        }
        orders = orders.filter(
          (o) => o.lineType === 'RampItem' && o.milestones && o.changeType !== 'DeleteMilestone',
        );
      }

      setManageMilestoneDialogProps({
        orders: orders,
        invoiceTabIndex: getTabIndex('Invoices'),
        setActiveTabDetails: handleUpdateModeActions,
        metadata: argument.objectMetadata,
        open: true,
        object: argument.object,
        action: argument.action,
        label: argument.objectMetadata.label,
        apiName: argument.objectMetadata.apiName,
        setManageMilestoneDialogProps: setManageMilestoneDialogProps,
        onSaveAndClose: onSaveAndClose,
        AddUpdateMilestoneToCart: AddUpdateMilestoneToCart,
      });
    }
  };

  const shouldDisableRowSelection = (row: any) => {
    if (tabFilters[activeTabDetail.tabIndex]?.name === 'Subscriptions') {
      return row.status === 'ScheduledForCancel' || row.status === 'Cancelled';
    }
    return false;
  };

  const getLineTaxBreakdown = async (lineId: string) => {
    if (getTaxBreakdown) {
      const lineTaxBreakdown = await getTaxBreakdown(lineId);
      setTaxBreakdown(lineTaxBreakdown);
      if (lineTaxBreakdown) {
        setShowTaxBreakdown(true);
      } else {
        snackbarService.showSnackbar(
          'info',
          'Info',
          `There is no tax breakdown available for this ${
            tabFilters[activeTabDetail.tabIndex].name === 'Invoices' ? 'invoice' : 'credit memo'
          }.`,
        );
      }
    }
  };

  return (
    <RubyListViewLocalStateContext.Provider value={listViewLocalStateProvider}>
      <GridAndListView
        scrollButtons="auto"
        getInvoiceIdsForAsset={getInvoiceIdsForAsset}
        onDeleteSelectedSubscription={(_key: string) => {
          const filters = [...selectedSubscriptions].filter(
            (x: SelectedSubscription) => x.key !== _key,
          );
          setSelectedSubscriptions(filters);
        }}
        shouldDisableRowSelection={shouldDisableRowSelection}
        rubySettings={rubySettings}
        selectedCreditPoolId={selectedCreditPoolId}
        customer={customer}
        orderMetadata={orderMetadata}
        entitlementMetadata={entitlementMetadata}
        selectedSubscriptions={selectedSubscriptions}
        executeGetCountQuery={executeGetCountQuery}
        RightOfTitleSlot={tabFilters[activeTabDetail.tabIndex].renderExtraHeader?.(objects)}
        tabFilters={tabFilters}
        executeQuery={async (...queryProps) => {
          setActiveFilters(queryProps?.[2]);
          const data =
            (await tabFilters[activeTabDetail.tabIndex]?.executeQuery?.(...queryProps)) ||
            (await executeQuery?.(...queryProps));

          const ids = data?.map((item) => item.nueId || item.Ruby__NueId__c || item.id) || [];
          const selectedObjectName = selectedObjectMetadata?.name.replace(/ /g, '');
          if (
            ['Account', 'Order', 'Invoice', 'CreditMemo'].includes(selectedObjectName) &&
            ids.length > 0 &&
            showTransactionHub(integrations)
          ) {
            const _transactions = await loadTransactions(
              ids,
              selectedObjectName,
              transactionHubMetadata,
              executeQuery,
            );
            if (_transactions && _transactions.length > 0) {
              _transactions.forEach((o: TransactionDetail, i: number) => {
                const queriedObject = data?.find((s) =>
                  [s.nueId, s.Ruby__NueId__c, s.id].includes(o.nueId),
                );
                if (!queriedObject.transactionHub) {
                  queriedObject.transactionHub = [];
                }
                queriedObject.transactionHub.push(o);
              });
            }
          }

          return data;
        }}
        objectMetadata={selectedObjectMetadata}
        handleUpdateObjects={handleUpdateObjects}
        activeTabIndex={activeTabDetail.tabIndex}
        activeTabDetail={activeTabDetail}
        handleUpdateActiveTabIndex={(newTabIndex: number) => {
          setSelectedObjectMetadata(undefined);
          setActiveTabDetails({
            ...activeTabDetail,
            tabIndex: newTabIndex,
          });
        }}
        fieldSetMetadata={fieldSetMetadata}
        listViewMetadata={listViewMetadata || []}
        searchConfigs={searchConfigs}
        objects={objects}
        personPlaceholderImage={personPlaceholderImage}
        currencyIsoCode={currencyIsoCode}
        locale={locale}
        loadChildernAndBillingAccounts={loadChildernAndBillingAccounts}
        loadChildernAndBillingAccountsByIds={onLoadChildernAndBillingAccountsByIds}
        GridViewItem={tabFilters[activeTabDetail.tabIndex].GridViewItem}
        gridViewMetadata={gridViewMetadata}
        orderFieldSetMetadata={orderFieldSetMetadata}
        subscriptionPricingFieldSet={subscriptionPricingFieldSet}
        orderGridViewMetadata={orderGridViewMetadata}
        subscriptionMetadata={subscriptionMetadata}
        assetMetadata={assetMetadata}
        disableSelectAll={
          tabFilters[activeTabDetail.tabIndex]?.name === 'Subscriptions' && objects?.length
            ? objects.find((x) => shouldDisableRowSelection(x))
            : false
        }
        subscriptionDefaultFilter={{
          id: `objectFilter-3`,
          name: `Object Filter`,
          isApplied: true,
          conditions: [
            {
              apiName: 'customerId',
              name: 'Customer Id',
              operand: ' _and ',
              operator: ' _in ',
              type: 'Picklist',
              value: JSON.stringify(customerIds),
              nestedConditions: [
                {
                  apiName: 'billingAccountId',
                  name: 'BillingAccount',
                  operand: ' _or ',
                  operator: ' _eq ',
                  type: 'Picklist',
                  value: `"${customer.id}"`,
                },
              ],
            },
          ],
        }}
        refreshDataFlag={refreshDataFlag}
        actionEventHandler={async (argument) => {
          if (
            argument.objectMetadata.apiName === 'Subscription__c' &&
            handleUpdateModeActions(argument, 'Subscription')
          ) {
            return;
          }
          if (
            argument.objectMetadata.apiName === 'Order' &&
            handleUpdateModeActions(argument, 'Order')
          ) {
            return;
          }

          if (
            argument.objectMetadata.apiName === 'Entitlement__c' ||
            argument.objectMetadata.apiName === 'Asset'
          ) {
            if (argument.action.id === 'updateQuantity') {
              setUpdateDialogProps({
                open: true,
                quantityDimension: argument.object.uom.quantityDimension,
                object: argument.object,
                action: argument.action,
                apiName: argument.objectMetadata.apiName,
                label: argument.objectMetadata.label,
              });
              return;
            }
            if (argument.action.id === 'changeLogs') {
              setSelectedObject(argument.object);
              return;
            }
            if (argument.action.id === 'manageMilestones') {
              await getMilestonesOrdersForObject(argument);
              return;
            }
          }

          if (
            argument.objectMetadata.apiName === 'Entitlement__c' &&
            handleUpdateModeActions(argument, 'Entitlement')
          ) {
            return;
          }

          if (
            argument.objectMetadata.apiName === 'Asset' &&
            handleUpdateModeActions(argument, 'Asset')
          ) {
            return;
          }

          if (customerViewService?.actionEventHandler) {
            const result = await customerViewService.actionEventHandler(argument);
            const { succeeded, nextAction, message, orderNumber } = result;
            if (message) {
              snackbarService.showSnackbar(message.variant, message.title, message.detail);
            }
            if (succeeded) {
              if (
                argument.objectMetadata.apiName === 'Subscription__c' &&
                argument.action.id === 'auto-renew'
              ) {
                handleUpdateAutoRenew(argument.object);
              }

              switch (nextAction) {
                case 'refresh':
                  setRefreshDataFlag(refreshDataFlag + 1);
                  return;
                case 'navigate-tab':
                  return;
                case 'refresh-current':
                case 'none':
                default:
                  return;
              }
            }
          }
        }}
        getFieldSetMetadata={getFieldSetMetadata}
        handleCardClick={handleCardClick}
        selectedObject={selectedObject}
        setSelectedObject={setSelectedObject}
        currentAccountIdANdChildrenAccountIds={currentAccountIdANdChildrenAccountIds}
        getSwapUpgradeDowngradeOptions={getSwapUpgradeDowngradeOptions}
      />
      <ChangeCart
        customerId={customer.id}
        childrenCustomerIds={childrenAccountIds}
        handleOpenChangeCart={handleOpenChangeCart}
        orderMetadata={orderMetadata}
        quoteMetadata={quoteMetadata}
        opportunityMetadata={opportunityMetadata}
        executeQuery={executeQuery}
        handleRemoveChangeItems={handleRemoveChangeItems}
        navigateToQuoteBuilder={navigateToQuoteBuilder}
        open={openChangeCart}
        changeGroups={changeGroups}
        handleRemoveChangesFromCart={handleRemoveChangesFromCart}
        handleRemoveMultipleGroupsFromCart={handleRemoveMultipleGroupsFromCart}
        rubySettings={rubySettings}
        checkOut={checkOut}
        handleNavigateToOrder={() => {
          setActiveTabDetails({
            ...activeTabDetail,
            tabIndex: getTabIndex('Orders'),
          });
          updateBreadCrumb([]);
          setCustomerViewPageMode(undefined);
          handleOpenChangeCart(false);
        }}
      />
      <ActionDialog customerViewService={customerViewService} rubySettings={rubySettings} />
      <InvoiceCommentPopupContent
        commentsBtnId={commentDetails?.domID}
        open={commentDetails?.show}
        handleCommentsClose={() => {
          setCommentDetails(null);
        }}
        invoice={commentDetails?.invoice}
      />

      {openChangeModal && changeModalProps && (
        <SubscriptionActionDialog
          openSubscriptionActionDialog={openChangeModal}
          handleSetOpenSubscriptionActionDialog={(newOpenChangeModal: boolean) =>
            setOpenChangeModal(newOpenChangeModal)
          }
          changeModalProps={changeModalProps}
          handleSetChangeModalProps={setChangeModalProps}
          handleAddToCart={handleAddToCart}
          cotermDate={cotermDate}
          handleSetCotermDate={setCotermDate}
          rubySettings={rubySettings}
          handleErrorValidation={(changeItemRequestChanges: Record<string, any>) => {
            return undefined;
          }}
          handleBulkActions={handleBulkActions}
          submitButtonText="Add to Cart"
        />
      )}
      {updateDialogProps && (
        <UpdateQuantityActionDialog
          apiName={updateDialogProps.apiName || ''}
          object={updateDialogProps.object}
          defaultValues={{
            action: 'add',
            startDate: dayjs(),
          }}
          open={updateDialogProps.open}
          quantityDimension={updateDialogProps.quantityDimension || ''}
          onClose={() => {
            setUpdateDialogProps({
              open: false,
            });
          }}
          onFormSubmit={(values) => {
            const changeGroup: ChangeGroup = {
              id: `${updateDialogProps.object.customerId}-${updateDialogProps.object.priceBook.id}-${updateDialogProps.object.currencyIsoCode}`,
              customer: {
                name: updateDialogProps.object.customer.name,
                id: updateDialogProps.object.customer.id,
              },
              name: updateDialogProps.object.name,
              priceBook: {
                name: updateDialogProps.object.priceBook.name,
                id: updateDialogProps.object.priceBook.id,
              },
              currencyIsoCode: updateDialogProps.object.currencyIsoCode,
            };
            const time = dayjs().format('MM/DD/YYYY hh:mm A');
            const changeDate = dayjs(values.startDate).format('YYYY-MM-DD');
            //TODO fix customerId
            const changeItem: ChangeItem = {
              id: `${updateDialogProps.object.id}-UpdateQuantity-${changeDate}`,
              time,
              // @ts-ignore
              asset: {
                name: updateDialogProps.object.name,
                subscriptionStartDate: updateDialogProps.object.startDate,
                uom: updateDialogProps.object.uom,
                quantity: updateDialogProps.object.quantity,
                customerId: updateDialogProps.object.customerId,
                // @ts-ignore
                customer: {
                  name: updateDialogProps.object.customer.name,
                },
                billingAccountId: updateDialogProps.object.billingAccountId,
                lifecycleEndDate: updateDialogProps.object.lifecycleEndDate,
                lifecycleStartDate: updateDialogProps.object.lifecycleStartDate,
              },
              cardAction: updateDialogProps.action,
              subscriptionMetadata,
              requestWasEdited: false,
              parentSubscription: null,
              request: {
                assetName: updateDialogProps.object.name,
                assetNumber:
                  updateDialogProps.apiName === 'Asset'
                    ? updateDialogProps.object.assetNumber
                    : updateDialogProps.object.entitlementNumber,
                changeType: 'UpdateQuantity',
                // @ts-ignore
                objectType: updateDialogProps.apiName,
                label: updateDialogProps.label,
                priceBookId: updateDialogProps.object.priceBook.id,
                quantity: values.action === 'add' ? values.quantity : values.quantity * -1,
                subscriptionStartDate: changeDate,
                startDate: changeDate,
              },
              objects: updateDialogProps.object,
            };
            handleAddToCart(changeGroup, [changeItem]);
            setUpdateDialogProps({
              open: false,
            });
          }}
          submitButtonText="Add to Cart"
        />
      )}

      {manageMilestoneDialogProps && manageMilestoneDialogProps.open && (
        <ManageMilestoneDialog
          object={manageMilestoneDialogProps.object}
          open={manageMilestoneDialogProps.open}
          orders={manageMilestoneDialogProps.orders}
          onClose={() => {
            setManageMilestoneDialogProps({
              open: false,
            });
          }}
          invoiceTabIndex={manageMilestoneDialogProps.invoiceTabIndex}
          metadata={manageMilestoneDialogProps.metadata}
          setActiveTabDetails={manageMilestoneDialogProps.setActiveTabDetails}
          setManageMilestoneDialogProps={manageMilestoneDialogProps.setManageMilestoneDialogProps}
          getInvoicesByOrderItemIds={getInvoicesByOrderItemIds}
          UpdateMileStonesStartDate={UpdateMileStonesStartDate}
          onSaveAndClose={onSaveAndClose}
          AddUpdateMilestoneToCart={AddUpdateMilestoneToCart}
        />
      )}
      <PreviewInvoiceDialog
        open={showPreviewInvoiceDialog}
        onClose={() => {
          setShowPreviewInvoiceDialog(false);
        }}
        onSubmit={(newValues: any) => {
          handlePreviewDialogClicked(newValues);
          setShowPreviewInvoiceDialog(false);
        }}
      />
      <PreviewInvoiceGridDialog
        open={previewInvoiceGridData?.open}
        values={previewInvoiceGridData.values}
        loadInvoicesPreviewData={loadInvoicesPreviewData}
        currencyCode={currencyIsoCode}
        onClose={() => {
          setPreviewInvoiceGridData({
            open: false,
            values: {},
          });
        }}
        handleSaveInvoicePreview={async (data) => {
          if (handleSaveInvoicePreview) {
            await handleSaveInvoicePreview(data);
            await getInvoicePeriods();
            setRefreshDataFlag(refreshDataFlag + 1);
          }
        }}
      />

      <CashOutDialog
        selectedCreditPoolId={selectedCreditPoolId}
        currencyIsoCode={currencyIsoCode}
        creditsStatistics={creditsStatistics}
        locale={locale}
        open={showCashOutDialog}
        formatNumberForDisplay={formatNumberForDisplay}
        getErrMsg={getErrMsg}
        creditCashOut={creditCashOut}
        onClose={async (isComplete: boolean) => {
          if (isComplete) {
            setShowCashOutDialog(false);
            const res = await getPanelStatistics({
              AccountCreditPoolId: selectedCreditPoolId,
            });
            if (customer) {
              const result = await loadCreditStatistics(customer.id);
              setCreditsStatistics(result);
            }
            setRefreshDataFlag(refreshDataFlag + 1);
          } else {
            setShowCashOutDialog(false);
          }
        }}
      />

      {(rubySettings?.showInvoiceOnCustomerLifecycle ||
        rubySettings?.showCreditMemoOnCustomerLifecycle) && (
        <div>
          <ActivateInvoiceDialog
            dialogAction={dialogAction}
            open={showDialogForActivating}
            customerViewService={customerViewService}
            selectedRowData={selectedRowData}
            selectedRowType={selectedRowType}
            invoiceMetadata={invoiceMetadata}
            creditMemosMetaData={creditMemosMetaData}
            getErrMsg={getErrMsg}
            onClose={(isComplete: boolean) => {
              if (isComplete) {
                setShowDialogForActivating(false);
                setRefreshDataFlag(refreshDataFlag + 1);
              } else {
                setShowDialogForActivating(false);
              }
            }}
          />
          <CancelInvoiceDialog
            dialogAction={dialogAction}
            open={showDialogForCanceling}
            customerViewService={customerViewService}
            selectedRowData={selectedRowData}
            selectedRowType={selectedRowType}
            invoiceMetadata={invoiceMetadata}
            creditMemosMetaData={creditMemosMetaData}
            getErrMsg={getErrMsg}
            onClose={(isComplete: boolean) => {
              if (isComplete) {
                setShowDialogForCanceling(false);
                setRefreshDataFlag(refreshDataFlag + 1);
              } else {
                setShowDialogForCanceling(false);
              }
            }}
          />
          <DialogComponent
            title={`Delete ${dialogAction}`}
            open={showDialogForDeleting}
            submitButtonText="Yes"
            cancelButtonText="No"
            width={'sm'}
            handleSubmit={async () => {
              try {
                const deleteResult = await customerViewService.actionEventHandler({
                  object: selectedRowData,
                  objectMetadata:
                    selectedRowType === 'Invoice' ? invoiceMetadata : creditMemosMetaData,
                  action: {
                    id: 'delete',
                    name: 'Delete Invoice',
                    description: `Delete Selected ${dialogAction}`,
                  },
                });
                if (!deleteResult.succeeded) {
                  snackbarService.showSnackbar(
                    'error',
                    'Error',
                    deleteResult?.message?.title || '',
                  );
                }
              } catch (e) {
                snackbarService.showSnackbar(
                  'error',
                  'Error',
                  getErrMsg('delete', selectedRowData.name, e),
                );
              }
              setShowDialogForDeleting(false);
              setRefreshDataFlag(refreshDataFlag + 1);
            }}
            handleClose={() => {
              setShowDialogForDeleting(false);
            }}
          >
            <p>
              Do you want to delete {dialogAction} {selectedRowData ? selectedRowData.name : '---'}{' '}
              ?
            </p>
          </DialogComponent>
        </div>
      )}
      {changeModalProps && changeModalProps.cardAction.id === 'bulkRenew' && (
        <BulkRenewDialog
          open={true}
          onClose={() => {
            setChangeModalProps(null);
          }}
          subscriptions={changeModalProps.subscriptions}
          onSubmit={(values) => {
            handleBulkActions(values);
          }}
          submitButtonText="Add to Cart"
        />
      )}
      {changeModalProps && changeModalProps.cardAction.id === 'bulkCancel' && (
        <SubscriptionBulkCancelForm
          open={true}
          onClose={() => {
            setChangeModalProps(null);
          }}
          subscriptions={changeModalProps.subscriptions}
          onSubmit={(values) => {
            handleBulkActions(values);
          }}
          submitButtonText="Add to Cart"
        />
      )}
      {changeModalProps && changeModalProps.cardAction.id === 'adjustPrice' && (
        <AdjustPriceDialog
          open={true}
          defaultValues={changeModalProps.defaultValues}
          onClose={() => {
            setChangeModalProps(null);
          }}
          subscription={changeModalProps.subscriptions[0]}
          onSubmit={(values) => {
            handleBulkActions(values);
          }}
          submitButtonText="Add to Cart"
        />
      )}
      {changeModalProps && changeModalProps.cardAction.id === 'bulkUpgradeDowngradeSwap' && (
        <SubscriptionBulkActions
          open={true}
          handleClose={() => {
            setChangeModalProps(null);
          }}
          subscriptions={changeModalProps.subscriptions}
          subscriptionMetadata={subscriptionMetadata}
          rubySettings={rubySettings}
          onSubmit={(items) => {
            handleAddMultipleGroupsToCart(items);
          }}
          setChangeModalProps={setChangeModalProps}
          submitButtonText="Add to Cart"
          getSwapUpgradeDowngradeOptions={getSwapUpgradeDowngradeOptions}
        />
      )}
      <TaxBreakdownModal
        taxLines={toTaxLines(taxBreakdown)}
        showTaxBreakdownModal={showTaxBreakdown}
        currencyIsoCode={defaultCurrency}
        locale={locale}
        handleTaxBreakdownModalClose={() => {
          setShowTaxBreakdown(false);
          setTaxBreakdown(null);
        }}
      />
    </RubyListViewLocalStateContext.Provider>
  );
};

export default CustomerView;

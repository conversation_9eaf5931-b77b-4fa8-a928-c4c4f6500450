import React from 'react';

import { CardAction, RubyObject } from '../metadata';
import { Props as GridSearchConfigs } from '../grid-search-bar';
import { CustomerHomeProps as CustomerHomeConfigs } from '../customer-home';

export interface CustomerHomeService {
  getCustomerHomeConfig: () => {
    searchConfigs: Partial<GridSearchConfigs>;
    customerHomeConfigs: CustomerHomeConfigs;
  };
  actionEventHandler: (argument: {
    object: any;
    objectMetadata: RubyObject;
    action: CardAction;
  }) => void | Promise<void>;
}

export interface CustomerFacade {
  customerHomeService?: CustomerHomeService;
}

export const CustomerContext = React.createContext<CustomerFacade>({});

export default CustomerContext;

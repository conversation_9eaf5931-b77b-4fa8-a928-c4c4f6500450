import React from 'react';

import { AccordionSummary, Typography } from '@material-ui/core';
import Accordion from '@material-ui/core/Accordion';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';

import ChangeCard from '../change-card';
import { LayersIcon } from '../icons';
import { ChangeGroup, ChangeItem } from './interface';

const useStyles = makeStyles({
  changeCardContainer: {
    paddingTop: '24px',
  },
  accordionRoot: {
    boxShadow: 'none',
    marginTop: 16,
    width: '100%',
  },
  accordionSummaryRoot: {
    display: '-webkit-inline-flex',
    padding: '0px',
    width: '100%',
    alignItems: 'center',
  },
  layersIcon: {
    marginRight: 2,
    marginTop: 2,
    color: '#6239EB',
  },
});

interface AllCustomersChangeCardsProps {
  customerChangeGroups: ChangeGroup[];
  onCheckOut: (change: ChangeGroup) => void;
  handleRemoveChangeItems: (changeGroupId: string, changeItemIds: string[]) => void;
  handleRemoveChangesFromCart: (id: string) => void;
  changeGroupStartNumber?: number;
  isCurrentUser: boolean;
  onAddToCartComplete: (changeGroup: ChangeGroup, changeItems: ChangeItem[]) => void;
}

const AllCustomersChangeCards: React.FC<AllCustomersChangeCardsProps> = ({
  onCheckOut,
  customerChangeGroups,
  changeGroupStartNumber = 1,
  handleRemoveChangeItems,
  handleRemoveChangesFromCart,
  isCurrentUser,
  onAddToCartComplete,
}: AllCustomersChangeCardsProps) => {
  const classes = useStyles();

  const [open, setOpen] = React.useState(false);

  if (!customerChangeGroups || !customerChangeGroups.length) {
    return null;
  }

  if (isCurrentUser) {
    return (
      <>
        {customerChangeGroups.map((change: ChangeGroup, index: number) => {
          return (
            <Grid item xs={12} className={classes.changeCardContainer} key={change.id}>
              <ChangeCard
                onAddToCartComplete={onAddToCartComplete}
                changeGroupNumber={index + changeGroupStartNumber}
                changeGroup={change}
                handleCheckout={() => onCheckOut(change)}
                handleRemoveChangeItems={handleRemoveChangeItems}
                handleRemoveChangesFromCart={handleRemoveChangesFromCart}
              />
            </Grid>
          );
        })}
      </>
    );
  }

  return (
    <div>
      <Accordion
        expanded={open}
        onChange={(_: React.ChangeEvent<{}>, expanded: boolean) => setOpen(expanded)}
        className={classes.accordionRoot}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />} className={classes.accordionSummaryRoot}>
          <LayersIcon className={classes.layersIcon} />
          <Typography>Change Sets for Other Customers</Typography>
        </AccordionSummary>
        {customerChangeGroups.map((change: ChangeGroup, index: number) => {
          return (
            <Grid item xs={12} className={classes.changeCardContainer} key={change.id}>
              <ChangeCard
                onAddToCartComplete={onAddToCartComplete}
                changeGroupNumber={index + changeGroupStartNumber}
                changeGroup={change}
                handleCheckout={() => onCheckOut(change)}
                handleRemoveChangeItems={handleRemoveChangeItems}
                handleRemoveChangesFromCart={handleRemoveChangesFromCart}
              />
            </Grid>
          );
        })}
      </Accordion>
    </div>
  );
};

export default AllCustomersChangeCards;

//print.js
// print specific DOM in frontend
const printHtml = (element: HTMLElement) => {
  const body = document.body;

  // Store current children of the body in an array
  const originalChildren: Node[] = [...body.children];

  // Clone the element to be printed
  const elementClone = element.cloneNode(true) as HTMLElement;

  // Remove all current children from the body
  while (body.firstChild) {
    body.firstChild.remove();
  }

  // Add the cloned element to be printed
  body.append(elementClone);

  // Call window.print and revert the state back after printing
  window.print();

  // Revert the state back
  while (body.firstChild) {
    (body.firstChild as HTMLElement).remove();
  }

  for (const child of originalChildren) body.append(child);
};

export default printHtml;

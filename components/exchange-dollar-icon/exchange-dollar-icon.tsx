import React from 'react';
import { Props } from './interface';
import { SvgIcon } from '@material-ui/core';

const defaultProps = {};

const ExchangeDollarIcon: React.FC<Props> = ({ width, height, viewBox, className, style }) => {
  return (
    <SvgIcon viewBox={viewBox} className={className} style={style}>
      <svg
        width={width || '25px'}
        height={height || '25px'}
        viewBox="0 -.5 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>0D210987-C2ED-4FB4-92C5-FFF6BE12A344</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-5-2-Create-Quote" transform="translate(-1014.000000, -797.000000)">
            <g id="Product-Options" transform="translate(361.000000, 283.000000)">
              <g id="Profile" transform="translate(633.000000, 165.000000)">
                <g id="exchange-dollar-line" transform="translate(20.000000, 349.000000)">
                  <polygon id="Path" points="0 0 24 0 24 24 0 24" />
                  <path
                    d="M12.9166667,10.0686667 C13.8036129,7.96125987 13.2487731,5.52296194 11.5372841,4.00683456 C9.82579499,2.49070717 7.33837634,2.23401193 5.35333333,3.36866667 L4.692,2.21066667 C6.75092657,1.03391182 9.2801006,1.04000499 11.3333333,2.22666667 C14.3266667,3.95466667 15.4733333,7.65466667 14.078,10.74 L14.9726667,11.256 L12.196,12.732 L12.086,9.58933333 L12.9166667,10.0686667 Z M3.08333333,5.93133333 C2.19638706,8.03874013 2.75122686,10.4770381 4.46271594,11.9931654 C6.17420501,13.5092928 8.66162366,13.7659881 10.6466667,12.6313333 L11.308,13.7893333 C9.24907343,14.9660882 6.7198994,14.959995 4.66666667,13.7733333 C1.67333333,12.0453333 0.526666667,8.34533333 1.922,5.26 L1.02666667,4.74466667 L3.80333333,3.26866667 L3.91333333,6.41133333 L3.08266667,5.932 L3.08333333,5.93133333 Z M5.66666667,9.33333333 L9.33333333,9.33333333 C9.51742825,9.33333333 9.66666667,9.18409492 9.66666667,9 C9.66666667,8.81590508 9.51742825,8.66666667 9.33333333,8.66666667 L6.66666667,8.66666667 C5.74619208,8.66666667 5,7.92047458 5,7 C5,6.07952542 5.74619208,5.33333333 6.66666667,5.33333333 L7.33333333,5.33333333 L7.33333333,4.66666667 L8.66666667,4.66666667 L8.66666667,5.33333333 L10.3333333,5.33333333 L10.3333333,6.66666667 L6.66666667,6.66666667 C6.48257175,6.66666667 6.33333333,6.81590508 6.33333333,7 C6.33333333,7.18409492 6.48257175,7.33333333 6.66666667,7.33333333 L9.33333333,7.33333333 C10.2538079,7.33333333 11,8.07952542 11,9 C11,9.92047458 10.2538079,10.6666667 9.33333333,10.6666667 L8.66666667,10.6666667 L8.66666667,11.3333333 L7.33333333,11.3333333 L7.33333333,10.6666667 L5.66666667,10.6666667 L5.66666667,9.33333333 Z"
                    id="Shape"
                    fill="#6239EB"
                    fillRule="nonzero"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default ExchangeDollarIcon;

import React from 'react';
import { CronDailyFormProps } from './interface';
import { Grid, MenuItem, RadioGroup, Select } from '@material-ui/core';
import FormControl from '@material-ui/core/FormControl';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Radio from '@material-ui/core/Radio';
import { makeStyles } from '@material-ui/core/styles';
import SelectInputBase from '../select-input-base';
import { HourConst, MinuteConst } from '../common/cron-const';

const useStyles = makeStyles({
  titleLabel: {
    textTransform: 'uppercase',
    color: '#9c9c9c',
    fontWeight: 'bold',
    fontSize: '.80rem',
    marginTop: '8px',
  },
  formControl: {
    flexDirection: 'row',
    width: '100%',
  },
  optionLabel: {
    '& > span': {
      color: '#6e6e6e',
      fontSize: '.9rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
  },
});

export const DailyCronExpression: React.FC<CronDailyFormProps> = (userProps) => {
  const props = { ...userProps };

  const { dailyObject, setDailyObject, disabled } = props;

  const styles = useStyles();

  let _options = ['Everyday', 'Every weekday'];

  return (
    <>
      <FormControl className={styles.formControl}>
        <Grid container item spacing={3} xs={12}>
          <Grid container item spacing={2} xs={12} style={{ marginTop: '5px' }}>
            <Grid item lg={2} xs={12} className={styles.titleLabel}>
              FREQUENCY
            </Grid>
            <Grid item lg={4} xs={12}>
              <RadioGroup
                aria-label="metadata-radio"
                row
                name={'FREQUENCY'}
                value={dailyObject.dailyFrequency}
                onChange={(event, value) => {
                  if (dailyObject.dailyExpression) {
                    if (value === 'Everyday') {
                      const copyCronExpression = dailyObject.dailyExpression;
                      copyCronExpression[3] = ' *';
                      copyCronExpression[5] = ' ?';
                      setDailyObject({
                        ...dailyObject,
                        dailyExpression: copyCronExpression,
                      });
                    } else {
                      const copyCronExpression = dailyObject.dailyExpression;
                      copyCronExpression[3] = ' ?';
                      copyCronExpression[5] = ' MON-FRI';
                      setDailyObject({
                        ...dailyObject,
                        dailyExpression: copyCronExpression,
                      });
                    }
                  }
                  setDailyObject({
                    ...dailyObject,
                    dailyFrequency: value,
                  });
                }}
              >
                {_options.map((option) => {
                  return (
                    <FormControlLabel
                      key={option}
                      value={option}
                      control={<Radio size="small" />}
                      label={option}
                      disabled={disabled}
                      className={styles.optionLabel}
                    />
                  );
                })}
              </RadioGroup>
            </Grid>
          </Grid>
          <Grid container item spacing={2} xs={12}>
            <Grid item lg={2} xs={12} className={styles.titleLabel}>
              RUN AT
            </Grid>
            <Grid container item spacing={3} xs={8}>
              <Grid item lg={2} xs={8} style={{ maxWidth: '6%' }}>
                <Select
                  value={dailyObject.dailyRunAt === undefined ? '' : dailyObject.dailyRunAt.hour}
                  displayEmpty
                  name={'field'}
                  placeholder={'Field'}
                  onChange={(event) => {
                    const value = event.target.value;
                    if (typeof value === 'string') {
                      dailyObject.dailyRunAt.hour = value;
                    }
                    setDailyObject({
                      ...dailyObject,
                      dailyRunAt: { hour: value, minute: dailyObject.dailyRunAt.minute },
                    });
                    if (dailyObject.dailyExpression) {
                      const copyCronExpression = dailyObject.dailyExpression;
                      copyCronExpression[2] = ' ' + value;
                      if (typeof copyCronExpression[1] === 'string') {
                        if (copyCronExpression[2].substring(1, 2) === '0') {
                          copyCronExpression[2] = copyCronExpression[2].replace('0', '');
                        }
                      }
                      setDailyObject({
                        ...dailyObject,
                        dailyExpression: copyCronExpression,
                      });
                    }
                  }}
                  fullWidth={false}
                  label={'Field'}
                  disabled={disabled}
                  input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
                >
                  {HourConst.map((field, index) => {
                    return (
                      <MenuItem key={index} value={field}>
                        {field}
                      </MenuItem>
                    );
                  })}
                </Select>
              </Grid>
              <Grid
                item
                lg={2}
                xs={12}
                style={{
                  marginTop: '8px',
                  maxWidth: '10px',
                  marginLeft: '5px',
                  marginRight: '50px',
                }}
              >
                <b style={{ marginLeft: '50px' }}>:</b>
              </Grid>
              <Grid item lg={4} xs={8} style={{ maxWidth: '10%' }}>
                <Select
                  value={dailyObject.dailyRunAt === undefined ? '' : dailyObject.dailyRunAt.minute}
                  displayEmpty
                  name={'field'}
                  placeholder={'Field'}
                  onChange={(event) => {
                    const value = event.target.value;
                    if (typeof value === 'string') {
                      dailyObject.dailyRunAt.minute = value;
                    }
                    setDailyObject({
                      ...dailyObject,
                      dailyRunAt: { hour: dailyObject.dailyRunAt.hour, minute: value },
                    });
                    if (dailyObject.dailyExpression) {
                      const copyCronExpression = dailyObject.dailyExpression;
                      copyCronExpression[1] = ' ' + value;
                      if (typeof copyCronExpression[1] === 'string') {
                        if (copyCronExpression[1].substring(1, 2) === '0') {
                          copyCronExpression[1] = copyCronExpression[1].replace('0', '');
                        }
                      }
                      setDailyObject({
                        ...dailyObject,
                        dailyExpression: copyCronExpression,
                      });
                    }
                  }}
                  fullWidth={false}
                  label={'Field'}
                  disabled={disabled}
                  input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
                >
                  {MinuteConst.map((field, index) => {
                    return (
                      <MenuItem key={index} value={field}>
                        {field}
                      </MenuItem>
                    );
                  })}
                </Select>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </FormControl>
    </>
  );
};

export default DailyCronExpression;

import React, { useContext, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import localforage from 'localforage';
import BreadcrumbContext from '../breadcrumb-context';
import { CustomerContext } from '../customer-context/customer-context';
import { RubyFilter } from '../graph-ql-query-constructor';
import GraphQLQueryConstructor from '../graph-ql-query-constructor/graph-ql-query-constructor';
import GridAndListView from '../grid-and-list-view';
import GridViewItem from '../grid-view-item';
import GridViewMetadataApi from '../grid-view-metadata-api';
import { GridViewMetadata, RubyField, ValuePair } from '../metadata';
import { TabFilter } from '../panel-with-tabs';
import { Customer } from '../revenue-builder-types/interface';
import { buildExtraRelationFields } from '../ruby-list-view';
import RubyListViewLocalStateContext, {
  ListViewLocalStateService,
} from '../ruby-list-view-local-state-context';
import { Props } from './interface';
import { CellRendererRowProps } from '../grid-actions';

const defaultProps = {};

const CustomerHome = forwardRef<{ refreshCustomerData: () => void }, Props>((userProps, ref) => {
  const props = { ...defaultProps, ...userProps };
  const { customerHomeService } = useContext(CustomerContext);
  const { breadcrumbService } = useContext(BreadcrumbContext);

  if (!breadcrumbService) {
    throw Error(
      'Customer Home component requires breadcrumbService to be declared in context provider',
    );
  }

  if (!customerHomeService) {
    throw Error(
      'CustomerHome component requires customerService to be declared in context provider',
    );
  }

  // const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [customers, setCustomers] = useState<any[] | null>(null);
  const [gridViewMetadata, setGridViewMetadata] = useState<GridViewMetadata | null>(null);
  const [fieldSetMetadata, setFieldSetMetadata] = useState<RubyField[] | null>(null);
  const [tabFilters, setTabFilters] = useState<TabFilter[]>([]);
  const { searchConfigs, customerHomeConfigs } = customerHomeService.getCustomerHomeConfig();
  const { breadcrumbs, updateBreadcrumbs } = breadcrumbService.getBreadcrumbConfig();
  const [loading, setLoading] = useState(false);
  const [refreshDataFlag, setRefreshDataFlag] = useState(0);
  useImperativeHandle(ref, () => ({
    refreshCustomerData: () => {
      setRefreshDataFlag((prevFlag) => prevFlag + 1);
    },
  }));
  const { activeTabIndex, setActiveTabIndex, idsCondition, jumpFrom } = props;

  const {
    customerMetadata,
    executeQuery,
    executeGetCountQuery,
    currencyIsoCode,
    navigateToNewCustomerViewPage,
    getFieldSetMetadata,
    getTransactionHubStatus,
    transactionHubColumn,
  } = customerHomeConfigs;

  const setupTabs = (): TabFilter[] => {
    const recordTypeField = customerMetadata.fields?.find(
      (field) => field.apiName === 'recordType',
    );
    if (!recordTypeField) {
      return [];
    }
    const tabFields = recordTypeField?.valueSet?.valuePairs;
    const initialTabFilters: TabFilter[] = [];

    if (!jumpFrom) {
      tabFields?.forEach((valuePair: ValuePair, index: number) => {
        initialTabFilters.push({
          id: index.toString(),
          objectMetadata: customerMetadata,
          name: valuePair.name,
          displayName: valuePair.name,
          gridViewMetadataName: 'customer-grid-view-metadata',
          pageKey: 'customer__home',
          GridViewItem: GridViewItem,
          fieldSetApiName: 'CustomerFields',
          filter: [
            {
              id: `objectFilter-1`,
              name: `Object Filter`,
              conditions: [
                {
                  apiName: recordTypeField.apiName,
                  name: recordTypeField.name,
                  operand: ' _and ',
                  operator: ' _eq ',
                  type: recordTypeField.type,
                  value: `"${valuePair.apiName}"`,
                },
              ],
            },
          ],
          enablePagination: true,
          remotePaging: true,
          defaultPageSize: 20,
        });
      });
    }

    let initFilter: RubyFilter | undefined;
    if (idsCondition) {
      initFilter = {
        id: `objectFilter-3`,
        name: `Object Filter`,
        conditions: [
          {
            apiName: 'id',
            name: 'Id',
            operand: ' _and ',
            operator: ' _in ',
            type: 'bId',
            value: idsCondition,
          },
        ],
      };
    } else {
      initFilter = undefined;
    }

    // add the 'All' to view all filters
    initialTabFilters.push({
      id: `${initialTabFilters.length + 1}`,
      name: 'All',
      displayName: 'All',
      gridViewMetadataName: 'customer-grid-view-metadata',
      pageKey: 'customer__home',
      objectMetadata: customerMetadata,
      GridViewItem: GridViewItem,
      fieldSetApiName: 'CustomerFields',
      enablePagination: true,
      remotePaging: true,
      defaultPageSize: 20,
      filter: initFilter ? [initFilter] : [],
    });
    return initialTabFilters;
  };

  const handleSearch = async (newTabIndex: number) => {
    const filtersCopy: RubyFilter[] = [];
    if (tabFilters[newTabIndex] && tabFilters[newTabIndex].filter) {
      const filter = tabFilters[newTabIndex].filter?.[0];
      if (filter) {
        filtersCopy.push(filter);
      }
    }

    const filterConditions = GraphQLQueryConstructor.construct().conditionsFromFilters(filtersCopy);
    const whereCondition = filtersCopy.length !== 0 ? `( where: ${filterConditions})` : '';
    const relationFields = buildExtraRelationFields([]);
    const query = GraphQLQueryConstructor.construct().queryWithWhereCondition(
      customerMetadata,
      whereCondition,
      relationFields,
    );
    const queriedObjects = await executeQuery(query, customerMetadata, filtersCopy);
    setCustomers(queriedObjects);
  };

  const handleCardClick = (object: any) => {
    // const breadcrumbsCopy = [...breadcrumbs];
    // breadcrumbsCopy.push({
    //   link: `customers/${object.id}`,
    //   label: `${object.name}`,
    // });
    // updateBreadcrumbs(breadcrumbsCopy);
    // navigateToNewCustomerViewPage(object);
    //
    // Do nothing for now!
  };

  const setup = async () => {
    const currentTabFilters = setupTabs();
    const defaultGridViewMetadata = GridViewMetadataApi.getGridViewMetadata(
      currentTabFilters[activeTabIndex].gridViewMetadataName,
    );
    setGridViewMetadata(defaultGridViewMetadata);
    const defaultFieldSetMetadata = await getFieldSetMetadata(
      currentTabFilters[activeTabIndex].fieldSetApiName,
      customerMetadata.apiName,
    );
    setFieldSetMetadata(defaultFieldSetMetadata);
    setTabFilters(currentTabFilters);
  };

  useEffect(() => {
    setup();
  }, [idsCondition]);

  useEffect(() => {
    setRefreshDataFlag(refreshDataFlag + 1);
  }, [tabFilters]);

  const handleUpdateObjects = (newCustomers: Customer[] | null, searchResult: string) =>
    setCustomers(newCustomers);

  const handleUpdateActiveTabIndex = async (newTabIndex: number) => {
    setActiveTabIndex(newTabIndex);
    setRefreshDataFlag(refreshDataFlag + 1);
  };

  if (!gridViewMetadata || !fieldSetMetadata || tabFilters.length === 0) {
    return null;
  }

  const key = `customer-home-grid-and-list-view-tab-${activeTabIndex}`;

  const listViewLocalStateProvider: ListViewLocalStateService = {
    getLocalState: async () => {
      console.debug('getLocalState: ' + key);
      return await localforage.getItem(key);
    },
    setLocalState: async (listViewLocalState) => {
      console.debug('setLocalState', listViewLocalState);
      await localforage.setItem(key, listViewLocalState);
    },
  };

  if (!tabFilters[activeTabIndex].objectMetadata) {
    return null;
  }

  return (
    <RubyListViewLocalStateContext.Provider value={listViewLocalStateProvider}>
      <GridAndListView
        listViewMetadata={fieldSetMetadata}
        currencyIsoCode={currencyIsoCode}
        tabFilters={tabFilters}
        executeQuery={executeQuery}
        executeGetCountQuery={executeGetCountQuery}
        //@ts-ignore
        objectMetadata={tabFilters[activeTabIndex].objectMetadata}
        handleUpdateObjects={handleUpdateObjects}
        activeTabIndex={activeTabIndex}
        handleUpdateActiveTabIndex={handleUpdateActiveTabIndex}
        fieldSetMetadata={fieldSetMetadata}
        searchConfigs={searchConfigs}
        objects={customers}
        refreshDataFlag={refreshDataFlag}
        gridViewMetadata={gridViewMetadata}
        GridViewItem={GridViewItem}
        handleCardClick={handleCardClick}
        actionEventHandler={async (argument) => {
          if (customerHomeService?.actionEventHandler) {
            await customerHomeService.actionEventHandler(argument);
          }
        }}
        loading={loading}
        getTransactionHubStatus={getTransactionHubStatus}
        transactionHubColumn={transactionHubColumn}
      />
    </RubyListViewLocalStateContext.Provider>
  );
});

export default CustomerHome;

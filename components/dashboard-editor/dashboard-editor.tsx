import React, { useEffect } from 'react';
import RGL, { Layout, WidthProvider } from 'react-grid-layout';

import '!style-loader!css-loader!react-grid-layout/css/styles.css';
import { Button, Grid, IconButton, Paper, Tooltip, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import {
  Close as CloseIcon,
  Help as HelpIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  MoreVert as MoreVertIcon,
  Replay as ReplayIcon,
  Save as SaveIcon,
} from '@material-ui/icons';

import SettingsIcon from '../settings-icon';
import { decodeDragDataTransfer, encodeDragDataTransfer } from '../util/encodeDragDataTransfor';
import { DashboardEditorProps } from './interface';

const ReactGridLayout = WidthProvider(RGL);

const useStyles = makeStyles({
  layoutWrapper: {
    position: 'relative',
    display: 'flex',
    flexWrap: 'wrap',
    '& .react-grid-item.react-grid-placeholder': {
      backgroundColor: '#A52FF5',
    },
    marginTop: '60px',
  },
  setting: {
    display: 'flex',
    width: '100%',
    justifyContent: 'flex-end',
    marginRight: '60px',
  },
  settingBtn: {
    transform: 'none',
    opacity: 0.5,
    padding: '10px 20px',
    borderRadius: '10px',
  },
  btnBorder: {
    border: '1px solid rgba(0, 0, 0, 0.5)',
  },
  btnNoBorder: {
    border: '1px solid transparent',
  },
  showSetting: {
    display: 'flex',
  },
  libraryTitle: {
    border: 'none',
    display: 'flex',
    flexDirection: 'row-reverse',
    width: '100%',
  },
  library: {
    boxSizing: 'border-box',
    padding: '16px',
    height: '100%',
    width: '400px',
    overflowY: 'scroll',
    boxShadow: '-5px 5px 10px 5px rgba(0,0,0,0.2)',
  },
  libraryWrapper: {
    position: 'absolute',
    top: '-70px',
    paddingTop: '10px',
    right: '0',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
    zIndex: 1,
  },
  libraryWrapperOpen: {
    bottom: '0',
    background: 'radial-gradient(circle, #bbb, white)',
  },
  saveNReset: {
    display: 'flex',
    flexGrow: 1,
    borderBottomRightRadius: '10px',
    justifyContent: 'flex-end',
  },
  helpIcon: {
    fontSize: '16px',
    marginLeft: '16px',
  },
  widgetWrapper: {
    display: 'flex',
    height: '100%',
    position: 'relative',
  },
  closeIcon: {
    right: '5px',
    top: '5px',
    zIndex: 1,
    position: 'absolute',
    color: '#999999',
    cursor: 'pointer',
  },
  widget: {
    padding: '16px',
    borderRadius: '4px',
    border: '1px solid #E0E0E0',
    '&:hover': {
      padding: '15px',
      border: '2px solid #A52FF5',
    },
    marginTop: '16px',
    marginBottom: '16px',
  },
  widgetTitle: {
    fontSize: '18px',
    fontWeight: 'bold',
  },
  widgetInfo: {
    marginTop: '8px',
    fontSize: '14px',
    lineHeight: '18px',
  },
  moveIcon: {
    marginLeft: '-8px',
  },
  previewNodeWrapper: {
    width: '50%',
    '& *': {
      boxSizing: 'border-box',
    },
  },
  previewInfoWrapper: {
    width: '50%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    marginLeft: '16px',
  },
});

export const DashboardEditor: React.FC<DashboardEditorProps> = (props: DashboardEditorProps) => {
  const classes = useStyles();
  const DROP_SIZE_PREFIX = 'drop-size-';
  const { widgets, defaultLayout, onEditMode, onReadMode, isInList, objectName, hiddenSetting } =
    props;
  const objectMap = new Map<string, string>([['usage', '_usage_dashboard_layout']]);

  const getSavedLayout = () => {
    let layout;
    if (objectName) {
      const dashboard = objectMap.get(objectName);
      if (dashboard) {
        layout = localStorage.getItem(dashboard);
      }
    } else {
      layout = localStorage.getItem('_dashboard_layout');
    }
    if (layout) {
      return JSON.parse(layout);
    }
    return defaultLayout || [];
  };
  const [savedLayout, setSavedLayout] = React.useState<Array<Layout>>(getSavedLayout());
  const [layout, setLayout] = React.useState<Array<Layout>>(savedLayout);
  const [editing, setEditing] = React.useState<boolean>(false);
  const [showLibrary, setShowLibrary] = React.useState<boolean>(false);

  useEffect(() => {
    if (objectName) {
      const dashboard = objectMap.get(objectName);
      if (dashboard) {
        localStorage.setItem(dashboard, JSON.stringify(layout));
      }
    } else {
      localStorage.setItem('_dashboard_layout', JSON.stringify(layout));
    }
  }, [savedLayout]);

  interface WithDragProps {
    id: string;
    node: React.ReactNode;
    title: string;
    info: string;
  }

  const WithDrag: React.FC<WithDragProps> = (props) => {
    const { id, node, title, info } = props;
    return (
      <div
        className={classes.widget}
        draggable={true}
        unselectable="on"
        onDragStart={(e) => {
          e.dataTransfer.setData('moveComponent', id);
          e.dataTransfer.setData(
            encodeDragDataTransfer(
              `${DROP_SIZE_PREFIX}${JSON.stringify({
                h: widgets.get(props.id)?.h || 1,
                w: widgets.get(props.id)?.w || 2,
              })}`,
            ),
            '',
          );
          e.nativeEvent.dataTransfer;
        }}
      >
        <Grid container alignItems="center" wrap="nowrap">
          <div>
            <MoreVertIcon />
          </div>
          <Grid container item wrap="nowrap" alignContent="center">
            <div className={classes.previewNodeWrapper}>{node}</div>
            <div className={classes.previewInfoWrapper}>
              <div className={classes.widgetTitle}>{title}</div>
              <div className={classes.widgetInfo}>{info}</div>
            </div>
          </Grid>
        </Grid>
      </div>
    );
  };

  const onDrop = (_layout: Layout[], item: Layout, e: DragEvent) => {
    const widgetName = e.dataTransfer?.getData('moveComponent') || '';
    setLayout((prevLayout) => [
      ...prevLayout.filter((item) => item.i !== '__dropping-elem__'), // Prevent duplicate dropping item
      {
        ...item,
        i: widgetName,
      },
    ]);
  };

  const onDropDragOver = (e: any) => {
    // A hack to get the drop size, which is not provided by react-grid-layout
    const key = (e as React.DragEvent).dataTransfer.types
      .map(decodeDragDataTransfer)
      .find((t) => t.startsWith(DROP_SIZE_PREFIX));
    if (key) {
      const componentSize = JSON.parse(key?.replace(DROP_SIZE_PREFIX, '') || '');
      return componentSize;
    }
  };

  const onResize = (layout: Layout[]) => {
    setLayout(layout);
  };

  const onRemove = (id: string) => {
    setLayout((prevLayout) => prevLayout.filter((item) => item.i !== id));
  };

  const saveLayout = () => {
    setLayout(layout.map((l) => ({ ...l, isDraggable: false, static: true })));
    setSavedLayout(layout);
    if (objectName) {
      const dashboard = objectMap.get(objectName);
      if (dashboard) {
        localStorage.setItem(dashboard, JSON.stringify(layout));
      }
    } else {
      localStorage.setItem('_dashboard_layout', JSON.stringify(layout));
    }
    setEditing(false);
    setShowLibrary(false);
    onReadMode && onReadMode();
  };

  const editLayout = () => {
    setLayout(layout.map((l) => ({ ...l, isDraggable: true, static: false })));
    setEditing(true);
    setShowLibrary(true);
    onEditMode && onEditMode();
  };

  const resetLayout = () => {
    setLayout((defaultLayout || []).map((l) => ({ ...l, isDraggable: true, static: false })));
    setSavedLayout(defaultLayout || []);
  };

  const collapseLibrary = () => {
    setShowLibrary(!showLibrary);
  };

  return (
    <div style={{ position: 'relative' }}>
      <div
        className={classes.libraryWrapper + (showLibrary ? ' ' + classes.libraryWrapperOpen : '')}
      >
        {!hiddenSetting && (
          <div className={editing ? classes.libraryTitle : classes.showSetting}>
            <div style={{ display: 'flex', borderTopLeftRadius: '10px', backgroundColor: 'white' }}>
              <Button
                startIcon={!editing && <SettingsIcon styles={{ opacity: 1 }} />}
                endIcon={!showLibrary ? <KeyboardArrowDownIcon /> : <KeyboardArrowUpIcon />}
                onClick={editing ? collapseLibrary : editLayout}
                className={
                  classes.settingBtn + ' ' + (!editing ? classes.btnBorder : classes.btnNoBorder)
                }
              >
                {editing ? 'Insights Library ' : 'Settings'}
              </Button>
            </div>
            {editing && (
              <div className={classes.saveNReset}>
                <Tooltip arrow title="Save layout" enterDelay={300}>
                  <IconButton onClick={saveLayout}>
                    <SaveIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip arrow title="Edit layout" enterDelay={300}>
                  <IconButton onClick={resetLayout}>
                    <ReplayIcon />
                  </IconButton>
                </Tooltip>
              </div>
            )}
          </div>
        )}
        {showLibrary && (
          <Paper className={classes.library}>
            <Grid container alignItems="center">
              <Typography className="libraryTitle">Revenue Insights Library</Typography>
              <Tooltip arrow title={'Add a new widget'} className="tooltip">
                <HelpIcon className={classes.helpIcon} />
              </Tooltip>
            </Grid>
            <div className="widgetWrapper">
              {Array.from(widgets.keys())
                .filter((id) => !layout.find((item) => item.i === id))
                .map((id) => {
                  const widget = widgets.get(id);
                  return (
                    <div key={id}>
                      {widget && (
                        <WithDrag
                          id={id}
                          node={widget.preview}
                          title={widget.title}
                          info={widget.info}
                        />
                      )}
                    </div>
                  );
                })}
            </div>
          </Paper>
        )}
      </div>
      <Paper className={classes.layoutWrapper} style={isInList ? { boxShadow: 'none' } : undefined}>
        <ReactGridLayout
          style={
            isInList
              ? {
                  flex: '1 1 0%',
                  minHeight: '446px',
                }
              : {
                  flex: '1 1 0%',
                  minHeight: '500px',
                  border: `${editing ? '2px dashed #A52FF5' : '2px transparent'}`,
                }
          }
          cols={12}
          rowHeight={120}
          width={100}
          // @ts-ignore
          onDropDragOver={onDropDragOver}
          onLayoutChange={onResize}
          onDrop={onDrop}
          isDroppable={true}
          layout={layout}
        >
          {layout.map((item, i) => (
            <div key={item.i}>
              <div className={classes.widgetWrapper}>
                {editing && (
                  <CloseIcon
                    className={classes.closeIcon}
                    fontSize="small"
                    onClick={() => onRemove(item.i)}
                  />
                )}
                {widgets.get(item.i)?.node}
              </div>
            </div>
          ))}
        </ReactGridLayout>
      </Paper>
    </div>
  );
};

export default DashboardEditor;

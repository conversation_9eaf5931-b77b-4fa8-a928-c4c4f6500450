import React, { useState } from 'react';
import {
  App<PERSON>ar,
  Button,
  <PERSON>alogActions,
  Grid,
  IconButton,
  InputAdornment,
  List,
  Tab,
  Tabs,
  Theme,
  Typography,
  makeStyles,
  withStyles,
} from '@material-ui/core';
import { Tooltip } from '@material-ui/core';
import ChatOutlinedIcon from '@material-ui/icons/ChatBubbleOutline';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import {
  DialogComponent,
  GetLookupOptionByValue,
  InvoiceDownloadIcon,
  LookupOptionsLoader,
  ModalAppBar,
  RubyButtonBar,
  RubyButtonBarProps,
  RubyField,
  SearchIcon,
  TextInput,
  RoundDownloadIcon,
  EInvoicingMessage,
  Loading,
} from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import { formatNumberForDisplay } from '../../list';
import { InvoiceCalculatedStatusRenderer } from '../../ruby-list-view-renderer/invoice-calculated-status-renderer';
import { DisplayText } from '../display-text';
import { EditableDateInput } from '../editable-date-input';
import { EditableLookup } from '../editable-lookup';
import { CreditMemoFixedFieldArray } from '../fixed-field';

interface Props {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: any) => void;
  creditMemoMetadata: RubyField[];
  creditMemoData: any;
  currencyIsoCode: string;
  locale: string;
  lookupOptionsLoader?: LookupOptionsLoader;
  getLookupOptionByValue?: GetLookupOptionByValue;
  renderCustomTabPanel?: (tabKey: string) => React.ReactNode;
  transactionHub?: {
    id: string;
    Renderer: (actionHelpers: any) => React.ReactNode;
  }[];
  manageInvoiceTemplate?: boolean;
  actions?: Partial<RubyButtonBarProps>;
  onDownloadEInvoicingDocument?: (id: string) => Promise<any>;
  getTenantDetails?: () => Promise<void>;
}
const TabValue = {
  generalInformation: 'generalInformation',
  creditMemoItems: 'creditMemoItems',
  paymentApplicationsItems: 'paymentApplicationsItems',
  invoice: 'invoice',
};

const TextOnlyTooltip = withStyles({
  tooltip: {
    position: 'relative',
  },
})(Tooltip);

const TabPanel = (props: any) => {
  const { children, value, index, ...others } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...others}
    >
      {value === index && <div>{children}</div>}
    </div>
  );
};
const useStyles = makeStyles((theme: Theme) => ({
  dialogFooter: {
    padding: '8px 24px 24px',
  },
  root: {
    flexGrow: 1,
  },
  tabsRoot: {
    '& .MuiTabScrollButton-root': {
      width: 40,
    },
    zIndex: 1000,
  },
  divider: {
    opacity: 0.1,
    border: '1px solid #727171',
    zIndex: 999,
    margin: '-2px -80px 32px',
  },
  searchBar: {
    borderRightColor: 'whitesmoke',
    borderRadius: '4px 0px 0px 4px !important',
    fontSize: '.875rem',
    height: '100%',
    marginTop: '0 !important',
    backgroundColor: '#f1f0f2 !important',
    border: 'none',
    minHeight: '22px',
    paddingLeft: 0,
    paddingTop: 10,
    paddingBottom: 10,
  },
  searchBarRoot: {
    overflow: 'hidden',
    borderRadius: '4px 0 0 4px !important',
    backgroundColor: '#f1f0f2 !important',
  },
  tabsIndicator: {
    top: '0px',
    height: '3px',
    backgroundColor: '#6239EB',
    borderTopLeftRadius: '4px',
    borderTopRightRadius: '4px',
  },
  tabText: {
    color: '#979697',
  },
  tabTextWidth: {
    color: '#979697',
    width: '228px',
  },
  tabRoot: {
    backgroundColor: '#eeeef0',
    borderTopLeftRadius: '4px',
    borderTopRightRadius: '4px',
    textTransform: 'none',
    boxShadow: '0 3px 5px -2px rgba(0,0,0,0.2)',
  },
  selected: {
    backgroundColor: '#FFF',
    color: '#6239EB',
    fontWeight: 500,
  },
  wrapper: {
    fontSize: '.9rem',
  },
  appBarRoot: {
    boxShadow: 'none',
    marginTop: 65,
    backgroundColor: 'unset',
  },
  dialogTitle: {
    padding: 0,
    position: 'absolute',
    top: 0,
    width: '100%',
    left: 0,
  },
  titleLabel: {
    fontSize: 12,
    color: '#000',
    opacity: '0.4',
    paddingBottom: 14,
  },
  titleText: {
    fontSize: 16,
    fontWeight: 500,
    color: '#000',
    opacity: '0.7',
  },
  nagtiveNumber: {
    color: '#ff0000',
  },
  postiveNumber: {
    color: '#6239EB',
  },
  pdfDownloadIcon: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: 25,
  },
}));

export const ViewCreditMemoDialog = ({
  open,
  onClose,
  creditMemoMetadata,
  creditMemoData,
  currencyIsoCode,
  locale,
  lookupOptionsLoader,
  getLookupOptionByValue,
  renderCustomTabPanel,
  transactionHub,
  manageInvoiceTemplate,
  actions,
  onDownloadEInvoicingDocument,
  getTenantDetails,
}: Props) => {
  const classes = useStyles();
  const [searchText, setSearchText] = useState('');
  const [copied, setCopied] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const paymentStatus = creditMemoMetadata
    .find((x: any) => x.apiName === 'paymentStatus')
    ?.valueSet?.valuePairs.find((x: any) => x.apiName === creditMemoData?.paymentStatus)?.name;
  const [activedTab, setActivedTab] = useState('summary');
  const [selectedTab, setSelectedTab] = useState<string>(TabValue.creditMemoItems);

  const buildArray = () => {
    const result: any = [];
    CreditMemoFixedFieldArray.forEach((x) => {
      const metadata = creditMemoMetadata.find((d: any) => d.apiName === x.apiName);
      if (metadata) {
        result.push(metadata);
      }
    });
    const rest = creditMemoMetadata
      .filter((x: any) => !CreditMemoFixedFieldArray.find((d) => d.apiName === x.apiName))
      .sort((a: any, b: any) => a.name.localeCompare(b.name));
    return result.concat(rest);
  };

  const getCreditMemoStatus = () => {
    const status = creditMemoData?.status;
    const pStatus = creditMemoData?.paymentStatus?.toLowerCase();
    if (!status || !pStatus) return '';

    switch (status?.toLowerCase()) {
      case 'draft':
        return 'Draft';
      case 'pendingactivation':
        return 'Pending';
      case 'active':
        const today = dayjs();
        if (pStatus === 'writeoff') {
          return 'Uncollectible';
        }

        if (pStatus === 'partialrefunded' || pStatus === 'refunded') {
          return 'Refunded';
        }

        if (creditMemoData.balance === 0 || pStatus === 'applied') {
          return 'Closed';
        }

        if (creditMemoData.balance !== 0) {
          if (today.isAfter(creditMemoData.dueDate)) {
            return 'Overdue';
          } else {
            return 'Open';
          }
        }

        return status;
      default:
        return status;
    }
  };

  const getFormattedValue = (valueType: string, metadata: any) => {
    let value = '';
    let displayValue = '';
    if (metadata.apiName !== 'billToContactId' && metadata.lookupRelation) {
      value = creditMemoData[metadata.lookupRelation.relationName]?.name;
    } else {
      value = creditMemoData[metadata.apiName];
      displayValue =
        metadata.apiName === 'billToContactId'
          ? creditMemoData[metadata.lookupRelation.relationName]?.name
          : '';
    }

    if (metadata.apiName === 'paymentStatus') {
      return (
        <DisplayText
          apiName={metadata.apiName}
          value={metadata.valueSet.valuePairs.find((x: any) => x.apiName === value)?.name || value}
          type={'text'}
        />
      );
    }
    if (metadata.apiName === 'billToContactId') {
      return (
        <EditableLookup
          displayValue={displayValue}
          field={metadata}
          value={value}
          lookupOptionsLoader={lookupOptionsLoader}
          getLookupOptionByValue={getLookupOptionByValue}
          isEditMode={isEditMode}
          onEdit={() => {
            setIsEditMode(true);
          }}
          disableEdit
        />
      );
    }

    if (metadata.apiName === 'einvoicingMessage') {
      return value ? <EInvoicingMessage invoice={creditMemoData} /> : '';
    }

    if (metadata.apiName === 'einvoicingDocId') {
      return value &&
        (creditMemoData.einvoicingDocStatus === 'Complete' ||
          creditMemoData.einvoicingDocStatus === 'Accepted') ? (
        <Tooltip title={'Download E-Invoicing Xml Document'} arrow>
          <Grid
            style={{
              color: 'black',
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
            }}
            onClick={async () => {
              if (onDownloadEInvoicingDocument) {
                setIsDownloading(true);
                const res = await onDownloadEInvoicingDocument(value);
                if (res.xmlDoc) {
                  const blob = new Blob([res.xmlDoc], { type: 'application/xml' });
                  const link = document.createElement('a');
                  link.href = URL.createObjectURL(blob);
                  link.download = `${res.id}.xml`;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }
                setIsDownloading(false);
              }
            }}
          >
            {isDownloading ? (
              <Loading
                size="17px"
                style={{
                  width: 'auto',
                  marginRight: '10px',
                }}
              />
            ) : (
              <RoundDownloadIcon
                style={{
                  color: 'black',
                  marginRight: '10px',
                  fontSize: '17px',
                }}
              />
            )}{' '}
            Download
          </Grid>
        </Tooltip>
      ) : (
        ''
      );
    }

    switch (valueType) {
      case 'bLookup':
        return (
          <DisplayText
            apiName={metadata.apiName}
            value={value}
            type={metadata.lookupRelation ? 'text' : 'bLookup'}
          />
        );
      case 'dateTime':
        return (
          <DisplayText
            apiName={metadata.apiName}
            value={dayjs(value).format('MM/DD/YYYY hh:mm A')}
            type={valueType}
          />
        );
      case 'boolean':
        return (
          <DisplayText apiName={metadata.apiName} value={value?.toString()} type={valueType} />
        );
      case 'date':
        const data = dayjs(value).format('MM/DD/YYYY');
        if (metadata.apiName === 'invoiceDate' || metadata.apiName === 'dueDate') {
          return (
            <EditableDateInput
              field={metadata}
              value={data}
              isEditMode={isEditMode}
              onEdit={() => {
                setIsEditMode(true);
              }}
              disableEdit
            />
          );
        }
        return <DisplayText apiName={metadata.apiName} value={data} type={valueType} />;
      case 'currency':
        return (
          <DisplayText
            apiName={metadata.apiName}
            value={formatNumberForDisplay(
              Number(value || 0),
              creditMemoData.currencyIsoCode || currencyIsoCode,
              true,
              false,
              locale,
            )}
            type={valueType}
            actualValue={value || 0}
          />
        );
      case 'text':
      default:
        return <DisplayText apiName={metadata.apiName} value={value} type={valueType} />;
    }
  };

  const navigateToViewInvoiceTemplate = () => {
    window.open(`/invoice-pdf/${creditMemoData.id}`, '_blank');
  };

  return (
    <DialogComponent
      width="xl"
      title=""
      open={open}
      customTitleMode
      handleClose={onClose}
      //@ts-ignore
      closeBar={
        <ModalAppBar
          position="absolute"
          handleClose={() => {
            onClose();
          }}
        />
      }
      actions={actions}
      dialogContentStyle={{ padding: 0, overflowX: 'hidden' }}
      dontShowCloseIcon={true}
    >
      <div style={{ paddingTop: 51 }}>
        <>
          <Grid container xs={12} style={{ height: '100%', paddingLeft: 40, paddingRight: 40 }}>
            <Grid item xs={8}>
              <Grid container xs={12}>
                <Grid item>
                  <Grid container xs={12} style={{ width: '100%' }} alignItems="center">
                    <Typography style={{ fontSize: 26, fontWeight: 500 }}>
                      {creditMemoData.name}
                    </Typography>
                    <TextOnlyTooltip
                      arrow
                      placement="bottom"
                      title={
                        <span style={{ minWidth: 'fit-content' }}>
                          {copied ? 'Copied!' : 'Click to copy'}
                        </span>
                      }
                      TransitionProps={{ onExited: () => setCopied(false) }}
                    >
                      <IconButton
                        style={{
                          position: 'relative',
                          bottom: 10,
                          width: 16,
                          height: 16,
                          marginLeft: 2,
                        }}
                        onClick={() => {
                          setCopied(true);
                          navigator.clipboard.writeText(creditMemoData.name);
                        }}
                      >
                        <FileCopyOutlinedIcon
                          fontSize="inherit"
                          style={{ color: '#D9D9D9', width: 16, height: 16 }}
                        />
                      </IconButton>
                    </TextOnlyTooltip>

                    <Grid style={{ marginLeft: 16 }}>
                      <InvoiceCalculatedStatusRenderer status={getCreditMemoStatus()} />
                    </Grid>
                  </Grid>
                  <Grid container xs={12}>
                    <Typography
                      style={{ fontSize: 12, color: '#000000', opacity: '0.3', fontWeight: 700 }}
                    >
                      CREDIT MEMO
                    </Typography>
                  </Grid>
                </Grid>
                <Grid alignItems="center" style={{ display: 'flex' }}>
                  {manageInvoiceTemplate && (
                    <Grid className={classes.pdfDownloadIcon}>
                      <Button
                        onClick={() => {
                          navigateToViewInvoiceTemplate();
                        }}
                        variant="outlined"
                        color="default"
                        size="large"
                        style={{
                          borderRadius: 44,
                          border: '1px solid rgba(0, 0, 0, 0.10)',
                        }}
                        startIcon={<InvoiceDownloadIcon style={{ height: 16, width: 16 }} />}
                      >
                        <span
                          style={{
                            fontSize: 16,
                            fontWeight: 500,
                            textTransform: 'none',
                            color: '#000',
                            opacity: '0.7',
                          }}
                        >
                          Credit Memo PDF
                        </span>
                      </Button>
                    </Grid>
                  )}
                  {transactionHub && transactionHub.length > 0 && (
                    <Grid style={{ marginLeft: 16 }}>
                      {transactionHub.map((x) => {
                        return (
                          <span key={x?.id} style={{ marginRight: 4 }}>
                            {x?.Renderer({
                              actionHelpers: {
                                row: creditMemoData,
                              },
                            })}
                          </span>
                        );
                      })}
                    </Grid>
                  )}
                </Grid>
              </Grid>

              <Grid
                container
                xs={12}
                style={{ flexDirection: 'row', display: 'flex', marginTop: 28 }}
                spacing={4}
              >
                <Grid item style={{ flexDirection: 'column' }}>
                  <Grid item className={classes.titleLabel}>
                    BILLING ACCOUNT
                  </Grid>
                  <Grid item className={classes.titleText}>
                    {creditMemoData.customer.name}
                  </Grid>
                </Grid>
                <Grid item style={{ flexDirection: 'column' }}>
                  <Grid item className={classes.titleLabel}>
                    STATUS
                  </Grid>
                  <Grid item className={classes.titleText}>
                    {creditMemoData.status}
                  </Grid>
                </Grid>
                <Grid item style={{ flexDirection: 'column' }}>
                  <Grid item className={classes.titleLabel}>
                    PAYMENT STATUS
                  </Grid>
                  <Grid item className={classes.titleText}>
                    {paymentStatus}
                  </Grid>
                </Grid>
                <Grid item style={{ flexDirection: 'column' }}>
                  <Grid item className={classes.titleLabel}>
                    CREDIT MEMO DATE
                  </Grid>
                  <Grid item className={classes.titleText}>
                    {dayjs(creditMemoData.invoiceDate).format('MM/DD/YYYY')}
                  </Grid>
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={4}>
              <div
                style={{
                  border: '0.5px solid #DEDEDE',
                  backgroundColor: '#FAFAFA',
                  borderRadius: 12,
                  height: 200,
                  overflowY: 'auto',
                  padding: 24,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                }}
              >
                <Grid container>
                  <Grid container alignItems="center">
                    <Grid>
                      <Typography
                        onClick={() => {
                          setActivedTab('summary');
                        }}
                        style={{
                          fontSize: 20,
                          fontWeight: 500,
                          marginRight: 18,
                          cursor: 'pointer',
                          color: activedTab === 'summary' ? '#000' : '#AFAFAF',
                        }}
                      >
                        Summary
                      </Typography>
                    </Grid>
                    {creditMemoData?.comments && (
                      <Grid>
                        <Typography
                          style={{
                            fontSize: 20,
                            fontWeight: 500,
                            marginRight: 18,
                            cursor: 'pointer',
                            color: activedTab === 'changeHisotry' ? '#000' : '#AFAFAF',
                          }}
                          onClick={() => {
                            setActivedTab('changeHisotry');
                          }}
                        >
                          Change History
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                  {activedTab === 'changeHisotry' && creditMemoData?.comments && (
                    <List
                      style={{
                        paddingTop: '0px',
                        paddingBottom: 16,
                      }}
                    >
                      {creditMemoData?.comments &&
                        (JSON.parse(creditMemoData.comments) || []).map((item: any, index: any) => (
                          <Grid
                            container
                            xs={12}
                            key={index}
                            spacing={2}
                            style={{ marginTop: 6 }}
                            alignItems="baseline"
                          >
                            <Grid item xs={3}>
                              <div
                                style={{
                                  fontWeight: 'normal',
                                  color: '#AFAFAF',
                                  fontSize: '0.85rem',
                                }}
                              >
                                {dayjs(item.createTime).format('YYYY/MM/DD hh:mm A')}
                              </div>
                            </Grid>
                            <Grid item xs={9}>
                              <div
                                style={{
                                  fontWeight: 'normal',
                                  color: '#666',
                                  fontSize: '0.85rem',
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                <div>
                                  User{' '}
                                  <span style={{ color: '#6239EB' }}>
                                    {item.createUserName ? item.createUserName : ''}
                                  </span>{' '}
                                  left a comment{' '}
                                  <TextOnlyTooltip title={item.comment} placement="bottom">
                                    <ChatOutlinedIcon
                                      fontSize="small"
                                      style={{
                                        color: '#6239EB',
                                        cursor: 'pointer',
                                        position: 'relative',
                                        top: 6,
                                        left: 2,
                                      }}
                                    />
                                  </TextOnlyTooltip>
                                </div>
                              </div>
                            </Grid>
                          </Grid>
                        ))}
                    </List>
                  )}
                  {activedTab === 'summary' && (
                    <>
                      <Grid
                        container
                        alignItems="center"
                        justifyContent="space-between"
                        style={{ marginTop: 16 }}
                      >
                        <Grid>
                          <Typography
                            style={{
                              fontSize: 14,
                              fontWeight: 400,
                              color: '#AFAFAF',
                            }}
                          >
                            Amount without Tax
                          </Typography>
                        </Grid>
                        <Grid>
                          <Typography
                            style={{
                              fontSize: 14,
                              fontWeight: 500,
                            }}
                            className={
                              Number(creditMemoData.amountWithoutTax) < 0
                                ? classes.nagtiveNumber
                                : ''
                            }
                          >
                            {formatNumberForDisplay(
                              creditMemoData.amountWithoutTax,
                              creditMemoData.currencyIsoCode || currencyIsoCode,
                              true,
                              false,
                              locale,
                            )}
                          </Typography>
                        </Grid>
                      </Grid>
                      <Grid
                        container
                        alignItems="center"
                        justifyContent="space-between"
                        style={{ marginTop: 8 }}
                      >
                        <Grid>
                          <Typography
                            style={{
                              fontSize: 14,
                              fontWeight: 400,
                              color: '#AFAFAF',
                            }}
                          >
                            Tax
                          </Typography>
                        </Grid>
                        <Grid>
                          <Typography
                            style={{
                              fontSize: 14,
                              fontWeight: 500,
                            }}
                            className={
                              Number(creditMemoData.taxAmount) < 0 ? classes.nagtiveNumber : ''
                            }
                          >
                            {formatNumberForDisplay(
                              creditMemoData.taxAmount,
                              creditMemoData.currencyIsoCode || currencyIsoCode,
                              true,
                              false,
                              locale,
                            )}
                          </Typography>
                        </Grid>
                      </Grid>
                    </>
                  )}
                </Grid>
                {activedTab === 'summary' && (
                  <Grid
                    container
                    justifyContent="flex-end"
                    style={{ textAlign: 'right', flexDirection: 'row' }}
                  >
                    <div style={{ flexDirection: 'column', paddingRight: 26 }}>
                      <div>
                        <Typography
                          style={{
                            fontSize: 12,
                            fontWeight: 600,
                            color: '#AFAFAF',
                          }}
                        >
                          TOTAL
                        </Typography>
                      </div>
                      <div>
                        <Typography
                          style={{
                            fontSize: 20,
                            fontWeight: 500,
                          }}
                          className={
                            Number(creditMemoData.amount) < 0
                              ? classes.nagtiveNumber
                              : classes.postiveNumber
                          }
                        >
                          {formatNumberForDisplay(
                            creditMemoData.amount,
                            creditMemoData.currencyIsoCode || currencyIsoCode,
                            true,
                            false,
                            locale,
                          )}
                        </Typography>
                      </div>
                    </div>
                    <div style={{ flexDirection: 'column' }}>
                      <div>
                        <Typography
                          style={{
                            fontSize: 12,
                            fontWeight: 600,
                            color: '#AFAFAF',
                          }}
                        >
                          BALANCE
                        </Typography>
                      </div>
                      <div>
                        <Typography
                          style={{
                            fontSize: 20,
                            fontWeight: 500,
                          }}
                          className={
                            Number(creditMemoData.balance) < 0
                              ? classes.nagtiveNumber
                              : classes.postiveNumber
                          }
                        >
                          {formatNumberForDisplay(
                            creditMemoData.balance,
                            creditMemoData.currencyIsoCode || currencyIsoCode,
                            true,
                            false,
                            locale,
                          )}
                        </Typography>
                      </div>
                    </div>
                  </Grid>
                )}
              </div>
            </Grid>
          </Grid>
        </>
        <AppBar
          position="static"
          color={'default'}
          classes={{
            root: classes.appBarRoot,
          }}
        >
          <Tabs
            classes={{
              indicator: classes.tabsIndicator,
              root: classes.tabsRoot,
            }}
            scrollButtons={'on'}
            value={selectedTab}
            onChange={(event: React.ChangeEvent<{}>, newValue: number) => {
              //@ts-ignore
              setSelectedTab(newValue);
            }}
            variant={'scrollable'}
          >
            <Tab
              classes={{
                textColorInherit: classes.tabText,
                root: classes.tabRoot,
                selected: classes.selected,
                wrapper: classes.wrapper,
              }}
              wrapped
              value={TabValue.generalInformation}
              style={{ width: 220, borderLeft: '1px solid #f5f5f5' }}
              key={TabValue.generalInformation}
              label={'General Information'}
            />
            <Tab
              classes={{
                textColorInherit: classes.tabText,
                root: classes.tabRoot,
                selected: classes.selected,
                wrapper: classes.wrapper,
              }}
              wrapped
              value={TabValue.creditMemoItems}
              style={{ width: 220 }}
              key={TabValue.creditMemoItems}
              label={'Credit Memo Items'}
            />
            {creditMemoData?.status?.toLowerCase() !== 'draft' && (
              <Tab
                classes={{
                  textColorInherit: classes.tabText,
                  root: classes.tabRoot,
                  selected: classes.selected,
                  wrapper: classes.wrapper,
                }}
                wrapped
                value={TabValue.invoice}
                style={{ width: 220 }}
                key={TabValue.invoice}
                label={'Invoices'}
              />
            )}
            {creditMemoData?.status?.toLowerCase() !== 'draft' && (
              <Tab
                classes={{
                  textColorInherit: classes.tabText,
                  root: classes.tabRoot,
                  selected: classes.selected,
                  wrapper: classes.wrapper,
                }}
                wrapped
                value={TabValue.paymentApplicationsItems}
                style={{ width: 220 }}
                key={TabValue.paymentApplicationsItems}
                label={'Payment Applications'}
              />
            )}
          </Tabs>
        </AppBar>
        <div className={classes.divider} />
        <TabPanel value={selectedTab} index={TabValue.generalInformation} style={{ height: 400 }}>
          <Grid
            container
            style={{
              minHeight: 300,
              padding: 32,
              paddingTop: 0,
              paddingLeft: 40,
              flexDirection: 'column',
            }}
          >
            <div>
              <Typography style={{ fontSize: 26, fontWeight: 500 }}>General Information</Typography>
            </div>
            <Grid container xs={6} style={{ marginTop: 24 }}>
              <TextInput
                disableLabel
                applyBottomPadding
                classes={{
                  input: classes.searchBar,
                  root: classes.searchBarRoot,
                }}
                placeholder="Search"
                startAdornment={
                  <InputAdornment position="start">
                    <SearchIcon viewBox={'-2 -2 21 21'} style={{ paddingLeft: 4 }} />
                  </InputAdornment>
                }
                handleInputChange={(newValue) => {
                  setSearchText(newValue);
                }}
                field={{
                  apiName: 'search',
                  type: 'text',
                  name: 'Search',
                }}
                value={searchText}
              />
            </Grid>
            <div
              style={{
                flexDirection: 'row',
                display: 'flex',
                marginTop: 24,
                flexWrap: 'wrap',
              }}
            >
              {buildArray()
                .filter((element: any) => {
                  return (
                    element.apiName.toLowerCase().includes(searchText?.toLowerCase()) ||
                    element.name.toLowerCase().includes(searchText?.toLowerCase())
                  );
                })
                .map((metadata: any) => {
                  return (
                    <div
                      style={{
                        flexDirection: 'column',
                        width: 300,
                        padding: 16,
                        paddingLeft: 0,
                        paddingRight: 32,
                        textTransform: 'uppercase',
                      }}
                      key={metadata.apiName}
                    >
                      <div
                        style={{ fontSize: 12, color: '#000', opacity: '0.4', paddingBottom: 14 }}
                      >
                        {metadata.lookupRelation && metadata.apiName !== 'billToContactId'
                          ? metadata.lookupRelation.relationLabel
                          : metadata.name}
                      </div>
                      <div
                        className={classes.titleText}
                        style={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {getFormattedValue(metadata.type, metadata)}
                      </div>
                    </div>
                  );
                })}
            </div>
          </Grid>
          {isEditMode && (
            <DialogActions className={classes.dialogFooter}>
              <RubyButtonBar
                rightButtons={
                  isEditMode
                    ? [
                        {
                          text: 'Save',
                          onClick: () => {
                            try {
                              setIsEditMode(false);
                            } catch (err) {
                              console.log('err: ', err);
                            }
                          },
                        },
                        {
                          text: 'Cancel',
                          onClick: () => {
                            setIsEditMode(false);
                          },
                        },
                      ]
                    : []
                }
                variant={'inPlace'}
                leftButtons={[]}
              />
            </DialogActions>
          )}
        </TabPanel>
        {[TabValue.creditMemoItems, TabValue.invoice, TabValue.paymentApplicationsItems].map(
          (x) => {
            return (
              <TabPanel value={selectedTab} index={x} style={{ height: 400 }} key={`tab-${x}`}>
                {renderCustomTabPanel && renderCustomTabPanel(x)}
              </TabPanel>
            );
          },
        )}
      </div>
    </DialogComponent>
  );
};

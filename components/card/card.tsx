import React from 'react';

import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';

import CardBody from '../card-body';
import CardHeader from '../card-header';
import { Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  card: {
    backgroundColor: '#fff',
    width: '325px',
    borderStyle: 'solid',
    borderColor: 'white',
    display: 'flex',
    flexDirection: 'column',
  },
  summaryCard: {
    backgroundColor: '#fff',
    width: '325px',
    display: 'flex',
    flexDirection: 'column',
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
  },
  container: {
    position: 'relative',
    display: 'flex',
    height: '100%',
    fontFamily: 'inherit',
  },
  contentContainer: {
    display: 'flex',
  },
  content: {
    padding: '30px',
    height: '100%',
    flexDirection: 'column',
    borderRadius: 'calc(20px - 10px)',
    backgroundColor: '#f5f5f5',
    display: 'flex',
  },
});

export const Card: React.FC<Props> = (userProps: Props) => {
  const props = { ...defaultProps, ...userProps };

  const {
    status,
    imageSignedUrl,
    title,
    description,
    items,
    statusStyle,
    statusShadowStyle,
    activeBorderStyle,
    statusHeaderStyle,
    StatusIcon,
    subtitle,
    summaryItem,
    currencyIsoCode,
    actions,
    onTitleClick,
    actionEventHandler,
    numCardActionsPerRow,
    uom,
    showSubmitBtn,
    onShowDetails,
    showDetails,
    moreActions,
    locale,
    summaryView,
    transactionHub,
  } = props;

  const classes = useStyles();

  return (
    <div className={classes.container} role="card">
      <div className={classes.contentContainer}>
        <div className={`${summaryView ? classes.summaryCard : classes.card}`}>
          <Grid
            className={classes.content}
            style={summaryView ? { backgroundColor: '#fff' } : activeBorderStyle}
          >
            <CardHeader
              onTitleClick={onTitleClick}
              status={status}
              imageSignedUrl={imageSignedUrl}
              title={title}
              description={description}
              statusStyle={statusStyle}
              statusShadowStyle={statusShadowStyle}
              statusHeaderStyle={statusHeaderStyle}
              StatusIcon={StatusIcon}
              subtitle={subtitle}
              transactionHub={transactionHub}
            />
            <CardBody
              moreActions={moreActions}
              uom={uom}
              currencyIsoCode={currencyIsoCode}
              items={items}
              summaryItem={summaryItem}
              actions={actions}
              actionEventHandler={actionEventHandler}
              numCardActionsPerRow={numCardActionsPerRow}
              showSubmitBtn={showSubmitBtn === undefined ? true : showSubmitBtn}
              showDetails={showDetails}
              onShowDetails={onShowDetails}
              locale={locale}
            />
          </Grid>
        </div>
      </div>
    </div>
  );
};

export default Card;

import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CardAction,
  ChangeGroup,
  ChangeItem,
  DeleteFilterHandler,
  ListFiltersHandler,
  QueryExecutor,
  RightRevIntegration,
  RubyField,
  RubyObject,
  SaveFilterHandler,
  TabFilter,
  TaxDetail,
  TaxIntegrationResponse,
  UpdateFiltersHandler,
  change_cart,
  ruby_settings,
} from '@nue-apps/ruby-ui-component';
import { ExpressCheckoutRequest } from '../change-cart';
import type { GridItemActionEventHandler } from '../customer-view-context/customer-view-context';
import type { RevenueContractsResult } from '../customer-view/interface';
import type {
  GeneralInformationAccount,
  SubscriptionSteamData,
} from '../general-information-summary-view/interface';
import type {
  InvoiceByOrderItem,
  MilestonesChange,
} from '../manage-milestone-dialog/manage-milestone-dialog';
import type { CountQueryExecutor } from '../query-executor';
import type { Customer, Subscription } from '../revenue-builder-types';
import type { CreditTypes } from '../ruby-settings';
import { ProductRelations } from '../modals/subscription-bulkactions/types';

type RubySettings = ruby_settings.RubySettings;
type NavigateToQuoteBuilderRequest = change_cart.NavigateToQuoteBuilderRequest;

export type ActionEventHandlerParams = {
  object: any;
  objectMetadata: RubyObject;
  action: CardAction;
};

export type AccountHierarchy = {
  id: string;
  name: string;
  children?: AccountHierarchy[];
};

export interface Props {
  subscriptionMetadata: RubyObject;
  invoiceMetadata: RubyObject;
  creditMemosMetaData: RubyObject;
  paymentMethodsMetaData: RubyObject;
  getInvoicePeriods?: (params: { customerId: string; startDate: string; endDate: string }) => any;
  loadCreditStatistics: (customerId: string) => any;
  getPanelStatistics: (credit: { AccountCreditPoolId: string }) => any;
  getInvoiceIdsForAsset: (params: { assetNumbers: string[]; assetType: string }) => any;
  getInvoicesByOrderItemIds: (params: {
    orderItemIds: string[];
  }) => Promise<Record<string, InvoiceByOrderItem[]>>;
  getSwapUpgradeDowngradeOptions: (params: {
    subscriptionIds: string[];
  }) => Promise<ProductRelations>;
  UpdateMileStonesStartDate: (params: { orderItems: MilestonesChange[] }) => Promise<void>;
  getOrderProductIdsByAssetId: (params: { assetId: string }) => Promise<string[]>;
  loadUsageMetadata: () => Promise<RubyObject>;
  loadCreditMetadata: () => Promise<RubyObject>;
  customerMetadata: RubyObject;
  assetMetadata: RubyObject;
  changeHistoryMetadata: RubyObject;
  orderMetadata: RubyObject;
  orderItemMetadata: RubyObject;
  quoteMetadata: RubyObject;
  opportunityMetadata: RubyObject;
  transactionHubMetadata: RubyObject;
  userMetadata: RubyObject;
  customer: Customer;
  orderId?: string;
  defaultTab?: string;
  currencyIsoCode: string;
  breadcrumbs: Breadcrumb[];
  // TODO: change the interface of these when we implement filter builder in Salesforce
  updateFilters: UpdateFiltersHandler;
  saveFilter: SaveFilterHandler;
  listFilters: ListFiltersHandler;
  deleteFilter: DeleteFilterHandler;
  executeQuery: QueryExecutor;
  executeGetCountQuery: CountQueryExecutor;
  getFieldSetMetadata: (fieldSetApiName: string, objectApiName: string) => Promise<RubyField[]>;
  navigateToQuoteBuilder: ({
    mode,
    recordId,
    lineObject,
    changeItemRequests,
  }: NavigateToQuoteBuilderRequest) => Promise<void>;
  navigateToCustomerViewPage: (
    objectApiName: string,
    objectId: string,
    orderId?: string,
  ) => Promise<void>;
  personPlaceholderImage: string;
  getChangeCartLocalState: () => Promise<Record<string, any>>;
  setChangeCartLocalState: (newChangeCartLocalState: Record<string, any>) => Promise<void>;
  actionEventHandler: GridItemActionEventHandler;
  tabFilters?: (Partial<TabFilter> | null)[];
  rubySettings: RubySettings;
  getSumOrderProductQuantityAtDate: (
    currentDate: Date,
    subscriptionNumber: string,
    subscriptionVersion: number,
  ) => Promise<number>;
  getSubscriptionsForCoterm: (
    lineEditorStartDate: Date,
    customerId: string | undefined,
  ) => Promise<Subscription[]>;
  entitlementMetadata: RubyObject;
  productOptionMetadata: RubyObject;
  getRevenueContracts: (
    search: string | null,
    filter: string | null,
  ) => Promise<RevenueContractsResult>;
  getRevenueIntegration: () => Promise<RightRevIntegration>;
  isInSalesforce?: boolean;
  getCreditTypes: () => Promise<CreditTypes[]>;

  getAccountHierarchy: (accountId: string) => Promise<AccountHierarchy>;
  //deprecated
  getRelatedBillingAccountsIdOfSalesAccount: ({
    salesAccountId,
  }: {
    salesAccountId: string;
  }) => Promise<string[]>;
  loadInvoicesPreviewData?: (option: string, date?: string) => Promise<any>;
  handleSaveInvoicePreview?: (data: any) => Promise<void>;
  getFutureInvoicePeriods?: () => Promise<any>;
  loadSubscriptionStreamData?: () => Promise<SubscriptionSteamData>;
  loadChildernAndBillingAccounts?: () => Promise<GeneralInformationAccount>;
  loadStripeCardInfo?: (customerIds: string[]) => Promise<any>;
  getTaxBreakdown?: (id: string) => Promise<TaxDetail | null>;
  getTaxConfig?: () => Promise<TaxIntegrationResponse | null>;
  creditCashOut: (
    accountPoolId: string,
    includeExpiredCredits: boolean,
    amount: number,
  ) => Promise<void>;
  isFromSalesConsole?: boolean;
  sync?: (request: SyncRequest) => Promise<SyncResult>;
  getIntegrations?: () => Promise<IntegrationResult[]>;
  checkOut?: (request: ExpressCheckoutRequest) => Promise<any>;
}

export interface AddChangeItemToChangeGroup {
  changeGroupIdToChangeGroup: Record<string, ChangeGroup>;
  changeGroupId: string;
  newChangeItemsToAdd: ChangeItem[];
  changeCartLocalState: Record<string, any>;
}

export interface CreateChangeGroupWithChangeItem {
  changeGroupIdToChangeGroup: Record<string, ChangeGroup>;
  changeGroup: ChangeGroup;
  newChangeItemsToAdd: ChangeItem[];
  changeCartLocalState: Record<string, any>;
}

export interface BulletListChangeItems {
  changeItems: ChangeItem[];
}

export interface AddSingleChangeItemToExistingChangeGroup {
  changeGroupIdToChangeGroup: Record<string, ChangeGroup>;
  changeGroup: ChangeGroup;
  newChangeItemToAdd: ChangeItem;
  changeCartLocalState: Record<string, any>;
}

export interface SyncRequest {
  itemId: string;
  itemType: string;
}

export interface SyncResult {
  success: boolean;
  message: string;
  successCount: number;
}

export interface IntegrationResult {
  integrationName: string;
  status: string;
}

import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Typography from '@material-ui/core/Typography';
import { Props } from './interface';

const useStyles = makeStyles({
  linkContainer: {
    display: 'flex',
    alignItems: 'center',
    fontWeight: 400,
  },
  linkIcon: {
    fontSize: '1.1rem',
    paddingRight: '4px',
    display: 'flex',
    alignSelf: 'center',
  },
  link: {
    color: '#6239eb',
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    fontSize: '.8rem',
  },
  position: {
    justifyContent: 'flex-end',
  },
});

const LinkComponent: React.FC<Props> = ({ icon, message, handleClick, styles }) => {
  const classes = useStyles();
  return (
    <div className={classes.linkContainer} style={styles}>
      <Typography variant="body2" component="p" className={classes.link} onClick={handleClick}>
        {/* TODO: pass in styling for link-icon */}
        <div className={classes.linkIcon}>{icon}</div>
        {message}
      </Typography>
    </div>
  );
};

export default LinkComponent;

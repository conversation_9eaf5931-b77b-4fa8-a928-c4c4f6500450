import React from 'react';

import { makeStyles } from '@material-ui/core';
import Grid from '@material-ui/core/Grid';

import Card from '../card';
import { CellRendererRowProps } from '../grid-actions';
import { SummaryItem } from '../list';
import { CardAction } from '../metadata';
import { displayValuesFromFieldSet } from '../metadata/metadata-utils';
import { useUserLocale } from '../use-user-locale';
import { Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  card: {
    padding: '24px',
  },
  cardSummary: {},
});

export const AccountViewItem: React.FC<Props> = (userProps: Props) => {
  const props = { ...defaultProps, ...userProps };

  const classes = useStyles();

  const {
    gridViewMetadata,
    fieldSetMetadata,
    objectMetadata,
    object,
    currencyIsoCode,
    handleCardClick,
    actionEventHandler,
    showSubmitBtn,
    onShowDetails,
    showDetails,
    isPopup,
    locale,
    summaryView,
    addTransactionHub,
  } = props;
  const { getUserLocale } = useUserLocale();

  if (!object) {
    return null;
  }

  let items = displayValuesFromFieldSet({
    objectFieldSet: fieldSetMetadata,
    currencyIsoCode: (object.currencyIsoCode as string) || currencyIsoCode,
    object,
    objectMetadata,
    locale: locale || getUserLocale(),
  });

  if (!(object.hasChildSalesAccounts || object.hasChildBillingAccounts || object.salesAccountId)) {
    const ROLLUP_FIELDS = new Set(['todayRollupARR', 'todayRollupCMRR', 'rollupTCV']);
    items = items.filter((field) => !ROLLUP_FIELDS.has(field.apiName));
  }

  if (!gridViewMetadata || !gridViewMetadata.cardHeaderMetadata) {
    return null;
  }

  const {
    title,
    description,
    status,
    subtitle,
    imageSignedUrl,
    statusStyle,
    statusShadowStyle,
    statusHeaderStyle,
    statusIcon,
  } = gridViewMetadata.cardHeaderMetadata;

  const cardDescription =
    typeof description === 'function' ? description(object) : object[description];
  const cardStatus = typeof status === 'function' ? status(object) : object[status];
  const cardTitle = typeof title === 'function' ? title(object) : object[title];
  const cardSubtitle = typeof subtitle === 'function' ? subtitle(object) : object[subtitle];
  const cardHeaderImage =
    typeof imageSignedUrl === 'function' ? imageSignedUrl(object) : object[imageSignedUrl];
  const actions = gridViewMetadata.cardBodyMetadata?.actions[cardStatus];

  const getMoreActions = () => {
    const actions = isPopup
      ? gridViewMetadata.cardBodyMetadata?.popUpMoreActions
      : gridViewMetadata.cardBodyMetadata?.moreActions;
    const hideActionIds: string[] = [];

    if (
      objectMetadata.name === 'Order' &&
      !props.rubySettings?.showSubscriptionOnCustomerLifecycle
    ) {
      hideActionIds.push('changeOrder');
    }

    if (!props.rubySettings?.showInvoiceOnCustomerLifecycle) {
      hideActionIds.push('viewInvoice');
    }

    if (!props.rubySettings?.showUsageOnCustomerLifecycle) {
      hideActionIds.push('viewUsage');
    }

    if (!props.rubySettings?.showAssetOnCustomerLifecycle) {
      hideActionIds.push('viewAsset');
    }

    if (!props.rubySettings?.showEntitlementOnCustomerLifecycle) {
      hideActionIds.push('viewEntitlement');
    }

    return actions?.filter((x: CardAction) => !hideActionIds.find((y: string) => y === x.id));
  };

  const uom =
    objectMetadata.name === 'Asset' || (objectMetadata.name === 'Entitlement' && object.uom)
      ? object.uom
      : undefined;

  let cardSummaryItem: SummaryItem | undefined;

  const activeBorderStyle = gridViewMetadata.cardBodyMetadata?.activeBorderStyle;
  const summaryItemField = gridViewMetadata.cardBodyMetadata?.summaryItemField;
  const summaryItemPosition = gridViewMetadata.cardBodyMetadata?.summaryItemPosition;
  const numCardActionsPerRow = gridViewMetadata.cardBodyMetadata?.numCardActionsPerRow || 3;

  const onTitleClick = () => {
    if (handleCardClick) {
      handleCardClick(object);
    }
  };

  if (!items[0]) {
    return null;
  }

  if (summaryItemField) {
    const summaryItemMetadataField = fieldSetMetadata.find(
      (field) => field.apiName === summaryItemField,
    );
    cardSummaryItem = {
      apiName: summaryItemMetadataField?.apiName || '',
      name: summaryItemMetadataField?.name || '',
      value: object[summaryItemField],
      type: summaryItemMetadataField?.type || '',
      position: summaryItemPosition,
    };
    items.splice(
      items.findIndex((item) => item.name === summaryItemMetadataField?.name),
      1,
    );
  } else {
    cardSummaryItem = {
      apiName: items[0].apiName,
      name: items[0].name,
      value: items[0].value,
      type: items[0].type,
      position: summaryItemPosition,
    };
    items.splice(0, 1);
  }

  const getAccountStatus = () => {
    switch (object.type) {
      case 'Account':
        return 'Customer';
      case 'Subsidiary':
        return 'Customer';
      case 'BillingAccount':
        return 'Billing Account';
      default:
        return 'Active';
    }
  };

  return (
    <Grid item className={summaryView ? classes.cardSummary : classes.card}>
      <Card
        moreActions={getMoreActions()}
        showDetails={showDetails}
        summaryView={summaryView}
        numCardActionsPerRow={numCardActionsPerRow}
        onTitleClick={handleCardClick ? onTitleClick : undefined}
        activeBorderStyle={{
          ...activeBorderStyle,
          borderColor: statusStyle[cardStatus]?.backgroundColor,
        }}
        status={getAccountStatus()}
        statusStyle={statusStyle[cardStatus]}
        statusShadowStyle={statusShadowStyle[cardStatus]}
        statusHeaderStyle={statusHeaderStyle && statusHeaderStyle[cardStatus]}
        imageSignedUrl={cardHeaderImage}
        title={cardTitle}
        description={cardDescription}
        items={items}
        currencyIsoCode={object.currencyIsoCode || currencyIsoCode}
        subtitle={cardSubtitle}
        StatusIcon={statusIcon[cardStatus]}
        summaryItem={cardSummaryItem}
        actions={typeof actions === 'function' ? actions({ object }) : actions}
        showSubmitBtn={showSubmitBtn === undefined ? true : showSubmitBtn}
        uom={uom}
        onShowDetails={() => {
          if (onShowDetails) {
            onShowDetails(object);
          }
        }}
        actionEventHandler={async (action) => {
          actionEventHandler &&
            (await actionEventHandler({
              object,
              objectMetadata,
              action,
            }));
        }}
        locale={locale}
        transactionHub={addTransactionHub?.().cellRenderer({ row: object } as CellRendererRowProps)}
      />
    </Grid>
  );
};

export default AccountViewItem;

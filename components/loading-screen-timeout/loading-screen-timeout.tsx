import React from 'react';
import { makeStyles, Typography } from '@material-ui/core';
import type { Props } from './interface';

import { LoadingScreen, RubyButton } from '@nue-apps/ruby-ui-component';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '10px',
  },
});

const LoadingScreenTimeout: React.FC<Props> = ({ delayMs = 20_000, children }) => {
  const [showTimeoutMessage, setShowTimeoutMessage] = React.useState(false);

  const classes = useStyles();

  React.useEffect(() => {
    const timeout = setTimeout(() => {
      setShowTimeoutMessage(true);
    }, delayMs);

    return () => {
      clearTimeout(timeout);
    };
  }, [delayMs]);

  if (!showTimeoutMessage) {
    return <LoadingScreen loadingText="Loading..." />;
  }

  return (
    <div className={classes.root}>
      {children ?? (
        <>
          <Typography variant="body1" component="p">
            An error may have occurred while loading, please try refreshing the page.
          </Typography>

          <RubyButton
            text="Refresh"
            onClick={() => {
              window.location.reload();
            }}
          />
        </>
      )}
    </div>
  );
};

export default LoadingScreenTimeout;

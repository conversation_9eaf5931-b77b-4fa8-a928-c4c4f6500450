import React, { useContext } from 'react';

import { FormControl, makeStyles } from '@material-ui/core';
import Button from '@material-ui/core/Button';
import IconButton from '@material-ui/core/IconButton';
import AddCircleOutlineIcon from '@material-ui/icons/AddCircleOutline';
import ClearRoundedIcon from '@material-ui/icons/ClearRounded';

import Autocomplete from '../autocomplete';
import { LookupRecordContext, LookupRecordContextService } from '../form-field';
import LabelWithTooltip from '../label-with-tooltip';
import { LookupOption, Props } from './interface';

const defaultProps: Partial<Props> = {
  options: [],
  showNone: true,
  withCreateLookupRecord: false,
  withClearIcon: true,
  withSearch: true,
};

const useStyles = makeStyles({
  root: {},
  select: {
    backgroundColor: '#EFEFE0',
  },
  btn: {
    fontSize: '.875rem',
    textTransform: 'none',
    color: '#6239eb',
    minWidth: 0,
    borderRadius: '0px 4px 4px 0px',
    padding: '10px 24px',
    cursor: 'pointer',
    fontWeight: 500,
    backgroundColor: '#EFEBFD',
    border: 'none',
    '&:hover': {
      // you want this to be the same as the backgroundColor above
      backgroundColor: '#EFEBFD',
    },
  },
  iconButton: {
    padding: '10px 24px',
    color: '#6239EB',
    fontWeight: 500,
    textTransform: 'none',
  },
  divider: {
    height: 34,
    margin: 4,
  },
});

const Lookup: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const {
    value,
    field,
    label,
    disabled,
    showNone,
    handleInputChange,
    className,
    name,
    readOnly,
    lookupOptionsLoader,
    getLookupOptionByValue,
    formContext,
    withCreateLookupRecord,
    onClickCreateLookupRecord,
    showTooltip,
    toolTipText,
    withClearIcon,
    withSearch,
    inputWrapperStyles,
    usePopper,
    poperStyle,
    getFocusElement,
    disableLabel,
  } = props;

  const classes = useStyles();
  const getValues = formContext?.getValues;
  const referenceTo = field.lookupRelation?.referenceTo;
  const createLookupRecordContext: LookupRecordContextService | null =
    useContext(LookupRecordContext);

  if (!referenceTo) {
    console.error(`ReferenceTo field not given for lookup field [${field.apiName}]`);
    return null;
  }

  if (!lookupOptionsLoader) {
    console.error(`lookupOptionsLoader field not given for lookup field [${field.apiName}]`);
    return null;
  }

  if (!getLookupOptionByValue) {
    console.error(`getLookupOptionByValue field not given for lookup field [${field.apiName}]`);
    return null;
  }

  let showLookupPreview = false;
  let LookupRecordPreview = null;
  if (
    createLookupRecordContext &&
    createLookupRecordContext.showLookupRecordPreview &&
    createLookupRecordContext.getLookupRecordPreviewComponent
  ) {
    showLookupPreview = createLookupRecordContext.showLookupRecordPreview(field);
    LookupRecordPreview = createLookupRecordContext.getLookupRecordPreviewComponent(field);
  }

  const _label = `select ${label || field.name}`;
  const isRequired = field ? field.required : false;
  return (
    <>
      <FormControl style={{ width: '100%' }}>
        {!disableLabel && (
          <LabelWithTooltip
            label={_label}
            required={isRequired}
            shrink
            showTooltip={showTooltip}
            tooltipText={toolTipText}
            htmlFor={name || field.apiName}
          />
        )}

        <Autocomplete
          getFocusElement={getFocusElement}
          useDorpdownPopper={usePopper}
          poperStyle={poperStyle}
          style={{
            // if without the clear icon, the height of the input will decrease, so we add some padding to make the height beautifull
            paddingTop: withClearIcon ? 0 : '6px',
            paddingRight: 0,
            paddingBottom: withClearIcon ? 0 : '6px',
            marginTop: usePopper ? 0 : '8px',
          }}
          inputStyle={inputWrapperStyles}
          value={value}
          placeholder={`Search ${label || field.name}`}
          async
          asyncFetchSuggestions={(inputValue) => {
            return lookupOptionsLoader(
              field.apiName,
              inputValue,
              getValues ? getValues() : undefined,
            );
          }}
          asyncFetchSelectedItem={(value) => {
            return getLookupOptionByValue(field.apiName, value);
          }}
          getSuggestionItemLabel={(x: LookupOption) => {
            return x.optionName || x.name;
          }}
          getSuggestionItemValue={(x: LookupOption) => x.id}
          onChange={(event) => {
            if (handleInputChange) {
              handleInputChange(event.value, event.label || '');
            }
          }}
          readOnly={readOnly}
          endAdornment={
            <>
              {value && withClearIcon && (
                <IconButton
                  style={{ padding: '8px' }}
                  aria-label="delete"
                  onClick={() => {
                    if (handleInputChange) {
                      handleInputChange(null);
                    }
                  }}
                >
                  <ClearRoundedIcon />
                </IconButton>
              )}
              {withCreateLookupRecord && !readOnly && !disabled && (
                <Button
                  className={classes.iconButton}
                  startIcon={<AddCircleOutlineIcon />}
                  onClick={(event) => {
                    if (onClickCreateLookupRecord) {
                      onClickCreateLookupRecord({
                        lookupField: field,
                      });
                    } else {
                      console.error(
                        `onClickCreateLookupRecord props is not defined on lookupField ${field.apiName}`,
                      );
                    }
                  }}
                >
                  {'Create ' + field.lookupRelation?.referenceTo}
                </Button>
              )}
              {!readOnly && !disabled && withSearch && (
                <Button
                  className={classes.btn}
                  type="button"
                  onClick={() => {
                    // TODO: find out how search should work
                  }}
                >
                  Search
                </Button>
              )}
            </>
          }
        />
      </FormControl>
      {showLookupPreview && LookupRecordPreview && value && (
        <LookupRecordPreview lookupField={field} lookupRecordId={value as string} />
      )}
    </>
  );
};

export default Lookup;

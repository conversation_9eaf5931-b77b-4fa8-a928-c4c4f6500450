import React from 'react';
import { Breadcrumb } from './interface';

export interface BreadcrumbService {
  getBreadcrumbConfig: () => {
    breadcrumbs: Array<Breadcrumb>;
    updateBreadcrumbs: (newBreadcrumb: Array<Breadcrumb>) => void;
  };
}

export interface BreadcrumbFacade {
  breadcrumbService?: BreadcrumbService;
}

export const BreadcrumbContext = React.createContext<BreadcrumbFacade>({});

export default BreadcrumbContext;

import React from 'react';

import { <PERSON><PERSON>, <PERSON>rid, Icon<PERSON>utton, Typography, makeStyles } from '@material-ui/core';
import ArrowForwardIcon from '@material-ui/icons/ArrowForward';
import ClearIcon from '@material-ui/icons/Clear';

import TextInput from '../text-input';
import { NetSuiteFileUploader } from './netsuite-file-uploader';
import NetSuiteMappingGrid from './netsuite-mapping-grid';

const useStyle = makeStyles({
  mappingContainer: {
    marginTop: 48,
  },
  subHeader: {
    marginBottom: 28,
    marginTop: 8,
    color: '#000',
    opacity: 0.8,
    fontSize: '14px !important',
  },
  clearInputIcon: {
    padding: '0px',
    margin: '12px',
    '& svg': {
      fontSize: '1rem',
    },
  },
  searchBarInput: {
    borderRightColor: 'whitesmoke',
    fontSize: '.875rem',
    height: '100%',
    marginTop: '0 !important',
    backgroundColor: '#f1f0f2 !important',
    border: 'none',
    minHeight: '22px',
  },
  searchBarRoot: {
    overflow: 'hidden',
    borderRadius: '4px 0 0 4px !important',
    backgroundColor: '#f1f0f2 !important',
  },
});

interface Props {
  handleUploadFiles: (data: any, type: string) => Promise<any>;
  loadMappingData: (
    type: string,
    env: string,
    pageSize?: number,
    currentPage?: number,
    keyword?: string,
  ) => Promise<any>;
  handleUpdateRows: (rows: any, env: string, type: string) => any;
  handleDeleteRow: (rows: any, type: string) => any;
  columns: any;
  columnWidths: any;
  title: string;
  type: string;
  isSandBox: boolean;
}

export const NetSuiteConnectionMapping = ({
  handleUploadFiles,
  loadMappingData,
  handleUpdateRows,
  handleDeleteRow,
  columns,
  columnWidths,
  title,
  type,
  isSandBox,
}: Props) => {
  const classes = useStyle();

  const [currentPage, setCurrentPage] = React.useState(0);
  const [pageSize, setPageSize] = React.useState(5);
  const [totalCount, setTotalCount] = React.useState(0);
  const [dataSource, setDataSource] = React.useState([]);
  const [keywords, setKeywords] = React.useState('');

  const loadNetSuiteMappingData = React.useCallback(
    async (nPageSize?: number, nCurrentPage?: number, nKeyword?: string) => {
      const result = await loadMappingData(
        type,
        isSandBox ? 'Sandbox' : 'Production',
        nPageSize,
        nCurrentPage,
        nKeyword,
      );

      if (result) {
        setTotalCount(result.totalCounts);
        setDataSource(result.data as any);
      }
    },
    [loadMappingData, type, isSandBox],
  );

  const onUploadFile = React.useCallback(
    async (request: any) => {
      if (handleUploadFiles) {
        const result = await handleUploadFiles(request, type);
        if (result && result.length === 0) {
          setPageSize(5);
          setCurrentPage(0);
          setKeywords('');
          loadNetSuiteMappingData(5, 0);
        }
        return result;
      }
      return null;
    },
    [handleUploadFiles, loadNetSuiteMappingData, type],
  );

  React.useEffect(() => {
    loadNetSuiteMappingData(pageSize, currentPage, keywords);
  }, []);

  const handlePageSizeChange = React.useCallback(
    async (newPageSize: number) => {
      setPageSize(newPageSize);
      await loadNetSuiteMappingData(newPageSize, currentPage, keywords);
    },
    [currentPage, keywords, loadNetSuiteMappingData],
  );

  const handleCurrentPageChange = React.useCallback(
    async (newPage: number) => {
      setCurrentPage(newPage);
      await loadNetSuiteMappingData(pageSize, newPage, keywords);
    },
    [keywords, loadNetSuiteMappingData, pageSize],
  );

  const onKeyDown = React.useCallback(
    async (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        await loadNetSuiteMappingData(pageSize, currentPage, keywords);
      }
    },
    [currentPage, keywords, loadNetSuiteMappingData, pageSize],
  );

  const handleEditRows = React.useCallback(
    async (rows: any) => {
      await handleUpdateRows(rows, isSandBox ? 'Sandbox' : 'Production', type);
    },
    [handleUpdateRows, isSandBox, type],
  );

  return (
    <>
      <Grid container xs={12} className={classes.mappingContainer}>
        <Grid item xs={6}>
          <Typography variant="h6">{title}</Typography>
        </Grid>
      </Grid>
      <Grid item xs={12} className={classes.subHeader}>
        <span>
          Upload a CSV file to map existing {type === 'customer' ? 'accounts' : 'products'} in
          Salesforce with {type === 'customer' ? 'customers' : 'products'} in NetSuite.
        </span>
      </Grid>
      <NetSuiteFileUploader handleUploadFiles={onUploadFile} isSandBox={isSandBox} type={type} />
      <Grid
        container
        style={{ alignItems: 'center', marginTop: 24, justifyContent: 'space-between' }}
      >
        <Grid xs={2} md={2}>
          Mapping: {totalCount}
        </Grid>
        <Grid container xs={10} md={10} style={{ alignItems: 'center', justifyContent: 'end' }}>
          <Grid item xs={6} md={6}>
            <TextInput
              value={keywords}
              handleInputChange={(value: any) => {
                setKeywords(value);
              }}
              disableLabel
              field={{
                apiName: 'search',
                type: 'text',
                name: 'Search',
              }}
              onKeyDown={onKeyDown}
              placeholder={
                type === 'customer' ? 'Type in customer / account name' : 'Type in product name'
              }
              classes={{
                input: classes.searchBarInput,
                root: classes.searchBarRoot,
              }}
              endAdornment={
                <>
                  {keywords && (
                    <IconButton
                      className={classes.clearInputIcon}
                      onClick={() => {
                        setKeywords('');
                      }}
                    >
                      <ClearIcon />
                    </IconButton>
                  )}

                  <Button
                    type="button"
                    variant="contained"
                    color="primary"
                    size="large"
                    style={{ backgroundColor: '#6239eb' }}
                    onClick={async () => {
                      await loadNetSuiteMappingData(pageSize, currentPage, keywords);
                    }}
                  >
                    <ArrowForwardIcon />
                  </Button>
                </>
              }
            />
          </Grid>
        </Grid>
      </Grid>
      <div style={{ marginTop: 12 }}>
        <NetSuiteMappingGrid
          handleDeleteRow={handleDeleteRow}
          handleEditRows={handleEditRows}
          handleCurrentPageChange={handleCurrentPageChange}
          defaultPageSizes={[5, 10, 20, 40, 80, 100]}
          handlePageSizeChange={handlePageSizeChange}
          rows={dataSource}
          pageSize={pageSize}
          totalCount={totalCount}
          //@ts-ignore
          setRows={setDataSource}
          columns={columns}
          columnWidths={columnWidths}
          type={type}
        />
      </div>
    </>
  );
};

import React, { <PERSON><PERSON><PERSON>Handler } from 'react';
import { SubmitHandler } from 'react-hook-form';
import { Condition } from '../graph-ql-query-constructor';
import { RubyField } from '../metadata';
import { PickListOption } from '../pick-list';

export type PaymentRuleStatus = 'Draft' | 'Active';
export type PaymentRuleSchema = {
  id?: string;
  createdDate?: string;
  createdById?: string;
  lastModifiedDate?: string;
  lastModifiedById?: string;
  name?: string;
  status?: PaymentRuleStatus;
  startDate?: string;
  endDate?: string;
  description?: string;
  conditions?: string;
};
export type PaymentRule = {
  id: string;
  name: string;
  status: PaymentRuleStatus;
  startDate: string;
  endDate: string;
  conditions: any[];
  description: string;
};
export type InvoiceActionTarget = {
  action: string | null;
  externalSystemType: string | null;
  externalSystemId: string | null;
  externalSystemName: string | null;
};

export type InvoiceActionOption = {
  action: string | null;
  externalSystemType: string | null;
  name: string | null;
  value: string | null;
};

export interface PaymentRuleProps {
  values: {
    id?: string;
    name?: string;
    groupingField?: string;
    status?: PaymentRuleStatus;
    startDate?: string;
    endDate?: string;
    description?: string;
    conditions?: any[];
  };
  mode?: 'create' | 'edit' | 'view';
  onCancel: MouseEventHandler;
  onSubmit: SubmitHandler<PaymentRule>;
}

export interface EmptyPaymentRuleConditionsProps {
  initPaymentRuleConditions: any;
}

export interface PaymentRuleConditionItem {
  name: string;
  id?: string | null;
  filters?: Condition[];
  action?: string | null;
  externalSystemType?: string | null;
  externalSystemId?: string | null;
}

export interface PaymentRuleConditionItemProps {
  condition: PaymentRuleConditionItem;
  conditionArray: PaymentRuleConditionItem[];
  index: number;
  onChange: Function;
  field: RubyField;
  options: PickListOption[];
}

export interface PaymentRuleFilterProps {
  open: boolean;
  onClose: () => void;
  condition: PaymentRuleConditionItem;
  handleChange: Function;
  conditionArray: PaymentRuleConditionItem[];
  field: RubyField;
  hiddenColumns?: string[];
}

export type BillSysSetting = {
  key: string;
  value: string;
};

import React from 'react';

import FormControl from '@material-ui/core/FormControl';

import DateInput from '../date-input';
import DateTimeInput from '../date-time-input';
import type { FilterBuilderCondition } from '../graph-ql-query-constructor/interface';
import type { RubyObject } from '../metadata/interface';
import MultiSelect from '../multi-select';
import NumberInput from '../number-input';
import PickList from '../pick-list';
import TextInput from '../text-input';
import { GraphQLConverter } from './graphql-query-converter';
import LookupValueInput from './lookup-value-input';
import { SwitchableDateInput } from './switchable-date-input';
import _ from 'lodash';

interface Props {
  useNestedField?: boolean;
  classes: any;
  conditionField: FilterBuilderCondition;
  conditionFieldIndex: number;
  type:
    | 'boolean'
    | 'Boolean'
    | 'pickList'
    | 'Picklist'
    | 'date'
    | 'Date'
    | 'dateTime'
    | 'integer'
    | 'Decimal'
    | 'decimal'
    | 'text'
    | 'TextArea'
    | 'String'
    | 'number'
    | 'datetime'
    | 'bLookup'
    | 'bMasterDetail'
    | 'Reference'
    | string;
  conditions: FilterBuilderCondition[];
  updateConditions: (conditionsCopy: FilterBuilderCondition[]) => void;
  handleChange: (
    selectedField: FilterBuilderCondition,
    index: number,
    value: string,
    changeType: string,
    translateValue: string | null,
    conditions: FilterBuilderCondition[],
    updateConditions: (newConditions: FilterBuilderCondition[]) => void,
  ) => void;
  objectMetadata: RubyObject;
  disabled?: boolean;
  forFilter?: boolean;
}

function getBooleanValue(conditionField: FilterBuilderCondition) {
  if (conditionField.value && conditionField.value.value) {
    if (typeof conditionField.value.value === 'boolean') {
      return conditionField.value.value.toString();
    }
    if (conditionField.value.value.toLowerCase() === 'true') {
      return 'true';
    }
    return 'false';
  }
  return false;
}

const unquote = (str: string) => {
  if ((str.startsWith("'") && str.endsWith("'")) || (str.startsWith('"') && str.endsWith('"'))) {
    return str.slice(1, str.length - 1);
  }
  return str;
};

function getDateTimeValue(conditionField: FilterBuilderCondition) {
  const value = conditionField.value?.value;
  if (!value) {
    return '';
  }
  if (typeof value === 'string') {
    const dateTimeString = unquote(value);
    return dateTimeString;
  }
  if (value instanceof Date) {
    return value;
  }
  console.warn('Unrecognized value type, should be either a string or Date');
  return '';
}

function ValueInput(props: Props) {
  const {
    classes,
    conditionField,
    conditionFieldIndex,
    handleChange,
    conditions,
    updateConditions,
    type,
    objectMetadata,
    disabled,
    forFilter,
    useNestedField,
  }: Props = props;
  const onDateInputChange = (date: string) => {
    props.handleChange(
      conditionField,
      conditionFieldIndex,
      date,
      'value',
      GraphQLConverter.get(type)(date),
      conditions,
      updateConditions,
    );
  };

  if (
    conditionField.operator?.id === ' _is_not_null ' ||
    conditionField.operator?.id === ' _is_null '
  ) {
    return null;
  }

  const fieldToUse = useNestedField
    ? { ...conditionField, field: conditionField.field?.field }
    : conditionField;

  switch (type) {
    case 'Date':
    case 'date':
      if (forFilter) {
        return (
          <SwitchableDateInput
            classes={classes}
            disabled={disabled}
            conditionField={fieldToUse}
            value={
              conditionField.value && conditionField.value.value ? conditionField.value.value : ''
            }
            onDateInputChange={onDateInputChange}
            type={type}
          />
        );
      }

      return (
        <FormControl className={classes.formControl} style={{ flex: 1 }}>
          <DateInput
            disabled={disabled}
            className={classes.dateInput}
            // @ts-ignore
            field={fieldToUse.field}
            required={false}
            value={
              conditionField.value && conditionField.value.value ? conditionField.value.value : ''
            }
            handleInputChange={onDateInputChange}
            name="date-input"
          />
        </FormControl>
      );
    case 'dateTime':
      if (forFilter) {
        return (
          <SwitchableDateInput
            classes={classes}
            disabled={disabled}
            conditionField={fieldToUse}
            value={getDateTimeValue(conditionField)}
            onDateInputChange={onDateInputChange}
            type={type}
          />
        );
      }
      return (
        <FormControl className={classes.formControl} style={{ flex: 1 }}>
          <DateTimeInput
            className={classes.dateInput}
            // @ts-ignore
            field={fieldToUse.field}
            disabled={disabled}
            required={false}
            value={getDateTimeValue(conditionField)}
            name="date-time-input"
            handleInputChange={onDateInputChange}
            dateFormat={'MM/dd/yyyy hh:mm a'}
          />
        </FormControl>
      );
    case 'Decimal':
    case 'decimal':
    case 'currency':
    case 'integer':
      return (
        <FormControl className={classes.formControl} style={{ flex: 1 }}>
          <NumberInput
            // @ts-ignore
            field={fieldToUse.field}
            required={false}
            disabled={disabled}
            value={conditionField.value ? conditionField.value.value : ''}
            name="number-input"
            handleInputChange={(newValue: string) =>
              handleChange(
                conditionField,
                conditionFieldIndex,
                newValue,
                'value',
                GraphQLConverter.get(type)(newValue),
                conditions,
                updateConditions,
              )
            }
          />
        </FormControl>
      );
    case 'Boolean':
    case 'boolean':
      return (
        <FormControl className={classes.formControl} style={{ flex: 1 }}>
          <PickList
            // @ts-ignore
            field={fieldToUse.field}
            required={false}
            disabled={disabled}
            handleInputChange={(newValue: string) =>
              handleChange(
                conditionField,
                conditionFieldIndex,
                newValue,
                'value',
                GraphQLConverter.get(type)(newValue),
                conditions,
                updateConditions,
              )
            }
            options={[
              { name: 'True', value: 'true' },
              { name: 'False', value: 'false' },
            ]}
            value={getBooleanValue(conditionField)}
          />
        </FormControl>
      );
    case 'Picklist':
    case 'pickList':
    case 'multiPickList':
    case 'MultiPicklist':
    case 'currencyIsoCode':
      return conditionField.operator?.id === ' _in ' ? (
        <FormControl className={classes.formControl} style={{ flex: 1 }}>
          <MultiSelect
            // @ts-ignore
            field={fieldToUse.field}
            required={false}
            disabled={disabled}
            value={conditionField.value ? conditionField.value.value : []}
            handleInputChange={(e: any, selectedItems: string) => {
              handleChange(
                conditionField,
                conditionFieldIndex,
                selectedItems,
                'value',
                GraphQLConverter.get(type)(selectedItems),
                conditions,
                updateConditions,
              );
            }}
          />
        </FormControl>
      ) : (
        <FormControl className={classes.formControl} style={{ flex: 1 }}>
          <PickList
            // @ts-ignore
            field={fieldToUse.field}
            required={false}
            disabled={disabled}
            handleInputChange={(newValue: string) =>
              handleChange(
                conditionField,
                conditionFieldIndex,
                newValue,
                'value',
                GraphQLConverter.get(type)(newValue),
                conditions,
                updateConditions,
              )
            }
            options={(
              conditionField.field?.field?.valueSet?.valuePairs ||
              conditionField.field?.valueSet?.valuePairs
            )?.map((currentValuePair) => ({
              name: currentValuePair.name,
              value: currentValuePair.apiName,
            }))}
            value={conditionField.value ? conditionField.value.value : ''}
          />
        </FormControl>
      );
    case 'Reference':
    case 'bLookup':
    case 'lookup':
    case 'bMasterDetail':
      return (
        <FormControl className={classes.formControl} style={{ flex: 1 }}>
          <LookupValueInput
            objectMetadata={objectMetadata}
            conditionField={conditionField}
            disabled={disabled}
            // value={conditionField.value ? conditionField.value.value : ''}
            handleInputChange={(event, newValue) => {
              //@ts-ignore
              const lookupRecordId =
                conditionField.field?.field?.apiName &&
                //@ts-ignore
                newValue[conditionField.field?.field?.apiName]
                  ? //@ts-ignore
                    newValue[conditionField.field?.field?.apiName]
                  : //@ts-ignore
                    newValue.id || newValue[conditionField.field?.nestedApiName];
              handleChange(
                conditionField,
                conditionFieldIndex,
                lookupRecordId,
                'value',
                GraphQLConverter.get(type)(lookupRecordId),
                conditions,
                updateConditions,
              );
            }}
          />
        </FormControl>
      );
    default:
      return (
        <FormControl className={classes.formControl} style={{ flex: 1 }}>
          <TextInput
            // @ts-ignore
            field={conditionField.field?.field || conditionField.field}
            required={false}
            disabled={disabled}
            value={conditionField.value ? conditionField.value.value : ''}
            handleInputChange={(newValue: string) =>
              handleChange(
                conditionField,
                conditionFieldIndex,
                newValue,
                'value',
                GraphQLConverter.get(type)(newValue),
                conditions,
                updateConditions,
              )
            }
          />
        </FormControl>
      );
  }
}

export default ValueInput;

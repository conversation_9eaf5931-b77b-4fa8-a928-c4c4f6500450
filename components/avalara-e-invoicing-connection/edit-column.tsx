import { TableEditColumn } from '@devexpress/dx-react-grid-material-ui';
import React from 'react';
import { <PERSON><PERSON>, Get<PERSON>, Plugin, Template, TemplateConnector } from '@devexpress/dx-react-core';
import { Table } from '@devexpress/dx-react-grid';
import { IconButton } from '@material-ui/core';
import EditIcon from '@material-ui/icons/Edit';
import CopyIcon from '@material-ui/icons/FileCopy';
import CancelIcon from '@material-ui/icons/Cancel';
import DeleteIcon from '@material-ui/icons/Delete';
import SaveIcon from '@material-ui/icons/Save';
import UndoIcon from '@material-ui/icons/Undo';
import { EditColumnProps } from '../price-book-entry-grid/interface';
import Tooltip from '@material-ui/core/Tooltip';
import { makeStyles } from '@material-ui/core/styles';
import { RowEditStateFlag } from '../price-book-entry-grid/interface';
import { DollarCircleIcon } from '../icons';

const TABLE_EDIT_COMMAND_TYPE = Symbol.for('editCommand');
const pluginDependencies = [{ name: 'EditingState' }, { name: 'Table' }];

const useStyles = makeStyles({
  command: {
    '&:hover': {
      color: '#5132BB',
    },
  },
});

export type CommandProps = {
  id:
    | 'duplicate'
    | 'add'
    | 'edit'
    | 'delete'
    | 'commit'
    | 'cancel'
    | 'cancelDelete'
    | 'editPriceTags';
  text: string;
  onExecute: () => void | Promise<void>;
  Icon: React.FC<any>;
};

const Command: React.FC<CommandProps> = (props) => {
  const { onExecute, id, text, Icon } = props;
  const classes = useStyles();
  return (
    <Tooltip title={text || ''} placement="bottom" arrow>
      <IconButton onClick={onExecute} type="button">
        <Icon className={classes.command} />
      </IconButton>
    </Tooltip>
  );
};

export type IsSpecificRowFn = (tableRow: any) => boolean;

export const isAddedTableRow: IsSpecificRowFn = (tableRow) => {
  return tableRow.type?.toString() === 'Symbol(edit)' && tableRow.row?.id?.endsWith('_temp');
};

export const isEditTableRow: IsSpecificRowFn = (tableRow) =>
  tableRow.type?.toString() === 'Symbol(edit)';

export const isDeletedTableRow: IsSpecificRowFn = (tableRow) => {
  return tableRow.row[RowEditStateFlag] === 'deleted';
};

export const isSystemDefaultRow: IsSpecificRowFn = (tableRow) => {
  return tableRow.row.id?.includes('system');
};

class EditColumn extends React.PureComponent<EditColumnProps> {
  render() {
    const tableColumnsComputed = ({ tableColumns }: Getters) => [
      ...tableColumns,
      {
        width: 240,
        key: TABLE_EDIT_COMMAND_TYPE.toString(),
        type: TABLE_EDIT_COMMAND_TYPE,
      },
    ];

    const { onDuplicateRow, onCancelDeleteRow, onEditPriceTags } = this.props;

    return (
      <Plugin name="TableEditColumn" dependencies={pluginDependencies}>
        <Getter name="tableColumns" computed={tableColumnsComputed} />

        {/* Data Rows */}
        <Template
          name={'tableCell'}
          predicate={({ tableRow, tableColumn }: any) => {
            return tableRow.row && tableColumn.type === TABLE_EDIT_COMMAND_TYPE;
          }}
        >
          {(params: Table.CellProps) => {
            const rowIds = [params.tableRow.rowId];
            return (
              <TemplateConnector>
                {(getters, actions) => {
                  const row = params.tableRow.row;
                  const isAdded = isAddedTableRow(params.tableRow);
                  const isEdit = isEditTableRow(params.tableRow);
                  const isDeleted = isDeletedTableRow(params.tableRow);
                  const isSystemDefault = isSystemDefaultRow(params.tableRow);
                  return (
                    <TableEditColumn.Cell
                      {...params}
                      row={row}
                      style={{
                        textAlign: 'left',
                      }}
                    >
                      {/* {!isDeleted && (
                        <Command
                          id="duplicate"
                          text={'Duplicate'}
                          Icon={CopyIcon}
                          onExecute={() => {
                            if (onDuplicateRow) {
                              onDuplicateRow(
                                {
                                  row: row,
                                },
                                actions,
                                getters,
                              );
                            }
                          }}
                        />
                      )} */}
                      {!isEdit && !isDeleted && (
                        <Command
                          id={'edit'}
                          text={'Edit'}
                          Icon={EditIcon}
                          onExecute={() => {
                            actions.startEditRows({
                              rowIds: [row.id],
                            });
                          }}
                        />
                      )}
                      {isEdit && (
                        <Command
                          id={'cancel'}
                          text={'Cancel'}
                          Icon={UndoIcon}
                          onExecute={() => {
                            if (isAdded) {
                              actions.deleteRows({ rowIds });
                              actions.commitDeletedRows({ rowIds });
                              actions.stopEditRows({ rowIds: [row.id] });
                            } else {
                              actions.cancelChangedRows({ rowIds: [row.id] });
                              actions.stopEditRows({ rowIds: [row.id] });
                            }
                          }}
                        />
                      )}
                      {isEdit && (
                        <Command
                          id="commit"
                          Icon={SaveIcon}
                          text={'commit'}
                          onExecute={() => {
                            actions.commitChangedRows({ rowIds });
                            actions.stopEditRows({ rowIds });
                          }}
                        />
                      )}
                      {/* {!isEdit && !isDeleted && !isSystemDefault && (
                        <Command
                          id="delete"
                          text={'Delete'}
                          Icon={DeleteIcon}
                          onExecute={() => {
                            actions.deleteRows({ rowIds });
                            actions.commitDeletedRows({ rowIds });
                          }}
                        />
                      )} */}
                      {/* {isDeleted && (
                        <Command
                          id="cancelDelete"
                          text={'Cancel Delete'}
                          Icon={UndoIcon}
                          onExecute={() => {
                            if (onCancelDeleteRow) {
                              onCancelDeleteRow({ row }, actions, getters);
                            }
                          }}
                        />
                      )} */}
                      {!row?.id?.endsWith('_temp') && onEditPriceTags && (
                        <Command
                          id={'editPriceTags'}
                          text={'Edit Price Tags'}
                          Icon={DollarCircleIcon}
                          onExecute={() => {
                            if (onEditPriceTags) {
                              onEditPriceTags({ row }, actions, getters);
                            }
                          }}
                        />
                      )}
                    </TableEditColumn.Cell>
                  );
                }}
              </TemplateConnector>
            );
          }}
        </Template>
      </Plugin>
    );
  }
}

export default EditColumn;

import type { SetStateAction } from 'react';
import type {
  CardAction,
  GridViewMetadata,
  RubyField,
  RubyObject,
} from '@nue-apps/ruby-ui-component';
import type { LineBucketItem, LineItem } from '../revenue-builder-types';

export interface Milestone {
  id?: string;
  name: string;
  percentage: number;
  quantity: number;
  actualQuantity: number;
  price: number;
  startDate: string;
  subscriptionStartDate: string;
  parentId: string;
  rootId: string;
  nameChanged?: boolean;
  invoice?: string;
  invoiceNumber?: string;
  milestones?: string;
}

export interface Props {
  currencyIsoCode: string;
  openCreateMilestone: boolean;
  handleCloseCreateMilestone: () => void;
  activeLineItem: LineItem;
  handleConfirmMilestones: any;
}

export interface MilestoneLineItemProps {
  milestoneItem: Milestone;
  milestoneItems: Milestone[];
  row: LineItem;
  updateAllMilestonesDetail: () => void;
  setRefreshFlag: any;
  refreshFlag: any;
  currencyIsoCode?: string;
  handleDeleteMilestone: any;
  index: number;
  setMilestoneItems: any;
  allMilestonesTotalPercentage: any;
  allMilestonesTotalPrice: any;
}

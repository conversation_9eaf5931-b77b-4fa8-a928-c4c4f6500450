import React from 'react';
import Typography from '@material-ui/core/Typography';
import Link from '@material-ui/core/Link';
import { makeStyles } from '@material-ui/core/styles';
import { ChangeItemText } from './interface';
import dayjs from 'dayjs';

const useStyles = makeStyles({
  itemText: {
    opacity: 0.7,
    color: '#000000',
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
    opacity: '1 !important',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
});
const CancellationText: React.FC<ChangeItemText> = (props) => {
  const { changeItem } = props;

  const classes = useStyles();

  return (
    <Typography variant="body2" component="p" display="inline">
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        {changeItem.request.objectType}{' '}
      </Typography>
      <Typography variant="body2" className={classes.textUnderline} component="p" display="inline">
        {changeItem.request.assetName}{' '}
      </Typography>
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        will be cancelled on{' '}
      </Typography>
      <Link
        variant="body2"
        href="#"
        onClick={() => {}}
        className={classes.textHighlight}
        component="p"
        display="inline"
      >
        {
          //@ts-ignore
          dayjs(changeItem.request.cancellationDate).format('MM/DD/YYYY')
        }
      </Link>
    </Typography>
  );
};

export default CancellationText;

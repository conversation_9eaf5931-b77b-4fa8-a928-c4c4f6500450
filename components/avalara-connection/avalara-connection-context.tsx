import React from 'react';

import type { Company, TaxIntegrationResponse, UpsertTaxIntegrationRequestBody } from './interface';

export interface AvalaraConnectionService {
  createIntegration: (
    createConnectionRequest: UpsertTaxIntegrationRequestBody,
  ) => Promise<{ id: string }>;
  getIntegration: () => Promise<TaxIntegrationResponse>;
  updateIntegration: (updateConnectionRequest: UpsertTaxIntegrationRequestBody) => Promise<void>;
  activateIntegration: (id: string) => Promise<{ success: boolean; error: string }>;
  disableIntegration: (id: string) => Promise<void>;
  testIntegration: (id: string) => Promise<{ success: boolean; error: string }>;
  getCompanies: () => Promise<Company[]>;
  forceActivateIntegration: (
    integrationTypeActivate: string,
  ) => Promise<{ success: boolean; error: string }>;
  getActiveIntegration: () => Promise<TaxIntegrationResponse | null>;
}

export const AvalaraConnectionContext = React.createContext<AvalaraConnectionService | null>(null);

export default AvalaraConnectionContext;

import Button from '@material-ui/core/Button';
import { makeStyles } from '@material-ui/core/styles';
import ExpandLessIcon from '@material-ui/icons/ExpandLess';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import React from 'react';
import { Props } from './interface';

const useStyles = makeStyles({
  root: {
    padding: '0px 20px',
    position: 'fixed',
    zIndex: 100,
    top: '300px',
    right: '-66px',
    transform: 'rotate(270deg)',
    fontSize: '.875rem',
    display: 'flex',
    alignItems: 'center',
    minHeight: '47px',
    color: '#6239eb',
    border: '1px solid #f8f5fe',
    backgroundColor: '#f2ecfe',
    borderRadius: '5px',
    cursor: 'pointer',
    fontWeight: 500,
    textTransform: 'none',
    '&:hover': {
      // you want this to be the same as the backgroundColor above
      backgroundColor: '#f2ecfe',
    },
  },
});

const BundlePreviewBtn: React.FC<Props> = ({
  handleClick,
  text = 'Bundle Preview',
  expanded = false,
}) => {
  const classes = useStyles();

  return (
    <Button
      type="button"
      className={classes.root}
      onClick={handleClick}
      endIcon={expanded ? <ExpandMoreIcon /> : <ExpandLessIcon />}
    >
      {text}
    </Button>
  );
};

export default BundlePreviewBtn;

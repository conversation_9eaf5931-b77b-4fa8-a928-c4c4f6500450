export const TypeDisplay = new Map();

TypeDisplay.set('id', 'Id');

TypeDisplay.set('text', 'Text');

TypeDisplay.set('string', 'Text');

TypeDisplay.set('phone', 'Phone');

TypeDisplay.set('email', 'Email');

TypeDisplay.set('currency', 'Currency');

TypeDisplay.set('boolean', 'Boolean');

TypeDisplay.set('Boolean', 'Boolean');

TypeDisplay.set('autoNumber', 'AutoNumber');

TypeDisplay.set('date', 'Date');

TypeDisplay.set('dateTime', 'Date Time');

TypeDisplay.set('bLookup', 'Name');

TypeDisplay.set('bMasterDetail', 'Name');

TypeDisplay.set('integer', 'Integer');

TypeDisplay.set('pickList', 'PickList');

TypeDisplay.set('Picklist', 'PickList');

TypeDisplay.set('bId', 'Id');

import React from 'react';
import Tooltip from '@material-ui/core/Tooltip';
import { makeStyles } from '@material-ui/core/styles';
import IconButton from '@material-ui/core/IconButton';
import { Props } from './interface';

const useStyles = makeStyles({
  gridIcon: {
    background: 'transparent',
    border: 'none',
    color: '#6239eb',
    cursor: 'pointer',
    width: '18px',
    height: '18px',
  },
});

const IconBtn: React.FC<Props> = ({
  iconAlt,
  handleClick,
  Icon,
  tooltipText,
  viewBox,
  fontSize,
}) => {
  const classes = useStyles();

  return (
    <Tooltip title={tooltipText || ''} placement="bottom" arrow>
      <IconButton
        aria-label={iconAlt}
        className={classes.gridIcon}
        onClick={() => {
          if (typeof handleClick === 'function') {
            handleClick();
          }
        }}
      >
        <Icon fontSize={fontSize ? fontSize : undefined} viewBox={viewBox || undefined} />
      </IconButton>
    </Tooltip>
  );
};

export default IconBtn;

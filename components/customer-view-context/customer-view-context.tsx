import React from 'react';
import {
  CardAction,
  GridSearchBarProps as GridSearchConfigs,
  RubyObject,
  VariantEnums,
} from '@nue-apps/ruby-ui-component';
import { CustomerViewProps as CustomerViewConfigs } from '../customer-view';
import { Customer } from '../revenue-builder-types/interface';

export type EventHandlerResult = {
  succeeded: boolean;
  nextAction?: 'none' | 'refresh' | 'refresh-current' | 'navigate-tab';
  orderNumber?: string;
  message?: {
    variant: VariantEnums;
    title: string;
    detail: string;
  };
};

export type GridItemActionEventHandler = (argument: {
  object: any;
  objectMetadata: RubyObject;
  action: CardAction;
}) => EventHandlerResult | Promise<EventHandlerResult>;

export interface CustomerViewService {
  getCustomerViewConfig: () => {
    customer: Customer;
    searchConfigs: Partial<GridSearchConfigs>;
    customerViewConfigs: CustomerViewConfigs;
  };
  actionEventHandler: GridItemActionEventHandler;
}

export interface CustomerViewFacade {
  customerViewService?: CustomerViewService;
  showChildrenAccounts?: boolean;
  setShowChildrenBtn?: React.Dispatch<React.SetStateAction<boolean>>;
}

export const CustomerViewContext = React.createContext<CustomerViewFacade>({});

export default CustomerViewContext;

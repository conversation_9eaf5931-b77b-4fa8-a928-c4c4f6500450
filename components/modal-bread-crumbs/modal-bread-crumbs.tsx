import React from 'react';

import Breadcrumbs from '@material-ui/core/Breadcrumbs';
import { makeStyles } from '@material-ui/core/styles';

import { Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  navLabel: {
    fontSize: '.75rem',
    opacity: 0.7,
    color: '#000000',
    cursor: 'pointer',
  },
  navLabelSelected: {
    fontWeight: 'bold',
    color: '#6239eb',
    fontSize: '.75rem',
    cursor: 'pointer',
  },
  labelWrap: {
    maxWidth: '500px',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    display: 'inline-block',
  },
});

const ModalBreadCrumbs: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const classes = useStyles();

  return (
    <Breadcrumbs>
      {props.breadcrumbs.map((item: any, index: number) => {
        return (
          <span key={item.id} className={classes.labelWrap}>
            {index === props.breadcrumbs.length - 1 ? (
              <span
                className={classes.navLabelSelected}
                onClick={() => props.handleOnClick(index, item)}
              >
                {item.name}
              </span>
            ) : (
              <span className={classes.navLabel} onClick={() => props.handleOnClick(index, item)}>
                {item.name}
              </span>
            )}
          </span>
        );
      })}
    </Breadcrumbs>
  );
};

export default ModalBreadCrumbs;

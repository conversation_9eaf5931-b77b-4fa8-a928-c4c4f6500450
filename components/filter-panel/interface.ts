import type {
  Delete<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  UpdateFilters<PERSON>and<PERSON>,
  WarningBeforeUpdateProps,
} from '../filter-builder';
import type { Order<PERSON>yField, RubyFilter } from '../graph-ql-query-constructor/interface';
import type { RubyObject } from '../metadata/interface';
import { createContext } from 'react';

export interface Props extends WarningBeforeUpdateProps {
  openFilters: boolean;
  handleCloseFilters: () => void;
  searchResult: string;
  handleSearch: (
    searchResult: string,
    filters: RubyFilter[],
    orderByFields?: OrderByField[],
  ) => Promise<void>;
  deleteFilter: DeleteFilterHandler;
  objectMetadata: RubyObject;
  savedFilters: RubyFilter[];
  updateListOfSavedFilters: (filters: RubyFilter[]) => void;
  saveFilter: SaveFilterHandler;
  updateFilters: UpdateFiltersHandler;
  allMetadatas?: RubyObject[];
}

export interface FilterOperationPermissionValidator {
  hasPermissionOnFilter: (filter: <PERSON><PERSON>ilter, operation: 'create' | 'update' | 'delete') => boolean;
}

export const FilterOperationPermissionValidatorContext =
  createContext<FilterOperationPermissionValidator>({
    hasPermissionOnFilter: (filter: RubyFilter, operation: 'create' | 'update' | 'delete') => true,
  });

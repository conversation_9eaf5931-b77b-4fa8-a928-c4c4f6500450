import React, { useEffect, useState } from 'react';

import { Grid, Tooltip, Typography, makeStyles } from '@material-ui/core';
import InfoIcon from '@material-ui/icons/Info';
import dayjs from 'dayjs';

import type { Milestone } from '../create-milestone';
import DialogComponent from '../dialog-component';
import type { ColumnConfig, RubyObject } from '../metadata/interface';
import type { IsCellEditableProps, IsCellEditedProps } from '../ruby-grid';
import RubyGrid from '../ruby-grid';
import { CustomerViewPageTab } from '../ruby-list-view-local-state-context';
import { useRubySnackbar } from '../ruby-notifier';
import useConfirmDialog from '../use-confirm-dialog';

export interface InvoiceByOrderItem {
  invoiceId: string;
  invoiceNumber: string;
}

export interface MilestonesChange {
  orderProductId: string;
  startDate: string;
}

export interface Props {
  open: boolean;
  onClose: () => void;
  onSaveAndClose?: () => void;
  object?: any;
  orders?: any[];
  setActiveTabDetails?: Function;
  invoiceTabIndex?: number;
  metadata?: RubyObject;
  setManageMilestoneDialogProps?: Function;
  getInvoicesByOrderItemIds: (params: {
    orderItemIds: string[];
  }) => Promise<Record<string, InvoiceByOrderItem[]>>;
  UpdateMileStonesStartDate: (params: { orderItems: MilestonesChange[] }) => Promise<void>;
  AddUpdateMilestoneToCart: (row: Milestone) => void;
}

const useStyles = makeStyles({
  container: {
    marginBottom: '30px',
    width: '100%',
    overflow: 'auto',
  },
  wrapper: {
    marginLeft: '10px',
  },
  tipContainer: {
    marginTop: '5px',
  },
  info: {
    fontSize: '14px',
    color: '#999999',
    marginRight: '5px',
  },
});

const ManageMilestoneDialog: React.FC<Props> = (userProps: Props) => {
  const classes = useStyles();
  const { showSnackbar, Snackbar } = useRubySnackbar();
  const {
    open,
    onClose,
    object,
    orders,
    setActiveTabDetails,
    invoiceTabIndex,
    metadata,
    setManageMilestoneDialogProps,
    getInvoicesByOrderItemIds,
    UpdateMileStonesStartDate,
    onSaveAndClose,
    AddUpdateMilestoneToCart,
  } = userProps;

  const [scheduledRows, setScheduledRows] = useState<Milestone[]>([]);
  const [originalScheduledRows, setOriginalScheduledRows] = useState<Milestone[]>([]);
  const [invoicedRows, setInvoicedRows] = useState<Milestone[]>([]);
  const [hasUnsavedChange, setHasUnsavedChange] = useState(false);
  const [refreshFlag, setRefreshFlag] = useState(1);
  const [loading, setLoading] = useState(false);
  const [invoicedItems, setInvoicedItems] = useState<Record<string, InvoiceByOrderItem[]>>();
  const [saveAndClose, setSaveAndClose] = useState(false);

  const { showConfirmDialog, ConfirmDialog } = useConfirmDialog({
    submitButtonText: 'Yes',
    cancelButtonText: 'No',
    onOk: async () => {
      setLoading(true);
      const milestoneChanges = scheduledRows
        .filter(
          (s, index) =>
            s.subscriptionStartDate !== originalScheduledRows[index].subscriptionStartDate,
        )
        .map((s) => {
          return {
            orderProductId: s.id!,
            startDate: dayjs(s.subscriptionStartDate).format('YYYY-MM-DD'),
          };
        });
      if (milestoneChanges.length > 0) {
        try {
          await UpdateMileStonesStartDate({
            orderItems: milestoneChanges,
          });
          if (saveAndClose && onSaveAndClose) {
            onSaveAndClose();
            return;
          }
          setHasUnsavedChange(false);
          setScheduledRows([...scheduledRows]);
          setInvoicedRows([...invoicedRows]);
          setRefreshFlag(refreshFlag + 1);
          setLoading(false);
          showSnackbar('confirm', 'Success', `Milestone Date is updated successfully.`);
        } catch (e) {
          setLoading(false);
          showSnackbar('error', 'Error', e.body.message);
        }
      }
    },
    onCancel: () => {
      setSaveAndClose(false);
    },
  });

  const handleUpdateMilestoneAction = async (row: Milestone) => {
    const beforeUpdateChangeCartLocalState = await window.localStorage.getItem('change-cart');
    await AddUpdateMilestoneToCart(row);
    const afterUpdateChangeCartLocalState = await window.localStorage.getItem('change-cart');
    if (beforeUpdateChangeCartLocalState !== afterUpdateChangeCartLocalState) {
      showSnackbar(
        'confirm',
        'Success',
        'The request to update the milestone is added to the Change Cart.  Please checkout the Change Cart to complete the change.',
      );
    }
  };

  const navigateToInviceDetail = (invoiceId: string) => {
    if (setActiveTabDetails && invoiceTabIndex && invoiceTabIndex !== -1 && metadata) {
      setActiveTabDetails(
        {
          object: object,
          objectMetadata: metadata,
          action: {
            id: 'viewInvoice',
            name: 'View Invoice',
            invoiceIds: [invoiceId],
          },
        },
        metadata.name,
      );
    }
    if (setManageMilestoneDialogProps) {
      setManageMilestoneDialogProps({
        open: false,
      });
    }
  };

  const setup = async () => {
    if (orders && orders.length > 0) {
      setLoading(true);
      const invoiceItems = await getInvoicesByOrderItemIds({
        orderItemIds: orders.map((o) => o.id),
      });
      setInvoicedItems(invoiceItems);
      const sRows: any[] = [];
      const iRows: any[] = [];
      Object.keys(invoiceItems).forEach((invoiceKey: any, index: number) => {
        if (invoiceItems[invoiceKey].length === 0) {
          sRows.push(orders.find((o) => o.id === invoiceKey));
        } else {
          iRows.push(orders.find((o) => o.id === invoiceKey));
        }
      });
      sRows.sort((a, b) => +new Date(a.subscriptionStartDate) - +new Date(b.subscriptionStartDate));
      iRows.sort((a, b) => +new Date(a.subscriptionStartDate) - +new Date(b.subscriptionStartDate));
      setScheduledRows(sRows);
      setOriginalScheduledRows([...sRows]);
      setInvoicedRows(iRows);
      setLoading(false);
    }
  };

  useEffect(() => {
    setup();
  }, [orders]);

  const invoicedColumns = [
    {
      name: 'milestones',
      title: 'Milestone',
      type: 'text',
      apiName: 'milestones',
    },
    {
      name: 'totalAmount',
      title: 'Total Amount',
      type: 'currency',
      apiName: 'totalAmount',
    },
    {
      name: 'quantity',
      title: 'Quantity',
      type: 'number',
      apiName: 'quantity',
    },
    {
      name: 'subscriptionStartDate',
      title: 'Milestone Date',
      type: 'Date',
      apiName: 'subscriptionStartDate',
    },
    {
      name: 'invoice',
      title: 'Invoice',
      type: 'text',
      apiName: 'invoice',
      cellRenderer: async (props: any) => {
        const { row } = props;
        if (invoicedItems && invoicedItems[row.id] && invoicedItems[row.id].length > 0) {
          const invoice = invoicedItems[row.id];
          return (
            <div>
              {
                <div>
                  {invoice.map((i) => {
                    return (
                      <p key={i.invoiceId}>
                        <a onClick={() => navigateToInviceDetail(i.invoiceId)}>{i.invoiceNumber}</a>
                      </p>
                    );
                  })}
                </div>
              }
            </div>
          );
        } else {
          return <div />;
        }
      },
    },
  ];

  const scheduledColumns = [
    {
      name: 'milestones',
      title: 'Milestone',
      type: 'text',
      apiName: 'milestones',
      updatable: false,
    },
    {
      name: 'totalAmount',
      title: 'Total Amount',
      type: 'currency',
      apiName: 'totalAmount',
      updatable: false,
    },
    {
      name: 'quantity',
      title: 'Quantity',
      type: 'number',
      apiName: 'quantity',
      updatable: false,
    },
    {
      name: 'subscriptionStartDate',
      title: 'Milestone Date',
      type: 'Date',
      apiName: 'subscriptionStartDate',
      updatable: true,
    },
    {
      name: 'action',
      title: 'Action',
      type: 'text',
      apiName: 'action',
      cellRenderer: (props: any) => {
        const { row } = props;
        return <a onClick={() => handleUpdateMilestoneAction(row)}>Update Milestone</a>;
      },
    },
  ];

  const columnWidths = [
    { columnName: 'milestones', width: 180 },
    { columnName: 'totalAmount', width: 180 },
    { columnName: 'quantity', width: 100 },
    { columnName: 'subscriptionStartDate', width: 180 },
    { columnName: 'invoice', width: 150 },
    { columnName: 'action', width: 180 },
  ];

  const columnOrder = ['subscriptionStartDate'];

  const isCellEditable = (row: Milestone, column: ColumnConfig) => {
    if (column.apiName === 'subscriptionStartDate') {
      return true;
    }
    return false;
  };

  const isCellEdited = ({ column, changedField, row }: IsCellEditedProps) => {
    const originalRow = originalScheduledRows.find((o) => o.id === row.id);
    return (
      column &&
      column?.name &&
      changedField &&
      changedField.hasOwnProperty(column.name) &&
      row[column.name] !== null &&
      row[column.name] !== undefined &&
      originalRow &&
      //@ts-ignore
      row[column.name] !== originalRow[column.name]
    );
  };

  const isCellEditedAndEditable = ({ column, changedField, row }: IsCellEditableProps) => {
    return (
      column?.name &&
      changedField &&
      changedField.hasOwnProperty(column.name) &&
      isCellEditable(row, column)
    );
  };

  const isCellEditedAndNotEditable = ({ column, changedField, row }: IsCellEditableProps) => {
    return (
      column?.name &&
      changedField &&
      changedField.hasOwnProperty(column.name) &&
      !isCellEditable(row, column) &&
      row[column.name] !== null
    );
  };

  const columnExtensions = scheduledColumns.map((column) => {
    return { columnName: column.apiName, editingEnabled: column.updatable };
  });

  const handleUpdateRows = (
    rows: Milestone[],
    changedValue: Record<string, any>,
    originalRows: Map<string, Milestone>,
  ) => {
    Object.keys(changedValue).forEach((key) => {
      const row = rows.find((r) => r.id === key);
      row!.subscriptionStartDate = dayjs(changedValue[key].subscriptionStartDate).format(
        'YYYY-MM-DD',
      );
    });
    setScheduledRows(rows);
    let hasChange = false;
    rows.forEach((r) => {
      const originalRow = originalScheduledRows.find((o) => o.id === r.id);
      if (originalRow && originalRow.subscriptionStartDate !== r.subscriptionStartDate) {
        hasChange = true;
      }
    });
    setHasUnsavedChange(hasChange);
    return true;
  };

  const handleSaveUpdateMilestone = () => {
    showConfirmDialog({
      title: ' ',
      message: 'The milestone dates will take effect immediately.  Do you want to proceed?',
    });
  };

  const getDialogActions = () => {
    if (orders && orders.length > 0) {
      if (hasUnsavedChange) {
        return {
          rightButtons: [
            {
              text: 'Save and Close',
              onClick: async () => {
                await handleSaveUpdateMilestone();
                setSaveAndClose(true);
              },
            },
            {
              text: 'Save',
              onClick: async () => {
                await handleSaveUpdateMilestone();
              },
            },
          ],
          leftButtons: [
            {
              onClick: () => {
                onClose();
              },
              text: 'Cancel',
            },
          ],
        };
      } else {
        return {
          rightButtons: [],
          leftButtons: [],
        };
      }
    } else {
      return {
        rightButtons: [
          {
            text: 'Close',
            onClick: async () => {
              onClose();
            },
          },
        ],
        leftButtons: [],
      };
    }
  };

  return (
    <>
      <DialogComponent
        open={open}
        title={`Manage Milestones`}
        width={orders && orders.length > 0 ? 'md' : 'sm'}
        actions={getDialogActions()}
        handleClose={() => onClose()}
      >
        {orders && orders.length > 0 ? (
          <Grid>
            <Grid container xs={12} className={classes.container}>
              <Grid item className={classes.wrapper}>
                <Typography variant="h6" component="p">
                  {'Invoiced'}
                </Typography>
                <Grid item container className={classes.tipContainer}>
                  <Grid item>
                    <Tooltip title={''} arrow placement="top">
                      <InfoIcon className={classes.info} />
                    </Tooltip>
                  </Grid>
                  <Grid item className={classes.info}>
                    These milestones have been invoiced. You need to cancel the invoices before
                    making any changes to the milestones.
                  </Grid>
                </Grid>
              </Grid>
              <RubyGrid
                pageSize={50}
                showColumnChooser
                sort={false}
                enablePaging={false}
                columns={invoicedColumns}
                columnOrder={columnOrder}
                columnWidths={columnWidths}
                rows={invoicedRows}
                loading={loading}
                getRowId={(row) => {
                  return row.id;
                }}
              />
            </Grid>
            <Grid container xs={12} className={classes.container}>
              <Grid item className={classes.wrapper}>
                <Typography variant="h6" component="p">
                  {'Scheduled'}
                </Typography>
                <Grid item container className={classes.tipContainer}>
                  <Grid item>
                    <Tooltip title={''} arrow placement="top">
                      <InfoIcon className={classes.info} />
                    </Tooltip>
                  </Grid>
                  <Grid item className={classes.info}>
                    These milestones have been scheduled but not invoiced. You may change the
                    milestone dates, amounts, or split the milestones.
                  </Grid>
                </Grid>
              </Grid>
              <RubyGrid
                inlineEdit
                showChangeHistory
                showColumnChooser
                readOnly={false}
                pageSize={50}
                sort={false}
                enablePaging={false}
                columns={scheduledColumns}
                columnOrder={columnOrder}
                columnWidths={columnWidths}
                rows={scheduledRows}
                isCellEditable={isCellEditable}
                isCellEdited={isCellEdited}
                isCellEditedAndEditable={isCellEditedAndEditable}
                isCellEditedAndNotEditable={isCellEditedAndNotEditable}
                handleUpdateRows={handleUpdateRows}
                editingColumnStateExtensions={columnExtensions}
                getRowId={(row) => {
                  return row.id;
                }}
                loading={loading}
                key={refreshFlag}
              />
            </Grid>
          </Grid>
        ) : (
          <Grid>
            <Typography variant="subtitle1">{'There are no milestones available.'}</Typography>
          </Grid>
        )}
        <Snackbar />
      </DialogComponent>
      <ConfirmDialog />
    </>
  );
};

export default ManageMilestoneDialog;

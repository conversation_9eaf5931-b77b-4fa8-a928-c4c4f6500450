import assetFieldSetMetadata from './asset-field-set-metadata';
import creditFieldSetMetadata from './credit-field-set-metadata';
import creditMemosFieldSetMetadata from './credit-memo-field-set-metadata';
import customerFieldSetMetadata from './customer-field-set-metadata';
import entitlementFieldSetMetadata from './entitlement-field-set-metadata';
import includedAssetFieldSetMetadata from './included-assets-field-set-metadata';
import invoiceFieldSetMetadata from './invoice-field-set-metadata';
import orderFieldSetMetadata from './order-field-set-metadata';
import productFieldSetMetadata from './product-field-set-metadata';
import productSearchFieldSetMetadata from './product-search-field-set-metadata';
import revenueContractsFieldSetMetadata from './revenue-contracts-field-set-metadata';
import subscriptionFieldSetMetadata from './subscription-field-set-metadata';
import subscriptionPricingFieldSet from './subscription-pricing-field-set-metadata';
import usageFieldSetMetadata from './usage-field-set-metadata';
import paymentMethodsFieldSetMetadata from './payment-methods-field-set-metadata';

const registry = new Map();
registry.set('CustomerFields', customerFieldSetMetadata);
registry.set('AssetFields', assetFieldSetMetadata);
registry.set('SubscriptionFields', subscriptionFieldSetMetadata);
registry.set('SubscriptionPricingFields', subscriptionPricingFieldSet);
registry.set('OrderFields', orderFieldSetMetadata);
registry.set('ProductColumns', productFieldSetMetadata);
registry.set('ProductSearchColumns', productSearchFieldSetMetadata);
registry.set('IncludedAssetsFields', includedAssetFieldSetMetadata);
registry.set('EntitlementFields', entitlementFieldSetMetadata);
registry.set('InvoiceFields', invoiceFieldSetMetadata);
registry.set('UsageFields', usageFieldSetMetadata);
registry.set('CreditsFields', creditFieldSetMetadata);
registry.set('CreditMemoFields', creditMemosFieldSetMetadata);
registry.set('RevenueContractsFields', revenueContractsFieldSetMetadata);
registry.set('PaymentMethodFields', paymentMethodsFieldSetMetadata);

const FieldSetMetadataApi = {
  getFieldSetMetadata: (fieldSetApiName: string) => {
    if (registry.has(fieldSetApiName)) {
      return registry.get(fieldSetApiName);
    }
    throw new Error(`Unknown fieldSetApiName: ${fieldSetApiName}`);
  },
};

export default FieldSetMetadataApi;

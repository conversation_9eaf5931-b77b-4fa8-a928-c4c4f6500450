import React from 'react';
import { CronFormControlProps } from './interface';
import { Grid, MenuItem, RadioGroup, Select } from '@material-ui/core';
import FormControl from '@material-ui/core/FormControl';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Radio from '@material-ui/core/Radio';
import { makeStyles } from '@material-ui/core/styles';
import SelectInputBase from '../select-input-base';
import { HourConst, MinuteConst } from '../common/cron-const';
import RubyCheckbox from '../checkbox';

const useStyles = makeStyles({
  titleLabel: {
    textTransform: 'uppercase',
    color: '#9c9c9c',
    fontWeight: 'bold',
    fontSize: '.80rem',
    marginTop: '4px',
  },
  formControl: {
    flexDirection: 'row',
    width: '100%',
  },
  optionLabel: {
    '& > span': {
      color: '#6e6e6e',
      fontSize: '.9rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
  },
});

const defaultProps = {};

export const OnDemandCronExpression: React.FC<CronFormControlProps> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const {
    onDemandFrequency,
    setOnDemandFrequency,
    cronExpression,
    setCronExpression,
    runAt,
    setRunAt,
    disabled,
    runImmediately = false,
    setRunImmediately,
  } = props;

  const styles = useStyles();

  return (
    <>
      <FormControl className={styles.formControl}>
        <Grid container item spacing={3} xs={12}>
          <Grid container item spacing={2} xs={12} style={{ marginTop: '3px' }}>
            <Grid item lg={2} xs={12} className={styles.titleLabel}>
              FREQUENCY
            </Grid>
            <Grid item lg={4} xs={12}>
              <RadioGroup
                aria-label="metadata-radio"
                row
                name={'FREQUENCY'}
                value={onDemandFrequency}
                onChange={(event, value) => {
                  if (cronExpression) {
                    if (value === 'Everyday') {
                      const copyCronExpression = cronExpression;
                      copyCronExpression[3] = ' *';
                      copyCronExpression[5] = ' ?';
                      setCronExpression(copyCronExpression);
                    } else {
                      const copyCronExpression = cronExpression;
                      copyCronExpression[3] = ' ?';
                      copyCronExpression[5] = ' MON-FRI';
                      setCronExpression(copyCronExpression);
                    }
                  }
                  if (typeof setOnDemandFrequency === 'function') {
                    setOnDemandFrequency(value);
                  }
                }}
              >
                <FormControlLabel
                  key={'Once'}
                  value={'Once'}
                  control={<Radio size="small" />}
                  label={'Once'}
                  disabled={disabled}
                  checked={true}
                  className={styles.optionLabel}
                />
              </RadioGroup>
            </Grid>
          </Grid>
          <Grid container item spacing={3} xs={12}>
            <Grid item lg={2} xs={4} className={styles.titleLabel}>
              RUN AT
            </Grid>
            <Grid item xs={8}>
              <Grid item lg={2} xs={2} style={{ display: 'contents' }}>
                <RubyCheckbox
                  field={{
                    apiName: 'runImmediately',
                    name: 'Run Immediately',
                    type: 'boolean',
                  }}
                  value={runImmediately}
                  disabled={disabled}
                  handleInputChange={(newValue: boolean) => {
                    setRunImmediately(newValue);
                  }}
                />
              </Grid>
              {!runImmediately && (
                <Grid container item spacing={3} xs={4} style={{ marginTop: '6px' }}>
                  <Grid item lg={2} xs={8} style={{ maxWidth: '6%' }}>
                    <Select
                      value={runAt === undefined ? '' : runAt.hour}
                      displayEmpty
                      name={'field'}
                      placeholder={'Field'}
                      onChange={(event) => {
                        const value = event.target.value;
                        if (typeof value === 'string') {
                          runAt.hour = value;
                        }
                        const copyRunAt = { hour: value, minute: runAt.minute };
                        setRunAt(copyRunAt);
                        if (cronExpression) {
                          const copyCronExpression = cronExpression;
                          copyCronExpression[2] = ' ' + value;
                          if (typeof copyCronExpression[1] === 'string') {
                            if (copyCronExpression[2].substring(1, 2) === '0') {
                              copyCronExpression[2] = copyCronExpression[2].replace('0', '');
                            }
                          }
                          setCronExpression(copyCronExpression);
                        }
                      }}
                      fullWidth={false}
                      label={'Field'}
                      disabled={disabled || runImmediately}
                      input={
                        <SelectInputBase
                          placeholder={'Field'}
                          disabled={disabled || runImmediately}
                        />
                      }
                    >
                      {HourConst.map((field, index) => {
                        return (
                          <MenuItem key={index} value={field}>
                            {field}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </Grid>
                  <Grid
                    item
                    lg={2}
                    xs={12}
                    style={{
                      marginTop: '8px',
                      maxWidth: '10px',
                      marginLeft: '5px',
                      marginRight: '50px',
                    }}
                  >
                    <b style={{ marginLeft: '50px' }}>:</b>
                  </Grid>
                  <Grid item lg={4} xs={8} style={{ maxWidth: '10%' }}>
                    <Select
                      value={runAt === undefined ? '' : runAt.minute}
                      displayEmpty
                      name={'field'}
                      placeholder={'Field'}
                      onChange={(event) => {
                        const value = event.target.value;
                        if (typeof value === 'string') {
                          runAt.minute = value;
                        }
                        if (setRunAt !== undefined && runAt !== undefined) {
                          const copyRunAt = { hour: runAt.hour, minute: value };
                          setRunAt(copyRunAt);
                        }
                        if (cronExpression) {
                          const copyCronExpression = cronExpression;
                          copyCronExpression[1] = ' ' + value;
                          if (typeof copyCronExpression[1] === 'string') {
                            if (copyCronExpression[1].substring(1, 2) === '0') {
                              copyCronExpression[1] = copyCronExpression[1].replace('0', '');
                            }
                          }
                          setCronExpression(copyCronExpression);
                        }
                      }}
                      fullWidth={false}
                      label={'Field'}
                      disabled={disabled || runImmediately}
                      input={
                        <SelectInputBase
                          placeholder={'Field'}
                          disabled={disabled || runImmediately}
                        />
                      }
                    >
                      {MinuteConst.map((field, index) => {
                        return (
                          <MenuItem key={index} value={field}>
                            {field}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </Grid>
                </Grid>
              )}
            </Grid>
          </Grid>
        </Grid>
      </FormControl>
    </>
  );
};

export default OnDemandCronExpression;

import React, { SetStateAction } from 'react';
import { IconButton, ListItem, ListItemSecondaryAction, ListItemText } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { ImportContactsOutlined } from '@material-ui/icons';

type SuggestionItemSelectEvent = {
  label: string;
  value: any;
  index?: number;
};

type SuggestionItemRendererProps = {
  label: string;
  index: number;
  value: any;
  currentInput: string;
  onSelect: (event: SuggestionItemSelectEvent) => void;
  highlightedIndex: number;
  setHighlightedIndex: React.Dispatch<SetStateAction<number>>;
  icon?: any;
  isDisabled?: boolean;
};

const useStyles = makeStyles({
  root: {
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.08)',
    },
  },
  disabled: {
    pointerEvents: 'none',
    backgroundColor: 'transparent',
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
});

const SuggestionItemRenderer: React.FC<SuggestionItemRendererProps> = (props) => {
  const {
    label,
    value,
    onSelect,
    index,
    currentInput,
    highlightedIndex,
    setHighlightedIndex,
    icon,
    isDisabled = false,
  } = props;
  const classes = useStyles();
  return (
    <ListItem
      selected={index === highlightedIndex && !isDisabled}
      className={`${classes.root} ${isDisabled ? classes.disabled : ''}`}
      onMouseOver={(event) => {
        if (!isDisabled) setHighlightedIndex(index);
      }}
    >
      <ListItemText
        primary={label}
        onClick={() => {
          if (!isDisabled) {
            onSelect({ label, value, index });
          }
        }}
      />
      {icon && <ListItemSecondaryAction>{icon}</ListItemSecondaryAction>}
    </ListItem>
  );
};

export default SuggestionItemRenderer;

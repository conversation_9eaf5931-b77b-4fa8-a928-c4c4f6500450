const path = require('path');

module.exports = async ({ config }) => {
  // styles
  config.module.rules.push({
    test: /\.(sass|scss)$/,
    use: ['resolve-url-load'],
    include: path.resolve(__dirname, '../')
  });

  // fonts
  config.module.rules.push({
    test: /\.(woff)$/,
    use: [{
      loader: 'file-loader',
      query: {
        name: '[name].[ext]'
      }
    }],
    include: path.resolve(__dirname, '../')
  });

  config.node = {
    fs: 'empty'
  }
  return config;
}
import React from 'react';

import { Grid } from '@material-ui/core';
import Typography from '@material-ui/core/Typography';
import ExpandLessIcon from '@material-ui/icons/ExpandLess';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';

import { RubyAccordions } from '../ruby-accordion/ruby-accordion';

interface Props {
  expanded: boolean;
  handleChange: () => void;
  body: React.ReactNode;
  statics: {
    title: string;
    totalAmount: string;
    balance: string;
  };
}

export const InvoiceAccordion = ({ expanded, handleChange, body, statics }: Props) => {
  return (
    <RubyAccordions
      accordionBody={body}
      expanded={expanded}
      handleChange={handleChange}
      accordionTitle={
        <Grid container xs={12} justifyContent="space-between">
          <Grid item xs={4}>
            <Typography style={{ fontSize: 10, fontWeight: 700, opacity: 0.4 }}>
              CUSTOMER
            </Typography>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Typography style={{ fontSize: 20, fontWeight: 500, color: '#000' }}>
                {statics.title}
              </Typography>
              {expanded ? <ExpandLessIcon fontSize="large" /> : <ExpandMoreIcon fontSize="large" />}
            </div>
          </Grid>
          <Grid item xs={6}>
            <Grid container xs={12} justifyContent="flex-end">
              <Grid item xs={3}>
                <Typography style={{ fontSize: 10, fontWeight: 700, opacity: 0.4 }}>
                  TOTAL AMOUNT
                </Typography>
                <Typography style={{ fontSize: 20, fontWeight: 500, color: '#000' }}>
                  {statics.totalAmount}
                </Typography>
              </Grid>
              <Grid item xs={3}>
                <Typography style={{ fontSize: 10, fontWeight: 700, opacity: 0.4 }}>
                  BALANCE
                </Typography>
                <Typography style={{ fontSize: 20, fontWeight: 500, color: '#000' }}>
                  {statics.balance}
                </Typography>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      }
    />
  );
};

import { validateConflictingChanges } from '../subscription-action-dialog-validation-util';
import type { Subscription } from '../../revenue-builder-types/interface';
import type { ChangeGroup } from '../../change-cart/interface';
import type { ChangeItem } from '../../change-cart/interface';

describe('validateConflictingChanges', () => {
  // Helper function to create a mock change group
  const createMockChangeGroup = (
    items: {
      id: string;
      parentId?: string;
      subscriptionNumber: string;
      changeType: string;
    }[],
  ): ChangeGroup => {
    const changeItems: ChangeItem[] = items.map((item) => ({
      id: `item-${item.id}`,
      time: new Date().toISOString(),
      subscriptionMetadata: {} as any,
      cardAction: {} as any,
      request: {
        changeType: item.changeType,
      } as any,
      asset: {
        id: item.id,
        parentId: item.parentId,
        name: item.subscriptionNumber,
        status: 'Active',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
      } as unknown as Subscription,
      requestWasEdited: false,
    }));

    return {
      id: 'group1',
      name: 'Test Group',
      priceBook: { id: 'pricebook1', name: 'Standard' },
      currencyIsoCode: 'USD',
      customer: { id: 'customer1', name: 'Test Customer' },
      changeItems,
    };
  };

  it('should not throw when there are no change groups', () => {
    expect(() => validateConflictingChanges([])).not.toThrow();
  });

  it('should not throw when there are no change items', () => {
    const changeGroups = [
      {
        id: 'group1',
        name: 'Test Group',
        priceBook: { id: 'pricebook1', name: 'Standard' },
        currencyIsoCode: 'USD',
        customer: { id: 'customer1', name: 'Test Customer' },
        changeItems: [],
      },
    ];
    expect(() => validateConflictingChanges(changeGroups)).not.toThrow();
  });

  it('should not throw when there are no add-ons', () => {
    const changeGroups = [
      createMockChangeGroup([
        { id: 'sub1', subscriptionNumber: 'SUB-1', changeType: 'UpdateQuantity' },
        { id: 'sub2', subscriptionNumber: 'SUB-2', changeType: 'UpdateTerm' },
      ]),
    ];
    expect(() => validateConflictingChanges(changeGroups)).not.toThrow();
  });

  it('should not throw when add-ons have non-restricted change types', () => {
    const changeGroups = [
      createMockChangeGroup([
        { id: 'parent1', subscriptionNumber: 'SUB-1', changeType: 'UpdateQuantity' },
        {
          id: 'addon1',
          parentId: 'parent1',
          subscriptionNumber: 'SUB-2',
          changeType: 'UpdateBillingCycle',
        },
      ]),
    ];
    expect(() => validateConflictingChanges(changeGroups)).not.toThrow();
  });

  it('should not throw when parent has non-restricted change types', () => {
    const changeGroups = [
      createMockChangeGroup([
        { id: 'parent1', subscriptionNumber: 'SUB-1', changeType: 'UpdateBillingCycle' },
        { id: 'addon1', parentId: 'parent1', subscriptionNumber: 'SUB-2', changeType: 'Swap' },
      ]),
    ];
    expect(() => validateConflictingChanges(changeGroups)).not.toThrow();
  });

  it('should throw when add-on has restricted change type and parent has restricted change type', () => {
    const changeGroups = [
      createMockChangeGroup([
        { id: 'parent1', subscriptionNumber: 'SUB-1', changeType: 'UpdateQuantity' },
        { id: 'addon1', parentId: 'parent1', subscriptionNumber: 'SUB-2', changeType: 'Swap' },
      ]),
    ];

    expect(() => validateConflictingChanges(changeGroups)).toThrow(
      'Cannot checkout UpdateQuantity of SUB-1 and Swap of SUB-2 together. You can check them out individually',
    );
  });

  it('should handle multiple groups and items correctly', () => {
    const changeGroups: ChangeGroup[] = [
      createMockChangeGroup([
        { id: 'parent1', subscriptionNumber: 'SUB-1', changeType: 'UpdateQuantity' },
        { id: 'addon1', parentId: 'parent1', subscriptionNumber: 'SUB-2', changeType: 'Swap' },
      ]),
      {
        changeItems: [
          {
            // @ts-ignore
            asset: { id: 'parent2', subscriptionNumber: 'SUB-3' },
            // @ts-ignore
            request: { changeType: 'UpdateTerm' },
          },
          {
            // @ts-ignore
            asset: { id: 'addon2', parentId: 'parent2', subscriptionNumber: 'SUB-4' },
            // @ts-ignore
            request: { changeType: 'Upgrade' },
          },
        ],
      },
    ];

    expect(() => validateConflictingChanges(changeGroups)).toThrow(
      'Cannot checkout UpdateQuantity of SUB-1 and Swap of SUB-2 together. You can check them out individually',
    );
  });

  it('should handle missing subscription numbers gracefully', () => {
    const changeGroups = [
      createMockChangeGroup([
        { id: 'parent1', subscriptionNumber: 'SUB-1', changeType: 'UpdateQuantity' },
        { id: 'addon1', parentId: 'parent1', subscriptionNumber: 'SUB-2', changeType: 'Swap' },
      ]),
    ];

    // Manually set subscriptionNumber to undefined for the test case
    if (changeGroups[0].changeItems?.[1]?.asset) {
      changeGroups[0].changeItems[1].asset.subscriptionNumber = undefined as any;
    }

    expect(() => validateConflictingChanges(changeGroups)).toThrow(
      'Cannot checkout UpdateQuantity of SUB-1 and Swap of SUB-2 together. You can check them out individually',
    );
  });
});

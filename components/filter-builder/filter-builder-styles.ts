const filterBuilderStyles = (theme: any) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    '& .MuiSelect-select': {
      '&:focus': {
        backgroundColor: 'transparent',
      },
    },
    '& .MuiInputBase-root': {
      fontSize: '.875rem',
    },
  },
  conditionSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-end',
    maxWidth: 900,
  },
  linkSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-end',
    maxWidth: 900,
  },
  button: {
    '&:hover': {
      backgroundColor: 'transparent',
    },
    textTransform: 'none',
    padding: 10,
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 50,
  },
  operandControl: {
    margin: theme.spacing(1),
    minWidth: 120,
  },
  operandSelect: {
    marginLeft: theme.spacing(1.75),
    marginRight: theme.spacing(1),
  },
  operandTypeSelect: {
    borderBottom: 'none',
  },
  whereTextField: {
    fontWeight: '600',
    fontSize: '.875rem',
    marginLeft: '10px',
    marginRight: '15px',
    opacity: '.9',
    marginBlockStart: '1em',
    marginBlockEnd: '1em',
  },
  errorTextField: {
    fontSize: '.875rem',
    marginLeft: '94px',
    color: '#ca0035',
  },
  newConditionLink: {
    color: '#6239eb',
    fontSize: '.75rem',
    cursor: 'pointer',
    fontWeight: 600,
  },
  cancelButton: {
    padding: theme.spacing(1, 6, 1, 6),
    color: theme.palette.secondary.main,
    borderColor: theme.palette.secondary.main,
    backgroundColor: 'white',
    '&:hover': {
      backgroundColor: 'transparent',
    },
    textTransform: 'capitalize',
  },
  saveFilterText: {
    color: '#008000',
    fontSize: '.875rem',
  },
  errorFilterText: {
    color: '#ca0035',
    fontSize: '.875rem',
  },
  nestedCondition: {
    // transform: 'translateX(86px)'
    // paddingLeft: '86px'
    marginLeft: '86px',
  },
  fieldSelect: {
    visibility: 'visible',
  },
  operatorRoot: {
    fontWeight: 600,
    margin: '8px',
    fontSize: '.875rem',
    minWidth: '25px',
    opacity: '.9',
  },
  dateInput: {
    // TODO: There's a bug here where on @nue-apps/ruby-ui-component the border is shown twice
    // because of the border and the app theme doesn't match up
    borderRadius: '4px',
    marginTop: '8px',
    '&:focus-within': {
      borderColor: theme.palette.secondary.main,
    },
    '& input': {
      fontSize: '.875rem',
      padding: '12px 20px',
    },
    '& fieldset': {
      border: '0px',
    },
    '& button': {
      color: '#6239EB',
      opacity: 0.7,
    },
    '& button:hover': {
      color: '#6239EB',
      opacity: 1,
    },
    border: '1px solid #ced4da',
  },

  dialogContent: {
    marginBlockStart: '1em',
    marginBlockEnd: '1em',
  },
  NestedMenuItemClassName: {
    '&:hover': {
      background: 'rgba(0, 0, 0, 0.04)',
    },
  },
});

export default filterBuilderStyles;

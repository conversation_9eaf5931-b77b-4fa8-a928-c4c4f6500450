import React from 'react';
import { Props } from './interface';
import { makeStyles } from '@material-ui/core/styles';
import Button from '@material-ui/core/Button';
import ImagePlaceholder from '../../static/images/image-placeholder.png';

const defaultProps = {
  clickable: false,
};

const useStyles = makeStyles({
  imgBtn: {
    borderWidth: '1px',
    borderRadius: '5px',
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    textTransform: 'none',
    padding: 0,
    ':hover:not([disabled]):': {
      textDecoration: 'solid',
      backgroundColor: '#f7f4ff',
    },
  },
  label: {
    height: '100%',
  },
  img: {
    width: '100%',
    height: '100%',
    borderRadius: '5px',
    objectFit: 'cover',
  },
});

const ImageButton: React.FC<Props> = ({ imageUrl, onClick, clickable }) => {
  const classes = useStyles();
  return (
    <div style={{ width: '100%', height: 'inherit' }}>
      <Button
        className={classes.imgBtn}
        classes={{
          label: classes.label,
        }}
        type="button"
        onClick={onClick}
        disabled={!clickable}
      >
        <img
          alt="image"
          className={classes.img}
          style={{ cursor: clickable ? 'pointer' : 'auto' }}
          src={imageUrl || ImagePlaceholder}
        />
      </Button>
    </div>
  );
};

export default ImageButton;

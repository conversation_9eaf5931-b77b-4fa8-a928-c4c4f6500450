import React from 'react';
import { Typography, makeStyles } from '@material-ui/core';
import dayjs from 'dayjs';
import type { ChangeItemText } from './interface';
import type { DowngradeRequest } from '../change-cart';

const useStyles = makeStyles((theme) => ({
  itemText: {
    color: theme.palette.grey[900],
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
}));

const DowngradeSubscriptionText: React.FC<ChangeItemText> = (props) => {
  const classes = useStyles();

  const { changeItem } = props;

  // Ensure the request is actually a DowngradeRequest
  const request = changeItem.request as DowngradeRequest;
  // Add validation to handle potential type mismatches
  if (!request.toProduct || !request.startDate) {
    console.error('Invalid DowngradeRequest format', request);
    // Handle the error appropriately
  }
  const { objectType, assetName, toProduct, startDate } = request;
  const { product } = changeItem.asset;

  const formattedStartDate = dayjs(startDate).format('MM/DD/YYYY');

  return (
    <Typography variant="body2" component="p" display="inline" className={classes.itemText}>
      The {objectType} <span className={classes.textUnderline}>{assetName}</span> of product{' '}
      <span className={classes.textHighlight}>{product.name}</span> is downgraded to{' '}
      <span className={classes.textHighlight}>{toProduct.name}</span> on{' '}
      <span className={classes.textHighlight}>{formattedStartDate}</span>
    </Typography>
  );
};

export default DowngradeSubscriptionText;

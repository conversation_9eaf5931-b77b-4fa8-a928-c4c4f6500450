export default [
  {
    apiName: 'Name',
    name: 'Invoice Number',
    type: 'text',
    creatable: true,
    customField: false,
    filterable: true,
    lookupRelation: undefined,
    required: false,
    showTooltip: false,
    sortable: true,
    toolTipText: '',
    updatable: true,
    valueSet: undefined,
    xs: 4,
  },
  {
    apiName: 'status',
    creatable: true,
    customField: true,
    filterable: true,
    lookupRelation: undefined,
    name: 'Status',
    required: true,
    showTooltip: false,
    sortable: true,
    toolTipText: '',
    type: 'pickList',
    updatable: true,
    valueSet: undefined,
    xs: 4,
  },
  {
    apiName: 'invoiceDate',
    creatable: true,
    customField: true,
    filterable: true,
    lookupRelation: undefined,
    name: 'Invoice Date',
    required: false,
    showTooltip: false,
    sortable: true,
    toolTipText: '',
    type: 'date',
    updatable: true,
    valueSet: undefined,
    xs: 4,
  },
  {
    apiName: 'startDate',
    creatable: true,
    customField: true,
    filterable: true,
    lookupRelation: undefined,
    name: 'Start Date',
    required: true,
    showTooltip: false,
    sortable: true,
    toolTipText: '',
    type: 'date',
    updatable: true,
    valueSet: undefined,
    xs: 4,
  },
  {
    apiName: 'endDate',
    creatable: true,
    customField: true,
    filterable: true,
    lookupRelation: undefined,
    name: 'End Date',
    required: false,
    showTooltip: false,
    sortable: true,
    toolTipText: '',
    type: 'date',
    updatable: true,
    valueSet: undefined,
    xs: 4,
  },
  {
    apiName: 'dueDate',
    creatable: true,
    customField: true,
    filterable: true,
    lookupRelation: undefined,
    name: 'Due Date',
    required: false,
    showTooltip: false,
    sortable: true,
    toolTipText: '',
    type: 'date',
    updatable: true,
    valueSet: undefined,
    xs: 4,
  },
  {
    apiName: 'amount',
    creatable: true,
    customField: true,
    filterable: true,
    lookupRelation: undefined,
    name: 'Amount',
    required: true,
    showTooltip: false,
    sortable: true,
    toolTipText: '',
    type: 'currency',
    updatable: true,
    valueSet: undefined,
    xs: 4,
  },
  {
    apiName: 'taxAmount',
    creatable: true,
    customField: true,
    filterable: true,
    lookupRelation: undefined,
    name: 'Tax Amount',
    required: true,
    showTooltip: false,
    sortable: true,
    toolTipText: '',
    type: 'currency',
    updatable: true,
    valueSet: undefined,
    xs: 4,
  },
  {
    apiName: 'balance',
    creatable: true,
    customField: true,
    filterable: true,
    lookupRelation: undefined,
    name: 'Balance',
    required: true,
    showTooltip: false,
    sortable: true,
    toolTipText: '',
    type: 'currency',
    updatable: true,
    valueSet: undefined,
    xs: 4,
  },
];

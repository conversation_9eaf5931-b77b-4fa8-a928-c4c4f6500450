import React from 'react';
import { Paper } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  root: {
    height: 'auto',
    right: 0,
    width: '100%',
    padding: '20px 0',
    position: 'fixed',
    bottom: 0,
    display: 'flex',
    flexWrap: 'wrap',
    boxShadow: '0 -12px 32px -8px rgba(0,0,0,0.12)',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(2px)',
    zIndex: 400,
  },
  rootWithoutWidth: {
    height: 'auto',
    right: 0,
    padding: '20px 0',
    position: 'fixed',
    bottom: 0,
    display: 'flex',
    flexWrap: 'wrap',
    boxShadow: '0 -12px 32px -8px rgba(0,0,0,0.12)',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(2px)',
    zIndex: 400,
  },
});

export const ButtonBar: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const classes = useStyles();

  return (
    <Paper className={props.nonFullWidth ? classes.rootWithoutWidth : classes.root}>
      {props.children}
    </Paper>
  );
};

export default ButtonBar;

import MenuItem from '@material-ui/core/MenuItem';
import Select from '@material-ui/core/Select';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import KeyboardArrowDownIcon from '@material-ui/icons/KeyboardArrowDown';
import { useRubySnackbarContext } from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import React, { useContext, useEffect, useState } from 'react';
import type { RubyFilter } from '../graph-ql-query-constructor';
import GraphQLQueryConstructor from '../graph-ql-query-constructor';
import { MultiCurrencyContext } from '../multi-currency-context';
import RadioButtonDialog from '../radio-button-dialog';
import type { RadioItem, RadioSelection } from '../radio-group-component';
import type { LineObject, Opportunity, Order } from '../revenue-builder-types';
import { convertTerm } from '../util';
import type { ChangeGroup, ChangeItem, CheckoutDialogProps } from './interface';

const useStyles = makeStyles({
  root: {
    color: '#6239eb',
  },
  icon: {
    color: '#6239eb',
    paddingBottom: '4px',
    top: '2px',
  },
  select: {
    paddingRight: '28px !important',
    paddingLeft: '4px',
  },
});

const CheckoutDialog: React.FC<CheckoutDialogProps> = (props) => {
  const classes = useStyles();

  const {
    checkedOutChangeGroup,
    navigateToQuoteBuilder,
    handleOpenChangeCart,
    open,
    handleOpen,
    executeQuery,
    orderMetadata,
    quoteMetadata,
    opportunityMetadata,
    rubySettings,
  } = props;

  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [selectedQuoteId, setSelectedQuoteId] = useState<string | null>(null);
  const [quotes, setQuotes] = useState<any[] | null>(null);
  const [orders, setOrders] = useState<Order[] | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [options, setOptions] = useState<RadioItem[]>([]);
  const { isMultiCurrencyEnabled } = useContext(MultiCurrencyContext);

  const snackbarService = useRubySnackbarContext();

  const getOpenOpportunities = async (currentChangeGroup: ChangeGroup) => {
    const opportunityFilter: RubyFilter = {
      isApplied: true,
      conditions: [
        {
          apiName: 'customerId',
          name: 'Customer Id',
          operand: ' _and ',
          operator: ' _eq ',
          type: 'id',
          value: `"${currentChangeGroup.customer.id}"`,
        },
        {
          apiName: 'isClosed',
          name: 'Is Closed',
          type: 'boolean',
          value: 'false',
          operator: ' _eq ',
          operand: ' _and ',
          nestedConditions: [],
        },
      ],
      name: 'Opportunity Filter',
      isExclusive: true,
      id: 'opportunity-filter',
    };

    if (isMultiCurrencyEnabled) {
      opportunityFilter.conditions.push({
        apiName: 'currencyIsoCode',
        name: 'Currency Iso Code',
        type: 'string',
        value: `"${currentChangeGroup.currencyIsoCode}"`,
        operator: ' _eq ',
        operand: ' _and ',
        nestedConditions: [],
      });
    }

    const fields = opportunityMetadata.fields
      ? opportunityMetadata.fields.filter((field) => field.apiName === 'id')
      : [];
    const opportunityMetadataWithUpdatedFields = { ...opportunityMetadata, fields };
    const query = GraphQLQueryConstructor.construct().query(
      opportunityMetadataWithUpdatedFields,
      opportunityFilter.conditions,
      '',
      [],
      opportunityMetadataWithUpdatedFields.fields,
    );
    const opportunities: Opportunity[] = await executeQuery(
      query,
      opportunityMetadataWithUpdatedFields,
      [opportunityFilter],
    );
    return opportunities.map((opportunity) => `"${opportunity.id}"`);
  };

  const getOrders = async (currentChangeGroup: ChangeGroup) => {
    const orderFilter: RubyFilter = {
      isApplied: true,
      conditions: [
        {
          apiName: 'customerId',
          name: 'Customer Id',
          operand: ' _and ',
          operator: ' _eq ',
          type: 'id',
          value: `"${currentChangeGroup.customer.id}"`,
        },
        {
          apiName: 'status',
          name: 'Status',
          operand: ' _and ',
          operator: ' _eq ',
          type: 'pickList',
          value: '"Draft"',
        },
      ],
      name: 'Order Filter',
      isExclusive: true,
      id: 'order-filter',
    };

    if (isMultiCurrencyEnabled) {
      orderFilter.conditions.push({
        apiName: 'currencyIsoCode',
        name: 'Currency Iso Code',
        operand: ' _and ',
        operator: ' _eq ',
        type: 'string',
        value: `"${currentChangeGroup.currencyIsoCode}"`,
      });
    }
    const query = GraphQLQueryConstructor.construct().query(
      orderMetadata,
      orderFilter.conditions || [],
    );
    const ordersResults = await executeQuery(query, orderMetadata, [orderFilter]);
    return ordersResults.filter(
      (order) => order.priceBookId === currentChangeGroup.priceBook.id || !order.priceBookId,
    );
  };

  const getCheckoutGroupRelatedData = async (currentChangeGroup: ChangeGroup) => {
    const queriedOrders = await getOrders(currentChangeGroup);
    const opportunityIds = await getOpenOpportunities(currentChangeGroup);
    // couldn't extract this code into a separate function because when imported into salesforce
    // the result of executeQuery always returns undefined
    const quoteFilter: RubyFilter = {
      isApplied: true,
      conditions: [
        {
          apiName: 'customerId',
          name: 'Customer Id',
          operand: ' _and ',
          operator: ' _eq ',
          type: 'id',
          value: `"${currentChangeGroup.customer.id}"`,
        },
        {
          apiName: 'opportunityId',
          name: 'Opportunity Id',
          type: 'pickList',
          value: opportunityIds.length > 0 ? `[${opportunityIds}]` : '[]',
          operator: ' _in ',
          operand: ' _and ',
          nestedConditions: [],
        },
        {
          apiName: 'priceBookId',
          name: 'Price Book Id',
          type: 'id',
          value: `"${currentChangeGroup.priceBook.id}"`,
          operator: ' _eq ',
          operand: ' _and ',
          nestedConditions: [],
        },
      ],
      name: 'Quote Filter',
      isExclusive: true,
      id: 'quote-filter',
    };

    if (isMultiCurrencyEnabled) {
      quoteFilter.conditions.push({
        apiName: 'currencyIsoCode',
        name: 'Currency Iso Code',
        operand: ' _and ',
        operator: ' _eq ',
        type: 'string',
        value: `"${currentChangeGroup.currencyIsoCode}"`,
      });
    }

    const query = GraphQLQueryConstructor.construct().query(quoteMetadata, quoteFilter.conditions);
    const queriedQuotes: LineObject[] = await executeQuery(query, quoteMetadata, [quoteFilter]);
    const firstOrderId = queriedOrders.length > 0 ? queriedOrders[0].id : '';
    const firstQuoteId = queriedQuotes.length > 0 ? queriedQuotes[0].id : '';
    setSelectedOrderId(firstOrderId);
    setSelectedQuoteId(firstQuoteId);
    setOrders(queriedOrders);
    setQuotes(queriedQuotes);
    setOptions(
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      generateOptionsByRubySettings(firstOrderId, firstQuoteId, queriedOrders, queriedQuotes),
    );
  };

  const setupCheckoutGroupData = async () => {
    if (checkedOutChangeGroup) {
      await getCheckoutGroupRelatedData(checkedOutChangeGroup);
    }
  };

  const getPicklistValue = (objectName: string, selectedObjectId: string, objects: any[]) => {
    return (
      <Typography variant="body1" component="p">
        Add to an existing {objectName} {'  '}
        {selectedObjectId && objects && (
          <Select
            disableUnderline
            displayEmpty
            value={selectedObjectId}
            name={`select${objectName}`}
            classes={{
              root: classes.root,
              icon: classes.icon,
              select: classes.select,
            }}
            IconComponent={KeyboardArrowDownIcon}
            onChange={(event) => {
              const value = event.target.value;
              const selectedId = objects.find((object) => object.id === value)?.id;
              if (objectName === 'Order') {
                setSelectedOrderId(selectedId);
              } else {
                setSelectedQuoteId(selectedId);
              }
            }}
          >
            {objects.map((object: any) => (
              <MenuItem key={object.id} value={object.id}>
                {object.name}
              </MenuItem>
            ))}
          </Select>
        )}
      </Typography>
    );
  };

  const generateOptionsByRubySettings = (
    firstOrderId: string | null,
    firstQuoteId: string | null,
    queriedOrders: Order[] | null,
    queriedQuotes: any | null,
  ) => {
    const result: RadioItem[] = [];
    if (rubySettings.createNewQuote) {
      result.push({
        label: 'Create a new quote',
        radioSelection: { value: 'createNewQuote' },
        type: 'radio',
      });
    }
    if (rubySettings.createNewOrder) {
      result.push({
        label: 'Create a new order',
        radioSelection: { value: 'createNewOrder' },
        type: 'radio',
      });
    }
    if (rubySettings.useExistingQuote) {
      result.push({
        // @ts-ignore
        label: getPicklistValue('Quote', firstQuoteId, queriedQuotes),
        radioSelection: { value: 'useExistingQuote' },
        disabled: queriedQuotes === null || (queriedQuotes && queriedQuotes.length === 0),
        type: 'radio',
      });
    }
    if (rubySettings.useExistingOrder) {
      result.push({
        // @ts-ignore
        label: getPicklistValue('Order', firstOrderId, queriedOrders),
        radioSelection: { value: 'useExistingOrder' },
        disabled: queriedOrders === null || (queriedOrders && queriedOrders.length === 0),
        type: 'radio',
      });
    }
    return result;
  };

  const getObjectNameFromOption = (optionValue: string) => {
    if (optionValue.includes('Order')) {
      return 'Order';
    }
    return 'Quote';
  };

  useEffect(() => {
    setLoading(true);
    setupCheckoutGroupData();
    setLoading(false);
  }, [checkedOutChangeGroup]);

  useEffect(() => {
    if (selectedOrderId === null || selectedOrderId === null) {
      return;
    }
    setOptions(generateOptionsByRubySettings(selectedOrderId, selectedQuoteId, orders, quotes));
  }, [selectedOrderId, selectedQuoteId]);

  const getSubscriptionStartDate = (changeItems: ChangeItem[] | undefined) => {
    if (!changeItems) {
      return dayjs().format('MM/DD/YYYY');
    }
    let minDate = dayjs(changeItems[0].asset.subscriptionStartDate);
    let minDateStr = changeItems[0].asset.subscriptionStartDate;
    for (let i = 0; i < changeItems.length; i += 1) {
      if (minDate > dayjs(changeItems[i].asset.subscriptionStartDate)) {
        minDateStr = changeItems[i].asset.subscriptionStartDate;
        minDate = dayjs(changeItems[i].asset.subscriptionStartDate);
      }
    }
    return minDateStr;
  };

  const handleSubmit = async (radioSelectionValue: string) => {
    const objectName = getObjectNameFromOption(radioSelectionValue);
    const lineObject: LineObject = {
      id: '',
      number: '',
      priceBookId: checkedOutChangeGroup?.priceBook.id || '',
      name: '',
      status: 'Draft',
      subscriptionStartDate: '',
      currencyIsoCode: checkedOutChangeGroup?.currencyIsoCode,
      customerId: checkedOutChangeGroup?.customer.id,
      subscriptionTerm: convertTerm(
        Number(rubySettings.defaultSubscriptionTerm) || 12,
        'month',
        rubySettings.subscriptionTermDimension || 'month',
      ),
    };
    let recordId = null;
    if (radioSelectionValue === 'createNewQuote' || radioSelectionValue === 'createNewOrder') {
      lineObject.name = `Change ${objectName} for customer ${checkedOutChangeGroup?.customer.name}`;
      lineObject.subscriptionStartDate = getSubscriptionStartDate(
        checkedOutChangeGroup?.changeItems,
      );
    } else if (radioSelectionValue === 'useExistingQuote') {
      recordId = selectedQuoteId;
    } else {
      recordId = selectedOrderId;
    }
    handleOpenChangeCart(false);
    handleOpen(false);
    const subscriptionChanges = checkedOutChangeGroup?.changeItems?.filter(
      (changeItem) =>
        changeItem.request.objectType === 'Subscription' ||
        changeItem.request.objectType === 'Asset' ||
        //@ts-ignore
        changeItem.request.objectType === 'Entitlement__c',
    );
    const changeItemRequests = subscriptionChanges
      ? subscriptionChanges.map((subscriptionChange) => {
          if (
            subscriptionChange.request.changeType === 'Renew' ||
            subscriptionChange.request.changeType === 'RenewWithQuantityUpdate'
          ) {
            return {
              ...subscriptionChange.request,
              evergreen: subscriptionChange.asset?.evergreen,
              billingAccountId: subscriptionChange.asset?.billingAccountId,
              milestoneIds: subscriptionChange.asset?.milestoneIds,
              billingPeriod: subscriptionChange.asset?.billingPeriod,
              convertedTerm: convertTerm(
                //@ts-ignore
                Number(subscriptionChange.request.renewalTerm || 12),
                //@ts-ignore
                subscriptionChange.request.termDimension || 'month',
                subscriptionChange.asset.uom.termDimension || 'month',
              ),
            };
          }
          return {
            ...subscriptionChange.request,
            evergreen: subscriptionChange.asset?.evergreen,
            billingAccountId: subscriptionChange.asset?.billingAccountId,
            milestoneIds: subscriptionChange.asset?.milestoneIds,
            billingPeriod: subscriptionChange.asset?.billingPeriod,
          };
        })
      : [];
    if (changeItemRequests.length > 0) {
      // merge the milestone changeItem for the same asset
      for (let i = changeItemRequests.length - 1; i > 0; i--) {
        const changeItem = changeItemRequests[i];
        const sameAssetChangeItemIndex = changeItemRequests.findIndex(
          (c) =>
            c.assetName === changeItem.assetName &&
            c.assetNumber === changeItem.assetNumber &&
            c.changeType === 'UpdateMilestone',
        );
        if (sameAssetChangeItemIndex !== -1 && sameAssetChangeItemIndex !== i) {
          const sameAssetChangeItem = changeItemRequests[sameAssetChangeItemIndex];
          sameAssetChangeItem.milestoneIds = sameAssetChangeItem.milestoneIds?.concat(
            changeItem.milestoneIds!,
          );
          changeItemRequests.splice(i, 1);
        }
      }
    }

    try {
      await navigateToQuoteBuilder({
        changeGroupId: checkedOutChangeGroup?.id || '',
        mode: radioSelectionValue,
        recordId,
        lineObject,
        changeItemRequests,
      });
    } catch (err) {
      snackbarService.showSnackbar('error', 'Error Occurred', err.message);
    }
  };

  useEffect(() => {
    if (
      options.length === 1 &&
      (options[0].radioSelection.value === 'createNewQuote' ||
        options[0].radioSelection.value === 'createNewOrder')
    ) {
      handleSubmit(options[0].radioSelection.value);
    }
  }, [options]);

  if (!quotes || !orders) {
    return null;
  }

  if (options.length === 1 && (rubySettings.createNewQuote || rubySettings.createNewOrder)) {
    return null;
  }

  return (
    <RadioButtonDialog
      width="md"
      title="How do you want to proceed?"
      open={open}
      handleUpdateOptions={(newOptions: RadioItem[]) => setOptions(newOptions)}
      handleClose={() => handleOpen(false)}
      loadingText="Loading Checkout Options..."
      handleSubmit={async (radioSelection: RadioSelection) => {
        const radioSelectionValue = radioSelection.value;
        await handleSubmit(radioSelectionValue);
      }}
      loading={loading}
      options={options}
    />
  );
};

export default CheckoutDialog;

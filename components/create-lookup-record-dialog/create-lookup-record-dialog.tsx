import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import DialogComponent from '../dialog-component';
import { FormSectionWrapper as FormSection } from '../form-section/form-section';
import { buildValidationSchema, useYupValidationResolver } from '../form-validation';
import { Props } from './interface';

const defaultProps = {};

const CreateLookupRecordDialog: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const {
    open,
    onClose,
    originObject,
    lookupField,
    lookupObject,
    lookupOptionsLoader,
    getLookupOptionByValue,
  } = props;

  if (!lookupObject) {
    return null;
  }

  const validationSchema = buildValidationSchema(lookupObject);
  const resolver = useYupValidationResolver(validationSchema);
  //@ts-ignore
  const methods = useForm({ defaultValues: {}, resolver });
  const { getValues } = methods;
  const fields = lookupObject.fields;
  const initialValues = {};

  return (
    <DialogComponent
      open={open}
      title={' '}
      width={'md'}
      handleClose={onClose}
      handleSubmit={async () => {
        const values = getValues();
        console.debug('save contact values', values);
      }}
    >
      <FormProvider {...methods}>
        <form>
          <FormSection
            title={`Create ${lookupField.lookupRelation?.relationLabel}`}
            showDivider={false}
            fields={fields}
            values={initialValues}
            lookupOptionsLoader={lookupOptionsLoader}
            getLookupOptionByValue={getLookupOptionByValue}
            // error={n}
          />
        </form>
      </FormProvider>
    </DialogComponent>
  );
};

export default CreateLookupRecordDialog;

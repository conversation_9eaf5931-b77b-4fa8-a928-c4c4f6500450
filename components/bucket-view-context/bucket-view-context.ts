import React from 'react';
import { BucketO<PERSON>, QueryExecutor, RubyObject } from '@nue-apps/ruby-ui-component';

export interface BucketViewService {
  executeQuery: QueryExecutor;
  saveOrUpdate: (bucket: BucketObject) => Promise<void>;
}

export interface LineEditorFacade {
  bucketViewService?: BucketViewService;
}

const BucketViewContext = React.createContext<LineEditorFacade>({});

export default BucketViewContext;

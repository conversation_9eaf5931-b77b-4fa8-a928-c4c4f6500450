import React from 'react';

import { Grid, Typography, makeStyles } from '@material-ui/core';
import dayjs from 'dayjs';

const useStyle = makeStyles({
  container: {
    paddingTop: 30,
  },
  date: {
    paddingLeft: 40,
    fontSize: '1.3rem',
    color: 'black',
  },
  dateDetail: {
    fontWeight: 'bold',
    color: 'black',
  },
  item: {
    marginTop: 10,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    fontSize: 20,
    cursor: 'pointer',
    '&:hover': {
      background: '#EFECFD',
      fontWeight: '500',
    },
    padding: 10,
    paddingLeft: 40,
    paddingRight: 40,
    transition: 'all .5s ease',
    flexWrap: 'wrap',
  },
  activeItem: {
    marginTop: 10,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    fontSize: 20,
    cursor: 'pointer',
    background: '#EFECFD',
    padding: 10,
    paddingLeft: 40,
    paddingRight: 40,
    transition: 'all .5s ease',
    color: 'rgb(98, 57, 235)',
    flexWrap: 'wrap',
  },
  activeTime: {
    color: 'rgb(98, 57, 235)',
  },
  time: {
    color: 'black',
  },
  capital: {
    display: 'flex',
    alignItems: 'center',
    fontSize: '1rem',
  },
  statusIcon: {
    marginRight: 10,
  },
});

interface Props {
  event: any;
  dateKey: string;
  setActiveLog: any;
  activeLog: any;
}

const getEventlogStatusIcon = (status: number) => {
  switch (status) {
    case 400:
    case 500:
    case 404:
      return (
        <div
          style={{
            backgroundColor: '#CF2A27',
            padding: 4,
            fontWeight: 'bold',
            color: '#fff',
            marginRight: '10px',
            fontSize: '14px',
            textAlign: 'center',
          }}
        >
          {status}
        </div>
      );
    case 200:
      return (
        <div
          style={{
            backgroundColor: '#6AA84F',
            padding: 4,
            fontWeight: 'bold',
            color: '#fff',
            marginRight: '10px',
            textAlign: 'center',
            fontSize: '14px',
          }}
        >
          {status}
        </div>
      );
    default:
      return (
        <div
          style={{
            backgroundColor: '#FF3939',
            padding: 4,
            fontWeight: 'bold',
            color: '#fff',
            marginRight: '10px',
            textAlign: 'center',
            fontSize: '14px',
          }}
        >
          {status}
        </div>
      );
  }
};

const getMethod = (method: string) => {
  switch (method) {
    case 'GET':
      return (
        <div
          style={{
            color: '#6AA84F',
            padding: 4,
            fontWeight: 'bold',
            marginRight: '10px',
            fontSize: '14px',
            textAlign: 'center',
          }}
        >
          {method}
        </div>
      );
    case 'POST':
      return (
        <div
          style={{
            padding: 4,
            fontWeight: 'bold',
            color: '#FF7D00',
            marginRight: '10px',
            textAlign: 'center',
            fontSize: '14px',
          }}
        >
          {method}
        </div>
      );
    case 'PATCH':
      return (
        <div
          style={{
            padding: 4,
            fontWeight: 'bold',
            color: '#5D45FF',
            marginRight: '10px',
            textAlign: 'center',
            fontSize: '14px',
          }}
        >
          {method}
        </div>
      );
    case 'DELETE':
      return (
        <div
          style={{
            padding: 4,
            fontWeight: 'bold',
            color: '#FF3939',
            marginRight: '10px',
            textAlign: 'center',
            fontSize: '14px',
          }}
        >
          {method}
        </div>
      );
    case 'PUT':
      return (
        <div
          style={{
            padding: 4,
            fontWeight: 'bold',
            color: '#0564F5',
            marginRight: '10px',
            textAlign: 'center',
            fontSize: '14px',
          }}
        >
          {method}
        </div>
      );
    default:
      return null;
  }
};

const ApiKeysEventLog = ({ event, dateKey, activeLog, setActiveLog }: Props) => {
  const classes = useStyle();
  return (
    <>
      <Grid xs={12} container className={classes.container}>
        <Grid item xs={12} className={classes.date}>
          <Typography className={classes.dateDetail}>
            {dayjs(dateKey).format('MM/DD/YYYY')}
          </Typography>
        </Grid>
        {event.map((e: any, index: number) => {
          return (
            <Grid
              key={index}
              item
              xs={12}
              className={
                activeLog && activeLog.id === e.id ? `${classes.activeItem}` : `${classes.item}`
              }
              onClick={() => {
                setActiveLog(e);
              }}
            >
              <Grid className={classes.capital} xs={12}>
                <Grid xs={12} container>
                  <Grid xs={1} item>
                    {getEventlogStatusIcon(e.statusCode)}
                  </Grid>
                  <Grid xs={1} item>
                    {getMethod(e.method)}
                  </Grid>
                  <Grid
                    xs={7}
                    item
                    style={{
                      width: 300,
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
                      paddingLeft: 12,
                    }}
                  >
                    {e.url}
                  </Grid>
                  <Grid xs={3} item>
                    <Typography
                      style={{ marginLeft: 12 }}
                      className={
                        activeLog && activeLog.id === e.id
                          ? `${classes.activeTime}`
                          : `${classes.time}`
                      }
                    >
                      {dayjs(e.accessTime).format('hh:mm:ss A')}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          );
        })}
      </Grid>
    </>
  );
};

export default ApiKeysEventLog;

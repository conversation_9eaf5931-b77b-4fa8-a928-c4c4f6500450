import BarChartOutlinedIcon from '@material-ui/icons/BarChartOutlined';
import BusinessOutlinedIcon from '@material-ui/icons/BusinessOutlined';
import CheckCircleOutlineIcon from '@material-ui/icons/CheckCircleOutline';
import DeleteOutlineIcon from '@material-ui/icons/DeleteOutline';
import DescriptionOutlinedIcon from '@material-ui/icons/DescriptionOutlined';
import MoreHorizOutlinedIcon from '@material-ui/icons/MoreHorizOutlined';
import NoteAddOutlinedIcon from '@material-ui/icons/NoteAddOutlined';
import PlayForWorkIcon from '@material-ui/icons/PlayForWork';
import ZoomInIcon from '@material-ui/icons/ZoomIn';

import ActiveIcon from '../active-icon';
import DraftIcon from '../draft-icon';
import EditIcon from '../edit-icon';
import { CancelIcon } from '../icons';
import { Order } from '../revenue-builder-types';

export default {
  cardHeaderMetadata: {
    title: 'name',
    description: 'soldToAddress',
    subtitle: (order: Order) => order.owner?.name || '',
    statusStyle: {
      Draft: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
      Activated: {
        backgroundColor: '#6239EB',
        color: '#FFF',
      },
      Canceled: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
    },
    statusShadowStyle: {
      Draft: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
      Activated: {
        borderColor: 'transparent transparent #000000 transparent',
      },
      Canceled: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
    },
    statusIcon: {
      Active: ActiveIcon,
      Draft: DraftIcon,
      Canceled: CancelIcon,
    },
    // TODO: switch to using order interface
    status: 'status',
    imageSignedUrl: (order: Order) => order.owner?.imageSignedUrl,
  },
  cardBodyMetadata: {
    numCardActionsPerRow: 2,
    activeBorderStyle: {
      border: '1px solid #6239EB',
      borderRadius: '10px 10px 8px 8px',
      backgroundColor: '#F5F5F5',
    },
    summaryItemPosition: 'top-left',
    moreActions: [
      {
        id: 'changeOrder',
        name: 'View Subscription',
        description: 'View Subscription',
        icon: CheckCircleOutlineIcon,
      },
      {
        id: 'viewInvoice',
        name: 'View Invoice',
        description: 'View Invoice',
        icon: DescriptionOutlinedIcon,
      },
      {
        id: 'viewUsage',
        name: 'View Usage',
        description: 'View Usage',
        icon: BarChartOutlinedIcon,
      },
      {
        id: 'viewAsset',
        name: 'View Asset',
        description: 'View Asset',
        icon: BusinessOutlinedIcon,
      },
      {
        id: 'viewEntitlement',
        name: 'View Entitlement',
        description: 'View Entitlement',
        icon: NoteAddOutlinedIcon,
      },
    ],
    popUpMoreActions: [
      {
        id: 'viewUsage',
        name: 'View Usage',
        description: 'View Usage',
        icon: BarChartOutlinedIcon,
      },
      {
        id: 'viewInvoice',
        name: 'View Invoice',
        description: 'View Invoice',
        icon: DescriptionOutlinedIcon,
      },
      {
        id: 'viewAsset',
        name: 'View Asset',
        description: 'View Asset',
        icon: BusinessOutlinedIcon,
      },
      {
        id: 'viewEntitlement',
        name: 'View Entitlement',
        description: 'View Entitlement',
        icon: NoteAddOutlinedIcon,
      },
    ],
    actions: {
      Activated: [
        {
          id: 'cancel',
          name: 'Cancel',
          description: 'Cancel order',
          icon: CancelIcon,
        },
        {
          id: 'more',
          name: 'More...',
          description: 'More',
          icon: MoreHorizOutlinedIcon,
        },
        {
          id: 'details',
          name: 'Details',
          description: 'View order details',
          icon: ZoomInIcon,
        },
      ],
      Draft: [
        {
          id: 'edit',
          name: 'Edit',
          description: 'Edit',
          icon: EditIcon,
        },
        {
          id: 'delete',
          name: 'Delete',
          description: 'Delete',
          icon: DeleteOutlineIcon,
          confirmation: {
            title: 'Are you sure you want to delete this order?',
            message: 'This operation cannot be revoked.',
          },
        },
        {
          id: 'activate',
          name: 'Activate Order',
          description: 'Activate order',
          icon: PlayForWorkIcon,
        },
      ],
    },
  },
};

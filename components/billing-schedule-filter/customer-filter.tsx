import React, { useState } from 'react';
import { Checkbox, Grid, MenuItem, RadioGroup, Select } from '@material-ui/core';
import Accordion from '@material-ui/core/Accordion';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Radio from '@material-ui/core/Radio';
import TextField from '@material-ui/core/TextField';
import Tooltip from '@material-ui/core/Tooltip';
import Typography from '@material-ui/core/Typography';
import { Theme, createStyles, makeStyles } from '@material-ui/core/styles';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import InfoIcon from '@material-ui/icons/Info';
import { OrderByField, RubyFilter } from '@nue-apps/ruby-ui-component';
import SelectInputBase from '..//select-input-base';
import FilterBuilder from '../filter-builder/filter-builder';
import MultiSelectionSearchBar from '../multi-selection-search-bar';
import TabView from '../tab-view';
import { CustomerProps } from './interface';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      width: '100%',
    },
    heading: {
      fontSize: '12px',
      letterSpacing: 0,
      color: '#999999',
      fontWeight: 700,
    },
    secondaryHeading: {
      fontSize: theme.typography.pxToRem(15),
      color: theme.palette.text.secondary,
    },
    infoIcon: {
      fontSize: '.875rem',
      color: '#6239EB',
      cursor: 'pointer',
    },
    radio: {
      color: 'rgba(153,153,153,1)',
      fontSize: '14px',
      marginBottom: '-15px',
    },
    text: {
      width: '80%',
    },
    toolTipContainer: {
      display: 'inline-block',
      position: 'relative',
      top: '4px',
      left: '-4px',
    },
  }),
);

export const CustomerFilter: React.FC<CustomerProps> = (props) => {
  const {
    open,
    setOpen,
    title,
    activeTabIndex,
    setActiveTabIndex,
    objectMetadata,
    searchFields,
    executeQuery,
    selectedValues,
    setSelectedValues,
    selectedObjectStyleDecider,
    disabled = false,
    customerBillCycleDay,
    setCustomerBillCycleDay,
    billCycleDayOptions,
    customerBillCycleDaySelected,
    setCustomerBillCycleDaySelected,
    isWebCreated,
    customerFilter,
    setCustomerFilter,
    customerInvokeObject,
    savedFilters,
    associateSubsidiaries,
    setAssociateSubsidiaries,
    associateBillingAccounts,
    setAssociateBillingAccounts,
  } = props;
  const [value, setValue] = useState('');
  const [billCycleDay, setBillCycleDay] = useState('1st of Month');
  const [referencedFilter, setReferencedFilter] = useState<RubyFilter | null>(null);
  const classes = useStyles();
  const [temporaryFilter, setTemporaryFilter] = useState();

  const tabs = isWebCreated
    ? [
        { id: '0', name: 'Simple', displayName: 'Simple' },
        { id: '1', name: 'Advanced', displayName: 'Advanced' },
      ]
    : [{ id: '0', name: 'GraphQL Filter', displayName: 'GraphQL Filter' }];

  const topTitle =
    'If there are no specific customers selected, all customers meet the filter criteria will be included in this billing schedule.';

  const associatedTitle = `Indicates that all subsidiaries (child customers) of the filtered customers will be included in this billing schedule.`;
  return (
    <>
      <Grid>
        <Accordion
          expanded={open}
          onChange={(event, expanded) => setOpen(expanded)}
          style={{ boxShadow: 'none' }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            style={{ display: '-webkit-inline-flex', padding: '0px' }}
          >
            <Typography className={classes.heading}>{title}</Typography>
          </AccordionSummary>
          <TabView
            tabs={tabs}
            variant={'standard'}
            activeTabIndex={activeTabIndex}
            disabledIndex={
              disabled
                ? tabs.map((tab) => {
                    return Number(tab.id);
                  })
                : []
            }
            reverseColorStyle={true}
            tabHeightStyle={true}
            handleUpdateTabIndex={(newActiveTabIndex) => {
              setActiveTabIndex(newActiveTabIndex);
            }}
          >
            <Grid
              container
              spacing={1}
              xs={12}
              style={{
                margin: '0px',
                border: '1px solid #EEE',
                minHeight: '300px',
                display: 'block',
                paddingBottom: '40px',
              }}
            >
              {isWebCreated && activeTabIndex === 0 && (
                <Grid container spacing={3} xs={12}>
                  <Grid
                    item
                    container
                    spacing={2}
                    style={{ marginTop: '20px', marginLeft: '25.5px' }}
                  >
                    <Grid item style={{ fontSize: '14px', color: '#999999' }}>
                      Include Customers:
                    </Grid>
                    <Grid item style={{ marginTop: '1px', marginLeft: '-10px' }}>
                      <Tooltip title={topTitle} arrow placement="top">
                        <InfoIcon className={classes.infoIcon} />
                      </Tooltip>
                    </Grid>
                  </Grid>
                  <Grid item xs={12}>
                    <MultiSelectionSearchBar
                      value={value}
                      setValue={setValue}
                      selectedValues={selectedValues}
                      setSelectedValues={setSelectedValues}
                      selectedObjectStyleDecider={selectedObjectStyleDecider}
                      fullWidth={true}
                      searchStyle={{
                        marginLeft: '26px',
                        marginTop: '-10px',
                      }}
                      orderByField={{
                        columnName: 'name',
                        direction: 'asc',
                      }}
                      searchField={
                        searchFields
                          ? searchFields
                          : [
                              {
                                apiName: 'id',
                                name: 'id',
                                type: 'bId',
                              },
                              {
                                apiName: 'name',
                                name: 'Name',
                                type: 'text',
                              },
                            ]
                      }
                      executeQuery={executeQuery}
                      objectMetadata={objectMetadata}
                      disabled={disabled}
                    />
                  </Grid>
                  <Grid item container xs={12} style={{ marginLeft: '26.5px' }}>
                    <Grid item xs={12}>
                      <Grid container style={{ marginTop: '5px' }} xs={12}>
                        <Grid item xs={3}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                readOnly={false}
                                value={associateSubsidiaries}
                                checked={associateSubsidiaries}
                                name={'Subsidiaries'}
                                required={false}
                                disabled={disabled}
                                disableRipple={true}
                                onChange={(event) => {
                                  setAssociateSubsidiaries(event.target.checked);
                                }}
                              />
                            }
                            label={'Include Subsidiaries'}
                          />
                          <div className={classes.toolTipContainer}>
                            <Tooltip title={associatedTitle} arrow placement="top">
                              <InfoIcon className={classes.infoIcon} />
                            </Tooltip>
                          </div>
                        </Grid>
                        {/* Hide the billing accounts temporarily */}
                        <Grid item style={{ display: 'none' }}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                readOnly={false}
                                value={associateBillingAccounts}
                                checked={associateBillingAccounts}
                                name={'Billing Accounts'}
                                required={false}
                                disabled={disabled}
                                disableRipple={true}
                                onChange={() => {
                                  setAssociateBillingAccounts(true);
                                }}
                              />
                            }
                            label={'Billing Accounts'}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item container xs={12} style={{ marginLeft: '26.5px' }}>
                    <Grid item xs={12}>
                      <span className={classes.radio} style={{ marginBottom: '5px' }}>
                        {' '}
                        With Bill Cycle Day{' '}
                      </span>
                      <RadioGroup
                        aria-label="metadata-radio"
                        row
                        name={'With Bill Cycle Day'}
                        value={customerBillCycleDaySelected}
                        onChange={(event, value) => {
                          setCustomerBillCycleDaySelected(event.target.value);
                          console.debug('newValue', event.target.value);
                        }}
                      >
                        <Grid item container xs={12}>
                          <Grid item xs={3} style={{ display: 'flex', alignItems: 'center' }}>
                            <FormControlLabel
                              value={'allBillCycleDay'}
                              control={<Radio size="small" />}
                              disabled={disabled}
                              label={'All Bill Cycle Day'}
                              onChange={() => {
                                setCustomerBillCycleDay(null);
                              }}
                            />
                          </Grid>
                          <Grid item>
                            <FormControlLabel
                              value={'specificBillCycleDays'}
                              control={<Radio size="small" />}
                              label={
                                <Grid container spacing={2} xs={12}>
                                  <Grid item style={{ marginTop: '8px' }}>
                                    Specific Bill Cycle Days:
                                  </Grid>
                                  <Grid item xs={1}>
                                    <Select
                                      value={
                                        customerBillCycleDay ? customerBillCycleDay : billCycleDay
                                      }
                                      displayEmpty
                                      name={'field'}
                                      placeholder={'Field'}
                                      onChange={(event) => {
                                        const value = event.target.value;
                                        setCustomerBillCycleDay(value);
                                        // @ts-ignore
                                        setBillCycleDay(value);
                                      }}
                                      fullWidth={false}
                                      label={'Field'}
                                      disabled={disabled}
                                      input={
                                        <SelectInputBase
                                          placeholder={'Field'}
                                          disabled={disabled}
                                        />
                                      }
                                    >
                                      {billCycleDayOptions.map((field, index) => {
                                        return (
                                          <MenuItem key={index} value={field}>
                                            {field}
                                          </MenuItem>
                                        );
                                      })}
                                    </Select>
                                  </Grid>
                                </Grid>
                              }
                              disabled={disabled}
                            />
                          </Grid>
                        </Grid>
                      </RadioGroup>
                    </Grid>
                  </Grid>
                </Grid>
              )}

              {isWebCreated && activeTabIndex === 1 && (
                <Grid style={{ marginTop: '20px', marginLeft: '25.5px' }}>
                  <FilterBuilder
                    objectMetadata={objectMetadata}
                    referencedFilter={referencedFilter}
                    filterName={''}
                    savedFilters={savedFilters}
                    searchResult=""
                    hiddenButton={true}
                    graphqlGenerator={true}
                    setGraphqlGeneratorResult={setCustomerFilter}
                    invokeObject={customerInvokeObject}
                    disabled={disabled}
                    setTemporaryFilter={setTemporaryFilter}
                    temporaryFilter={temporaryFilter}
                  />
                </Grid>
              )}

              {!isWebCreated && (
                <Grid container spacing={1} xs={12}>
                  <Grid item xs={12}>
                    <TextField
                      multiline
                      value={customerFilter}
                      disabled={true}
                      aria-readonly={true}
                      variant="outlined"
                      style={{ width: '890px', marginLeft: '10px', marginTop: '5px' }}
                      inputProps={{
                        style: {
                          height: 275,
                          padding: '0 14px',
                          color: '#4d4c50',
                          fontWeight: 'normal',
                          fontSize: '14px',
                          overflow: 'auto',
                        },
                      }}
                    />
                  </Grid>
                </Grid>
              )}
            </Grid>
          </TabView>
        </Accordion>
      </Grid>
    </>
  );
};

export default CustomerFilter;

import React from 'react';
import Button from '@material-ui/core/Button';
import Menu from '@material-ui/core/Menu';
import MenuItem from '@material-ui/core/MenuItem';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { makeStyles } from '@material-ui/core/styles';
import PopupState, { bindTrigger, bindMenu } from 'material-ui-popup-state';
import { Props } from './interface';

const useStyles = makeStyles({
  btn: {
    color: '#6239eb',
    fontSize: '.8rem',
    textTransform: 'capitalize',
    '&:hover:not([disabled])': {
      textDecoration: 'solid',
      backgroundColor: '#e9e4f8',
      color: '#6239eb',
    },
  },
  menuItem: {
    fontSize: '.8rem',
  },
});

const ButtonDropDown: React.FC<Props> = ({ options, handleOnClick }) => {
  const classes = useStyles();

  const handleClose = (popupState: any, index: number) => {
    popupState.close();
    handleOnClick(index);
  };

  return (
    <>
      <PopupState variant="popover" popupId="demo-popup-popover">
        {(popupState) => (
          <div>
            <Button
              className={classes.btn}
              {...bindTrigger(popupState)}
              disableRipple
              endIcon={<ExpandMoreIcon />}
            >
              More
            </Button>
            <Menu
              {...bindMenu(popupState)}
              getContentAnchorEl={null}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'center',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'center',
              }}
            >
              {options.map((option, index) => (
                <MenuItem
                  className={classes.menuItem}
                  key={option}
                  onClick={() => handleClose(popupState, index)}
                >
                  {option}
                </MenuItem>
              ))}
            </Menu>
          </div>
        )}
      </PopupState>
    </>
  );
};

export default ButtonDropDown;

import React, { useState, useEffect } from 'react';
import { HintFlowProps } from '../hint';
import Hint from './hint';
import { Button } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import CheckBoxIcon from '@material-ui/icons/CheckBox';

const useStyles = makeStyles((theme) => ({
  action: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: '16px',
  },
  btnNext: {
    backgroundColor: 'rgba(236, 236, 236, 1)',
    '&:hover': {
      backgroundColor: 'rgba(255, 255, 255, 1)',
    },
  },
  hideTips: {
    cursor: 'pointer',
    display: 'inline-flex',
    alignItems: 'center',
    color: '#FFFFFF',
    opacity: 0.7,
    lineHeight: '32px',
    fontSize: '12px',
    fontWeight: 400,
    '&:hover': {
      opacity: 1,
    },
  },
}));

export const HintFlow: React.FC<HintFlowProps> = ({
  open,
  steps,
  onFlowEnd,
  autoScroll = false,
}: HintFlowProps) => {
  const classes = useStyles();
  const [current, setCurrent] = useState(0);
  useEffect(() => setCurrent(0), [open]);

  const { anchorEl, currentStep } = React.useMemo(() => {
    const currentStep = steps[current];
    let currentAnchorEl = null;
    if (typeof currentStep.anchorEl === 'string') {
      currentAnchorEl = document.getElementById(currentStep.anchorEl);
    } else if (typeof currentStep.anchorEl === 'function') {
      currentAnchorEl = currentStep.anchorEl();
    }

    if (autoScroll && currentAnchorEl?.scrollIntoView) {
      currentAnchorEl?.scrollIntoView();
    }

    return {
      anchorEl: currentAnchorEl,
      currentStep: currentStep,
    };
  }, [autoScroll, current, steps]);

  if (!open || steps.length === 0) {
    return null;
  }

  const showNext = () => {
    if (current < steps.length - 1) {
      setCurrent(current + 1);
    } else {
      onFlowEnd();
    }
  };

  return (
    <Hint
      open={open}
      anchorEl={anchorEl}
      title={steps.length > 1 ? `Step ${current + 1} of ${steps.length}` : undefined}
      message={currentStep.message}
      placement={currentStep.placement || 'bottom'}
      action={
        <div className={classes.action}>
          <a
            role="button"
            className={classes.hideTips}
            onClick={(event) => {
              onFlowEnd();
            }}
          >
            <CheckBoxIcon style={{ width: '14px', height: '14px', marginRight: '8px' }} />
            Hide these tips
          </a>
          <Button className={classes.btnNext} onClick={showNext}>
            {current === steps.length - 1 ? 'Got it' : 'Next'}
          </Button>
        </div>
      }
    />
  );
};

export default HintFlow;

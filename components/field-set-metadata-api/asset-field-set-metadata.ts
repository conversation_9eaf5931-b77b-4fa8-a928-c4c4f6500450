export default [
  {
    apiName: 'quantity',
    name: 'Quantity',
    type: 'decimal',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 10,
    scale: 2,
  },
  {
    apiName: 'price',
    name: 'Price',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 18,
    scale: 0,
  },
  {
    apiName: 'productFamily',
    name: 'Product Family',
    type: 'pickList',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    valueSet: {
      apiName: 'ProductCategory',
      name: 'Product Category',
      defaultValue: 'Product',
      alphabeticallyOrdered: true,
      valuePairs: [
        {
          apiName: 'Product',
          name: 'Product',
          orderNumber: 0,
        },
        {
          apiName: 'Service',
          name: 'Service',
          orderNumber: 0,
        },
        {
          apiName: 'None',
          name: 'None',
          orderNumber: 0,
        },
      ],
    },
  },
];

import React, { useCallback, useEffect } from 'react';
import Draggable, { ControlPosition, DraggableData, DraggableEvent } from 'react-draggable';
import { Icon, IconButton } from '@material-ui/core';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import Paper, { PaperProps } from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import { Close, DragHandle } from '@material-ui/icons';
import CloseIcon from '@material-ui/icons/Close';
import { CloseMenuIcon, MaterialSymbolsDragPan } from '../icons';
import Loading from '../loading';
import { RubyButtonBar, RubyButtonBarProps } from '../ruby-button';
import { Props } from './interface';

const boundaryMargin = 24;

const usePaperComponent = () => {
  const dialogContainerRef = React.useRef<HTMLDivElement>(null);
  const [dialogState, setDialogState] = React.useState<{
    bounds: {
      left: number;
      top: number;
      bottom: number;
      right: number;
    };
    position: ControlPosition | undefined;
  }>({
    bounds: { left: 0, top: 0, bottom: 0, right: 0 },
    position: undefined,
  });

  const onStart = (event: DraggableEvent, uiData: DraggableData) => {
    const { clientWidth, clientHeight } = window?.document?.documentElement;
    const targetRect = dialogContainerRef?.current?.getBoundingClientRect();
    if (targetRect) {
      setDialogState((prevState) => ({
        ...prevState,
        bounds: {
          left: -targetRect?.left + uiData?.x + boundaryMargin,
          right: clientWidth - (targetRect?.right - uiData?.x) - boundaryMargin,
          top: -targetRect?.top + uiData?.y + boundaryMargin,
          bottom: clientHeight - (targetRect?.bottom - uiData?.y) - boundaryMargin,
        },
        position: undefined,
      }));
    }
  };

  const onStop = (event: DraggableEvent, uiData: DraggableData) => {
    setDialogState((prevState) => ({
      ...prevState,
      position: { x: uiData?.x, y: uiData?.y },
    }));
  };

  useEffect(() => {
    const handleResize = () => {
      setDialogState((prevState) => ({
        ...prevState,
        bounds: { left: 0, top: 0, bottom: 0, right: 0 },
        position: { x: 0, y: 0 },
      }));
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return {
    dialogState,
    onStart,
    onStop,
    dialogContainerRef,
  };
};

const PaperComponentWithCloseSection: React.FC<PaperProps> = (props) => {
  const { dialogState, onStart, onStop, dialogContainerRef } = usePaperComponent();

  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
      nodeRef={dialogContainerRef}
      onStart={(event, uiData) => onStart(event, uiData)}
      onStop={(event, uiData) => onStop(event, uiData)}
      defaultPosition={{ x: 0, y: 0 }}
      bounds={dialogState.bounds}
      position={dialogState.position}
    >
      <Paper ref={dialogContainerRef} {...props} style={{ borderRadius: 16 }} />
    </Draggable>
  );
};

const PaperComponent: React.FC<PaperProps> = (props) => {
  const { dialogState, onStart, onStop, dialogContainerRef } = usePaperComponent();

  return (
    <Draggable
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
      nodeRef={dialogContainerRef}
      onStart={(event, uiData) => onStart(event, uiData)}
      onStop={(event, uiData) => onStop(event, uiData)}
      defaultPosition={{ x: 0, y: 0 }}
      bounds={dialogState.bounds}
      position={dialogState.position}
    >
      <Paper ref={dialogContainerRef} {...props} />
    </Draggable>
  );
};
const useStyles = makeStyles({
  dialogTitle: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 0,
    paddingTop: '24px',
  },
  dialogFooter: {
    padding: '8px 24px 24px',
  },
  closeIcon: {
    cursor: 'pointer',
  },
  dialogContent: {
    paddingBottom: '24px',
    overflowX: 'hidden',
  },
  title: {
    opacity: 0.8,
    padding: '10px 0',
  },
});

export const DialogComponent: React.FC<Props> = ({
  open,
  handleClose,
  title,
  width,
  children,
  handleSubmit,
  loading,
  loadingText,
  dialogOptions,
  dontShowCloseIcon,
  cancelButtonText = 'Cancel',
  submitButtonText = 'Confirm',
  actions,
  processing,
  titleStyle,
  className,
  closeBar,
  dialogContentStyle = {},
  customTitleMode,
  customDialogFooter,
}) => {
  const classes = useStyles();

  const getRightButtons = useCallback(() => {
    if (actions?.rightButtons) return actions.rightButtons;
    if (handleSubmit) return [{ text: submitButtonText, onClick: handleSubmit }];
    return [];
  }, [actions, handleSubmit, submitButtonText]);

  const getLeftButtons = useCallback(() => {
    if (actions?.leftButtons) return actions.leftButtons;
    if (handleClose && handleSubmit) return [{ text: cancelButtonText, onClick: handleClose }];
    return [];
  }, [actions, cancelButtonText, handleClose, handleSubmit]);

  const [buttonBarProps, setButtonBarProps] = React.useState<RubyButtonBarProps>({
    leftButtons: [],
    rightButtons: [],
    variant: 'inPlace',
  });
  const [showButtonBar, setShowButtonBar] = React.useState(false);

  useEffect(() => {
    setButtonBarProps({
      leftButtons: getLeftButtons(),
      rightButtons: getRightButtons(),
      variant: 'inPlace',
      justify: actions?.justify,
      processing: processing,
    });
  }, [getLeftButtons, getRightButtons, processing, actions]);

  useEffect(() => {
    if (buttonBarProps.leftButtons.length > 0 || buttonBarProps.rightButtons.length > 0) {
      setShowButtonBar(true);
    } else {
      setShowButtonBar(false);
    }
  }, [buttonBarProps]);

  const isFullScreen = dialogOptions ? dialogOptions.fullScreen : undefined;
  const defaultButtonBarStyles = isFullScreen
    ? {}
    : {
        paddingLeft: '12px',
        paddingRight: '12px',
      };
  return (
    <Dialog
      aria-labelledby="dialog draggable-dialog-title"
      aria-describedby="Dialog"
      open={open}
      maxWidth={width}
      fullWidth
      fullScreen={isFullScreen}
      onClose={(event, reason) => {
        if (reason !== 'backdropClick' && typeof handleClose === 'function') {
          handleClose();
        }
      }}
      PaperComponent={customTitleMode ? PaperComponentWithCloseSection : PaperComponent}
      className={className}
      style={{ fontFamily: 'AvenirNextMedium, avenirnext, Arial, sans-serif' }}
    >
      {loading ? (
        <Loading size="50px" />
      ) : (
        <>
          <DialogTitle
            id="draggable-dialog-title"
            style={{ cursor: 'move' }}
            disableTypography
            className={classes.dialogTitle}
          >
            {closeBar && <>{closeBar}</>}
            {title && (
              <Typography variant="h5" className={classes.title} style={titleStyle}>
                {title}
              </Typography>
            )}
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <MaterialSymbolsDragPan style={{ color: '#D8D8D8' }} />
              {!dontShowCloseIcon && (
                <CloseIcon aria-label="close" className={classes.closeIcon} onClick={handleClose} />
              )}
            </div>
          </DialogTitle>
          <DialogContent
            className={!handleSubmit ? classes.dialogContent : ''}
            style={dialogContentStyle}
          >
            {children}
          </DialogContent>
          {showButtonBar && (
            <DialogActions className={customDialogFooter || classes.dialogFooter}>
              <RubyButtonBar
                {...buttonBarProps}
                style={{
                  ...defaultButtonBarStyles,
                  ...(buttonBarProps.style || {}),
                }}
                customButtonBarStyles={{
                  paddingInline: 0,
                }}
              />
            </DialogActions>
          )}
        </>
      )}
    </Dialog>
  );
};

export default DialogComponent;

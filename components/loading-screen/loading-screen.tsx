import React from 'react';

import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';

import Loading from '../loading';
import { Props } from './interface';

const useStyles = makeStyles({
  center: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '20px',
    margin: '0 auto',
  },
});

const LoadingScreen: React.FC<Props> = ({ loadingText }) => {
  const classes = useStyles();

  return (
    <div className={classes.center}>
      <Loading size="50px" />
      <Typography variant="body1" component="p">
        {loadingText}
      </Typography>
    </div>
  );
};

export default LoadingScreen;

import React from 'react';

import type {
  NetSuiteConfigureDetail,
  NetSuiteIntegration,
  NetSuiteTrayDetail,
  NetSuiteUpdateDetail,
} from './interface';

export interface NetSuiteConnectionService {
  queryQuickBookConfigure: (environment?: string) => Promise<NetSuiteIntegration | null>;
  createQuickBooksIntegration: (environment?: string) => Promise<QuickBookTrayDetail>;
  updateQuickBooksIntegration: (configuration: QuickBookUpdateDetail) => Promise<void>;
  configureQuickBooksIntegration: (
    configuration: QuickBookConfigureDetail,
  ) => Promise<QuickBookTrayDetail | NetSuiteIntegration>;
  handleUploadFiles: (data: any, type: string) => Promise<any>;
  loadMappingData: (
    quickBookMappingType: string,
    env: string,
    pageSize?: number,
    currentPage?: number,
    keyword?: string,
  ) => Promise<any>;
  handleUpdateRows: (rows: any, env: string, type: string) => Promise<void>;
  handleDeleteRow: (rows: any, type: string) => Promise<void>;
}

export const QuickBooksConnectionContext = React.createContext<QuickBooksConnectionService | null>(
  null,
);

export default QuickBooksConnectionContext;

import Link from '@material-ui/core/Link';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import dayjs from 'dayjs';
import React from 'react';

import type { RenewalRequest } from '../change-cart';
import { calculateEndDate } from '../change-cart/change-order-util';
import { formatTerm } from '../util/term-converter';
import type { ChangeItemText } from './interface';

const useStyles = makeStyles((theme) => ({
  itemText: {
    color: theme.palette.grey[900],
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
}));

const RenewText: React.FC<ChangeItemText> = (props) => {
  const { changeItem } = props;

  const classes = useStyles();

  const request = changeItem.request as RenewalRequest;
  const renewalTerm = request.renewalTerm;
  const termDimension = (request.termDimension || changeItem.asset.uom.termDimension).toLowerCase();

  const dayAfterSubscriptionEndDate = dayjs(
    dayjs(changeItem.asset.subscriptionEndDate).add(1, 'day'),
  );

  if (request.updateToEvergreen) {
    return (
      <Typography variant="body2" component="p" display="inline">
        <Typography className={classes.itemText} variant="body2" component="p" display="inline">
          {request.objectType}{' '}
        </Typography>
        <Typography
          variant="body2"
          className={classes.textUnderline}
          component="p"
          display="inline"
        >
          {request.assetName}{' '}
        </Typography>
        <Typography className={classes.itemText} variant="body2" component="p" display="inline">
          renews to{' '}
        </Typography>
        <Link
          variant="body2"
          href="#"
          onClick={() => {}}
          className={classes.textHighlight}
          component="p"
          display="inline"
          style={{
            textDecoration: 'none',
          }}
        >
          evergreen
        </Link>
        <Typography className={classes.itemText} variant="body2" component="p" display="inline">
          .
        </Typography>
      </Typography>
    );
  }

  return (
    <Typography variant="body2" component="p" display="inline">
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        {request.objectType}{' '}
      </Typography>
      <Typography variant="body2" className={classes.textUnderline} component="p" display="inline">
        {request.assetName}{' '}
      </Typography>
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        renews for another{' '}
      </Typography>
      <Link
        variant="body2"
        href="#"
        onClick={() => {}}
        className={classes.textHighlight}
        component="p"
        display="inline"
      >
        {formatTerm(renewalTerm)}{' '}
      </Link>
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        {termDimension}(s) on{' '}
        <span className={classes.textHighlight}>
          {dayjs(request.renewalDate || dayAfterSubscriptionEndDate).format('MM/DD/YYYY')}
        </span>
        . The new subscription end date will be{' '}
      </Typography>
      <Typography variant="body2" className={classes.textHighlight} component="p" display="inline">
        {calculateEndDate(
          renewalTerm,
          dayjs(request.renewalDate || dayAfterSubscriptionEndDate).toDate(),
          termDimension,
          'MM/DD/YYYY',
        )}
      </Typography>
    </Typography>
  );
};

export default RenewText;

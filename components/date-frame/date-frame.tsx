import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Grid } from '@material-ui/core';
import { ExtendedFormContext, RubyButton, RubyField } from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import * as yup from 'yup';
import FormSection from '../form-section';
import { useYupValidationResolver } from '../form-validation';
import { DateFrameOption, DateFrameProps } from './interface';

const dateFormat = 'MM/DD/YYYY';
const thisYear = dayjs().year();
const today = dayjs().format(dateFormat);
const fiveYearsLater = dayjs().add(5, 'year').format(dateFormat);

const timeFrameOptions: Array<DateFrameOption> = [
  {
    name: 'Current and next fiscal year',
    apiName: 'currentAndNextFiscalYear',
    startDate: `01/01/${thisYear}`,
    endDate: `12/31/${thisYear + 1}`,
  },
  {
    name: 'Current and next 3 fiscal years',
    apiName: 'currentAndNext3FiscalYears',
    startDate: `01/01/${thisYear}`,
    endDate: `12/31/${thisYear + 3}`,
  },
  {
    name: 'Previous, current and next fiscal year',
    apiName: 'previousCurrentAndNextFiscalYear',
    startDate: `01/01/${thisYear - 1}`,
    endDate: `12/31/${thisYear + 1}`,
  },
  {
    name: 'Previous, current and next 3 fiscal years',
    apiName: 'previousCurrentAndNext3FiscalYears',
    startDate: `01/01/${thisYear - 1}`,
    endDate: `12/31/${thisYear + 3}`,
  },
  {
    name: 'Custom period',
    apiName: 'customPeriod',
    startDate: `01/01/${thisYear}`,
    endDate: `12/31/${thisYear + 3}`,
  },
];

const fields: Array<RubyField> = [
  {
    name: 'TIME FRAME',
    apiName: 'timeFrame',
    type: 'Picklist',
    xs: 4,
    required: true,
    creatable: true,
    valueSet: {
      valuePairs: timeFrameOptions.map((option) => ({
        name: option.name,
        apiName: option.apiName,
      })),
    },
  },
  {
    type: 'Date',
    apiName: 'startDate',
    name: 'Start Date',
    required: true,
    creatable: true,
    xs: 4,
  },
  {
    type: 'Date',
    apiName: 'endDate',
    name: 'End Date',
    required: true,
    creatable: true,
    xs: 4,
  },
];

export const DateFrame: React.FC<DateFrameProps> = (props) => {
  const { saveTimeFrameRange, defaultDateFrameOption } = props;

  const validationSchema = React.useMemo<yup.ObjectSchema>(() => {
    return yup.object().shape({
      startDate: yup
        .date()
        .required('Start Date is required')
        .max(yup.ref('endDate'), 'Start Date must be before End Date')
        .test(
          'is-in-range',
          'There should not be over 60 months between Start Date and End Date',
          function () {
            const { startDate, endDate } = this.parent;
            return dayjs(startDate).add(60, 'month').isAfter(dayjs(endDate));
          },
        ),
      endDate: yup.date().required('End Date is required'),
    });
  }, []);

  const defaultValues = defaultDateFrameOption
    ? {
        timeFrame: defaultDateFrameOption.apiName,
        startDate: defaultDateFrameOption.startDate,
        endDate: defaultDateFrameOption.endDate,
      }
    : {
        timeFrame: 'customPeriod',
        startDate: today,
        endDate: fiveYearsLater,
      };

  const resolver = useYupValidationResolver(validationSchema);

  const methods = useForm({
    defaultValues: defaultValues,
    // @ts-ignore
    resolver,
  });

  const afterFieldChange = (
    fieldApiName: string,
    fieldNewValue: any,
    fieldOldValue: any,
    values: Record<string, any>,
    formContext: ExtendedFormContext,
  ) => {
    const { setValue } = formContext;
    if (fieldApiName === 'timeFrame') {
      setValue(
        'startDate',
        timeFrameOptions.find((option) => option.apiName === fieldNewValue)?.startDate ||
          `01/01/${thisYear}`,
      );
      setValue(
        'endDate',
        timeFrameOptions.find((option) => option.apiName === fieldNewValue)?.endDate ||
          `12/31/${thisYear + 1}`,
      );
    }
    if (fieldApiName === 'startDate' || fieldApiName === 'endDate') {
      setValue(
        'timeFrame',
        timeFrameOptions.find((option) => {
          return (
            dayjs(option.startDate).format(dateFormat) ===
              dayjs(values.startDate).format(dateFormat) &&
            dayjs(option.endDate).format(dateFormat) === dayjs(values.endDate).format(dateFormat)
          );
        })?.apiName || 'customPeriod',
      );
    }
  };

  return (
    <FormProvider {...methods}>
      <Grid container alignItems="flex-end">
        <Grid item xs={9}>
          <FormSection
            hiddenFields={[]}
            setHiddenFields={() => {}}
            fields={fields}
            showDivider={false}
            defaultDateFormat="MM/dd/yyyy"
            afterFieldChange={afterFieldChange}
          />
        </Grid>
        <Grid item xs={3}>
          <RubyButton
            text=""
            // @ts-ignore
            onClick={methods.handleSubmit(saveTimeFrameRange)}
            style={{ margin: '0 20px' }}
          >
            Apply
          </RubyButton>
        </Grid>
      </Grid>
    </FormProvider>
  );
};

export default DateFrame;

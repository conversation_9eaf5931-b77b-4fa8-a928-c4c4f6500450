import type { CellRendererRowProps } from '../grid-actions';
import type { Props as GridViewItemProps } from '../grid-view-item/interface';
import type {
  CardAction,
  GridViewMetadata,
  RubyField,
  RubyObject,
  ViewMetadata,
} from '../metadata/interface';
import {
  ProductRelations,
  ProductRelationWithSubscription,
} from '../modals/subscription-bulkactions/types';
import type { RubySettings } from '../ruby-settings';

export interface GridViewProps {
  gridViewMetadata: GridViewMetadata;
  fieldSetMetadata: RubyField[];
  currencyIsoCode: string;
  handleCardClick?: (object: any) => void;
  GridViewItem: React.ComponentType<GridViewItemProps>;
  orderGridViewMetadata?: GridViewMetadata | null;
  orderFieldSetMetadata?: RubyField[] | null;
  subscriptionPricingFieldSet?: RubyField[] | null;
  actionEventHandler: (argument: {
    object: any;
    objectMetadata: RubyObject;
    action: CardAction;
  }) => void | Promise<void>;
  getFieldSetMetadata?: (fieldSetApiName: string, objectApiName: string) => Promise<RubyField[]>;
  onShowDetails?: (object: any) => void;
  showDetails?: boolean;
  rubySettings?: RubySettings;
  locale?: string;
  enablePagination?: boolean;
  totalCount?: number;
  currentPage?: number;
  pageSize?: number;
  handlePageSizeChange?: (newPageSize: number) => void;
  handleCurrentPageChange?: (newPage: number) => void;
  addTransactionHubColumn?: () => {
    apiName: string;
    type: string;
    name: string;
    title: string;
    cellRenderer: ({ row }: CellRendererRowProps) => JSX.Element | null;
  };
  currentTabName?: string;
  productRelationsByActionType?: Record<string, ProductRelationWithSubscription[]>;
  getSwapUpgradeDowngradeOptions?: (params: {
    subscriptionIds: string[];
  }) => Promise<ProductRelations>;
}

export interface Props extends ViewMetadata, GridViewProps {}

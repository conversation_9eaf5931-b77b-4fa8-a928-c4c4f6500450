import React, { useState } from 'react';

import { Button, IconButton } from '@material-ui/core';
import Popover from '@material-ui/core/Popover';
import Tooltip from '@material-ui/core/Tooltip';
import { makeStyles } from '@material-ui/core/styles';
import CloseIcon from '@material-ui/icons/Close';
import <PERSON> from 'papaparse';

import { UploadIcon } from '../icons';
import { useRubySnackbar } from '../ruby-notifier';
import UploadFile from '../upload-file';

export interface UploaderProps {
  handleUploadFiles: Function;
  isSandBox: boolean;
  type: string;
}

const useStyles = makeStyles({
  iconButtonRoot: {
    padding: '0',
    marginRight: '4px',
  },
  fileUpload: {
    background: 'rgba(98,57,235,0.05)',
    border: '1px solid #6239EB',
    borderRadius: '10px',
  },
  closeButton: {
    position: 'absolute',
    top: 4,
    right: 0,
    backgroundColor: '#ffffff',
    border: 'none',
    fontWeight: 'bolder',
    cursor: 'pointer',
    padding: 0,
  },
  uploaderWrapper: {
    position: 'relative',
    margin: '20px',
  },
  button: {
    marginTop: 12,
  },
});

const columnHeaders = [
  'SalesforceAccountID,SalesforceAccountName,NetSuiteCustomerName,NetSuiteCustomerID',
];

const columnProductHeaders = [
  'SalesforceProductID,SalesforceProductName,NetSuiteProductName,NetSuiteProductID',
];

const errorColumnHeaders = [
  'LineNo,Reason,SalesforceAccountID,SalesforceAccountName,NetSuiteCustomerID,NetSuiteCustomerName',
];

const errorColumnProductHeaders = [
  'LineNo,Reason,SalesforceProductID,SalesforceProductName,NetSuiteProductID,NetSuiteProductName',
];

const TemplateData = ['00DN0000000YnvCMAS,Nue.io,Nue,214'];
const TemplateProductData = ['00DN0000000YnvCMAS,Nue.io,Nue,3182'];

export const NetSuiteFileUploader = ({ handleUploadFiles, isSandBox, type }: UploaderProps) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const { showSnackbar } = useRubySnackbar();

  const classes = useStyles();

  const handlePopoverClose = (event: any) => {
    if (event.which === 27) {
      setAnchorEl(null);
    }
  };

  const handleFilUploadClose = () => {
    setAnchorEl(null);
  };

  const onToggle = (e: any) => {
    setAnchorEl(e.currentTarget);
  };

  const downloadFile = React.useCallback(({ data, fileName, fileType }: any) => {
    const blob = new Blob([data], { type: fileType });
    const a = document.createElement('a');
    a.download = fileName;
    a.href = window.URL.createObjectURL(blob);
    const clickEvt = new MouseEvent('click', {
      view: window,
      bubbles: true,
      cancelable: true,
    });
    a.dispatchEvent(clickEvt);
    a.remove();
  }, []);

  function hasEmptyValue(obj: any) {
    for (const key in obj) {
      if (obj[key] === null || obj[key] === '') {
        return true;
      }
    }
    return false;
  }

  const downloadErrorRecords = React.useCallback(
    (errorRecords: any) => {
      const dataCsv = errorRecords.reduce((acc: any, data: any) => {
        const resultData = {
          lineNo: '',
          paymentCustomerId: '',
          paymentCustomerName: '',
          sourceCustomerId: '',
          sourceCustomerName: '',
        };
        if (type === 'customer') {
          resultData.lineNo = data.ErrorLine;

          resultData.paymentCustomerId = data.NetSuiteCustomerID;
          resultData.paymentCustomerName = data.NetSuiteCustomerName;
          resultData.sourceCustomerId = data.SalesforceAccountID;
          resultData.sourceCustomerName = data.SalesforceAccountName;
        } else {
          resultData.lineNo = data.ErrorLine;
          resultData.paymentCustomerId = data.NetSuiteProductID;
          resultData.paymentCustomerName = data.NetSuiteProductName;
          resultData.sourceCustomerId = data.SalesforceProductID;
          resultData.sourceCustomerName = data.SalesforceProductName;
        }

        const {
          lineNo,
          paymentCustomerId,
          paymentCustomerName,
          sourceCustomerId,
          sourceCustomerName,
        } = resultData;

        let reason = data.reason;
        if (reason) {
          reason = '"' + reason + '"';
        } else {
          if (hasEmptyValue(data)) {
            reason = '"' + 'Missing Value' + '"';
          }
        }

        const result = [
          lineNo,
          reason,
          sourceCustomerId,
          sourceCustomerName,
          paymentCustomerId,
          paymentCustomerName,
        ].join(',');

        acc.push(result);
        return acc;
      }, []);

      downloadFile({
        data:
          type === 'customer'
            ? [...errorColumnHeaders, ...dataCsv].join('\n')
            : [...errorColumnProductHeaders, ...dataCsv].join('\n'),
        fileName: 'ErrorRecords.csv',
        fileType: 'text/csv',
      });
    },
    [downloadFile, type],
  );

  const onDownloadTemplate = React.useCallback(() => {
    downloadFile({
      data:
        type === 'customer'
          ? [...columnHeaders, ...TemplateData].join('\n')
          : [...columnProductHeaders, ...TemplateProductData].join('\n'),
      fileName: type === 'customer' ? 'CustomerMappingTemplate.csv' : 'ProductMappingTemplate.csv',
      fileType: 'text/csv',
    });
  }, [downloadFile, type]);

  const onHandleUploadFile = React.useCallback(
    (file: File | null) => {
      if (!file) return;
      return new Promise(function (resolve: any) {
        const reader = new FileReader();
        reader.readAsText(file);
        let request: any;
        let resultData: any;
        reader.onload = async ({ target }: any) => {
          const data = target.result;
          let error;
          try {
            Papa.parse(data, {
              header: true,
              complete: function (results: any) {
                const rawData = results.data;
                if (!rawData) return;
                const requestData = rawData
                  .map((x: any) => {
                    if (x) {
                      if (type === 'customer') {
                        return {
                          sourceCustomerId: x.SalesforceAccountID,
                          sourceCustomerName: x.SalesforceAccountName,
                          paymentCustomerId: x.NetSuiteCustomerID,
                          paymentCustomerName: x.NetSuiteCustomerName,
                        };
                      } else {
                        return {
                          sourceProductId: x.SalesforceProductID,
                          sourceProductName: x.SalesforceProductName,
                          paymentProductId: x.NetSuiteProductID,
                          paymentProductName: x.NetSuiteProductName,
                        };
                      }
                    }
                  })
                  .filter((x: any) => x);
                request = {
                  externalSystem: 'NetSuite',
                  environment: isSandBox ? 'Sandbox' : 'Product',
                  transactionType: type === 'customer' ? 'Customer' : 'Product',
                  data: requestData,
                };
              },
            });
          } catch (err) {
            error = 'Please check the file encoding or file format';
            showSnackbar('error', 'Error', error);
          }
          try {
            resultData = await handleUploadFiles(request, type);
          } catch (e) {
            console.log('error', e);
          }
          resolve(resultData);
        };
      }).then(function (result: any) {
        if (result) {
          if (result.length > 0) {
            return { status: 'FailedWithData', data: result };
          } else {
            return { status: 'succeed' };
          }
        } else {
          return { status: 'error' };
        }
      });
    },
    [handleUploadFiles, isSandBox, showSnackbar, type],
  );

  return (
    <div>
      <Tooltip title="Upload Usage" arrow placement="bottom" enterDelay={300}>
        <Button variant="outlined" color="secondary" className={classes.button} onClick={onToggle}>
          Import Mapping
        </Button>
      </Tooltip>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: 'center',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <IconButton onClick={handleFilUploadClose} className={classes.closeButton} component="span">
          <CloseIcon />
        </IconButton>

        {handleUploadFiles && (
          <div className={classes.uploaderWrapper}>
            <UploadFile
              columnHeaders={columnHeaders}
              classes={classes.fileUpload}
              handleUploadFiles={onHandleUploadFile}
              DefaultIcon={UploadIcon}
              downloadTemplate={onDownloadTemplate}
              fileUploadCallerType="stripe"
              downloadErrorRecords={downloadErrorRecords}
            />
          </div>
        )}
      </Popover>
    </div>
  );
};

import { Country } from './interface';
export const countryStateList: Country[] = [
  {
    states: [],
    name: 'Afghanistan',
    code: 'AF',
  },
  {
    states: [],
    name: 'Aland Islands',
    code: 'AX',
  },
  {
    states: [],
    name: 'Albania',
    code: 'AL',
  },
  {
    states: [],
    name: 'Algeria',
    code: 'DZ',
  },
  {
    states: [],
    name: 'Andorra',
    code: 'AD',
  },
  {
    states: [],
    name: 'Angola',
    code: 'AO',
  },
  {
    states: [],
    name: 'Anguilla',
    code: 'AI',
  },
  {
    states: [],
    name: 'Antarctica',
    code: 'AQ',
  },
  {
    states: [],
    name: 'Antigua and Barbuda',
    code: 'AG',
  },
  {
    states: [],
    name: 'Argentina',
    code: 'AR',
  },
  {
    states: [],
    name: 'Armenia',
    code: 'AM',
  },
  {
    states: [],
    name: 'Aruba',
    code: 'AW',
  },
  {
    states: [
      {
        name: 'Australian Capital Territory',
        code: 'ACT',
      },
      {
        name: 'New South Wales',
        code: 'NSW',
      },
      {
        name: 'Northern Territory',
        code: 'NT',
      },
      {
        name: 'Queensland',
        code: 'QLD',
      },
      {
        name: 'South Australia',
        code: 'SA',
      },
      {
        name: 'Tasmania',
        code: 'TAS',
      },
      {
        name: 'Victoria',
        code: 'VIC',
      },
      {
        name: 'Western Australia',
        code: 'WA',
      },
    ],
    name: 'Australia',
    code: 'AU',
  },
  {
    states: [],
    name: 'Austria',
    code: 'AT',
  },
  {
    states: [],
    name: 'Azerbaijan',
    code: 'AZ',
  },
  {
    states: [],
    name: 'Bahamas',
    code: 'BS',
  },
  {
    states: [],
    name: 'Bahrain',
    code: 'BH',
  },
  {
    states: [],
    name: 'Bangladesh',
    code: 'BD',
  },
  {
    states: [],
    name: 'Barbados',
    code: 'BB',
  },
  {
    states: [],
    name: 'Belarus',
    code: 'BY',
  },
  {
    states: [],
    name: 'Belgium',
    code: 'BE',
  },
  {
    states: [],
    name: 'Belize',
    code: 'BZ',
  },
  {
    states: [],
    name: 'Benin',
    code: 'BJ',
  },
  {
    states: [],
    name: 'Bermuda',
    code: 'BM',
  },
  {
    states: [],
    name: 'Bhutan',
    code: 'BT',
  },
  {
    states: [],
    name: 'Bolivia, Plurinational State of',
    code: 'BO',
  },
  {
    states: [],
    name: 'Bonaire, Sint Eustatius and Saba',
    code: 'BQ',
  },
  {
    states: [],
    name: 'Bosnia and Herzegovina',
    code: 'BA',
  },
  {
    states: [],
    name: 'Botswana',
    code: 'BW',
  },
  {
    states: [],
    name: 'Bouvet Island',
    code: 'BV',
  },
  {
    states: [
      {
        name: 'Acre',
        code: 'AC',
      },
      {
        name: 'Alagoas',
        code: 'AL',
      },
      {
        name: 'Amapá',
        code: 'AP',
      },
      {
        name: 'Amazonas',
        code: 'AM',
      },
      {
        name: 'Bahia',
        code: 'BA',
      },
      {
        name: 'Ceará',
        code: 'CE',
      },
      {
        name: 'Distrito Federal',
        code: 'DF',
      },
      {
        name: 'Espírito Santo',
        code: 'ES',
      },
      {
        name: 'Goiás',
        code: 'GO',
      },
      {
        name: 'Maranhão',
        code: 'MA',
      },
      {
        name: 'Mato Grosso',
        code: 'MT',
      },
      {
        name: 'Mato Grosso do Sul',
        code: 'MS',
      },
      {
        name: 'Minas Gerais',
        code: 'MG',
      },
      {
        name: 'Pará',
        code: 'PA',
      },
      {
        name: 'Paraíba',
        code: 'PB',
      },
      {
        name: 'Paraná',
        code: 'PR',
      },
      {
        name: 'Pernambuco',
        code: 'PE',
      },
      {
        name: 'Piauí',
        code: 'PI',
      },
      {
        name: 'Rio de Janeiro',
        code: 'RJ',
      },
      {
        name: 'Rio Grande do Norte',
        code: 'RN',
      },
      {
        name: 'Rio Grande do Sul',
        code: 'RS',
      },
      {
        name: 'Rondônia',
        code: 'RO',
      },
      {
        name: 'Roraima',
        code: 'RR',
      },
      {
        name: 'Santa Catarina',
        code: 'SC',
      },
      {
        name: 'São Paulo',
        code: 'SP',
      },
      {
        name: 'Sergipe',
        code: 'SE',
      },
      {
        name: 'Tocantins',
        code: 'TO',
      },
    ],
    name: 'Brazil',
    code: 'BR',
  },
  {
    states: [],
    name: 'British Indian Ocean Territory',
    code: 'IO',
  },
  {
    states: [],
    name: 'Brunei Darussalam',
    code: 'BN',
  },
  {
    states: [],
    name: 'Bulgaria',
    code: 'BG',
  },
  {
    states: [],
    name: 'Burkina Faso',
    code: 'BF',
  },
  {
    states: [],
    name: 'Burundi',
    code: 'BI',
  },
  {
    states: [],
    name: 'Cambodia',
    code: 'KH',
  },
  {
    states: [],
    name: 'Cameroon',
    code: 'CM',
  },
  {
    states: [
      {
        name: 'Alberta',
        code: 'AB',
      },
      {
        name: 'British Columbia',
        code: 'BC',
      },
      {
        name: 'Manitoba',
        code: 'MB',
      },
      {
        name: 'New Brunswick',
        code: 'NB',
      },
      {
        name: 'Newfoundland and Labrador',
        code: 'NL',
      },
      {
        name: 'Northwest Territories',
        code: 'NT',
      },
      {
        name: 'Nova Scotia',
        code: 'NS',
      },
      {
        name: 'Nunavut',
        code: 'NU',
      },
      {
        name: 'Ontario',
        code: 'ON',
      },
      {
        name: 'Prince Edward Island',
        code: 'PE',
      },
      {
        name: 'Quebec',
        code: 'QC',
      },
      {
        name: 'Saskatchewan',
        code: 'SK',
      },
      {
        name: 'Yukon Territories',
        code: 'YT',
      },
    ],
    name: 'Canada',
    code: 'CA',
  },
  {
    states: [],
    name: 'Cape Verde',
    code: 'CV',
  },
  {
    states: [],
    name: 'Cayman Islands',
    code: 'KY',
  },
  {
    states: [],
    name: 'Central African Republic',
    code: 'CF',
  },
  {
    states: [],
    name: 'Chad',
    code: 'TD',
  },
  {
    states: [],
    name: 'Chile',
    code: 'CL',
  },
  {
    states: [
      {
        name: 'Anhui',
        code: '34',
      },
      {
        name: 'Beijing',
        code: '11',
      },
      {
        name: 'Chongqing',
        code: '50',
      },
      {
        name: 'Fujian',
        code: '35',
      },
      {
        name: 'Gansu',
        code: '62',
      },
      {
        name: 'Guangdong',
        code: '44',
      },
      {
        name: 'Guangxi',
        code: '45',
      },
      {
        name: 'Guizhou',
        code: '52',
      },
      {
        name: 'Hainan',
        code: '46',
      },
      {
        name: 'Hebei',
        code: '13',
      },
      {
        name: 'Heilongjiang',
        code: '23',
      },
      {
        name: 'Henan',
        code: '41',
      },
      {
        name: 'Hong Kong',
        code: '91',
      },
      {
        name: 'Hubei',
        code: '42',
      },
      {
        name: 'Hunan',
        code: '43',
      },
      {
        name: 'Jiangsu',
        code: '32',
      },
      {
        name: 'Jiangxi',
        code: '36',
      },
      {
        name: 'Jilin',
        code: '22',
      },
      {
        name: 'Liaoning',
        code: '21',
      },
      {
        name: 'Macao',
        code: '92',
      },
      {
        name: 'Nei Mongol',
        code: '15',
      },
      {
        name: 'Ningxia',
        code: '64',
      },
      {
        name: 'Qinghai',
        code: '63',
      },
      {
        name: 'Shaanxi',
        code: '61',
      },
      {
        name: 'Shandong',
        code: '37',
      },
      {
        name: 'Shanghai',
        code: '31',
      },
      {
        name: 'Shanxi',
        code: '14',
      },
      {
        name: 'Sichuan',
        code: '51',
      },
      {
        name: 'Taiwan',
        code: '71',
      },
      {
        name: 'Tianjin',
        code: '12',
      },
      {
        name: 'Xinjiang',
        code: '65',
      },
      {
        name: 'Xizang',
        code: '54',
      },
      {
        name: 'Yunnan',
        code: '53',
      },
      {
        name: 'Zhejiang',
        code: '33',
      },
    ],
    name: 'China',
    code: 'CN',
  },
  {
    states: [],
    name: 'Christmas Island',
    code: 'CX',
  },
  {
    states: [],
    name: 'Cocos (Keeling) Islands',
    code: 'CC',
  },
  {
    states: [],
    name: 'Colombia',
    code: 'CO',
  },
  {
    states: [],
    name: 'Comoros',
    code: 'KM',
  },
  {
    states: [],
    name: 'Congo',
    code: 'CG',
  },
  {
    states: [],
    name: 'Congo, the Democratic Republic of the',
    code: 'CD',
  },
  {
    states: [],
    name: 'Cook Islands',
    code: 'CK',
  },
  {
    states: [],
    name: 'Costa Rica',
    code: 'CR',
  },
  {
    states: [],
    name: "Cote d'Ivoire",
    code: 'CI',
  },
  {
    states: [],
    name: 'Croatia',
    code: 'HR',
  },
  {
    states: [],
    name: 'Cuba',
    code: 'CU',
  },
  {
    states: [],
    name: 'Curaçao',
    code: 'CW',
  },
  {
    states: [],
    name: 'Cyprus',
    code: 'CY',
  },
  {
    states: [],
    name: 'Czech Republic',
    code: 'CZ',
  },
  {
    states: [],
    name: 'Denmark',
    code: 'DK',
  },
  {
    states: [],
    name: 'Djibouti',
    code: 'DJ',
  },
  {
    states: [],
    name: 'Dominica',
    code: 'DM',
  },
  {
    states: [],
    name: 'Dominican Republic',
    code: 'DO',
  },
  {
    states: [],
    name: 'Ecuador',
    code: 'EC',
  },
  {
    states: [],
    name: 'Egypt',
    code: 'EG',
  },
  {
    states: [],
    name: 'El Salvador',
    code: 'SV',
  },
  {
    states: [],
    name: 'Equatorial Guinea',
    code: 'GQ',
  },
  {
    states: [],
    name: 'Eritrea',
    code: 'ER',
  },
  {
    states: [],
    name: 'Estonia',
    code: 'EE',
  },
  {
    states: [],
    name: 'Ethiopia',
    code: 'ET',
  },
  {
    states: [],
    name: 'Falkland Islands (Malvinas)',
    code: 'FK',
  },
  {
    states: [],
    name: 'Faroe Islands',
    code: 'FO',
  },
  {
    states: [],
    name: 'Fiji',
    code: 'FJ',
  },
  {
    states: [],
    name: 'Finland',
    code: 'FI',
  },
  {
    states: [],
    name: 'France',
    code: 'FR',
  },
  {
    states: [],
    name: 'French Guiana',
    code: 'GF',
  },
  {
    states: [],
    name: 'French Polynesia',
    code: 'PF',
  },
  {
    states: [],
    name: 'French Southern Territories',
    code: 'TF',
  },
  {
    states: [],
    name: 'Gabon',
    code: 'GA',
  },
  {
    states: [],
    name: 'Gambia',
    code: 'GM',
  },
  {
    states: [],
    name: 'Georgia',
    code: 'GE',
  },
  {
    states: [],
    name: 'Germany',
    code: 'DE',
  },
  {
    states: [],
    name: 'Ghana',
    code: 'GH',
  },
  {
    states: [],
    name: 'Gibraltar',
    code: 'GI',
  },
  {
    states: [],
    name: 'Greece',
    code: 'GR',
  },
  {
    states: [],
    name: 'Greenland',
    code: 'GL',
  },
  {
    states: [],
    name: 'Grenada',
    code: 'GD',
  },
  {
    states: [],
    name: 'Guadeloupe',
    code: 'GP',
  },
  {
    states: [],
    name: 'Guatemala',
    code: 'GT',
  },
  {
    states: [],
    name: 'Guernsey',
    code: 'GG',
  },
  {
    states: [],
    name: 'Guinea',
    code: 'GN',
  },
  {
    states: [],
    name: 'Guinea-Bissau',
    code: 'GW',
  },
  {
    states: [],
    name: 'Guyana',
    code: 'GY',
  },
  {
    states: [],
    name: 'Haiti',
    code: 'HT',
  },
  {
    states: [],
    name: 'Heard Island and McDonald Islands',
    code: 'HM',
  },
  {
    states: [],
    name: 'Holy See (Vatican City State)',
    code: 'VA',
  },
  {
    states: [],
    name: 'Honduras',
    code: 'HN',
  },
  {
    states: [],
    name: 'Hungary',
    code: 'HU',
  },
  {
    states: [],
    name: 'Iceland',
    code: 'IS',
  },
  {
    states: [
      {
        name: 'Andaman and Nicobar Islands',
        code: 'AN',
      },
      {
        name: 'Andhra Pradesh',
        code: 'AP',
      },
      {
        name: 'Arunachal Pradesh',
        code: 'AR',
      },
      {
        name: 'Assam',
        code: 'AS',
      },
      {
        name: 'Bihar',
        code: 'BR',
      },
      {
        name: 'Chandigarh',
        code: 'CH',
      },
      {
        name: 'Chhattisgarh',
        code: 'CT',
      },
      {
        name: 'Dadra and Nagar Haveli',
        code: 'DN',
      },
      {
        name: 'Daman and Diu',
        code: 'DD',
      },
      {
        name: 'Delhi',
        code: 'DL',
      },
      {
        name: 'Goa',
        code: 'GA',
      },
      {
        name: 'Gujarat',
        code: 'GJ',
      },
      {
        name: 'Haryana',
        code: 'HR',
      },
      {
        name: 'Himachal Pradesh',
        code: 'HP',
      },
      {
        name: 'Jammu and Kashmir',
        code: 'JK',
      },
      {
        name: 'Jharkhand',
        code: 'JH',
      },
      {
        name: 'Karnataka',
        code: 'KA',
      },
      {
        name: 'Kerala',
        code: 'KL',
      },
      {
        name: 'Lakshadweep',
        code: 'LD',
      },
      {
        name: 'Madhya Pradesh',
        code: 'MP',
      },
      {
        name: 'Maharashtra',
        code: 'MH',
      },
      {
        name: 'Manipur',
        code: 'MN',
      },
      {
        name: 'Meghalaya',
        code: 'ML',
      },
      {
        name: 'Mizoram',
        code: 'MZ',
      },
      {
        name: 'Nagaland',
        code: 'NL',
      },
      {
        name: 'Odisha',
        code: 'OR',
      },
      {
        name: 'Puducherry',
        code: 'PY',
      },
      {
        name: 'Punjab',
        code: 'PB',
      },
      {
        name: 'Rajasthan',
        code: 'RJ',
      },
      {
        name: 'Sikkim',
        code: 'SK',
      },
      {
        name: 'Tamil Nadu',
        code: 'TN',
      },
      {
        name: 'Telangana',
        code: 'TG',
      },
      {
        name: 'Tripura',
        code: 'TR',
      },
      {
        name: 'Uttarakhand',
        code: 'UT',
      },
      {
        name: 'Uttar Pradesh',
        code: 'UP',
      },
      {
        name: 'West Bengal',
        code: 'WB',
      },
    ],
    name: 'India',
    code: 'IN',
  },
  {
    states: [],
    name: 'Indonesia',
    code: 'ID',
  },
  {
    states: [],
    name: 'Iran, Islamic Republic of',
    code: 'IR',
  },
  {
    states: [],
    name: 'Iraq',
    code: 'IQ',
  },
  {
    states: [
      {
        name: 'Carlow',
        code: 'CW',
      },
      {
        name: 'Cavan',
        code: 'CN',
      },
      {
        name: 'Clare',
        code: 'CE',
      },
      {
        name: 'Cork',
        code: 'CO',
      },
      {
        name: 'Donegal',
        code: 'DL',
      },
      {
        name: 'Dublin',
        code: 'D',
      },
      {
        name: 'Galway',
        code: 'G',
      },
      {
        name: 'Kerry',
        code: 'KY',
      },
      {
        name: 'Kildare',
        code: 'KE',
      },
      {
        name: 'Kilkenny',
        code: 'KK',
      },
      {
        name: 'Laois',
        code: 'LS',
      },
      {
        name: 'Leitrim',
        code: 'LM',
      },
      {
        name: 'Limerick',
        code: 'LK',
      },
      {
        name: 'Longford',
        code: 'LD',
      },
      {
        name: 'Louth',
        code: 'LH',
      },
      {
        name: 'Mayo',
        code: 'MO',
      },
      {
        name: 'Meath',
        code: 'MH',
      },
      {
        name: 'Monaghan',
        code: 'MN',
      },
      {
        name: 'Offaly',
        code: 'OY',
      },
      {
        name: 'Roscommon',
        code: 'RN',
      },
      {
        name: 'Sligo',
        code: 'SO',
      },
      {
        name: 'Tipperary',
        code: 'TA',
      },
      {
        name: 'Waterford',
        code: 'WD',
      },
      {
        name: 'Westmeath',
        code: 'WH',
      },
      {
        name: 'Wexford',
        code: 'WX',
      },
      {
        name: 'Wicklow',
        code: 'WW',
      },
    ],
    name: 'Ireland',
    code: 'IE',
  },
  {
    states: [],
    name: 'Isle of Man',
    code: 'IM',
  },
  {
    states: [],
    name: 'Israel',
    code: 'IL',
  },
  {
    states: [
      {
        name: 'Agrigento',
        code: 'AG',
      },
      {
        name: 'Alessandria',
        code: 'AL',
      },
      {
        name: 'Ancona',
        code: 'AN',
      },
      {
        name: 'Aosta',
        code: 'AO',
      },
      {
        name: 'Arezzo',
        code: 'AR',
      },
      {
        name: 'Ascoli Piceno',
        code: 'AP',
      },
      {
        name: 'Asti',
        code: 'AT',
      },
      {
        name: 'Avellino',
        code: 'AV',
      },
      {
        name: 'Bari',
        code: 'BA',
      },
      {
        name: 'Barletta-Andria-Trani',
        code: 'BT',
      },
      {
        name: 'Belluno',
        code: 'BL',
      },
      {
        name: 'Benevento',
        code: 'BN',
      },
      {
        name: 'Bergamo',
        code: 'BG',
      },
      {
        name: 'Biella',
        code: 'BI',
      },
      {
        name: 'Bologna',
        code: 'BO',
      },
      {
        name: 'Bolzano',
        code: 'BZ',
      },
      {
        name: 'Brescia',
        code: 'BS',
      },
      {
        name: 'Brindisi',
        code: 'BR',
      },
      {
        name: 'Cagliari',
        code: 'CA',
      },
      {
        name: 'Caltanissetta',
        code: 'CL',
      },
      {
        name: 'Campobasso',
        code: 'CB',
      },
      {
        name: 'Carbonia-Iglesias',
        code: 'CI',
      },
      {
        name: 'Caserta',
        code: 'CE',
      },
      {
        name: 'Catania',
        code: 'CT',
      },
      {
        name: 'Catanzaro',
        code: 'CZ',
      },
      {
        name: 'Chieti',
        code: 'CH',
      },
      {
        name: 'Como',
        code: 'CO',
      },
      {
        name: 'Cosenza',
        code: 'CS',
      },
      {
        name: 'Cremona',
        code: 'CR',
      },
      {
        name: 'Crotone',
        code: 'KR',
      },
      {
        name: 'Cuneo',
        code: 'CN',
      },
      {
        name: 'Enna',
        code: 'EN',
      },
      {
        name: 'Fermo',
        code: 'FM',
      },
      {
        name: 'Ferrara',
        code: 'FE',
      },
      {
        name: 'Florence',
        code: 'FI',
      },
      {
        name: 'Foggia',
        code: 'FG',
      },
      {
        name: 'Forlì-Cesena',
        code: 'FC',
      },
      {
        name: 'Frosinone',
        code: 'FR',
      },
      {
        name: 'Genoa',
        code: 'GE',
      },
      {
        name: 'Gorizia',
        code: 'GO',
      },
      {
        name: 'Grosseto',
        code: 'GR',
      },
      {
        name: 'Imperia',
        code: 'IM',
      },
      {
        name: 'Isernia',
        code: 'IS',
      },
      {
        name: "L'Aquila",
        code: 'AQ',
      },
      {
        name: 'La Spezia',
        code: 'SP',
      },
      {
        name: 'Latina',
        code: 'LT',
      },
      {
        name: 'Lecce',
        code: 'LE',
      },
      {
        name: 'Lecco',
        code: 'LC',
      },
      {
        name: 'Livorno',
        code: 'LI',
      },
      {
        name: 'Lodi',
        code: 'LO',
      },
      {
        name: 'Lucca',
        code: 'LU',
      },
      {
        name: 'Macerata',
        code: 'MC',
      },
      {
        name: 'Mantua',
        code: 'MN',
      },
      {
        name: 'Massa and Carrara',
        code: 'MS',
      },
      {
        name: 'Matera',
        code: 'MT',
      },
      {
        name: 'Medio Campidano',
        code: 'VS',
      },
      {
        name: 'Messina',
        code: 'ME',
      },
      {
        name: 'Milan',
        code: 'MI',
      },
      {
        name: 'Modena',
        code: 'MO',
      },
      {
        name: 'Monza and Brianza',
        code: 'MB',
      },
      {
        name: 'Naples',
        code: 'NA',
      },
      {
        name: 'Novara',
        code: 'NO',
      },
      {
        name: 'Nuoro',
        code: 'NU',
      },
      {
        name: 'Ogliastra',
        code: 'OG',
      },
      {
        name: 'Olbia-Tempio',
        code: 'OT',
      },
      {
        name: 'Oristano',
        code: 'OR',
      },
      {
        name: 'Padua',
        code: 'PD',
      },
      {
        name: 'Palermo',
        code: 'PA',
      },
      {
        name: 'Parma',
        code: 'PR',
      },
      {
        name: 'Pavia',
        code: 'PV',
      },
      {
        name: 'Perugia',
        code: 'PG',
      },
      {
        name: 'Pesaro and Urbino',
        code: 'PU',
      },
      {
        name: 'Pescara',
        code: 'PE',
      },
      {
        name: 'Piacenza',
        code: 'PC',
      },
      {
        name: 'Pisa',
        code: 'PI',
      },
      {
        name: 'Pistoia',
        code: 'PT',
      },
      {
        name: 'Pordenone',
        code: 'PN',
      },
      {
        name: 'Potenza',
        code: 'PZ',
      },
      {
        name: 'Prato',
        code: 'PO',
      },
      {
        name: 'Ragusa',
        code: 'RG',
      },
      {
        name: 'Ravenna',
        code: 'RA',
      },
      {
        name: 'Reggio Calabria',
        code: 'RC',
      },
      {
        name: 'Reggio Emilia',
        code: 'RE',
      },
      {
        name: 'Rieti',
        code: 'RI',
      },
      {
        name: 'Rimini',
        code: 'RN',
      },
      {
        name: 'Rome',
        code: 'RM',
      },
      {
        name: 'Rovigo',
        code: 'RO',
      },
      {
        name: 'Salerno',
        code: 'SA',
      },
      {
        name: 'Sassari',
        code: 'SS',
      },
      {
        name: 'Savona',
        code: 'SV',
      },
      {
        name: 'Siena',
        code: 'SI',
      },
      {
        name: 'Sondrio',
        code: 'SO',
      },
      {
        name: 'Syracuse',
        code: 'SR',
      },
      {
        name: 'Taranto',
        code: 'TA',
      },
      {
        name: 'Teramo',
        code: 'TE',
      },
      {
        name: 'Terni',
        code: 'TR',
      },
      {
        name: 'Trapani',
        code: 'TP',
      },
      {
        name: 'Trento',
        code: 'TN',
      },
      {
        name: 'Treviso',
        code: 'TV',
      },
      {
        name: 'Trieste',
        code: 'TS',
      },
      {
        name: 'Turin',
        code: 'TO',
      },
      {
        name: 'Udine',
        code: 'UD',
      },
      {
        name: 'Varese',
        code: 'VA',
      },
      {
        name: 'Venice',
        code: 'VE',
      },
      {
        name: 'Verbano-Cusio-Ossola',
        code: 'VB',
      },
      {
        name: 'Vercelli',
        code: 'VC',
      },
      {
        name: 'Verona',
        code: 'VR',
      },
      {
        name: 'Vibo Valentia',
        code: 'VV',
      },
      {
        name: 'Vicenza',
        code: 'VI',
      },
      {
        name: 'Viterbo',
        code: 'VT',
      },
    ],
    name: 'Italy',
    code: 'IT',
  },
  {
    states: [],
    name: 'Jamaica',
    code: 'JM',
  },
  {
    states: [],
    name: 'Japan',
    code: 'JP',
  },
  {
    states: [],
    name: 'Jersey',
    code: 'JE',
  },
  {
    states: [],
    name: 'Jordan',
    code: 'JO',
  },
  {
    states: [],
    name: 'Kazakhstan',
    code: 'KZ',
  },
  {
    states: [],
    name: 'Kenya',
    code: 'KE',
  },
  {
    states: [],
    name: 'Kiribati',
    code: 'KI',
  },
  {
    states: [],
    name: "Korea, Democratic People's Republic of",
    code: 'KP',
  },
  {
    states: [],
    name: 'Korea, Republic of',
    code: 'KR',
  },
  {
    states: [],
    name: 'Kuwait',
    code: 'KW',
  },
  {
    states: [],
    name: 'Kyrgyzstan',
    code: 'KG',
  },
  {
    states: [],
    name: "Lao People's Democratic Republic",
    code: 'LA',
  },
  {
    states: [],
    name: 'Latvia',
    code: 'LV',
  },
  {
    states: [],
    name: 'Lebanon',
    code: 'LB',
  },
  {
    states: [],
    name: 'Lesotho',
    code: 'LS',
  },
  {
    states: [],
    name: 'Liberia',
    code: 'LR',
  },
  {
    states: [],
    name: 'Libya',
    code: 'LY',
  },
  {
    states: [],
    name: 'Liechtenstein',
    code: 'LI',
  },
  {
    states: [],
    name: 'Lithuania',
    code: 'LT',
  },
  {
    states: [],
    name: 'Luxembourg',
    code: 'LU',
  },
  {
    states: [],
    name: 'Macao',
    code: 'MO',
  },
  {
    states: [],
    name: 'Macedonia, the former Yugoslav Republic of',
    code: 'MK',
  },
  {
    states: [],
    name: 'Madagascar',
    code: 'MG',
  },
  {
    states: [],
    name: 'Malawi',
    code: 'MW',
  },
  {
    states: [],
    name: 'Malaysia',
    code: 'MY',
  },
  {
    states: [],
    name: 'Maldives',
    code: 'MV',
  },
  {
    states: [],
    name: 'Mali',
    code: 'ML',
  },
  {
    states: [],
    name: 'Malta',
    code: 'MT',
  },
  {
    states: [],
    name: 'Martinique',
    code: 'MQ',
  },
  {
    states: [],
    name: 'Mauritania',
    code: 'MR',
  },
  {
    states: [],
    name: 'Mauritius',
    code: 'MU',
  },
  {
    states: [],
    name: 'Mayotte',
    code: 'YT',
  },
  {
    states: [
      {
        name: 'Aguascalientes',
        code: 'AG',
      },
      {
        name: 'Baja California',
        code: 'BC',
      },
      {
        name: 'Baja California Sur',
        code: 'BS',
      },
      {
        name: 'Campeche',
        code: 'CM',
      },
      {
        name: 'Chiapas',
        code: 'CS',
      },
      {
        name: 'Chihuahua',
        code: 'CH',
      },
      {
        name: 'Coahuila',
        code: 'CO',
      },
      {
        name: 'Colima',
        code: 'CL',
      },
      {
        name: 'Durango',
        code: 'DG',
      },
      {
        name: 'Federal District',
        code: 'DF',
      },
      {
        name: 'Guanajuato',
        code: 'GT',
      },
      {
        name: 'Guerrero',
        code: 'GR',
      },
      {
        name: 'Hidalgo',
        code: 'HG',
      },
      {
        name: 'Jalisco',
        code: 'JA',
      },
      {
        name: 'Mexico State',
        code: 'ME',
      },
      {
        name: 'Michoacán',
        code: 'MI',
      },
      {
        name: 'Morelos',
        code: 'MO',
      },
      {
        name: 'Nayarit',
        code: 'NA',
      },
      {
        name: 'Nuevo León',
        code: 'NL',
      },
      {
        name: 'Oaxaca',
        code: 'OA',
      },
      {
        name: 'Puebla',
        code: 'PB',
      },
      {
        name: 'Querétaro',
        code: 'QE',
      },
      {
        name: 'Quintana Roo',
        code: 'QR',
      },
      {
        name: 'San Luis Potosí',
        code: 'SL',
      },
      {
        name: 'Sinaloa',
        code: 'SI',
      },
      {
        name: 'Sonora',
        code: 'SO',
      },
      {
        name: 'Tabasco',
        code: 'TB',
      },
      {
        name: 'Tamaulipas',
        code: 'TM',
      },
      {
        name: 'Tlaxcala',
        code: 'TL',
      },
      {
        name: 'Veracruz',
        code: 'VE',
      },
      {
        name: 'Yucatán',
        code: 'YU',
      },
      {
        name: 'Zacatecas',
        code: 'ZA',
      },
    ],
    name: 'Mexico',
    code: 'MX',
  },
  {
    states: [],
    name: 'Moldova, Republic of',
    code: 'MD',
  },
  {
    states: [],
    name: 'Monaco',
    code: 'MC',
  },
  {
    states: [],
    name: 'Mongolia',
    code: 'MN',
  },
  {
    states: [],
    name: 'Montenegro',
    code: 'ME',
  },
  {
    states: [],
    name: 'Montserrat',
    code: 'MS',
  },
  {
    states: [],
    name: 'Morocco',
    code: 'MA',
  },
  {
    states: [],
    name: 'Mozambique',
    code: 'MZ',
  },
  {
    states: [],
    name: 'Myanmar',
    code: 'MM',
  },
  {
    states: [],
    name: 'Namibia',
    code: 'NA',
  },
  {
    states: [],
    name: 'Nauru',
    code: 'NR',
  },
  {
    states: [],
    name: 'Nepal',
    code: 'NP',
  },
  {
    states: [],
    name: 'Netherlands',
    code: 'NL',
  },
  {
    states: [],
    name: 'New Caledonia',
    code: 'NC',
  },
  {
    states: [],
    name: 'New Zealand',
    code: 'NZ',
  },
  {
    states: [],
    name: 'Nicaragua',
    code: 'NI',
  },
  {
    states: [],
    name: 'Niger',
    code: 'NE',
  },
  {
    states: [],
    name: 'Nigeria',
    code: 'NG',
  },
  {
    states: [],
    name: 'Niue',
    code: 'NU',
  },
  {
    states: [],
    name: 'Norfolk Island',
    code: 'NF',
  },
  {
    states: [],
    name: 'Norway',
    code: 'NO',
  },
  {
    states: [],
    name: 'Oman',
    code: 'OM',
  },
  {
    states: [],
    name: 'Pakistan',
    code: 'PK',
  },
  {
    states: [],
    name: 'Palestine',
    code: 'PS',
  },
  {
    states: [],
    name: 'Panama',
    code: 'PA',
  },
  {
    states: [],
    name: 'Papua New Guinea',
    code: 'PG',
  },
  {
    states: [],
    name: 'Paraguay',
    code: 'PY',
  },
  {
    states: [],
    name: 'Peru',
    code: 'PE',
  },
  {
    states: [],
    name: 'Philippines',
    code: 'PH',
  },
  {
    states: [],
    name: 'Pitcairn',
    code: 'PN',
  },
  {
    states: [],
    name: 'Poland',
    code: 'PL',
  },
  {
    states: [],
    name: 'Portugal',
    code: 'PT',
  },
  {
    states: [],
    name: 'Qatar',
    code: 'QA',
  },
  {
    states: [],
    name: 'Reunion',
    code: 'RE',
  },
  {
    states: [],
    name: 'Romania',
    code: 'RO',
  },
  {
    states: [],
    name: 'Russian Federation',
    code: 'RU',
  },
  {
    states: [],
    name: 'Rwanda',
    code: 'RW',
  },
  {
    states: [],
    name: 'Saint Barthélemy',
    code: 'BL',
  },
  {
    states: [],
    name: 'Saint Helena, Ascension and Tristan da Cunha',
    code: 'SH',
  },
  {
    states: [],
    name: 'Saint Kitts and Nevis',
    code: 'KN',
  },
  {
    states: [],
    name: 'Saint Lucia',
    code: 'LC',
  },
  {
    states: [],
    name: 'Saint Martin (French part)',
    code: 'MF',
  },
  {
    states: [],
    name: 'Saint Pierre and Miquelon',
    code: 'PM',
  },
  {
    states: [],
    name: 'Saint Vincent and the Grenadines',
    code: 'VC',
  },
  {
    states: [],
    name: 'Samoa',
    code: 'WS',
  },
  {
    states: [],
    name: 'San Marino',
    code: 'SM',
  },
  {
    states: [],
    name: 'Sao Tome and Principe',
    code: 'ST',
  },
  {
    states: [],
    name: 'Saudi Arabia',
    code: 'SA',
  },
  {
    states: [],
    name: 'Senegal',
    code: 'SN',
  },
  {
    states: [],
    name: 'Serbia',
    code: 'RS',
  },
  {
    states: [],
    name: 'Seychelles',
    code: 'SC',
  },
  {
    states: [],
    name: 'Sierra Leone',
    code: 'SL',
  },
  {
    states: [],
    name: 'Singapore',
    code: 'SG',
  },
  {
    states: [],
    name: 'Sint Maarten (Dutch part)',
    code: 'SX',
  },
  {
    states: [],
    name: 'Slovakia',
    code: 'SK',
  },
  {
    states: [],
    name: 'Slovenia',
    code: 'SI',
  },
  {
    states: [],
    name: 'Solomon Islands',
    code: 'SB',
  },
  {
    states: [],
    name: 'Somalia',
    code: 'SO',
  },
  {
    states: [],
    name: 'South Africa',
    code: 'ZA',
  },
  {
    states: [],
    name: 'South Georgia and the South Sandwich Islands',
    code: 'GS',
  },
  {
    states: [],
    name: 'South Sudan',
    code: 'SS',
  },
  {
    states: [],
    name: 'Spain',
    code: 'ES',
  },
  {
    states: [],
    name: 'Sri Lanka',
    code: 'LK',
  },
  {
    states: [],
    name: 'Sudan',
    code: 'SD',
  },
  {
    states: [],
    name: 'Suriname',
    code: 'SR',
  },
  {
    states: [],
    name: 'Svalbard and Jan Mayen',
    code: 'SJ',
  },
  {
    states: [],
    name: 'Swaziland',
    code: 'SZ',
  },
  {
    states: [],
    name: 'Sweden',
    code: 'SE',
  },
  {
    states: [],
    name: 'Switzerland',
    code: 'CH',
  },
  {
    states: [],
    name: 'Syrian Arab Republic',
    code: 'SY',
  },
  {
    states: [],
    name: 'Taiwan',
    code: 'TW',
  },
  {
    states: [],
    name: 'Tajikistan',
    code: 'TJ',
  },
  {
    states: [],
    name: 'Tanzania, United Republic of',
    code: 'TZ',
  },
  {
    states: [],
    name: 'Thailand',
    code: 'TH',
  },
  {
    states: [],
    name: 'Timor-Leste',
    code: 'TL',
  },
  {
    states: [],
    name: 'Togo',
    code: 'TG',
  },
  {
    states: [],
    name: 'Tokelau',
    code: 'TK',
  },
  {
    states: [],
    name: 'Tonga',
    code: 'TO',
  },
  {
    states: [],
    name: 'Trinidad and Tobago',
    code: 'TT',
  },
  {
    states: [],
    name: 'Tunisia',
    code: 'TN',
  },
  {
    states: [],
    name: 'Turkey',
    code: 'TR',
  },
  {
    states: [],
    name: 'Turkmenistan',
    code: 'TM',
  },
  {
    states: [],
    name: 'Turks and Caicos Islands',
    code: 'TC',
  },
  {
    states: [],
    name: 'Tuvalu',
    code: 'TV',
  },
  {
    states: [],
    name: 'Uganda',
    code: 'UG',
  },
  {
    states: [],
    name: 'Ukraine',
    code: 'UA',
  },
  {
    states: [],
    name: 'United Arab Emirates',
    code: 'AE',
  },
  {
    states: [],
    name: 'United Kingdom',
    code: 'GB',
  },
  {
    states: [
      {
        name: 'Alabama',
        code: 'AL',
      },
      {
        name: 'Alaska',
        code: 'AK',
      },
      {
        name: 'Arizona',
        code: 'AZ',
      },
      {
        name: 'Arkansas',
        code: 'AR',
      },
      {
        name: 'California',
        code: 'CA',
      },
      {
        name: 'Colorado',
        code: 'CO',
      },
      {
        name: 'Connecticut',
        code: 'CT',
      },
      {
        name: 'Delaware',
        code: 'DE',
      },
      {
        name: 'District of Columbia',
        code: 'DC',
      },
      {
        name: 'Florida',
        code: 'FL',
      },
      {
        name: 'Georgia',
        code: 'GA',
      },
      {
        name: 'Hawaii',
        code: 'HI',
      },
      {
        name: 'Idaho',
        code: 'ID',
      },
      {
        name: 'Illinois',
        code: 'IL',
      },
      {
        name: 'Indiana',
        code: 'IN',
      },
      {
        name: 'Iowa',
        code: 'IA',
      },
      {
        name: 'Kansas',
        code: 'KS',
      },
      {
        name: 'Kentucky',
        code: 'KY',
      },
      {
        name: 'Louisiana',
        code: 'LA',
      },
      {
        name: 'Maine',
        code: 'ME',
      },
      {
        name: 'Maryland',
        code: 'MD',
      },
      {
        name: 'Massachusetts',
        code: 'MA',
      },
      {
        name: 'Michigan',
        code: 'MI',
      },
      {
        name: 'Minnesota',
        code: 'MN',
      },
      {
        name: 'Mississippi',
        code: 'MS',
      },
      {
        name: 'Missouri',
        code: 'MO',
      },
      {
        name: 'Montana',
        code: 'MT',
      },
      {
        name: 'Nebraska',
        code: 'NE',
      },
      {
        name: 'Nevada',
        code: 'NV',
      },
      {
        name: 'New Hampshire',
        code: 'NH',
      },
      {
        name: 'New Jersey',
        code: 'NJ',
      },
      {
        name: 'New Mexico',
        code: 'NM',
      },
      {
        name: 'New York',
        code: 'NY',
      },
      {
        name: 'North Carolina',
        code: 'NC',
      },
      {
        name: 'North Dakota',
        code: 'ND',
      },
      {
        name: 'Ohio',
        code: 'OH',
      },
      {
        name: 'Oklahoma',
        code: 'OK',
      },
      {
        name: 'Oregon',
        code: 'OR',
      },
      {
        name: 'Pennsylvania',
        code: 'PA',
      },
      {
        name: 'Rhode Island',
        code: 'RI',
      },
      {
        name: 'South Carolina',
        code: 'SC',
      },
      {
        name: 'South Dakota',
        code: 'SD',
      },
      {
        name: 'Tennessee',
        code: 'TN',
      },
      {
        name: 'Texas',
        code: 'TX',
      },
      {
        name: 'Utah',
        code: 'UT',
      },
      {
        name: 'Vermont',
        code: 'VT',
      },
      {
        name: 'Virginia',
        code: 'VA',
      },
      {
        name: 'Washington',
        code: 'WA',
      },
      {
        name: 'West Virginia',
        code: 'WV',
      },
      {
        name: 'Wisconsin',
        code: 'WI',
      },
      {
        name: 'Wyoming',
        code: 'WY',
      },
    ],
    name: 'United States',
    code: 'US',
  },
  {
    states: [],
    name: 'Uruguay',
    code: 'UY',
  },
  {
    states: [],
    name: 'Uzbekistan',
    code: 'UZ',
  },
  {
    states: [],
    name: 'Vanuatu',
    code: 'VU',
  },
  {
    states: [],
    name: 'Venezuela, Bolivarian Republic of',
    code: 'VE',
  },
  {
    states: [],
    name: 'Vietnam',
    code: 'VN',
  },
  {
    states: [],
    name: 'Virgin Islands, British',
    code: 'VG',
  },
  {
    states: [],
    name: 'Wallis and Futuna',
    code: 'WF',
  },
  {
    states: [],
    name: 'Western Sahara',
    code: 'EH',
  },
  {
    states: [],
    name: 'Yemen',
    code: 'YE',
  },
  {
    states: [],
    name: 'Zambia',
    code: 'ZM',
  },
  {
    states: [],
    name: 'Zimbabwe',
    code: 'ZW',
  },
];

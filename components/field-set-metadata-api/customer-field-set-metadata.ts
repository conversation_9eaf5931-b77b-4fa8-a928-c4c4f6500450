export default [
  {
    apiName: 'customerSince',
    name: 'Customer Since',
    type: 'date',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'phone',
    name: 'Phone',
    type: 'phone',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'name',
    name: 'name',
    type: 'text',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'industry',
    name: 'Industry',
    type: 'pickList',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'todayARR',
    name: "Today's ARR",
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'todayCMRR',
    name: "Today's CMRR",
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'totalTCV',
    name: 'Total TCV',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'todayRollupARR',
    name: 'Rollup Today ARR',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'todayRollupCMRR',
    name: 'Rollup Today MRR',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'rollupTCV',
    name: 'Rollup TCV',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'autoRenew',
    name: 'Auto Renew',
    filterable: true,
    type: 'pickList',
    required: false,
    updatable: true,
    customField: true,
    xs: 4,
    creatable: true,
    sortable: true,
    valueSet: {
      apiName: 'autoRenew',
      name: 'Auto Renew',
      defaultValue: 'DefaultToSubscriptionConfiguration',
      alphabeticallyOrdered: true,
      valuePairs: [
        {
          apiName: 'DefaultToSubscriptionConfiguration',
          name: 'Default to Subscription Configuration',
          orderNumber: 0,
        },
        {
          apiName: 'Yes',
          name: 'Yes',
          orderNumber: 1,
        },
        {
          apiName: 'No',
          name: 'No',
          orderNumber: 2,
        },
      ],
    },
    showTooltip: false,
    toolTipText: '',
  },
];

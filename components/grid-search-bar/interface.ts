import { DeleteFilterResponse } from '../filter-builder/interface';
import { Condition, OrderByField, RubyFilter } from '../graph-ql-query-constructor';
import { RubyObject } from '../metadata/interface';
import { SortField } from '../multi-sort-builder';

export interface Props {
  onAppliedFiltersChange?: () => void;
  filterToDelete?: string;
  completeDeleteFilter?: () => void;
  placeholder: string;
  // api used to execute the search
  handleSearch: (
    searchResult: string,
    filters: Array<RubyFilter>,
    orderByFields?: Array<OrderByField>,
    updateFields?: string[],
    relationOrderConditions?: Array<OrderByField>,
    stayOnExistingPage?: boolean,
  ) => Promise<void>;
  predefinedFilters?: Array<RubyFilter>;
  objectMetadata: RubyObject;
  // api to save filter
  listFilters: (objectApiName: string) => Promise<Array<RubyFilter>>;
  saveFilter: (
    objectMetadataApiName: string,
    filterName: string,
    conditions: Array<Condition>,
  ) => Promise<RubyFilter>;
  deleteFilter: (objectApiName: string, filterId: string) => Promise<DeleteFilterResponse>;
  // api to update existing filters
  updateFilters: (
    objectMetadataApiName: string,
    referenceFilterId: string,
    filterName: string,
    conditions: Array<Condition>,
  ) => Promise<RubyFilter>;
  useMultiSort?: boolean;
  sortingList: SortField[];
  onSortingListChange: (newSortingList: SortField[]) => void;
  searchBtnLabel?: string;
  showFiltersLabel?: boolean;
  showFilters?: boolean;
  showSearchBar?: boolean;
  searchBarWidth?: 'full' | 'half';
  layoutColumns?: 3 | 2;
  // if false the component does not show the search history.
  showFilterHistory?: boolean;
  // for graphql generate setting
  isSettingSearch?: boolean;
  allFilter?: RubyFilter[];
  setAllFilter?: Function;
  allMetadatas?: RubyObject[];
}

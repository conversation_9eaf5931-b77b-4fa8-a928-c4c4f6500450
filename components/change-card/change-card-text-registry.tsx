import type { ChangeType } from '../revenue-builder-types';
import AdjustText from './adjustPrice-text';
import CancellationText from './cancellation-text';
import CoTermText from './co-term-text';
import ReconfigureText from './reconfigure-text';
import RenewText from './renew-text';
import UpdateMilestoneText from './update-milestone-text';
import UpdateQuantityText from './update-quantity-text';
import RenewUpdateQuantityText from './renew-update-quantity-text';
import UpdateTermText from './update-term-text';
import ConvertFreeTrialText from './convert-free-trial-text';
import UpgradeSubscriptionText from './upgrade-subscription-text';
import DowngradeSubscriptionText from './downgrade-subscription-text';
import SwapSubscriptionText from './swap-subscription-text';

const registry = new Map();
registry.set('Renew', RenewText);
registry.set('UpdateQuantity', UpdateQuantityText);
registry.set('RenewWithQuantityUpdate', RenewUpdateQuantityText);
registry.set('UpdateTerm', UpdateTermText);
registry.set('CoTerm', CoTermText);
registry.set('Cancel', CancellationText);
registry.set('Reconfigure', ReconfigureText);
registry.set('AdjustPrice', AdjustText);
registry.set('UpdateMilestone', UpdateMilestoneText);
registry.set('ConvertFreeTrial', ConvertFreeTrialText);
registry.set('Upgrade', UpgradeSubscriptionText);
registry.set('Downgrade', DowngradeSubscriptionText);
registry.set('Swap', SwapSubscriptionText);

const ChangeCardTextRegistry = {
  getChangeCardText: (changeType: ChangeType) => {
    if (registry.has(changeType)) {
      return registry.get(changeType);
    }
    throw new Error(`Unknown action: ${changeType}`);
  },
};

export default ChangeCardTextRegistry;

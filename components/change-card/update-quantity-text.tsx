import Link from '@material-ui/core/Link';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import dayjs from 'dayjs';
import React from 'react';

import type { ChangeItemText } from './interface';

const useStyles = makeStyles({
  itemText: {
    opacity: 0.7,
    color: '#000000',
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
    opacity: '1 !important',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
});

const UpdateQuantityText: React.FC<ChangeItemText> = (props) => {
  const { changeItem } = props;

  const classes = useStyles();
  const action =
    //@ts-ignore
    changeItem.asset.quantity + changeItem.request.quantity >= changeItem.asset.quantity
      ? 'added'
      : 'reduced';

  const changeItemTitle = changeItem.request.label || changeItem.request.objectType;
  return (
    <Typography variant="body2" component="p" display="inline">
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        The quantity of {changeItemTitle}{' '}
      </Typography>
      <Typography variant="body2" className={classes.textUnderline} component="p" display="inline">
        {changeItem.request.assetName}{' '}
      </Typography>
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        is {action} by{' '}
      </Typography>
      <Link
        variant="body2"
        href="#"
        onClick={() => {}}
        className={classes.textHighlight}
        component="p"
        display="inline"
      >
        {
          //@ts-ignore
          Math.abs(changeItem.request.quantity)
        }{' '}
      </Link>
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        {changeItem.asset.uom.quantityDimension} starting on{' '}
      </Typography>
      <Link
        variant="body2"
        href="#"
        onClick={() => {}}
        className={classes.textHighlight}
        component="p"
        display="inline"
      >
        {dayjs(
          //@ts-ignore
          changeItem.request.renewalDate || changeItem.request.subscriptionStartDate,
        ).format('MM/DD/YYYY')}
      </Link>
    </Typography>
  );
};

export default UpdateQuantityText;

import React from 'react';

export type Children = React.ReactElement | React.ReactElement[];
export type Provider = ({ children }: { children: Children }) => React.ReactElement;

export const ComposeProviders = ({
  providers,
  children,
}: {
  providers: Provider[];
  children: React.ReactElement;
}): React.ReactElement =>
  providers.reduceRight((acc, Provider) => <Provider>{acc}</Provider>, children);

import accountFieldSetMetadata from './account-grid-view-metadata';
import assetGridViewMetadata from './asset-grid-view-metadata';
import creditFieldSetMetadata from './credit-grid-view-metadata';
import creditMemosFieldSetMetadata from './credit-memos-grid-view-metadata';
import customerGridViewMetadata from './customer-grid-view-metadata';
import entitlementFieldSetMetadata from './entitlement-grid-view-metadata';
import invoiceFieldSetMetadata from './invoice-grid-view-metadata';
import orderFieldSetMetadata from './order-grid-view-metadata';
import orderFieldSetMetadataBucket from './order-grid-view-metadata-bucket';
import subscriptionGridViewMetadata from './subscription-grid-view-metadata';
import usageFieldSetMetadata from './usage-grid-view-metadata';
import paymentMethodsGridViewMetadata from './payment-methods-grid-view-metadata';

const registry = new Map();
registry.set('customer-grid-view-metadata', customerGridViewMetadata);
registry.set('order-grid-view-metadata', orderFieldSetMetadata);
registry.set('order-grid-view-metadata-bucket', orderFieldSetMetadataBucket);
registry.set('subscription-grid-view-metadata', subscriptionGridViewMetadata);
registry.set('asset-grid-view-metadata', assetGridViewMetadata);
registry.set('entitlement-grid-view-metadata', entitlementFieldSetMetadata);
registry.set('invoice-grid-view-metadata', invoiceFieldSetMetadata);
registry.set('credit-memos-grid-view-metadata', creditMemosFieldSetMetadata);
registry.set('payment-methods-grid-view-metadata', paymentMethodsGridViewMetadata);
registry.set('usage-grid-view-metadata', usageFieldSetMetadata);
registry.set('credit-grid-view-metadata', creditFieldSetMetadata);
registry.set('account-grid-view-metadata', accountFieldSetMetadata);

const GridViewMetadataApi = {
  getGridViewMetadata: (gridApiName: string) => {
    if (registry.has(gridApiName)) {
      return registry.get(gridApiName);
    }
    throw new Error(`Unknown gridApiName: ${gridApiName}`);
  },
};

export default GridViewMetadataApi;

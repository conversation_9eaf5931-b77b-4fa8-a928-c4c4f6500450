import React, { useEffect, useState } from 'react';
import { Grid } from '@material-ui/core';
import Accordion from '@material-ui/core/Accordion';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import TextField from '@material-ui/core/TextField';
import Typography from '@material-ui/core/Typography';
import { Theme, createStyles, makeStyles } from '@material-ui/core/styles';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { RubyFilter } from '@nue-apps/ruby-ui-component';
import RubyCheckbox from '../checkbox';
import FilterBuilder from '../filter-builder';
import TabView from '../tab-view';
import { ProductProps } from './interface';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      width: '100%',
    },
    heading: {
      fontSize: '12px',
      letterSpacing: 0,
      color: '#999999',
      fontWeight: 700,
    },
    secondaryHeading: {
      fontSize: theme.typography.pxToRem(15),
      color: theme.palette.text.secondary,
    },
    content: {
      fontFamily: 'HelveticaNeue-Bold',
      fontSize: '18px',
      color: '#666666',
      letterSpacing: 0,
      fontWeight: 700,
      marginBottom: '24px',
    },
    checkContent: {
      fontFamily: 'PingFangSC-Semibold',
      fontSize: '14px',
      color: '#666666',
      letterSpacing: 0,
      fontWeight: 600,
    },
  }),
);

export const ProductFilter: React.FC<ProductProps> = (props) => {
  const {
    open,
    setOpen,
    title,
    activeTabIndex,
    objectMetadata,
    setActiveTabIndex,
    disabled = false,
    revenueModel,
    setRevenueModel,
    isWebCreated,
    productFilter,
    productInvokeObject,
    savedFilters,
  } = props;
  const classes = useStyles();

  const tabs = isWebCreated
    ? [
        { id: '0', name: 'Simple', displayName: 'Simple' },
        { id: '1', name: 'Advanced', displayName: 'Advanced' },
      ]
    : [{ id: '0', name: 'GraphQL Filter', displayName: 'GraphQL Filter' }];

  const [oneTime, setOneTime] = useState(true);
  const [recurring, setRecurring] = useState(true);
  const [usage, setUsage] = useState(true);
  const [creditBurndown, setCreditBurndown] = useState(true);
  const [credit, setCredit] = useState(true);
  const [referencedFilter, setReferencedFilter] = useState<RubyFilter | null>(null);
  const [temporaryFilter, setTemporaryFilter] = useState();

  useEffect(() => {
    setUp();
  });

  const setUp = async () => {
    setOneTime(revenueModel.oneTime);
    setRecurring(revenueModel.recurring);
    setUsage(revenueModel.usage);
    setCreditBurndown(revenueModel.creditBurndown);
    setCredit(revenueModel.credit);
  };

  return (
    <>
      <Grid>
        <Accordion
          expanded={open}
          onChange={(event, expanded) => setOpen(expanded)}
          style={{ boxShadow: 'none' }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            style={{ display: '-webkit-inline-flex', padding: '0px' }}
          >
            <Typography className={classes.heading}>{title}</Typography>
          </AccordionSummary>
          <TabView
            tabs={tabs}
            variant={'standard'}
            activeTabIndex={activeTabIndex}
            reverseColorStyle={true}
            tabHeightStyle={true}
            disabledIndex={
              disabled
                ? tabs.map((tab) => {
                    return Number(tab.id);
                  })
                : []
            }
            handleUpdateTabIndex={(newActiveTabIndex) => {
              setActiveTabIndex(newActiveTabIndex);
            }}
          >
            <Grid
              container
              spacing={1}
              xs={12}
              style={{
                margin: '0px',
                border: '1px solid #EEE',
                minHeight: '365px',
                display: 'block',
              }}
            >
              {isWebCreated && activeTabIndex === 0 && (
                <Grid container spacing={2} xs={12}>
                  <Grid
                    item
                    container
                    spacing={2}
                    style={{ marginTop: '20px', marginLeft: '25.5px' }}
                  >
                    <Grid item className={classes.content}>
                      The billing schedule will run against all types of revenue model by default,
                      please uncheck the types you want to exclude in this billing schedule.
                    </Grid>
                    <Grid container spacing={3} item xs={12}>
                      <Grid item xs={12}>
                        <RubyCheckbox
                          field={{
                            apiName: 'oneTime',
                            name: 'One Time',
                            type: 'boolean',
                          }}
                          value={oneTime}
                          disabled={disabled}
                          handleInputChange={(newValue: boolean) => {
                            setOneTime(newValue);
                            revenueModel.oneTime = newValue;
                            revenueModel.recurring = recurring;
                            revenueModel.usage = usage;
                            revenueModel.creditBurndown = creditBurndown;
                            revenueModel.credit = credit;
                            setRevenueModel(revenueModel);
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <RubyCheckbox
                          field={{
                            apiName: 'recurring',
                            name: 'Recurring',
                            type: 'boolean',
                          }}
                          className={classes.checkContent}
                          value={recurring}
                          disabled={disabled}
                          handleInputChange={(newValue: boolean) => {
                            setRecurring(newValue);
                            revenueModel.oneTime = oneTime;
                            revenueModel.recurring = newValue;
                            revenueModel.usage = usage;
                            revenueModel.creditBurndown = creditBurndown;
                            revenueModel.credit = credit;
                            setRevenueModel(revenueModel);
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <RubyCheckbox
                          field={{
                            apiName: 'usage',
                            name: 'Overage Usage',
                            type: 'boolean',
                          }}
                          value={usage}
                          disabled={disabled}
                          handleInputChange={(newValue: boolean) => {
                            setUsage(newValue);
                            revenueModel.oneTime = oneTime;
                            revenueModel.recurring = recurring;
                            revenueModel.usage = newValue;
                            revenueModel.creditBurndown = creditBurndown;
                            revenueModel.credit = credit;
                            setRevenueModel(revenueModel);
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <RubyCheckbox
                          field={{
                            apiName: 'creditBurndown',
                            name: 'Credit Burn-down',
                            type: 'boolean',
                          }}
                          value={creditBurndown}
                          disabled={disabled}
                          handleInputChange={(newValue: boolean) => {
                            setCreditBurndown(newValue);
                            revenueModel.oneTime = oneTime;
                            revenueModel.recurring = recurring;
                            revenueModel.usage = usage;
                            revenueModel.creditBurndown = newValue;
                            revenueModel.credit = credit;
                            setRevenueModel(revenueModel);
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <RubyCheckbox
                          field={{
                            apiName: 'credit',
                            name: 'Credit',
                            type: 'boolean',
                          }}
                          value={credit}
                          disabled={disabled}
                          handleInputChange={(newValue: boolean) => {
                            setCredit(newValue);
                            revenueModel.oneTime = oneTime;
                            revenueModel.recurring = recurring;
                            revenueModel.usage = usage;
                            revenueModel.creditBurndown = creditBurndown;
                            revenueModel.credit = newValue;
                            setRevenueModel(revenueModel);
                          }}
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              )}

              {isWebCreated && activeTabIndex === 1 && (
                <Grid style={{ marginTop: '20px', marginLeft: '25.5px' }}>
                  <FilterBuilder
                    objectMetadata={objectMetadata}
                    referencedFilter={referencedFilter}
                    filterName={''}
                    searchResult=""
                    hiddenButton={true}
                    graphqlGenerator={true}
                    savedFilters={savedFilters}
                    invokeObject={productInvokeObject}
                    disabled={disabled}
                    temporaryFilter={temporaryFilter}
                    setTemporaryFilter={setTemporaryFilter}
                  />
                </Grid>
              )}

              {!isWebCreated && (
                <Grid container spacing={1} xs={12}>
                  <Grid item xs={12}>
                    <TextField
                      multiline
                      value={productFilter}
                      disabled={true}
                      aria-readonly={true}
                      variant="outlined"
                      style={{ width: '890px', marginLeft: '10px', marginTop: '5px' }}
                      inputProps={{
                        style: {
                          height: 275,
                          padding: '0 14px',
                          color: '#4d4c50',
                          fontWeight: 'normal',
                          fontSize: '14px',
                          overflow: 'auto',
                        },
                      }}
                    />
                  </Grid>
                </Grid>
              )}
            </Grid>
          </TabView>
        </Accordion>
      </Grid>
    </>
  );
};

export default ProductFilter;

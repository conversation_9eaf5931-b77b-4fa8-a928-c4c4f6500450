import React, { useContext, useEffect, useState } from 'react';
import { Grid, IconButton, Paper, TextField, Tooltip } from '@material-ui/core';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import { MuiThemeProvider, createTheme, makeStyles } from '@material-ui/core/styles';
import CloseIcon from '@material-ui/icons/Close';
import LanguageIcon from '@material-ui/icons/Language';
import MailOutlineIcon from '@material-ui/icons/MailOutline';
import SearchIcon from '@material-ui/icons/Search';
import Autocomplete from '@material-ui/lab/Autocomplete';
import { DialogComponent, ModalAppBar } from '@nue-apps/ruby-ui-component';
import { get } from 'lodash';
import { RefreshTokenIcon, SendEmailIcon, SquareDownloadIcon } from '../../icons';
import Loading from '../../loading';
import MultiSelectionSearchBar, { SelectedObject } from '../../multi-selection-search-bar';
import { useRubySnackbar } from '../../ruby-notifier';
import { BillingSettingsContext } from '../../ruby-settings/interface';
import TemplateBuilderContext from '../../template-builder-context';
import {
  TemplateBuilderFacade,
  TemplateSchema,
} from '../../template-builder-context/template-builder-context';
import { Props } from '../interface';
import PreviewRecordList from '../record-list';
import templateClassicSchema from '../template-classic';
import TemplateDesign from '../template-design';
import templateFriendlySchema from '../template-friendly';
import { downloadHtmlPdf } from '../template-html2pdf';
import TemplatePreview from '../template-preview';
import { templateOrderProduct as OrderProduct, templateRecord as Record } from '../template-record';
import TemplateTheme from '../template-theme';

const defaultProps = {};

const useStyles = makeStyles({
  templateContainer: {
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
  },
  toolBarContainer: {
    height: '100%',
    width: '300px',
    overflow: 'scroll',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
  },
  designContainer: {
    height: '100%',
    overflow: 'hidden',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
    flex: 1,
  },
  previewContainer: {
    height: '100%',
    overflow: 'scroll',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
    flex: 1.5,
    borderRaidus: '0',
  },
  toolBarPaper: {
    height: '100%',
    overflow: 'scroll',
    padding: '15px 30px',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    background: '#F9F8FA',
    boxSizing: 'border-box',
  },
  designPaper: {
    height: '100%',
    padding: '20px 5px',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
    boxSizing: 'border-box',
  },
  previewPaper: {
    height: '100%',
    padding: '35px 30px',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
    boxSizing: 'border-box',
    background: 'white',
    boxShadow: '0px 2px 10px 0px rgba(200,200,200,0.5);',
    borderTop: '2px solid #6239EB',
    borderRadius: 0,
  },
  dialogTitle: {
    padding: 0,
  },
  dialogContent: {},
  tableWrapper: {
    maxWidth: '50%',
  },
  downloadPdf: {
    maxWidth: 'auto',
  },
  titleContainer: {
    marginLeft: 32,
    display: 'flex',
    alignItems: 'center',
    width: '90vw',
    justifyContent: 'flex-end',
  },
  titleWrapper: {
    display: 'flex',
    alignItems: 'center',
  },
  title: {
    color: '#6239eb',
    fontSize: '16px',
  },
  iconContainer: {
    color: '#8b66f4',
    height: 24,
    marginRight: 4,
    cursor: 'pointer',
    marginLeft: '20px',
  },
  inputRoot: {
    '&>div': {
      padding: '0!important',
    },
    '& input': {
      paddingLeft: '15px!important',
    },
    '& fieldset': {
      border: 0,
    },
  },
  autocompleteOption: {
    fontSize: '.875rem',
    color: '#4d4c50',
  },
});

export const ViewInvoicePdf: React.FC<Props> = (userProps: Props) => {
  const templateTheme = createTheme(TemplateTheme);
  const billingSettingsCtx = useContext(BillingSettingsContext);

  const {
    schema,
    invoice,
    getInvoiceById,
    invoiceList,
    setRefreshPdfUrl,
    sendInvoiceEmail,
    checkInvoiceTokenByInvoiceId,
    setSchema,
    tenant,
  } = userProps;

  const [config, setConfig] = useState<TemplateSchema>(templateFriendlySchema);
  const [templateOrderProduct, setTemplateOrderProduct] = useState<any>(OrderProduct);
  const [templateRecord, setRecord] = useState<any>(invoice || Record);
  const [activateComponent, setActivateComponent] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const { showSnackbar, Snackbar } = useRubySnackbar();
  const [refreshToolTip, setRefreshToolTip] = useState('');
  const [showDialogForRefreshPdfUrl, setShowDialogForRefreshPdfUrl] = useState(false);
  const [showDialogForSendEmail, setShowDialogForSendEmail] = useState(false);
  const [isExpiring, setIsExpiring] = useState(false);
  const [loaded, setLoaded] = useState(false);

  const props = { ...defaultProps, ...userProps };

  const { open, setOpen } = props;

  const classes = useStyles();

  const checkInvoiceToken = async () => {
    if (invoice && checkInvoiceTokenByInvoiceId) {
      setLoading(true);
      const data = await checkInvoiceTokenByInvoiceId(invoice.id);
      setIsExpiring(data.expired);
      if (!data.expired) {
        setRefreshToolTip(
          `The URL to view the invoice will expire in ${data.data.remainDays} days on ${data.data.expireDate}.`,
        );
      } else {
        setRefreshToolTip(
          `The URL to view the invoice has expired, please click to generate a new one.`,
        );
      }
      setLoading(false);
    }
  };

  useEffect(() => {
    if (schema) {
      setConfig(schema);
    }
  }, [schema]);

  useEffect(() => {
    if (invoice) {
      setRecord(invoice);
      // checkInvoiceToken();
    }
  }, [invoice]);

  const templateBuilderFacade: TemplateBuilderFacade = {
    templateBuilderService: {
      templateConfig: config,
      getTemplateBuilderConfig: () => {
        return {
          templateConfig: config,
        };
      },
      setTemplateBuilderConfig: (c: TemplateSchema) => {
        setConfig(c);
      },
      templateOrderProduct: templateOrderProduct,
      getTemplateOrderProduct: () => {
        return {
          templateOrderProduct: templateOrderProduct,
        };
      },
      setTemplateOrderProduct: (c: any) => {
        setTemplateOrderProduct(c);
      },
      templateRecord: templateRecord,
      getTemplateRecord: () => {
        return {
          templateRecord: templateRecord,
        };
      },
      setTemplateRecord: (c: any) => {
        setRecord(c);
      },
      activateComponent: activateComponent,
      getActivateComponent: () => {
        return {
          activateComponent: activateComponent,
        };
      },
      setActivateComponent: (c: any) => {
        setActivateComponent(c);
      },
    },
  };

  const handleUpdateRecord = async (invoice: any, template: any) => {
    if (getInvoiceById && invoice) {
      setLoading(true);
      const data = await getInvoiceById('invoice', invoice.id, template);
      if (data.data.company && data.data.Invoice) {
        data.data.Invoice[0].company = data.data.company;
        setRecord(data.data.Invoice[0]);
      }
      if (data.data.company && data.data.Quote) {
        data.data.Quote[0].company = data.data.company;
        setRecord(data.data.Quote[0]);
      }
      setLoading(false);
    }
  };

  const previewPdf = () => {};

  const downloadPdf = () => {
    if (templateRecord) {
      setIsDownloading(true);
      downloadHtmlPdf(
        templateRecord?.tenantId + '_' + templateRecord?.name || 'invoice',
        'a4',
        document.getElementById('previewContainer')!,
        ['no2canvas'],
        true,
        setIsDownloading,
      );
    }
  };

  const handleSetRefreshPdfUrl = async () => {
    try {
      if (setRefreshPdfUrl) {
        const res = await setRefreshPdfUrl('invoices', [invoice.id]);
        if (res.success) {
          showSnackbar('confirm', 'Success', res.message || 'Successfully generate the pdf url.');
        }
        setIsRefreshing(false);
        setShowDialogForRefreshPdfUrl(false);
      }
    } catch (e) {}
  };

  const handleSendInvoiceEmail = async () => {
    try {
      if (sendInvoiceEmail) {
        const res = await sendInvoiceEmail([invoice.id]);
        if (res.success) {
          showSnackbar('confirm', 'Success', 'The email is sent successfully.');
        }
        setIsSending(false);
        setShowDialogForSendEmail(false);
      }
    } catch (e) {}
  };
  return (
    <MuiThemeProvider theme={templateTheme}>
      <TemplateBuilderContext.Provider value={templateBuilderFacade}>
        <Snackbar />
        <Dialog
          open={open}
          fullWidth
          fullScreen
          PaperProps={{
            style: {
              backgroundColor: '#F9F8FA',
            },
          }}
        >
          <DialogTitle className={classes.dialogTitle}>
            <ModalAppBar
              title={
                <div
                  className={classes.titleContainer}
                  style={{
                    justifyContent: invoice ? 'flex-end' : 'space-between',
                  }}
                >
                  {
                    <div
                      className={classes.titleWrapper}
                      style={{
                        visibility: invoice ? 'hidden' : 'visible',
                      }}
                    >
                      <div className={classes.title}>Previewing Record</div>
                      <div>
                        <Autocomplete
                          id="combo-box-demo"
                          options={invoiceList || []}
                          disableClearable={true}
                          getOptionLabel={(option) => option.name}
                          style={{
                            width: 200,
                            padding: 0,
                            marginLeft: '20px',
                            background: 'white',
                            borderRadius: '5px',
                          }}
                          classes={{ option: classes.autocompleteOption }}
                          onChange={(event: object, value: any, reason: string) => {
                            const newValue = value;
                            handleUpdateRecord(newValue, config);
                          }}
                          renderInput={(params: any) => (
                            <TextField
                              {...params}
                              label=""
                              variant="outlined"
                              className={classes.inputRoot}
                            />
                          )}
                          defaultValue={invoiceList && invoiceList[0]}
                        />
                      </div>
                    </div>
                  }
                  {loaded && (
                    <>
                      <div
                        className={classes.iconContainer}
                        style={{
                          marginRight: invoice ? '4px' : '30px',
                          visibility: 'hidden',
                        }}
                        onClick={() => {
                          downloadPdf();
                        }}
                      >
                        {isDownloading ? (
                          <Loading size="24px" />
                        ) : (
                          <Tooltip title="Download PDF" arrow placement="bottom">
                            <IconButton
                              style={{ padding: '5px', marginTop: '-15px', marginRight: '-10px' }}
                              disableRipple={true}
                            >
                              <SquareDownloadIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </div>
                      <div
                        className={classes.iconContainer}
                        style={{
                          marginRight: invoice ? '4px' : '30px',
                        }}
                        onClick={() => {
                          downloadPdf();
                        }}
                      >
                        {isDownloading ? (
                          <Loading size="24px" />
                        ) : (
                          <Tooltip title="Download PDF" arrow placement="bottom">
                            <IconButton
                              style={{ padding: '5px', marginTop: '-15px', marginRight: '-10px' }}
                              disableRipple={true}
                            >
                              <SquareDownloadIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </div>
                    </>
                  )}
                  {/*invoice && (
                    <div
                      className={classes.iconContainer}
                      onClick={() => {
                        setShowDialogForRefreshPdfUrl(true);
                      }}
                    >
                      <Tooltip title={refreshToolTip} arrow placement="bottom">
                        <IconButton
                          style={{ padding: '5px', marginTop: '-15px', marginRight: '-10px' }}
                        >
                          <RefreshTokenIcon />
                          {isExpiring && (
                            <CloseIcon
                              style={{
                                width: '17px',
                                height: '17px',
                                fontWeight: 700,
                                color: 'red',
                                position: 'relative',
                                left: '-13px',
                                top: '7px',
                              }}
                            />
                          )}
                        </IconButton>
                      </Tooltip>
                    </div>
                  )*/}
                  {invoice && invoice.status === 'Active' && (
                    <div
                      className={classes.iconContainer}
                      onClick={() => {
                        setShowDialogForSendEmail(true);
                        return;
                      }}
                      style={{
                        marginLeft: isExpiring ? '2px' : '20px',
                      }}
                    >
                      <Tooltip title="Send Email to Customer" arrow placement="bottom">
                        <IconButton style={{ padding: '5px', marginTop: '-15px' }}>
                          <SendEmailIcon />
                        </IconButton>
                      </Tooltip>
                    </div>
                  )}
                </div>
              }
              handleClose={() => {
                setOpen();
              }}
              showColoseButton={invoice ? false : true}
            />
          </DialogTitle>
          <DialogContent className={classes.dialogContent}>
            <div
              role="template-builder"
              style={{
                height: '100%',
              }}
            >
              <Grid container className={classes.templateContainer}>
                <Grid
                  item
                  style={{
                    height: '100%',
                    maxWidth: navigator.userAgent.match(
                      /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i,
                    )
                      ? '90%'
                      : '80%',
                  }}
                  id="tableWrapper"
                >
                  <Paper
                    style={{
                      padding: `${
                        get(config, 'details.settings.pageMargins').top === 'zeropx'
                          ? '0'
                          : get(config, 'details.settings.pageMargins').top
                      } ${
                        get(config, 'details.settings.pageMargins').right === 'zeropx'
                          ? '0'
                          : get(config, 'details.settings.pageMargins').right
                      } ${
                        get(config, 'details.settings.pageMargins').bottom === 'zeropx'
                          ? '0'
                          : get(config, 'details.settings.pageMargins').bottom
                      } ${
                        get(config, 'details.settings.pageMargins').left === 'zeropx'
                          ? '0'
                          : get(config, 'details.settings.pageMargins').left
                      }`,
                      background: '#FAFAFA',
                      height: '100%',
                      overflow: 'hidden',
                      borderRadius: '0',
                    }}
                  >
                    <Grid item className={classes.previewContainer}>
                      {loading ? (
                        <div
                          style={{
                            width: '50vw',
                          }}
                        >
                          <Loading />
                        </div>
                      ) : (
                        <TemplatePreview mode="view" setLoaded={setLoaded} tenant={tenant} />
                      )}
                    </Grid>
                  </Paper>
                </Grid>
              </Grid>
            </div>
          </DialogContent>
        </Dialog>

        <DialogComponent
          title={`Regenerate Url`}
          open={showDialogForRefreshPdfUrl}
          submitButtonText="Confirm"
          cancelButtonText="Cancel"
          width={'sm'}
          handleClose={() => {
            setShowDialogForRefreshPdfUrl(false);
          }}
          actions={{
            processing: isRefreshing,
            leftButtons: [
              {
                text: 'Cancel',
                onClick: () => {
                  setShowDialogForRefreshPdfUrl(false);
                },
                processing: isRefreshing,
              },
            ],
            rightButtons: [
              {
                text: 'Confirm',
                onClick: async () => {
                  setIsRefreshing(true);
                  await handleSetRefreshPdfUrl();
                },
                processing: isRefreshing,
              },
            ],
          }}
        >
          <p>A new URL to view the invoice will be generated. Do you want to continue ?</p>
        </DialogComponent>

        <DialogComponent
          title={`Email`}
          open={showDialogForSendEmail}
          submitButtonText="Confirm"
          cancelButtonText="Cancel"
          width={'sm'}
          handleClose={() => {
            setShowDialogForSendEmail(false);
          }}
          actions={{
            processing: isSending,
            leftButtons: [
              {
                text: 'Cancel',
                onClick: () => {
                  setShowDialogForSendEmail(false);
                },
                processing: isSending,
              },
            ],
            rightButtons: [
              {
                text: 'Confirm',
                onClick: async () => {
                  setIsSending(true);
                  await handleSendInvoiceEmail();
                },
                processing: isSending,
              },
            ],
          }}
        >
          <p>
            {invoice?.customer?.email
              ? `An email containing the invoice will be sent to ${invoice?.customer?.email}.`
              : 'An email containing the invoice will be sent to the customer.'}
          </p>
          <p>Do you wish to continue ?</p>
        </DialogComponent>
      </TemplateBuilderContext.Provider>
    </MuiThemeProvider>
  );
};

export default ViewInvoicePdf;

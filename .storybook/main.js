const path = require('path');

module.exports = {
  stories: ['../stories/**/*.stories.mdx', '../stories/**/*.stories.@(js|jsx|ts|tsx)'],
  addons: ['@storybook/addon-links', '@storybook/addon-essentials'],
  webpackFinal: async (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@nue-apps/ruby-ui-component': path.resolve(__dirname, '../components'),
    };
    config.resolve.extensions.push('.ts', '.tsx');
    return config;
  },
};

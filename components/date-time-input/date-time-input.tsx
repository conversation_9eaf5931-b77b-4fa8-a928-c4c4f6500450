import React, { useCallback, useEffect, useState } from 'react';

import DateFnsUtils from '@date-io/date-fns';
import { FormControl, ThemeProvider } from '@material-ui/core';
import CalendarTodayIcon from '@material-ui/icons/CalendarToday';
import { KeyboardDateTimePicker, MuiPickersUtilsProvider } from '@material-ui/pickers';
import { KeyboardDateTimePickerProps } from '@material-ui/pickers/DateTimePicker/DateTimePicker';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import useDateInputStyles from '../date-input/date-input-style';
import { CalendarIcon } from '../icons';
import LabelWithTooltip from '../label-with-tooltip';
import { MuiPickersTheme } from '../theme/theme';
import { Props } from './interface';

const defaultProps = {
  required: false,
  readOnly: false,
  value: null,
  dateFormat: 'yyyy-MM-dd',
};

dayjs.extend(utc);

export const StyledKeyboardDateTimePicker: React.FC<KeyboardDateTimePickerProps> = ({
  className,
  ...otherProps
}) => {
  const classes = useDateInputStyles();
  return (
    <ThemeProvider theme={MuiPickersTheme}>
      <KeyboardDateTimePicker
        className={className || classes.root}
        keyboardIcon={<CalendarIcon />}
        {...otherProps}
      />
    </ThemeProvider>
  );
};

const DateTimeInput: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const {
    value,
    label,
    field,
    name,
    disabled,
    handleInputChange,
    className,
    readOnly,
    required,
    dateFormat,
    showTooltip,
    toolTipText,
  } = props;

  const setupDate = useCallback(() => {
    if (value !== null) {
      return dayjs(value).format('MM/DD/YYYY hh:mm a');
    }
    return undefined;
  }, [value]);

  const [displayDate, setDisplayDate] = useState(setupDate());

  useEffect(() => {
    setDisplayDate(setupDate());
  }, [setupDate, value]);

  //todo fix styling
  const _label = label || field.name;
  const isRequired = required || field.required;

  return (
    <FormControl style={{ width: '100%' }}>
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        <LabelWithTooltip
          label={_label}
          required={isRequired}
          shrink
          showTooltip={showTooltip}
          tooltipText={toolTipText}
          htmlFor={name || field.apiName}
        />
        <StyledKeyboardDateTimePicker
          KeyboardButtonProps={{
            children: <CalendarTodayIcon />,
            'aria-label': label,
          }}
          invalidDateMessage=""
          format={dateFormat}
          disabled={disabled || readOnly}
          inputVariant="outlined"
          error={false}
          name={name || field.apiName}
          placeholder={_label}
          className={className}
          autoOk
          onBlur={() => {
            // convert date back to what it was previously if the date is invalid and user clicks out
            if (!dayjs(displayDate).isValid()) {
              setDisplayDate(dayjs.utc(value).format('MM/DD/YYYY hh:mm a'));
            }
          }}
          label={label}
          value={displayDate || new Date()}
          style={{ flexGrow: 1 }}
          required={isRequired}
          readOnly={readOnly}
          onChange={(date, value) => {
            console.debug('date-input-change', { date, value });
            if (handleInputChange && date !== null && value !== null) {
              const formattedDate = dayjs.utc(date).format('YYYY-MM-DDTHH:mm:ss[Z]');
              setDisplayDate(dayjs(value).format('MM/DD/YYYY hh:mm a'));
              if (dayjs(date).isValid()) {
                // only trigger updates to pricing calculator when date is valid
                handleInputChange(formattedDate);
              }
            }
          }}
        />
      </MuiPickersUtilsProvider>
    </FormControl>
  );
};

export default DateTimeInput;

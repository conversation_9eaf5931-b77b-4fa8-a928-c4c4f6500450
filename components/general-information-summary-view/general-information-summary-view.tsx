import React, { useEffect, useState } from 'react';

import type { Theme } from '@material-ui/core';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Grid,
  IconButton,
  Paper,
  Typography,
  makeStyles,
} from '@material-ui/core';
import { Autorenew } from '@material-ui/icons';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { Alert } from '@material-ui/lab';
import * as echarts from 'echarts';

import AccountViewItem from '../account-view-item';
import GridActions, { CellRendererRowProps } from '../grid-actions';
import { ViewButton } from '../grid-actions/grid-actions';
import Loading from '../loading/loading';
import type { ColumnConfig, GridViewMetadata, RubyField, RubyObject } from '../metadata';
import NoteIcon from '../note-icon/note-icon';
import PickList from '../pick-list';
import RubyGrid from '../ruby-grid';
import { buildRubyListColumns } from '../ruby-list-view';
import type { RubySettings } from '../ruby-settings';
import RubySwitch from '../ruby-switch';
import { SubscriptionRevenueStreamStackedChart } from '../subscription-revenue-stream-chart/subscription-revenue-stream-stacked-chart';
import TabView from '../tab-view';
import { useDebounce } from '../webhook-connection/webhook-connection-hook';
import type {
  GeneralInformationAccount,
  SubscriptionData,
  SubscriptionSteamData,
} from './interface';

interface Props {
  objects: any;
  rubySettings: RubySettings;
  fieldSetMetadata: RubyField[];
  gridViewMetadata: GridViewMetadata;
  objectMetadata: RubyObject;
  locale: string;
  currencyIsoCode: string;
  listViewMetadata: RubyField[];
  loadSubscriptionStreamData: () => SubscriptionSteamData;
  loadChildernAndBillingAccounts: () => GeneralInformationAccount;
  loadChildernAndBillingAccountsByIds: (
    ids: string[],
    billingAccountIds: any,
    childAccountIds: any,
    accountDetails: any,
  ) => any;
  addTransactionHubColumn?: () => {
    apiName: string;
    type: string;
    name: string;
    title: string;
    cellRenderer: ({ row }: CellRendererRowProps) => JSX.Element | null;
  };
}

const useStyles = makeStyles((theme: Theme) => ({
  main: {
    flexGrow: 1,
    flexDirection: 'column',
  },
  container: {
    display: 'flex',
  },
  root: {
    width: '100%',
  },
  heading: {
    fontWeight: 'bold',
  },
  biilingAccContainer: {
    [theme.breakpoints.up('md')]: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'end',
      paddingRight: '4%',
    },
    [theme.breakpoints.down('md')]: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'end',
      paddingRight: 0,
    },
  },
  chartSection: {
    [theme.breakpoints.up('md')]: {
      width: 400,
      marginTop: -8,
      marginRight: 42,
    },
    [theme.breakpoints.down('md')]: {
      width: '100%',
      marginTop: -8,
      marginRight: 24,
    },
  },
  streamDataContainer: {
    [theme.breakpoints.up('md')]: {
      backgroundColor: '#fff',
      borderRadius: 6,
      width: '100%',
      marginLeft: 42,
    },
    [theme.breakpoints.down('md')]: {
      backgroundColor: '#fff',
      borderRadius: 6,
      width: '100%',
      marginLeft: 24,
    },
  },
  tabsIndicator: {
    top: '0px',
    height: '3px',
    backgroundColor: '#6239EB',
    borderTopLeftRadius: '4px',
    borderTopRightRadius: '4px',
  },
  tabText: {
    color: '#979697',
  },
  iconBtn: {
    marginLeft: '10px',
    '&:hover, &:focus': {
      color: '#6239EB',
    },
    height: 45,
  },
  tabTextWidth: {
    color: '#979697',
    width: '228px',
  },
  selected: {
    backgroundColor: '#FFF',
    color: '#6239EB',
    fontWeight: 500,
  },
  selectedColor: {
    backgroundColor: '#6239eb',
    color: '#FFF',
    fontWeight: 500,
  },
  wrapper: {
    fontSize: '.9rem',
  },
  appBarRoot: {
    boxShadow: 'none',
    background: 'transparent',
  },
}));

const streamColors = [
  {
    line: '#FAD267',
    color: '#FAD267',
    secondaryColor: '#fdf3d7',
  },
  {
    line: '#3EBCF1',
    color: '#3EBCF1',
    secondaryColor: '#ccedfb',
  },
  {
    line: '#A62FF5',
    color: '#A62FF5',
    secondaryColor: '#e7c8fc',
  },
  {
    line: '#5ED6CB',
    color: '#5ED6CB',
    secondaryColor: '#d4f4f1',
  },
];

const tooltipStyle =
  'display: flex; flex-direction: column; align-items: flex-start; color: #BEBEBE; padding: 16px; width: 33%;';

const allSubsTooltipStyle =
  'display: flex; flex-direction: column; align-items: flex-start; color: #BEBEBE; padding: 16px; width: 50%;';

const tooltipFieldStyle = 'font-size: 10px;';

export const GeneralInformationSummaryView = ({
  objects,
  rubySettings,
  fieldSetMetadata,
  gridViewMetadata,
  objectMetadata,
  locale,
  currencyIsoCode,
  listViewMetadata,
  loadSubscriptionStreamData,
  loadChildernAndBillingAccounts,
  loadChildernAndBillingAccountsByIds,
  addTransactionHubColumn,
}: Props) => {
  const classes = useStyles();
  const currentCustomer = objects.find((x: any) => x.type === 'Account');
  const [value, setValue] = React.useState(0);
  const columns = [...(buildRubyListColumns(objectMetadata, listViewMetadata) || [])];

  const [showSubscriptions, setShowSubscriptions] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [loadingAccounts, setLoadingAccounts] = React.useState(true);
  const [billingAccounts, setBillingAccounts] = React.useState<any>(null);
  const [childAccounts, setChildAccounts] = React.useState<any>(null);
  const [allExpandedRowIds, setAllExpandedRowIds] = useState<string[]>([]);
  const [subscriptionSteamData, setSubscriptionSteamData] = React.useState<SubscriptionSteamData>({
    data: {},
  });
  const [salesAccounts, setSalesAccounts] = React.useState<any>(null);
  const [childSubscriptionRelationships, setChildSubscriptionRelationships] = useState<any>(null);
  const [hiddenColumns, setHiddenColumns] = useState<string[]>(
    columns?.filter((_) => _.hidden === true).map((_) => _.name),
  );
  const [selectedAcc, setSelectedAcc] = React.useState('__none__');
  const handleChange = (newActiveTabIndex: number) => {
    if (newActiveTabIndex === undefined) {
      return;
    }
    setValue(newActiveTabIndex);
  };
  const columnWidths = [
    ...columns.map((_) => {
      if (_.name === 'name') {
        return { columnName: _.name, width: _.width || 320 };
      }

      if (_.name === 'autoRenew') {
        return { columnName: _.name, width: _.width || 180 };
      }
      return { columnName: _.name, width: _.width || 110 };
    }),
  ];

  useEffect(() => {
    if (!hiddenColumns || hiddenColumns.length <= 0) {
      const nHiddenColumns = columns.filter((_) => _.hidden === true).map((_) => _.name);
      setHiddenColumns(nHiddenColumns);
    }
  }, [columns, hiddenColumns]);

  const loadData = async () => {
    if (loadSubscriptionStreamData) {
      setLoading(true);
      const data = await loadSubscriptionStreamData();
      setSubscriptionSteamData(data);

      if (data.accountHierarchy?.children?.length) {
        setSalesAccounts(data.accountHierarchy);
      }

      setLoading(false);
    }
  };

  const getChildAccountIds = (
    accounts: GeneralInformationAccount,
    skipItSelf: boolean,
    level: number,
  ) => {
    let result: string[] = skipItSelf ? [] : [accounts.id];
    let childStructures: any = skipItSelf
      ? []
      : [
          {
            id: accounts.id,
            children: accounts.children?.map((c) => c.id) || [],
            level: level,
          },
        ];
    if (accounts && accounts.children?.length) {
      const newLevel = level + 1;
      accounts.children.forEach((a) => {
        const newResult = getChildAccountIds(a, false, newLevel);
        result = result.concat(newResult.ids);
        childStructures = childStructures.concat(newResult.childStructures);
      });
    }

    return { ids: result, childStructures: childStructures };
  };

  const loadAccountData = async () => {
    if (loadChildernAndBillingAccounts) {
      try {
        setLoadingAccounts(true);
        const accounts = await loadChildernAndBillingAccounts();
        if (accounts) {
          const accountDetails = getChildAccountIds(accounts, true, 0);
          setChildSubscriptionRelationships(accountDetails.childStructures);
          const childAccountIds = accountDetails?.ids || [];
          const billingAccountIds = accounts?.billingAccounts?.map((x) => x.id) || [];

          if (
            (childAccountIds?.length > 0 || billingAccountIds?.length > 0) &&
            loadChildernAndBillingAccountsByIds
          ) {
            //doing query
            const loadResult = await loadChildernAndBillingAccountsByIds(
              childAccountIds.concat(billingAccountIds),
              billingAccountIds,
              childAccountIds,
              accountDetails,
            );
            setBillingAccounts(loadResult.billingAccountResult);
            setChildAccounts(loadResult.childAccountResult);
            setAllExpandedRowIds(loadResult.expandedIds);
          }
        }
      } catch {
        console.error('Something wrong when loading Account Data');
      } finally {
        setLoadingAccounts(false);
      }
    }
  };

  useEffect(() => {
    loadData();
    loadAccountData();
  }, []);

  const columnOrder = [...columns.map((_) => _.name)];
  const getTabs = () => {
    const tabResult = [];
    if (childAccounts && childAccounts.length) {
      tabResult.push({
        id: 'cAccount',
        displayName: 'Children Accounts',
        name: 'Children Accounts',
      });
    }

    if (billingAccounts && billingAccounts.length) {
      tabResult.push({
        id: 'bAccount',
        displayName: 'Billing Accounts',
        name: 'Billing Accounts',
      });
    }

    return tabResult;
  };

  const tabs = getTabs();
  const selectedTab = tabs?.[value];

  const subscriptionsByAccountId = subscriptionSteamData.data
    ? Object.entries(subscriptionSteamData.data)
        ?.map(([key, dataValue]: any) => {
          if (selectedAcc === key || (selectedAcc === '__none__' && key === currentCustomer?.id)) {
            const result: any = {
              billingAccountId: key,
              subscriptions: [],
            };
            dataValue.data.forEach((x: any) => {
              if (x.details) {
                Object.entries(x.details).map(([sKey, sValue]: any) => {
                  const existing = result.subscriptions.find(
                    (s: { name: string }) => s.name === sKey,
                  );
                  if (existing) {
                    existing.data.push({
                      startDate: x.startDate,
                      label: x.label,
                      quantity: sValue.quantity,
                      CMRR: Number(sValue.cmrr),
                      ARR: Number(sValue.arr),
                    });
                  } else {
                    result.subscriptions.push({
                      name: sKey,
                      data: [
                        {
                          startDate: x.startDate,
                          label: x.label,
                          quantity: sValue.quantity,
                          CMRR: Number(sValue.cmrr),
                          ARR: Number(sValue.arr),
                        },
                      ],
                    });
                  }
                });
              }
            });
            return result;
          }
        })
        .filter((x) => x)
    : [];

  const allSubsriptions: SubscriptionData[] = [];
  subscriptionsByAccountId.forEach((x) => {
    x.subscriptions.forEach((sub: any) => {
      const existingInList = allSubsriptions.find((as: any) => as.name === sub.name);

      if (existingInList) {
        sub.data.forEach((subd: any) => {
          const existDataInList: any = existingInList.data.find(
            (ed: any) => subd.startDate === ed.startDate,
          );

          if (existDataInList) {
            existDataInList.ARR = Number(existDataInList.ARR) + subd.ARR;
            existDataInList.CMRR = Number(existDataInList.CMRR) + subd.CMRR;
            existDataInList.quantity = Number(existDataInList.quantity) + subd.quantity;
          } else {
            existingInList.data.push(subd);
          }
        });
      } else {
        allSubsriptions.push(sub);
      }
    });
  });

  const combined = subscriptionSteamData.data
    ? Object.values(subscriptionSteamData.data)?.[0]?.data?.map((x) => {
        return {
          startDate: x.startDate,
          label: x.label,
          quantity: 0,
          CMRR: 0,
          ARR: 0,
        };
      })
    : [];

  const debounceLoadData = useDebounce(async () => {
    await loadData();
  }, 500);

  useEffect(() => {
    const handleResize = () => {
      debounceLoadData();
    };

    window.addEventListener('resize', handleResize);

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [loadData]);

  allSubsriptions.forEach((sub: SubscriptionData) => {
    sub.data.forEach((data: any) => {
      const exit = combined.find((x: any) => x.startDate === data.startDate);
      if (exit) {
        exit.quantity = exit.quantity + data.quantity;
        exit.CMRR = Number(exit.CMRR) + Number(data.CMRR);
        exit.ARR = Number(exit.ARR) + Number(data.ARR);
      } else {
        combined.push({
          startDate: data.startDate,
          label: data.label,
          quantity: data.quantity,
          CMRR: Number(data.CMRR),
          ARR: Number(data.ARR),
        });
      }
    });
  });

  const subscriptionToShow = () => {
    if (allSubsriptions.length > 3) {
      const newArr = [...allSubsriptions];
      newArr.sort((x: any, y: any) => {
        return y.data[0].ARR - x.data[0].ARR;
      });

      const topThree = newArr.slice(0, 3).sort((x: any, y: any) => {
        const totalX = x.data.reduce((z: any, h: any) => {
          return z + h.ARR;
        }, 0);
        const totalY = y.data.reduce((z: any, h: any) => {
          return z + h.ARR;
        }, 0);

        return totalX - totalY;
      });
      const rest = newArr.slice(3);

      const otherSubscriptions: any = {
        name: 'Others',
        data: [],
      };
      rest.forEach((x: any) => {
        if (x.data?.length) {
          x.data.forEach((d: any) => {
            const existData = otherSubscriptions.data.find(
              (ed: any) => ed.startDate === d.startDate,
            );
            if (existData) {
              existData.quantity = existData.quantity + d.quantity;
              existData.CMRR = Number(existData.CMRR) + d.CMRR;
              existData.ARR = Number(existData.ARR) + d.ARR;
            } else {
              otherSubscriptions.data.push({
                ...d,
              });
            }
          });
        }
      });
      if (otherSubscriptions.data.find((d: any) => d.CMRR > 0)) {
        return [otherSubscriptions].concat(topThree).map((x: any, index: number) => {
          return {
            ...x,
            ...streamColors[index],
          };
        });
      }
      return topThree.map((x: any, index: number) => {
        return {
          ...x,
          ...streamColors[index],
        };
      });
    } else {
      return allSubsriptions.map((x: any, index: number) => {
        return {
          ...x,
          ...streamColors[index],
        };
      });
    }
  };

  const browserLocale = navigator.language ? navigator.language : 'en-US';

  const currencyFormatter = new Intl.NumberFormat(browserLocale, {
    style: 'currency',
    currency: currencyIsoCode,
  });
  const shortFormatter = new Intl.NumberFormat(browserLocale, {
    // @ts-ignore
    notation: 'compact',
    compactDisplay: 'short',
  });

  const labelDetails = subscriptionSteamData.data
    ? Object.values(subscriptionSteamData.data)?.[0]?.data?.map((x: any) => {
        return { label: x.label, startDate: x.startDate };
      })
    : [];

  const customTooltipFormatter = () => {
    const tooltipGenerator = (
      CMRR: number,
      ARR: number,
      quantity: number,
      name: string,
      color: string,
      label: string,
      date: string,
    ) => {
      if (!showSubscriptions) {
        return `
        <div>
        <div style="display: flex; width: 100%; flex: 1 0 auto;"> 
        <div style="width: 12px; height: 12px; border:1px solid ${color}; border-radius: 12px; margin-top: 3px; margin-right:6px; background-color: ${color}"></div>
        <div>${label}<span style="margin-left:8px">(${date})</span></div>
      </div>
        <div style="display: flex; width: 100%; flex: 1 0 auto;">
          <div style="${allSubsTooltipStyle}">
            <div>${currencyFormatter.format(CMRR)}</div>
            <div style="${tooltipFieldStyle}">CMRR</div>
          </div>
          <div style="${allSubsTooltipStyle}">
            <div>${currencyFormatter.format(ARR)}</div>
            <div style="${tooltipFieldStyle}">ARR </div>
          </div>
        </div>
        </div>
      `;
      }
      return `
      <div>
      <div style="display: flex; width: 100%; flex: 1 0 auto;"> 
        <div style="width: 12px; height: 12px; border:1px solid ${color}; border-radius: 12px; margin-top: 3px; margin-right:6px; background-color: ${color}"></div>
        <div> ${
          name.split('::')[0]
        } <span style="margin-left: 8px"> ${label}<span style="margin-left:8px">(${date})</span></span></div>
      </div>
     
      <div style="display: flex; width: 100%; flex: 1 0 auto;">
        <div style="${tooltipStyle}">
          <div>${currencyFormatter.format(CMRR)}</div>
          <div style="${tooltipFieldStyle}">CMRR</div>
        </div>
        <div style="${tooltipStyle}">
          <div>${currencyFormatter.format(ARR)}</div>
          <div style="${tooltipFieldStyle}">ARR </div>
        </div>
        <div style="${tooltipStyle}">
          <div>${shortFormatter.format(quantity)}</div>
          <div style="${tooltipFieldStyle}">QUANTITY</div>
        </div>
      </div>
      </div>
    `;
    };

    return (params: any) => {
      const tooltips: string[] = [];

      if (!Array.isArray(params)) {
        return params;
      }

      params.reverse().forEach((x: any) => {
        const name = x.seriesName;
        const label = x.axisValue;
        const dateLabel = x.name;
        const getDate = labelDetails.find((labels: any) => labels.label === dateLabel);

        if (!getDate) {
          return;
        }
        if (!showSubscriptions) {
          const v: any = combined.find((c: any) => c.label === label);
          tooltips.push(
            tooltipGenerator(
              v.CMRR,
              v.ARR,
              v.quantity,
              name,
              x.color,
              dateLabel,
              getDate.startDate,
            ),
          );
        } else {
          const sub = subscriptionToShow().find((y: any) => y.name === name);

          if (sub) {
            const v = sub.data.find((d: any) => d.label === label);
            if (v) {
              tooltips.push(
                tooltipGenerator(
                  v.CMRR,
                  v.ARR,
                  v.quantity,
                  name,
                  x.color,
                  dateLabel,
                  getDate.startDate,
                ),
              );
            } else {
              tooltips.push(tooltipGenerator(0, 0, 0, name, x.color, dateLabel, getDate.startDate));
            }
          }
        }
      });
      const formattedHtml = `
        <div style="display: flex; flex-direction: column;background-color: #000;">
            ${tooltips.map((x, index) => {
              return `<div key={${index}}>${x}</div>`;
            })}
        </div>
      `
        //@ts-ignore
        .replaceAll(',', '');

      return formattedHtml;
    };
  };

  const subscriptionNames = showSubscriptions
    ? subscriptionToShow().map((x: any) => x.name)
    : ['All Subscriptions'];

  const getSeries = () => {
    const markdoneData = Object.values(subscriptionSteamData.data)?.[0];
    if (showSubscriptions) {
      const resultData: any = [];
      const calculatedSubscriptions: any = [];

      subscriptionToShow().forEach((subscription: any) => {
        const calculatedData: any = [];
        const allData: any = [];

        calculatedSubscriptions.forEach((x: any) => {
          if (x.data?.length) {
            x.data.forEach((d: any) => {
              allData.push(d);
            });
          }
        });

        labelDetails.forEach((details: any) => {
          const subData = subscription.data.find((x: any) => x.startDate == details.startDate);

          if (subData) {
            calculatedData.push(subData.CMRR);
          } else {
            calculatedData.push(0);
          }
        });

        resultData.push({
          name: subscription.name, // name needed in tooltip display
          type: 'line',
          stack: 'Total',
          smooth: true,
          emphasis: {
            focus: 'series',
          },
          showSymbol: false,
          itemStyle: {
            color: subscription.line,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: subscription.color,
              },
              {
                offset: 1,
                color: subscription.secondaryColor,
              },
            ]),
          },
          markLine: {
            symbol: ['circle', 'none'],
            data: markdoneData
              ? [
                  ...markdoneData?.data?.map((item) => {
                    const xAxisLabel = {
                      symbolSize: 0,
                      xAxis: item.label,
                      label: {
                        formatter: item.label,
                        position: 'start',
                        distance: 33,
                        rotate: 45,
                      },
                      lineStyle: { type: 0, width: 0 },
                    };
                    return xAxisLabel;
                  }),
                ]
              : [],
          },
          data: calculatedData,
        });

        calculatedSubscriptions.push(subscription);
      });

      return {
        seriesData: resultData,
      };
    }

    return {
      seriesData: [
        {
          name: 'All Subscriptions',
          type: 'line',
          stack: 'Total',
          smooth: true,
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            color: '#6239EB',
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#C0AEF6',
              },
              {
                offset: 1,
                color: '#ECECF6',
              },
            ]),
          },
          markLine: {
            symbol: ['circle', 'none'],
            data: markdoneData
              ? [
                  ...markdoneData?.data?.map((item) => {
                    const xAxisLabel = {
                      symbolSize: 0,
                      xAxis: item.label,
                      label: {
                        formatter: item.label,
                        position: 'start',
                        distance: 33,
                        rotate: 45,
                      },
                      lineStyle: { type: 0, width: 0 },
                    };
                    return xAxisLabel;
                  }),
                ]
              : [],
          },
          data: combined?.map((_: any) => _.CMRR),
        },
      ],
    };
  };

  const getSalesAccountList = (sAccount: any, isFirst: boolean, level: number) => {
    let sAccountResult = isFirst
      ? []
      : [
          {
            name: <div style={{ paddingLeft: `${level * 18}px` }}>{sAccount.name}</div>,
            value: sAccount.id,
            originalName: sAccount.name,
          },
        ];
    if (sAccount.children && sAccount.children.length) {
      const newLevel = isFirst ? level : level + 1;
      sAccount.children.forEach((ch: any) => {
        const sCAccount = getSalesAccountList(ch, false, newLevel);
        sAccountResult = sAccountResult.concat(sCAccount);
      });
    }
    return sAccountResult;
  };

  const salesAccountsMapped = salesAccounts ? getSalesAccountList(salesAccounts, true, 0) : [];
  const mergedColumnsWidths = [
    { columnName: 'actions', width: 80 },
    {
      columnName: 'transactionHub',
      width: 150,
    },
    ...columnWidths,
  ];

  const showStreamChartSection = () => {
    const series = getSeries();
    if (showSubscriptions) {
      if (!series?.seriesData?.length) {
        return (
          <div style={{ padding: 32 }}>
            <Alert
              severity="warning"
              style={{ marginTop: 24, backgroundColor: '#F2F0FF', color: '#000' }}
              icon={<NoteIcon />}
            >
              There is no data available.
            </Alert>
          </div>
        );
      }
    }

    if (!combined?.length) {
      return (
        <div style={{ padding: 32 }}>
          <Alert
            severity="warning"
            style={{ marginTop: 24, backgroundColor: '#F2F0FF', color: '#000' }}
            icon={<NoteIcon />}
          >
            There is no data available.
          </Alert>
        </div>
      );
    }

    return (
      <SubscriptionRevenueStreamStackedChart
        minHeight="450px"
        {...series}
        currencyIsoCode={currencyIsoCode}
        labels={labelDetails.map((x) => x.label)}
        subscriptionNames={subscriptionNames}
        customTooltipFormatter={customTooltipFormatter}
      />
    );
  };

  const combinedColumns = [
    {
      apiName: 'actions',
      type: 'action',
      name: 'actions',
      title: 'Actions',
      cellRenderer: ({ row }: any) => {
        const iconStyles = {
          color: '#6239eb',
          opacity: 1,
          height: '18px',
          width: '18px',
        };
        const viewBox = '0 0 24 24';
        const actions = [];

        actions.push(
          ViewButton({
            tooltipText: `View Customer`,
            onClickHandler: async () => {
              window.open(
                '/lightning/cmp/Ruby__LifecycleManager?c__customerId=' + row.id,
                '_blank',
              );
            },
          }),
        );
        return <GridActions actionHelpers={{ iconStyles, viewBox }} actions={actions} />;
      },
    },
    addTransactionHubColumn?.(),
    ...columns,
  ];

  return (
    <Grid className={classes.main} container>
      {!loadingAccounts && !currentCustomer && <div style={{ textAlign: 'center' }}>No Data</div>}
      <Grid item style={{ display: 'flex' }}>
        <AccountViewItem
          handleCardClick={() => {}}
          key={currentCustomer?.id}
          rubySettings={rubySettings}
          fieldSetMetadata={fieldSetMetadata.filter((x) => x.apiName !== 'name')}
          gridViewMetadata={gridViewMetadata}
          object={currentCustomer}
          currencyIsoCode={currencyIsoCode}
          objectMetadata={objectMetadata}
          actionEventHandler={() => {}}
          locale={locale}
          summaryView={true}
          addTransactionHub={addTransactionHubColumn}
        />
        {loading && (
          <Grid item xs={8} sm={8} md={9}>
            <Loading />
          </Grid>
        )}
        {!loading && subscriptionSteamData?.data && currentCustomer && (
          <Grid className={classes.streamDataContainer}>
            <Grid
              container
              justifyContent="space-between"
              alignItems="center"
              style={{ padding: 12 }}
            >
              <Grid item xs={4}>
                <Grid container alignItems="center">
                  <h3 style={{ fontSize: 20, paddingLeft: '6%' }}>Revenue Stream</h3>
                  <IconButton
                    onClick={async () => {
                      await loadData();
                    }}
                    className={classes.iconBtn}
                  >
                    <Autorenew />
                  </IconButton>
                </Grid>
              </Grid>
              <Grid item xs={8}>
                <div className={classes.biilingAccContainer}>
                  <div className={classes.chartSection}>
                    <PickList
                      value={selectedAcc}
                      label=" "
                      field={{
                        apiName: 'savedFilter',
                        name: 'Saved Filter',
                        type: 'text',
                      }}
                      useOriginalName
                      showNone
                      name="filter"
                      defaultText="All Sales Accounts"
                      //@ts-ignore
                      options={salesAccountsMapped}
                      handleInputChange={(sValue: string) => {
                        setSelectedAcc(sValue || '__none__');
                      }}
                    />
                  </div>
                  <RubySwitch
                    value={showSubscriptions}
                    name={''}
                    labelPlacement="end"
                    label="Show Subscriptions"
                    handleInputChange={() => {
                      setShowSubscriptions(!showSubscriptions);
                    }}
                  />
                </div>
              </Grid>

              <Grid item xs={12} style={{ marginTop: 24 }}>
                {showStreamChartSection()}
              </Grid>
            </Grid>
          </Grid>
        )}
      </Grid>
      {!loadingAccounts && objects?.length > 0 && currentCustomer && (
        <Grid item style={{ width: '100%' }}>
          <Grid container className={classes.container} style={{ paddingTop: 24 }}>
            <Accordion style={{ width: '100%' }} defaultExpanded>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls="panel1a-content"
                id="panel1a-header"
              >
                <Typography className={classes.heading}>Related Accounts</Typography>
              </AccordionSummary>
              {tabs && tabs.length > 0 && (
                <AccordionDetails>
                  <Paper style={{ width: '100%', overflow: 'hidden' }}>
                    {selectedTab?.id === 'cAccount' ? (
                      <RubyGrid
                        removeToolBarRootStyle
                        columnReordering
                        tooltip={
                          <TabView
                            activeTabIndex={value}
                            scrollButtons={'off'}
                            tabs={tabs}
                            reverseColorStyle
                            handleUpdateTabIndex={handleChange}
                          />
                        }
                        getChildRows={(row: any, rootRows: any[]) => {
                          if (!row) {
                            const rootIds = childSubscriptionRelationships.filter(
                              (x: any) => x.level == 1,
                            );
                            if (!rootIds?.length) {
                              return rootRows;
                            }
                            const roots = rootRows.filter((x) => {
                              const f = rootIds.find((c: any) => c.id === x.id);
                              return !!f;
                            });
                            return roots;
                          }

                          const childObject = childSubscriptionRelationships.find(
                            (x: any) => x.id === row.id,
                          );
                          if (childObject && childObject.children?.length) {
                            const cRoots = rootRows.filter((x) => {
                              const f = childObject.children.find((c: any) => c === x.id);
                              return !!f;
                            });
                            return cRoots;
                          } else {
                            return null;
                          }
                        }}
                        expandRows
                        expandedRowIds={allExpandedRowIds}
                        expandRowField="name"
                        pageSize={20}
                        columns={combinedColumns.filter((column) => !!column) as ColumnConfig[]}
                        columnWidths={mergedColumnsWidths}
                        rows={childAccounts}
                        columnOrder={columnOrder}
                        currencyIsoCode={currencyIsoCode}
                        hiddenColumns={hiddenColumns}
                        showColumnChooser
                        hideBorderRadius
                      />
                    ) : (
                      <RubyGrid
                        removeToolBarRootStyle
                        columnReordering
                        tooltip={
                          <TabView
                            activeTabIndex={value}
                            scrollButtons={'off'}
                            tabs={getTabs()}
                            reverseColorStyle
                            handleUpdateTabIndex={handleChange}
                          />
                        }
                        pageSize={20}
                        columns={combinedColumns.filter((column) => !!column) as ColumnConfig[]}
                        columnWidths={mergedColumnsWidths}
                        rows={billingAccounts}
                        columnOrder={columnOrder}
                        currencyIsoCode={currencyIsoCode}
                        hiddenColumns={hiddenColumns}
                        showColumnChooser
                        hideBorderRadius
                      />
                    )}
                  </Paper>
                </AccordionDetails>
              )}
            </Accordion>
          </Grid>
        </Grid>
      )}
    </Grid>
  );
};

import { Grid, Paper, Typography } from '@material-ui/core';
import FormControl from '@material-ui/core/FormControl';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import type { ClassNameMap } from '@material-ui/core/styles/withStyles';
import dayjs from 'dayjs';
import _, { get, isInteger, isNull } from 'lodash';
import numbro from 'numbro';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import Logo from '../../../static/images/logo.svg';
import { BillingSettingsContext } from '../../ruby-settings/interface';
import { TenantPropsContext } from '../../ruby-settings/setting-editors/organization-settings-editor';
import type {
  TemplateField,
  TemplateSchema,
} from '../../template-builder-context/template-builder-context';
import type { Tenant } from '../../tenant-detail/interface';
import { useUserLocale } from '../../use-user-locale';
import { TemplateLanguageCodes } from '../config/language_config';
import type {
  TemplateColumnGeneratorProps,
  TemplateContainerGeneratorProps,
  TemplateFieldGeneratorProps,
  TemplatePaperGeneratorProps,
  TemplateRelatedListGeneratorProps,
  TemplateRichTextGeneratorProps,
  TemplateTextGeneratorProps,
} from '../interface';
import { useStyles } from '../template-preview';
//@ts-ignore
import * as numbroLanguages from 'numbro/dist/languages.min.js';
import Loading from '../../loading';

export const TemplatePaperGenerator = (props: TemplatePaperGeneratorProps) => {
  const { content, children, templateConfig, activateComponent } = props;
  //@ts-ignore
  const classes = useStyles({ templateConfig });
  return (
    <Grid
      className={classes.PaperContainer}
      style={{
        display: content.deleted ? 'none' : '',
        minHeight: content.PaperContainerMinHeight || '120px',
      }}
    >
      {content.title && (
        <p
          style={{
            fontSize: '20px',
            fontWeight: 'bold',
            paddingLeft: '13px',
            color: get(templateConfig, 'details.settings.themeColor'),
          }}
        >
          {content.title}
        </p>
      )}
      <Grid
        item
        container
        xs={12}
        style={{
          ...content.styleSetting,
          borderColor:
            content.styleSetting?.borderColor || get(templateConfig, 'details.settings.themeColor'),
          backgroundColor:
            content.styleSetting?.backgroundColor ||
            get(templateConfig, 'details.settings.themeColor'),
        }}
        id={content.id}
        className={activateComponent?.id === content.id ? classes.activatePreviewComponent : ''}
        role="templatePaperGenerator-Grid"
      >
        {children}
      </Grid>
    </Grid>
  );
};

export const TemplateContainerGenerator = (props: TemplateContainerGeneratorProps) => {
  const { container, children, activateComponent, templateConfig } = props;
  //@ts-ignore
  const classes = useStyles({ templateConfig });

  const hexToRgb = (str: any) => {
    const r = /^\#?[0-9A-Fa-f]{6}$/;
    str = str.replace('#', '');
    const hxs = str.match(/../g);
    //alert('bf:'+hxs)
    for (let i = 0; i < 3; i++) hxs[i] = parseInt(hxs[i], 16);
    //alert(parseInt(80, 16))
    //console.log(hxs);
    return hxs;
  };

  const rgbToHex = (a: any, b: any, c: any) => {
    const r = /^\d{1,3}$/;
    const hexs = [a.toString(16), b.toString(16), c.toString(16)];
    for (let i = 0; i < 3; i++) if (hexs[i].length == 1) hexs[i] = '0' + hexs[i];
    return '#' + hexs.join('');
  };

  const getSectionBackgroundColor = (column: any) => {
    const cmpBackgroundColor = get(column, 'styleSetting.backgroundColor');
    const cmpOpacity = get(column, 'styleSetting.level') || 0;
    const themeColor = get(templateConfig, 'details.settings.themeColor');
    let backgroundColor;
    backgroundColor = !cmpBackgroundColor
      ? getLightColor(themeColor, cmpOpacity)
      : cmpBackgroundColor;
    return backgroundColor || 'white';
  };

  const getLightColor = (color: any, level: any) => {
    const r = /^\#?[0-9A-F]{6}$/;
    const rgbc = hexToRgb(color);
    //@ts-ignore
    for (let i = 0; i < 3; i++) rgbc[i] = Math.floor((255 - rgbc[i]) * level + rgbc[i]);
    //@ts-ignore
    return rgbToHex(rgbc[0], rgbc[1], rgbc[2]);
  };

  return (
    <Grid
      item
      container
      style={{
        padding: 0,
        display: container.deleted ? 'none' : '',
        ...container.styleSetting,
        borderColor:
          container.styleSetting?.borderColor || get(templateConfig, 'details.settings.themeColor'),
        backgroundColor: getSectionBackgroundColor(container),
        flex: 1,
      }}
      id={container.id}
      className={
        activateComponent?.id === container.id ? classes.activatePreviewContainerComponent : ''
      }
      role="templateContainerGenerator-Grid"
    >
      {children}
    </Grid>
  );
};

export const TemplateColumnGenerator = (props: TemplateColumnGeneratorProps) => {
  const { column, children, activateComponent, templateConfig } = props;
  //@ts-ignore
  const classes = useStyles({ templateConfig });

  const hexToRgb = (str: any) => {
    const r = /^\#?[0-9A-Fa-f]{6}$/;
    //test方法检查在字符串中是否存在一个模式，如果存在则返回true，否则返回false
    if (!r.test(str)) return window.alert('输入错误的hex');
    //replace替换查找的到的字符串
    str = str.replace('#', '');
    //match得到查询数组
    const hxs = str.match(/../g);
    //alert('bf:'+hxs)
    for (let i = 0; i < 3; i++) hxs[i] = parseInt(hxs[i], 16);
    //alert(parseInt(80, 16))
    //console.log(hxs);
    return hxs;
  };

  const rgbToHex = (a: any, b: any, c: any) => {
    const r = /^\d{1,3}$/;
    const hexs = [a.toString(16), b.toString(16), c.toString(16)];
    for (let i = 0; i < 3; i++) if (hexs[i].length == 1) hexs[i] = '0' + hexs[i];
    return '#' + hexs.join('');
  };

  const getSectionBackgroundColor = (column: any) => {
    const cmpBackgroundColor = get(column, 'styleSetting.backgroundColor');
    const cmpOpacity = get(column, 'styleSetting.level') || 0;
    const themeColor = get(templateConfig, 'details.settings.themeColor');
    let backgroundColor;
    backgroundColor = !cmpBackgroundColor
      ? getLightColor(themeColor, cmpOpacity)
      : cmpBackgroundColor;
    return backgroundColor || 'white';
  };

  const getLightColor = (color: any, level: any) => {
    const r = /^\#?[0-9A-F]{6}$/;
    const rgbc = hexToRgb(color);
    //@ts-ignore
    for (let i = 0; i < 3; i++) rgbc[i] = Math.floor((255 - rgbc[i]) * level + rgbc[i]);
    //@ts-ignore
    return rgbToHex(rgbc[0], rgbc[1], rgbc[2]);
  };

  return (
    <Grid
      item
      container
      style={{
        display: column.deleted ? 'none' : '',
        ...column.styleSetting,
        borderColor:
          column.styleSetting?.borderColor || get(templateConfig, 'details.settings.themeColor'),
        backgroundColor: getSectionBackgroundColor(column),
        flex: column.styleSetting?.flex || 1,
        height: column.styleSetting?.height || '100%',
      }}
      id={column.id}
      className={activateComponent?.id === column.id ? classes.activatePreviewComponent : ''}
      role="templateColumnGenerator-Grid"
    >
      {children}
    </Grid>
  );
};

export const TemplateContentGenerator = (props: TemplateFieldGeneratorProps) => {
  const { content, children } = props;
  switch (content.type) {
    case 'field':
      return TemplateFieldGenerator(props);
    case 'image':
      return TemplateImageGenerator(props);
    case 'relatedList':
      return TemplateRelatedListGenerator(props);
    case 'richText':
      return TemplateRichTextGenerator(props);
    case 'text':
      return TemplateTextGenerator(props);
    default:
      return TemplateFieldGenerator(props);
  }
};

export const TemplateRelatedListGenerator = (props: TemplateRelatedListGeneratorProps) => {
  const {
    content,
    children,
    templateOrderProduct,
    templateRecord,
    activateComponent,
    templateConfig,
  } = props;
  const tenant = templateRecord?.company;
  //@ts-ignore
  const classes = useStyles({ templateConfig });
  const billingSettingsCtx = useContext(BillingSettingsContext);
  const borderColor =
    get(content, 'styleSetting.borderColor') || get(templateConfig, 'details.settings.themeColor');
  const getTableAlignment = (field: TemplateField) => {
    switch (field.type) {
      case 'text':
        return 'left';
      case 'decimal':
        return 'right';
      case 'currency':
        return 'right';
      case 'dateTime':
        return 'left';
      case 'date':
        return 'left';
      default:
        return 'left';
    }
  };

  const { getUserLocale } = useUserLocale();

  const scaleTableToFixParentContainer = (parentNode: any, tableNode: any) => {
    const tableContainerWidth = parseFloat(getComputedStyle(parentNode!).width);
    const tableWidth = parseFloat(getComputedStyle(tableNode!).width);
    console.log('parentNode?.style.width', getComputedStyle(parentNode!).width);
    console.log('tableNode?.style.width', getComputedStyle(tableNode!).width);
    // tableNode!.style.width = getComputedStyle(parentNode!).width
    tableNode!.style.transform = `scale(${734 / tableWidth})`;
  };

  useEffect(() => {
    if (
      document.getElementsByClassName('relatedListTableContainer') &&
      document.getElementsByClassName('relatedListTable')
    ) {
      const parentNodeList = document.getElementsByClassName('relatedListTableContainer');
      const tableNodeList = document.getElementsByClassName('relatedListTable');
      Array.from(parentNodeList).forEach((parentNode: any, index: any) => {
        scaleTableToFixParentContainer(parentNode, tableNodeList[index]);
      });
    }
  }, [document.getElementsByClassName('relatedListTableContainer')]);

  const getLocale = () => {
    return getUserLocale().replace('_', '-');
  };

  const convertToCurrency = (value: number, currencyIsoCode?: string) => {
    const currencyCode = tenant?.currency || 'USD';
    return Number(value).toLocaleString(getLocale(), {
      style: 'currency',
      currency: currencyIsoCode || templateRecord.currency || currencyCode,
      maximumFractionDigits: 2,
    });
  };

  const converToJSON = (value: any) => {
    let json = '';
    if (value && value.length > 0) {
      json += value[0].Ruby__PriceDimension__r.Name + '<ul>';
      value
        .sort((a: any, b: any) => {
          return a.Ruby__TierNumber__c - b.Ruby__TierNumber__c;
        })
        .forEach((val: any) => {
          const {
            Name,
            Ruby__TierNumber__c,
            Ruby__StartUnit__c,
            Ruby__EndUnit__c,
            Ruby__DiscountPercentage__c,
            Ruby__Amount__c,
            Ruby__ChargeModel__c,
            Ruby__StartUnitDimension__c,
            Ruby__EndUnitDimension__c,
            Ruby__PriceDimensionType__c,
            Ruby__UomDimension__c,
          } = val;
          if (Ruby__PriceDimensionType__c === 'Quantity') {
            json += `<li>${Ruby__StartUnit__c}${Ruby__EndUnit__c ? ' - ' + Ruby__EndUnit__c : '+ '} ${Ruby__UomDimension__c || ''}: ${Ruby__DiscountPercentage__c ? Ruby__DiscountPercentage__c + '%' : convertToCurrency(Ruby__Amount__c)}  ${Ruby__ChargeModel__c}</li>`;
          } else if (Ruby__PriceDimensionType__c === 'Term') {
            json += `<li>${Ruby__StartUnit__c}${Ruby__EndUnit__c ? ' ' + Ruby__StartUnitDimension__c + ' - ' + Ruby__EndUnit__c + ' ' + Ruby__EndUnitDimension__c : '+ ' + Ruby__StartUnitDimension__c}: ${Ruby__DiscountPercentage__c ? Ruby__DiscountPercentage__c + '%' : convertToCurrency(Ruby__Amount__c)}  ${Ruby__ChargeModel__c}</li>`;
          } else {
            json += `<li>${Ruby__StartUnit__c}${Ruby__EndUnit__c ? ' - ' + Ruby__EndUnit__c : '+ '} ${Ruby__UomDimension__c || ''}: ${Ruby__DiscountPercentage__c ? Ruby__DiscountPercentage__c + '%' : convertToCurrency(Ruby__Amount__c)}  ${Ruby__ChargeModel__c}</li>`;
          }
        });
      json += '</ul>';
      return json;
    }
    return '';
  };

  const isJSON = (str: string) => {
    if (!str) {
      return false;
    }
    if (typeof str == 'string') {
      try {
        const obj = JSON.parse(str);
        if (typeof obj === 'object' && obj !== null) {
          return true;
        } else {
          return false;
        }
      } catch (e) {
        return false;
      }
    }
    return false;
  };

  const cellValueGenerator = (product: any, field: any) => {
    let value = Object.assign({}, product);
    field.field.forEach((f: any, index: number) => {
      if (index !== 0) {
        value = get(value, f);
      }
    });
    const fieldLanguageConfig = TemplateLanguageCodes.find(
      (t) => t.language_code === (templateConfig.locale || tenant?.language),
    );
    if (isNull(value)) {
      return '';
    }
    switch (field.type) {
      case 'text':
        if (isJSON(value)) {
          const obj = JSON.parse(value);
          let jsonUl = '<ul style="padding-left: 0">';
          Object.entries(obj).forEach(([key, val]) => {
            const safeKey = String(key).replace(/[<>&"']/g, (c) => {
              return (
                { '<': '&lt;', '>': '&gt;', '&': '&amp;', '"': '&quot;', "'": '&#39;' }[c] || c
              );
            });
            const safeVal = String(val).replace(/[<>&"']/g, (c) => {
              return (
                { '<': '&lt;', '>': '&gt;', '&': '&amp;', '"': '&quot;', "'": '&#39;' }[c] || c
              );
            });
            jsonUl += `<li style="list-style: none">${safeKey}: ${safeVal}</li>`;
          });
          jsonUl += '</ul>';
          return jsonUl;
        } else {
          return value;
        }
      case 'pickList':
        return value;
      case 'date':
        return dayjs
          .utc(value)
          .format(
            fieldLanguageConfig?.language_date_format
              ? fieldLanguageConfig?.language_date_format
              : 'MM/DD/YYYY',
          );
      case 'dateTime':
        return dayjs
          .utc(value)
          .format(
            fieldLanguageConfig?.language_datetime_format
              ? fieldLanguageConfig?.language_datetime_format
              : 'MM/DD/YYYY hh:mm a',
          );
      case 'decimal':
        return isInteger(value)
          ? numbro(value || 0).format()
          : numbro(value || 0).format({
              mantissa: 2,
            });
      case 'number':
        return isInteger(value)
          ? numbro(value || 0).format()
          : numbro(value || 0).format({
              mantissa: 2,
            });
      case 'currency':
        return convertToCurrency(value || 0, product.currency);
      case 'json':
        console.log('converToJSON', value);
        console.log('converToJSON', product);
        if (value.length > 0) {
          value.forEach((element: any) => {
            if (
              product.Ruby__PriceDimension__r &&
              product.Ruby__PriceDimension__r.Ruby__PriceDimensionType__c
            ) {
              element.Ruby__PriceDimensionType__c =
                product.Ruby__PriceDimension__r.Ruby__PriceDimensionType__c;
            }
            if (
              product.Ruby__PriceDimension__r &&
              product.Ruby__PriceDimension__r.Ruby__UomDimension__c
            ) {
              element.Ruby__UomDimension__c = product.Ruby__PriceDimension__r.Ruby__UomDimension__c;
            }

            if (product.priceDimension && product.priceDimension.priceDimensionType) {
              element.Ruby__PriceDimensionType__c = product.priceDimension.priceDimensionType;
            }
            if (product.priceDimension && product.priceDimension.uomDimension) {
              element.Ruby__UomDimension__c = product.priceDimension.uomDimension;
            }
          });
        }
        return converToJSON(value);
      case 'percent':
        return isNull(value) ? '' : `${value}%`;
      default:
        return isNull(value) ? '' : value;
    }
  };
  return (
    <Grid
      item
      xs={12}
      justifyContent="center"
      alignItems="center"
      className={activateComponent?.id === content.id ? classes.activatePreviewComponent : ''}
      id={content.id}
      style={{
        display: content.deleted ? 'none' : '',
        background: 'white',
      }}
    >
      <TableContainer
        className={'relatedListTableContainer'}
        style={{
          overflowX: 'hidden',
        }}
      >
        <Table
          aria-label="a dense table"
          style={{ width: '100%' }}
          className={classes.relatedListTable + ' relatedListTable'}
        >
          <TableHead>
            <TableRow
              style={{
                borderLeft: content.styleSetting?.borderContentLeftWidht
                  ? `${content.styleSetting.borderContentLeftWidht} solid ${borderColor}`
                  : 'none',
                borderRight: content.styleSetting?.borderContentRightWidht
                  ? `${content.styleSetting.borderContentRightWidht} solid ${borderColor}`
                  : 'none',
              }}
            >
              {content.displayFields?.map((field) => (
                <TableCell
                  align={getTableAlignment(field)}
                  style={{
                    borderTop: `2px solid ${borderColor}`,
                    borderBottom: `2px solid ${borderColor}`,
                    color: `${
                      get(content, 'styleSetting.headerColor') ||
                      get(content, 'styleSetting.color') ||
                      get(templateConfig, 'details.settings.themeColor')
                    }`,
                    fontWeight: 'bold',
                    // minWidth: '100px',
                    whiteSpace: 'nowrap',
                    fontSize: get(templateConfig, 'details.settings.fontSize') + 'px',
                    fontFamily: content.styleSetting.fontFamily
                      ? content.styleSetting.fontFamily
                      : get(templateConfig, 'details.settings.fontFamily'),
                    backgroundColor: content.styleSetting.backgroundColor
                      ? content.styleSetting.backgroundColor
                      : get(templateConfig, 'details.settings.themeColor'),
                  }}
                  role={field.label}
                >
                  {field.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody
            style={{
              borderBottom: `2px solid ${borderColor}`,
              borderLeft: content.styleSetting?.borderContentLeftWidht
                ? `${content.styleSetting.borderContentLeftWidht} solid ${borderColor}`
                : 'none',
              borderRight: content.styleSetting?.borderContentRightWidht
                ? `${content.styleSetting.borderContentRightWidht} solid ${borderColor}`
                : 'none',
              background: 'white',
            }}
          >
            {templateRecord[content.field![0]]
              ?.sort((a: any, b: any) => {
                if (a.product?.name && b.product?.name) {
                  if (a.product?.name !== b.product?.name) {
                    return a.product?.name.localeCompare(b.product?.name);
                  } else if (a.startTime && b.startTime) {
                    if (a.startTime !== b.startTime) {
                      return +new Date(a.startTime) > +new Date(b.startTime) ? 1 : -1;
                    } else {
                      return 0;
                    }
                  } else {
                    return 0;
                  }
                } else {
                  return 0;
                }
              })
              .map((product: any) => (
                <TableRow key={product.name}>
                  {content.displayFields?.map((field) => (
                    <TableCell
                      component="th"
                      scope="row"
                      style={{
                        border: 0,
                        color: content.styleSetting?.color || 'black',
                        textAlign: getTableAlignment(field),
                        minWidth: '100px',
                        whiteSpace: 'nowrap',
                        fontSize: get(templateConfig, 'details.settings.fontSize') + 'px',
                        fontFamily: content.styleSetting.fontFamily
                          ? content.styleSetting.fontFamily
                          : get(templateConfig, 'details.settings.fontFamily'),
                      }}
                    >
                      <div
                        dangerouslySetInnerHTML={{ __html: cellValueGenerator(product, field) }}
                      />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
      {content.summaryFields && content.summaryFields.length > 0 && (
        <TemplateSummaryGenerator
          content={content}
          templateRecord={templateRecord}
          templateConfig={templateConfig}
          currencyIsoCode={tenant?.currency}
        />
      )}
    </Grid>
  );
};

export const TemplateSummaryGenerator = (props: TemplateRelatedListGeneratorProps) => {
  const { content, templateRecord, templateConfig, currencyIsoCode } = props;
  //@ts-ignore
  const classes = useStyles({ templateConfig });

  const { getUserLocale } = useUserLocale();

  const getLocale = () => {
    return getUserLocale().replace('_', '-');
  };

  const convertToCurrency = (value: number) => {
    const currencyCode = templateRecord.currency || currencyIsoCode || 'USD';
    return Number(value).toLocaleString(getLocale(), {
      style: 'currency',
      currency: currencyCode,
      maximumFractionDigits: 2,
    });
  };

  const getSummaryFieldValue = (field: any) => {
    const value = templateRecord[content.field![0]];
    const valueField = field[0] === 'usage' ? 'amount' : field[1]; //TODO: Since there's no related attribute on usage, the result is total of each 'amount'
    return (
      value?.reduce((o: number, m: any) => {
        return o + (m[valueField] || 0);
      }, 0) || 0
    );
  };

  const c = useRef(null);

  useEffect(() => {
    if (c.current) {
      //@ts-ignore
      c.current.innerHTML = content.description?.replace(/(\r\n|\n|\r|\\n)/gm, '<br/>') || '';
    }
  }, [content.description]);

  return (
    <Grid
      item
      container
      justifyContent="space-between"
      style={{
        marginTop: '40px',
        fontSize: '0.875rem',
        background: 'white',
        color:
          content.styleSetting?.color ||
          get(templateConfig, 'details.settings.themeColor') ||
          'black',
      }}
    >
      <Grid
        item
        xs={5}
        // dangerouslySetInnerHTML={{
        //   __html: content.description?.replace(/(\r\n|\n|\r|\\n)/gm, '<br/>') || '',
        // }}
        style={{
          textAlign: 'left',
          fontSize: get(templateConfig, 'details.settings.fontSize') + 'px',
          fontFamily: content.styleSetting.fontFamily
            ? content.styleSetting.fontFamily
            : get(templateConfig, 'details.settings.fontFamily'),
        }}
      >
        {/* {content.description?.replace(/(\r\n|\n|\r|\\n)/gm, '') || ''} */}
        <div ref={c} />
      </Grid>
      <Grid item container xs={5} className={classes.summaryContainer}>
        {content.summaryFields?.map((summary, index) => {
          return (
            <Grid
              item
              container
              justifyContent="space-between"
              className={classes.summaryItem}
              style={{
                borderTop:
                  index === content!.summaryFields!.length - 1
                    ? `2px solid ${get(content, 'styleSetting.borderColor')}`
                    : 'none',
                borderBottom:
                  index === content!.summaryFields!.length - 1
                    ? `2px solid ${get(content, 'styleSetting.borderColor')}`
                    : 'none',
                fontSize: get(templateConfig, 'details.settings.fontSize') + 'px',
                fontFamily: content.styleSetting.fontFamily
                  ? content.styleSetting.fontFamily
                  : get(templateConfig, 'details.settings.fontFamily'),
              }}
            >
              <Grid item>{summary.label}</Grid>
              <Grid item>
                {templateRecord && convertToCurrency(getSummaryFieldValue(summary.field))}
              </Grid>
            </Grid>
          );
        })}
      </Grid>
    </Grid>
  );
};

export const TemplateRichTextGenerator = (props: TemplateRichTextGeneratorProps) => {
  const { content, children } = props;
  return (
    <Grid item xs={12} justifyContent="center" alignItems="center">
      <FormControl
        style={{
          width: '100%',
          marginTop: '20px',
          marginBottom: '20px',
        }}
      >
        <Typography variant="body2" component="p">
          {content.content}
        </Typography>
      </FormControl>
    </Grid>
  );
};

export const TemplateTextGenerator = (props: TemplateTextGeneratorProps) => {
  const { content, children, activateComponent, templateConfig } = props;
  //@ts-ignore
  const classes = useStyles({ templateConfig });

  const c = useRef(null);

  useEffect(() => {
    if (c.current) {
      //@ts-ignore
      c.current.innerHTML = content.content?.replace(/(\r\n|\n|\r|\\n)/gm, '<br/>') || '';
    }
  }, [content.content]);
  return (
    <Grid
      item
      xs={12}
      justifyContent="center"
      alignItems="center"
      id={content.id}
      className={activateComponent?.id === content.id ? classes.activatePreviewComponent : ''}
      style={{
        display:
          content.deleted || (content?.visible !== null && content?.visible === false)
            ? 'none'
            : 'flex',
        alignItems: 'center',
        height: content.styleSetting?.height || 'auto',
        alignSelf: content.alignSelf || 'auto',
        flex: content.flex || 'auto',
        width: content.width || 'auto',
        padding: content.padding || 'auto',
      }}
    >
      <FormControl
        style={{
          width: '100%',
        }}
      >
        <Typography
          component="p"
          align={content.styleSetting?.textAlign || 'center'}
          style={{
            fontSize: content.styleSetting?.fontSize
              ? content.styleSetting.fontSize + 'px'
              : get(templateConfig, 'details.settings.fontSize') + 'px',
            fontFamily: content.styleSetting?.fontFamily
              ? content.styleSetting.fontFamily
              : get(templateConfig, 'details.settings.fontFamily'),
            fontWeight: content.styleSetting?.fontWeight || '400',
            color: content.styleSetting?.color
              ? content.styleSetting.color
              : get(templateConfig, 'details.settings.themeColor'),
            backgroundColor: content.styleSetting?.backgroundColor
              ? content.styleSetting.backgroundColor
              : get(templateConfig, 'details.settings.themeColor'),
            paddingLeft: content.styleSetting?.paddingLeft ? content.styleSetting.paddingLeft : '0',
            paddingRight: content.styleSetting?.paddingRight
              ? content.styleSetting.paddingRight
              : '0',
          }}
          // dangerouslySetInnerHTML={{
          //   __html: sanitizer(content.content.replace(/(\r\n|\n|\r|\\n)/gm, '<br/>')),
          // }}
          // ref={content}
        >
          <div ref={c} />
          {/* {content.content.replace(/(\r\n|\n|\r|\\n)/gm, '')} */}
        </Typography>
      </FormControl>
    </Grid>
  );
};

export const TemplateFieldGenerator = (props: TemplateFieldGeneratorProps) => {
  const { content, children, templateRecord, templateConfig, activateComponent } = props;
  const tenant = templateRecord?.company;
  //@ts-ignore
  const classes = useStyles({ templateConfig, content });
  let detail = Object.assign({}, templateRecord);
  if (content.field && content.field.length > 0) {
    content.field?.forEach((f) => (detail = get(detail, f)));
  } else {
    detail = null;
  }

  const { getUserLocale } = useUserLocale();

  const getLocale = () => {
    return getUserLocale().replace('_', '-');
  };

  const convertToCurrency = (value: number, currencyIsoCode?: string) => {
    const currencyCode = templateRecord.currency || tenant?.currency || 'USD';
    console.log('convertToCurrency', currencyCode);
    return Number(value).toLocaleString(getLocale(), {
      style: 'currency',
      currency: currencyCode,
      maximumFractionDigits: 2,
    });
  };

  const getSectionBackgroundColor = (cmpBackgroundColor: any, cmpOpacity: number) => {
    const themeColor = get(templateConfig, 'details.settings.themeColor');
    let backgroundColor;
    backgroundColor = !cmpBackgroundColor
      ? getLightColor(themeColor, cmpOpacity)
      : cmpBackgroundColor;
    return backgroundColor || 'white';
  };

  const getLightColor = (color: any, level: any) => {
    const r = /^\#?[0-9A-F]{6}$/;
    const rgbc = hexToRgb(color);
    //@ts-ignore
    for (let i = 0; i < 3; i++) rgbc[i] = Math.floor((255 - rgbc[i]) * level + rgbc[i]);
    //@ts-ignore
    return rgbToHex(rgbc[0], rgbc[1], rgbc[2]);
  };

  const hexToRgb = (str: any) => {
    const r = /^\#?[0-9A-Fa-f]{6}$/;
    //test方法检查在字符串中是否存在一个模式，如果存在则返回true，否则返回false
    if (!r.test(str)) return window.alert('输入错误的hex');
    //replace替换查找的到的字符串
    str = str.replace('#', '');
    //match得到查询数组
    const hxs = str.match(/../g);
    //alert('bf:'+hxs)
    for (let i = 0; i < 3; i++) hxs[i] = parseInt(hxs[i], 16);
    //alert(parseInt(80, 16))
    //console.log(hxs);
    return hxs;
  };

  const rgbToHex = (a: any, b: any, c: any) => {
    const r = /^\d{1,3}$/;
    const hexs = [a.toString(16), b.toString(16), c.toString(16)];
    for (let i = 0; i < 3; i++) if (hexs[i].length == 1) hexs[i] = '0' + hexs[i];
    return '#' + hexs.join('');
  };

  const contentDetailGenerator = (content: any, detail: any) => {
    if (isNull(detail)) {
      return '';
    }
    if (typeof detail === 'object') {
      return JSON.stringify(detail);
    }
    if (content.currency) {
      return convertToCurrency(detail);
    }
    if (content.date) {
      const fieldLanguageConfig = TemplateLanguageCodes.find(
        (t) => t.language_code === (templateConfig.locale || tenant?.language),
      );
      return dayjs
        .utc(detail)
        .format(
          fieldLanguageConfig?.language_date_format
            ? fieldLanguageConfig?.language_date_format
            : 'MM/DD/YYYY',
        );
    }
    if (content.dateTime) {
      const fieldLanguageConfig = TemplateLanguageCodes.find(
        (t) => t.language_code === (templateConfig.locale || tenant?.language),
      );
      return dayjs
        .utc(detail)
        .format(
          fieldLanguageConfig?.language_datetime_format
            ? fieldLanguageConfig?.language_datetime_format
            : 'MM/DD/YYYY hh:mm:ss a',
        );
    }
    if (typeof detail === 'number') {
      return Number.isInteger(detail) ? detail : detail.toFixed(2);
    }
    return detail;
  };

  return (
    <Grid
      item
      xs={12}
      style={{
        justifyContent: content.alignment === 'up-down' ? 'space-between' : '',
        display: content.deleted ? 'none' : '',
        ...content.styleSetting,
        color:
          content.styleSetting?.color ||
          get(templateConfig, 'details.settings.themeColor') ||
          'black',
        flexDirection: content.styleSetting?.flexDirection || 'column',
        padding: content.styleSetting?.fieldItemPadding || '13px',
        textAlign: content.styleSetting?.textAlign || 'center',
        background:
          content.styleSetting?.fieldItemBackgroundColor &&
          getSectionBackgroundColor('', content.styleSetting?.fieldItemBackgroundLevel || 0),
      }}
      id={content.id}
      className={
        activateComponent?.id === content.id
          ? [classes.fieldItem, classes.activatePreviewComponent].join(' ')
          : classes.fieldItem
      }
    >
      {content.showLabel && (
        <div
          className={classes.fieldLabel}
          style={{
            fontSize: content.styleSetting.labelFontSize
              ? content.styleSetting.labelFontSize + 'px'
              : content.styleSetting.fontSize
                ? content.styleSetting.fontSize + 'px'
                : get(templateConfig, 'details.settings.fontSize') + 'px',
            fontFamily: content.styleSetting.fontFamily
              ? content.styleSetting.fontFamily
              : get(templateConfig, 'details.settings.fontFamily'),
            marginBottom: content.styleSetting?.marginBottom || '10px',
            marginRight: content.styleSetting?.labelTextMarginRight || '0',
            width: content.styleSetting?.labelWidth || 'auto',
            paddingRight: content.styleSetting?.labelTextPaddingRight || 'auto',
            paddingLeft: content.styleSetting?.labelTextPaddingLeft || 'auto',
            paddingTop: content.styleSetting?.labelTextPaddingTop || 'auto',
            paddingBottom: content.styleSetting?.labelTextPaddingBottom || 'auto',
            background:
              content.styleSetting?.labelBackground &&
              getSectionBackgroundColor('', content.styleSetting?.labelBackgroundLevel || 0),
            color:
              content.styleSetting?.labelColor ||
              (content.styleSetting.color
                ? content.styleSetting.color
                : get(templateConfig, 'details.settings.themeColor')),
            minWidth: content.styleSetting?.fieldLabelMinWidth || '30%',
          }}
          id="fieldLabel"
        >
          {content.label}
        </div>
      )}
      {!content.hideValue && (
        <div
          className={classes.fieldContent}
          id="fieldContent"
          style={{
            fontSize: content.styleSetting.fontSize
              ? content.styleSetting.fontSize + 'px'
              : get(templateConfig, 'details.settings.fontSize') + 'px',
            fontFamily: content.styleSetting.fontFamily
              ? content.styleSetting.fontFamily
              : get(templateConfig, 'details.settings.fontFamily'),
            width: content.styleSetting?.contentWidth || 'auto',
            // textAlign: content.styleSetting?.contentTextAlign || '',
            paddingRight: content.styleSetting?.contentTextPaddingRight || 'auto',
            paddingLeft: content.styleSetting?.contentTextPaddingLeft || 'auto',
            paddingTop: content.styleSetting?.contentTextPaddingTop || 'auto',
            paddingBottom: content.styleSetting?.contentTextPaddingBottom || 'auto',
            background:
              content.styleSetting?.contentBackground &&
              getSectionBackgroundColor('', content.styleSetting?.contentBackgroundLevel || 0),
            color: content.styleSetting.color
              ? content.styleSetting.color
              : get(templateConfig, 'details.settings.themeColor'),
          }}
        >
          {contentDetailGenerator(content, detail)}
        </div>
      )}
    </Grid>
  );
};

export const TemplateImageGenerator = (props: TemplateFieldGeneratorProps) => {
  const { content, children, templateConfig, activateComponent } = props;
  //@ts-ignore
  const classes = useStyles({ templateConfig, content });
  const imageUrl = content.logo ? templateConfig.details.settings.logo : content.url;
  const getImagePxFromSize = (size: string) => {
    switch (size) {
      case 'SmallSize':
        return '80px';
      case 'MiddleSize':
        return '100px';
      case 'LargeSize':
        return '120px';
      default:
        return '100px';
    }
  };
  return (
    <Grid
      item
      container
      xs={12}
      style={{
        flex: 1,
        display: content.deleted ? 'none' : '',
        justifyContent: 'center',
        alignItems: 'center',
        ...content.styleSetting,
      }}
      id={content.id}
      className={
        activateComponent?.id === content.id
          ? [classes.imageItem, classes.activatePreviewComponent].join(' ')
          : classes.imageItem
      }
    >
      {
        <img
          src={content.logo ? templateConfig.details.settings.logo || Logo : content.url}
          alt=""
          className={
            content.size !== 'CustomizeSize' ? classes[`${content.size || 'CustomizeSize'}Img`] : ''
          }
          style={{
            width:
              content.size !== 'CustomizeSize'
                ? getImagePxFromSize(content?.size || '')
                : content.imagePx + 'px',
          }}
        />
      }
    </Grid>
  );
};

export const TemplatePageBreakGenerator = () => {
  const classes = useStyles();
  return (
    <Paper className={classes.PaperContainer}>
      <Grid item container xs={12} spacing={3}>
        123
      </Grid>
    </Paper>
  );
};

export const PreviewGenerator = (
  templateConfig: TemplateSchema,
  classes: ClassNameMap<string>,
  templateRecord: any,
  templateOrderProduct: any,
  activateComponent: any,
  t?: Tenant,
  isDownload?: boolean,
  insideEditor?: boolean,
) => {
  const [pageBreakIndex, setPageBreakIndex] = useState<number[]>([]);
  const tenantPropsContext = useContext(TenantPropsContext);
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [watermark, setWaterMark] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [useTranslate, setUseTranslate] = useState(false);

  let originalChildren: Node[] = [];

  const registerLanguageConfiguration = (t: Tenant) => {
    numbro.setDefaults({
      thousandSeparated: true,
    });
    numbro.registerLanguage(numbroLanguages[t?.language.replace('_', '-')], true);
  };

  const originalTexts = new Map();

  // Multiple free translation services
  const translationServices = {
    // MyMemory - Free tier, 1000 chars/day without registration
    async mymemory(text: any, targetLang: any) {
      const response = await fetch(
        // `https://translated-mymemory---translation-memory.p.rapidapi.com/get?langpair=en%7C${targetLang}&q=${encodeURIComponent(text)}`,
        `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=en%7C${targetLang}`,
        // {
        //   headers: {
        //     'x-rapidapi-host': 'translated-mymemory---translation-memory.p.rapidapi.com',
        //     'x-rapidapi-key': '**************************************************',
        //   },
        // },
      );
      const data = await response.json();

      if (data.responseStatus === 200 || data.responseStatus === '200') {
        return data.responseData.translatedText;
      }
      throw new Error('MyMemory translation failed');
    },

    // LibreTranslate - Open source, multiple public instances
    async libretranslate(text: any, targetLang: any) {
      const instances = [
        'https://libretranslate.de/translate',
        'https://translate.argosopentech.com/translate',
        'https://libretranslate.com/translate',
      ];

      for (const url of instances) {
        try {
          const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              q: text,
              source: 'en',
              target: targetLang,
              format: 'text',
            }),
          });

          if (response.ok) {
            const data = await response.json();
            return data.translatedText;
          }
        } catch (error) {
          console.warn(`LibreTranslate instance ${url} failed:`, error);
          continue;
        }
      }
      throw new Error('All LibreTranslate instances failed');
    },

    // Lingva - Google Translate proxy (unofficial but reliable)
    async lingva(text: any, targetLang: any) {
      const instances = ['https://lingva.ml/api/v1/en', 'https://translate.igonato.eu/api/v1/en'];

      for (const baseUrl of instances) {
        try {
          const response = await fetch(`${baseUrl}/${targetLang}/${encodeURIComponent(text)}`);
          if (response.ok) {
            const data = await response.json();
            return data.translation;
          }
        } catch (error) {
          console.warn(`Lingva instance failed:`, error);
          continue;
        }
      }
      throw new Error('All Lingva instances failed');
    },
  };

  // Smart batch translation with fallbacks
  async function translateBatch(texts: any, targetLang: any, serviceName: any) {
    //@ts-ignore
    const service: any = translationServices[serviceName];

    const results: any = [];
    const batchSize = 10; // Process in smaller batches

    for (let i = 0; i < texts?.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      const batchNum = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(texts?.length / batchSize);

      // Process batch in parallel
      const batchPromises = batch.map(async (text: any, index: any) => {
        console.log('batchPromises', text);
        const translated = await service(text, targetLang);
        return Promise.resolve({ index: i + index, original: text, translated });
      });

      const batchResults = await Promise.allSettled(batchPromises);

      batchResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        }
      });

      // Rate limiting delay
      if (i + batchSize < texts?.length) {
        await new Promise((resolve) => setTimeout(resolve, 300));
      }
    }

    return results;
  }

  // Main translation function with service fallback
  async function publicTranslate(targetLang: any) {
    //@ts-ignore
    const serviceName = 'mymemory';

    // Find all text elements (more comprehensive)
    const allElements = Array.from(
      document.getElementById('previewContainer')!.querySelectorAll('*'),
    );
    const textElements = allElements.filter((el) => {
      const text = el.textContent?.trim();
      return (
        text &&
        text.length > 0 &&
        text.length < 300 &&
        el.children.length === 0 &&
        !['SCRIPT', 'STYLE', 'svg', 'path', 'INPUT', 'SELECT', 'BUTTON'].includes(
          el.tagName.toUpperCase(),
        ) &&
        text !== '​'
      );
    });

    console.log(`Found ${textElements.length} text elements`);

    // Prepare texts for translation
    const textsToTranslate: any = [];
    const elementMap = new Map();

    textElements.forEach((element) => {
      //@ts-ignore
      const text = element.textContent.trim();
      if (!originalTexts.has(element) && !textsToTranslate.includes(text)) {
        originalTexts.set(element, text);
        textsToTranslate.push(text);
        const elementArray = elementMap.get(text) || [];
        elementArray.push(element);
        elementMap.set(text, elementArray);
      } else if (!originalTexts.has(element) && textsToTranslate.includes(text)) {
        console.log('textElements', text);
        originalTexts.set(element, text);
        const elementArray = elementMap.get(text) || [];
        elementArray.push(element);
        elementMap.set(text, elementArray);
      }
    });

    console.log(`Translating ${textsToTranslate.length} unique texts using ${serviceName}`);

    try {
      // Try primary service
      //@ts-ignore
      const results = await translateBatch(textsToTranslate, targetLang, serviceName);

      // If many failed, try fallback service
      const failedCount = results.filter((r: any) => r.translated === r.original).length;
      if (failedCount > results.length * 0.5) {
        console.log(`${failedCount} translations failed, trying fallback service...`);
        const fallbackService = 'libretranslate';

        const failedTexts = results
          .filter((r: any) => r.translated === r.original)
          .map((r: any) => r.original);
        const fallbackResults = await translateBatch(failedTexts, targetLang, fallbackService);

        // Merge results
        fallbackResults.forEach((result: any) => {
          const originalIndex = results.findIndex((r: any) => r.original === result.original);
          if (originalIndex !== -1) {
            results[originalIndex] = result;
          }
        });
      }

      // Apply translations
      let applied = 0;
      results.forEach((result: any) => {
        const elementArray = elementMap.get(result.original);
        if (elementArray && elementArray.length > 0 && result.translated !== result.original) {
          elementArray.forEach((element: any) => {
            element.textContent = result.translated;
          });
          applied++;
        }
      });
      const successRate = ((applied / textsToTranslate.length) * 100).toFixed(1);
    } catch (error) {
      console.error('Translation failed:', error);
    }
  }

  const setup = async () => {
    let compareTenant;
    if (tenantPropsContext) {
      const detailProps = await tenantPropsContext.getTenantDetailProps();
      const t = await detailProps.fetchTenant();
      console.log('fetchTenant', t);
      setTenant(t);
      if (t) {
        compareTenant = t;
        registerLanguageConfiguration(t);
        numbro.setLanguage(t?.language.replace('_', '-'));
      }
    } else if (t) {
      compareTenant = t;
      setTenant(t);
      registerLanguageConfiguration(t);
      numbro.setLanguage(t?.language.replace('_', '-'));
    } else {
      //@ts-ignore
      setTenant({ language: 'en_US', locale: 'en_US', currency: 'USD' });
      numbro.setLanguage('en-US');
    }

    if (templateConfig.ifTranslate && !insideEditor && useTranslate) {
      setIsLoading(true);
      const lang = window.navigator.language;
      if (!lang) {
        alert('Please select a target language!');
        setIsLoading(false);
        return;
      }
      publicTranslate(lang.substr(0, 2)).then((res) => {
        setIsLoading(false);
      });
    } else {
      setIsLoading(false);
    }
  };

  const updatePreivewPage = () => {
    const pArray = [];
    templateConfig.details.data.forEach((c: any, index: number) => {
      if (c.type === 'pageBreak' && !c.deleted) {
        pArray.push(index);
      }
    });
    if (pArray.length > 0) {
      pArray.push(templateConfig.details.data.length);
    }
    setPageBreakIndex(pArray);
  };

  useEffect(() => {
    updatePreivewPage();
    //@ts-ignore
    if (templateConfig.ifTranslate && !insideEditor && useTranslate) {
      setIsLoading(true);
      const timeoutId = setTimeout(() => {
        const lang = window.navigator.language;
        if (!lang) {
          alert('Please select a target language!');
          setIsLoading(false);
          return;
        }
        publicTranslate(lang.substr(0, 2)).then((res) => {
          setIsLoading(false);
        });
      }, 100);
    } else {
      setIsLoading(false);
    }
  }, [templateConfig]);

  const beforePrintCallback = () => {
    const element = document.getElementById('previewContainer');
    originalChildren = [...document.body.children];
    if (element) {
      const body = document.body;
      // Clone the element to be printed
      const elementClone = element.cloneNode(true) as HTMLElement;
      // Remove all current children from the body
      while (body.firstChild) {
        body.firstChild.remove();
      }
      // Add the cloned element to be printed
      body.append(elementClone);
    }
  };

  const afterPrintCallback = () => {
    // Revert the state back
    while (document.body.firstChild) {
      (document.body.firstChild as HTMLElement).remove();
    }
    for (const child of originalChildren) document.body.append(child);
  };

  useEffect(() => {
    setup();
    window.addEventListener('beforeprint', beforePrintCallback);
    window.addEventListener('afterprint', afterPrintCallback);
    return () => {
      window.removeEventListener('beforeprint', beforePrintCallback);
      window.removeEventListener('afterprint', afterPrintCallback);
    };
  }, []);

  if (!tenant) {
    return null;
  }

  return (
    <>
      {isLoading ? <Loading /> : <div />}
      <Grid
        id="previewContainer"
        className={classes.previewContainerGrid}
        style={{
          opacity: isLoading ? 0 : 1,
        }}
      >
        {pageBreakIndex.length > 0 ? (
          pageBreakIndex.map((i, index) => {
            const splitConfigToPageBreak = {
              ...templateConfig,
              details: {
                settings: templateConfig.details.settings,
                data: templateConfig.details.data.slice(
                  index === 0 ? 0 : pageBreakIndex[index - 1] + 1,
                  i,
                ),
              },
            };
            return (
              <>
                <Paper
                  key={index}
                  className={classes.previewPaper}
                  id="previewPaper"
                  role="paper"
                  style={{
                    borderTop: `2px solid ${get(templateConfig, 'details.settings.themeColor')}`,
                    borderRadius: '0',
                    width: '794px',
                    overflow: 'hidden',
                    padding: `
                ${
                  get(templateConfig, 'details.settings.pageMargins').top === 'zeropx'
                    ? '0'
                    : get(templateConfig, 'details.settings.pageMargins').top
                } ${
                  get(templateConfig, 'details.settings.pageMargins').right === 'zeropx'
                    ? '0'
                    : get(templateConfig, 'details.settings.pageMargins').right
                } ${
                  get(templateConfig, 'details.settings.pageMargins').bottom === 'zeropx'
                    ? '0'
                    : get(templateConfig, 'details.settings.pageMargins').bottom
                } ${
                  get(templateConfig, 'details.settings.pageMargins').left === 'zeropx'
                    ? '0'
                    : get(templateConfig, 'details.settings.pageMargins').left
                }`,
                  }}
                >
                  {splitConfigToPageBreak.details.data.map((d, index) => {
                    const { containers, type } = d;
                    return (
                      <>
                        {d?.visible !== null && d?.visible === false ? (
                          <div />
                        ) : (
                          <TemplatePaperGenerator
                            content={d}
                            templateConfig={splitConfigToPageBreak}
                            activateComponent={activateComponent}
                          >
                            {type === 'relatedList' && (
                              <TemplateRelatedListGenerator
                                content={d}
                                templateOrderProduct={templateOrderProduct}
                                templateRecord={templateRecord}
                                activateComponent={activateComponent}
                                templateConfig={splitConfigToPageBreak}
                                tenant={tenant}
                              />
                            )}
                            {type === 'text' && (
                              <TemplateTextGenerator
                                content={d}
                                activateComponent={activateComponent}
                                templateConfig={splitConfigToPageBreak}
                              />
                            )}
                            {containers?.map((container) => {
                              const { columns } = container;
                              return (
                                <TemplateContainerGenerator
                                  key={container.id}
                                  container={container}
                                  activateComponent={activateComponent}
                                  templateConfig={splitConfigToPageBreak}
                                >
                                  {columns?.map((column) => {
                                    const { contents } = column;
                                    return (
                                      <TemplateColumnGenerator
                                        key={column.id}
                                        column={column}
                                        activateComponent={activateComponent}
                                        templateConfig={splitConfigToPageBreak}
                                      >
                                        {contents?.map((content) => {
                                          return (
                                            <TemplateContentGenerator
                                              key={content.id}
                                              content={content}
                                              templateOrderProduct={templateOrderProduct}
                                              templateRecord={templateRecord}
                                              templateConfig={splitConfigToPageBreak}
                                              activateComponent={activateComponent}
                                              tenant={tenant}
                                            />
                                          );
                                        })}
                                      </TemplateColumnGenerator>
                                    );
                                  })}
                                </TemplateContainerGenerator>
                              );
                            })}
                          </TemplatePaperGenerator>
                        )}
                      </>
                    );
                  })}
                </Paper>
              </>
            );
          })
        ) : (
          <Paper
            className={classes.previewPaper}
            role="paper"
            style={{
              borderRadius: '0',
            }}
          >
            {templateConfig.details.data.map((d, index) => {
              const { containers, type } = d;
              return (
                <>
                  {
                    <TemplatePaperGenerator
                      content={d}
                      templateConfig={templateConfig}
                      activateComponent={activateComponent}
                    >
                      {type === 'relatedList' && (
                        <TemplateRelatedListGenerator
                          content={d}
                          templateOrderProduct={templateOrderProduct}
                          templateRecord={templateRecord}
                          activateComponent={activateComponent}
                          templateConfig={templateConfig}
                        />
                      )}
                      {type === 'text' && (
                        <TemplateTextGenerator
                          content={d}
                          activateComponent={activateComponent}
                          templateConfig={templateConfig}
                        />
                      )}
                      {containers?.map((container) => {
                        const { columns } = container;
                        return (
                          <TemplateContainerGenerator
                            key={container.id}
                            container={container}
                            activateComponent={activateComponent}
                            templateConfig={templateConfig}
                          >
                            {columns?.map((column) => {
                              const { contents } = column;
                              return (
                                <TemplateColumnGenerator
                                  key={column.id}
                                  column={column}
                                  activateComponent={activateComponent}
                                  templateConfig={templateConfig}
                                >
                                  {contents?.map((content) => {
                                    return (
                                      <TemplateContentGenerator
                                        key={content.id}
                                        content={content}
                                        templateOrderProduct={templateOrderProduct}
                                        templateRecord={templateRecord}
                                        templateConfig={templateConfig}
                                        activateComponent={activateComponent}
                                      />
                                    );
                                  })}
                                </TemplateColumnGenerator>
                              );
                            })}
                          </TemplateContainerGenerator>
                        );
                      })}
                    </TemplatePaperGenerator>
                  }
                </>
              );
            })}
          </Paper>
        )}
      </Grid>
    </>
  );
};

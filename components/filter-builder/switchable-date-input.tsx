import React, { useEffect, useState } from 'react';

import FormControl from '@material-ui/core/FormControl';
import dayjs from 'dayjs';

import DateInput from '../date-input';
import DateTimeInput from '../date-time-input';
import { RelavtiveDatePicker } from './relative-date-picker';

export const SwitchableDateInput = ({
  classes,
  conditionField,
  disabled,
  value,
  onDateInputChange,
  type,
}: any) => {
  const [mode, setMode] = useState('Absolute');
  const [open, setOpen] = React.useState(false);
  const [relativeValue, setRelativeValue] = useState(undefined);
  const [valueToUse, setValueToUse] = useState<any>(null);
  const handleClick = () => {
    if (disabled) {
      return;
    }
    if (mode === 'Absolute') {
      setOpen(true);
    } else {
      setMode('Absolute');
      const now = dayjs();
      setValueToUse(now);
      onDateInputChange(now);
    }
  };

  useEffect(() => {
    setValueToUse(value);
  }, [value]);

  useEffect(() => {
    if (value && value?.includes?.('$')) {
      setMode('Relative');
      const arr = value.split('.');
      if (arr.length >= 2) {
        const operationText = arr[2];
        const isMinus = operationText.includes('-');
        const numberPattern = /\d+/g;
        const matchCollection = operationText.match(numberPattern);
        const datePickerType = isMinus ? 'minus' : 'plus';
        const changeAmount = Number(matchCollection[0]);
        if (operationText.includes('Days')) {
          setRelativeValue({
            //@ts-ignore
            datePickerRadio: datePickerType,
            amount: changeAmount,
            option: 'day',
          });
        } else if (operationText.includes('Months')) {
          setRelativeValue({
            //@ts-ignore
            datePickerRadio: datePickerType,
            amount: changeAmount,
            option: 'month',
          });
        } else if (operationText.includes('Quarters')) {
          setRelativeValue({
            //@ts-ignore
            datePickerRadio: datePickerType,
            amount: changeAmount,
            option: 'quarter',
          });
        } else if (operationText.includes('Years')) {
          setRelativeValue({
            //@ts-ignore
            datePickerRadio: datePickerType,
            amount: changeAmount,
            option: 'year',
          });
        }
      } else {
        setRelativeValue({
          //@ts-ignore
          datePickerRadio: 'plus',
          amount: 0,
          option: 'day',
        });
      }
    }
  }, [value]);
  const isDatePicker = type.toLowerCase() === 'date';

  const getQueryString = (switchableValue: any) => {
    const isNegtiveValue = switchableValue.datePickerRadio !== 'plus';
    let endFunction = isNegtiveValue ? 'startOfDay()' : 'endOfDay()';
    let resultValue = 'addDays';
    if (switchableValue.option === 'month') {
      resultValue = 'addMonths';
      endFunction = isNegtiveValue ? 'startOfMonth()' : 'endOfMonth()';
    } else if (switchableValue.option === 'year') {
      resultValue = 'addYears';
      endFunction = isNegtiveValue ? 'startOfYear()' : 'endOfYear()';
    } else if (switchableValue.option === 'quarter') {
      resultValue = 'addQuarters';
      endFunction = isNegtiveValue ? 'startOfQuarter()' : 'endOfQuarter()';
    }
    const time = isDatePicker ? 'today' : 'now';
    const startText = isDatePicker ? 'Date' : 'DateTime';
    if ('Date' == startText) {
      if (switchableValue.amount == 0 && 'addDays' != resultValue) {
        return `$${startText}.${time}().${resultValue}(${isNegtiveValue ? '-' : ''}${
          switchableValue.amount
        }).${endFunction}`;
      } else {
        return `$${startText}.${time}().${resultValue}(${isNegtiveValue ? '-' : ''}${
          switchableValue.amount
        })`;
      }
    } else {
      if (switchableValue.amount == 0) {
        return `$${startText}.${time}().${resultValue}(${isNegtiveValue ? '-' : ''}${
          switchableValue.amount
        }).${endFunction}`;
      } else {
        endFunction = isNegtiveValue ? 'startOfDay()' : 'endOfDay()';
        return `$${startText}.${time}().${resultValue}(${isNegtiveValue ? '-' : ''}${
          switchableValue.amount
        }).${endFunction}`;
      }
    }
  };

  return (
    <FormControl className={classes.formControl} style={{ flex: 1 }}>
      {mode === 'Absolute' ? (
        isDatePicker ? (
          <DateInput
            disabled={disabled}
            className={classes.dateInput}
            // @ts-ignore
            field={conditionField.field}
            required={false}
            value={valueToUse}
            handleInputChange={onDateInputChange}
            name="date-input"
          />
        ) : (
          <DateTimeInput
            className={classes.dateInput}
            // @ts-ignore
            field={conditionField.field}
            disabled={disabled}
            required={false}
            value={valueToUse}
            name="date-time-input"
            handleInputChange={onDateInputChange}
            dateFormat={'MM/dd/yyyy hh:mm a'}
          />
        )
      ) : (
        <div
          className={classes.newConditionLink}
          style={{ height: 35, fontSize: '0.875rem' }}
          onClick={() => {
            setOpen(true);
          }}
          //@ts-ignore
        >{`${isDatePicker ? 'Today' : 'Now'} ${relativeValue.datePickerRadio} ${
          //@ts-ignore
          relativeValue.amount
          //@ts-ignore
        } ${relativeValue.option}${
          //@ts-ignore
          relativeValue.amount > 1 ? 's' : ''
        }`}</div>
      )}

      <span
        className={classes.newConditionLink}
        style={{
          position: 'absolute',
          bottom: '-33px',
        }}
        onClick={handleClick}
      >
        {mode === 'Absolute' ? 'Use Relative Date' : 'Use Absolute Date'}
      </span>
      <RelavtiveDatePicker
        open={open}
        onClose={() => {
          setOpen(false);
        }}
        type={type}
        defaultValues={relativeValue}
        onSubmit={(values) => {
          const queryStr = getQueryString(values);
          onDateInputChange(queryStr);
          setRelativeValue(values);
          setMode('Relative');
          setOpen(false);
        }}
      />
    </FormControl>
  );
};

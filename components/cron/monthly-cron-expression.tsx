import React from 'react';
import { CronMonthlyFormProps } from './interface';
import { Grid, MenuItem, RadioGroup, Select } from '@material-ui/core';
import FormControl from '@material-ui/core/FormControl';
import { makeStyles } from '@material-ui/core/styles';
import SelectInputBase from '../select-input-base';
import { HourConst, MinuteConst, MonthConst, NumberConst, WeekConst } from '../common/cron-const';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Radio from '@material-ui/core/Radio';
import InputBaseComponent from '../input-base-component';

const useStyles = makeStyles({
  titleLabel: {
    textTransform: 'uppercase',
    color: '#9c9c9c',
    fontWeight: 'bold',
    fontSize: '.80rem',
    alignItems: 'baseline',
  },
  colLabel: {
    textTransform: 'uppercase',
    color: '#9c9c9c',
    fontWeight: 'bold',
    fontSize: '.80rem',
    marginTop: '18px',
  },
  formControl: {
    flexDirection: 'row',
    width: '100%',
  },
  optionLabel: {
    '& > span': {
      color: '#6e6e6e',
      fontSize: '.9rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
  },
});

let numberMap = new Map([
  ['First', '1'],
  ['Second', '2'],
  ['Third', '3'],
  ['Fourth', '4'],
]);

let weeklyMap = new Map([
  ['Monday', 'MON'],
  ['Tuesday', 'TUE'],
  ['Wednesday', 'WED'],
  ['Thursday', 'THU'],
  ['Friday', 'FRI'],
  ['Saturday', 'SAT'],
  ['Sunday', 'SUN'],
]);

export const MonthDailyFrequency: React.FC<CronMonthlyFormProps> = (userProps) => {
  const props = { ...userProps };
  const { monthlyObject, setMonthlyObject, disabled } = props;
  return (
    <>
      <Grid container item>
        <Grid item style={{ paddingTop: '20px', paddingRight: '10px' }}>
          Day
        </Grid>
        <Grid item style={{ paddingTop: '6px', paddingRight: '10px', width: '90px' }}>
          <InputBaseComponent
            type="number"
            value={monthlyObject.monthDailyFrequencyDay}
            inputProps={{ min: '1', step: '1', max: '31' }}
            disabled={disabled}
            onChange={(event) => {
              let newValue = Number(event.target.value);
              if (newValue < 1) {
                newValue = 1;
              } else if (newValue > 31) {
                newValue = 31;
              }
              monthlyObject.monthDailyFrequencyDay = newValue.toString();
              setMonthlyObject({
                ...monthlyObject,
                monthDailyFrequencyDay: monthlyObject.monthDailyFrequencyDay,
              });
              if (
                monthlyObject.monthlyExpression &&
                monthlyObject.monthlyFrequencyType === 'monthDailyFrequency'
              ) {
                const copyCronExpression = monthlyObject.monthlyExpression;
                copyCronExpression[3] = ' ' + newValue;
                monthlyObject.monthlyExpression = copyCronExpression;
                setMonthlyObject({
                  ...monthlyObject,
                  monthlyExpression: monthlyObject.monthlyExpression,
                });
              }
            }}
          />
        </Grid>
        <Grid item style={{ paddingTop: '20px', paddingRight: '10px' }}>
          of every
        </Grid>
        <Grid item style={{ paddingTop: '14px', width: '70px' }}>
          <Select
            value={monthlyObject.monthDailyFrequencyMonth}
            displayEmpty
            name={'field'}
            placeholder={'Field'}
            onChange={(event) => {
              const value = event.target.value;
              if (typeof value === 'string') {
                monthlyObject.monthDailyFrequencyMonth = value;
              }
              setMonthlyObject({
                ...monthlyObject,
                monthDailyFrequencyMonth: monthlyObject.monthDailyFrequencyMonth,
              });
              if (
                monthlyObject.monthlyExpression &&
                monthlyObject.monthlyFrequencyType === 'monthDailyFrequency'
              ) {
                const copyCronExpression = monthlyObject.monthlyExpression;
                copyCronExpression[4] = ' 1/' + value;
                monthlyObject.monthlyExpression = copyCronExpression;
                setMonthlyObject({
                  ...monthlyObject,
                  monthlyExpression: monthlyObject.monthlyExpression,
                });
              }
            }}
            fullWidth={false}
            label={'Field'}
            disabled={disabled}
            input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
          >
            {MonthConst.map((field, index) => {
              return (
                <MenuItem key={index} value={field}>
                  {field}
                </MenuItem>
              );
            })}
          </Select>
        </Grid>
        <Grid item style={{ paddingTop: '20px', paddingLeft: '10px' }}>
          month(s)
        </Grid>
      </Grid>
    </>
  );
};

export const MonthWeeklyFrequency: React.FC<CronMonthlyFormProps> = (userProps) => {
  const props = { ...userProps };
  const { monthlyObject, setMonthlyObject, disabled } = props;

  return (
    <>
      <Grid container item style={{ width: '600px' }}>
        <Grid item style={{ marginTop: '10px', paddingTop: '10px', paddingRight: '10px' }}>
          The
        </Grid>
        <Grid item style={{ marginTop: '10px', paddingRight: '10px', width: '80px' }}>
          <Select
            value={monthlyObject.monthWeeklyFrequencyNumber}
            displayEmpty
            name={'field'}
            placeholder={'Field'}
            onChange={(event) => {
              const value = event.target.value;
              if (typeof value === 'string') {
                monthlyObject.monthWeeklyFrequencyNumber = value;
              }
              setMonthlyObject({
                ...monthlyObject,
                monthWeeklyFrequencyNumber: monthlyObject.monthWeeklyFrequencyNumber,
              });
              if (
                monthlyObject.monthlyExpression &&
                monthlyObject.monthlyFrequencyType === 'monthWeeklyFrequency'
              ) {
                const copyCronExpression = monthlyObject.monthlyExpression;
                if (
                  typeof monthlyObject.monthWeeklyFrequencyWeek === 'string' &&
                  typeof value === 'string'
                ) {
                  copyCronExpression[5] =
                    ' ' +
                    weeklyMap.get(monthlyObject.monthWeeklyFrequencyWeek) +
                    '#' +
                    numberMap.get(value);
                }
                monthlyObject.monthlyExpression = copyCronExpression;
                setMonthlyObject({
                  ...monthlyObject,
                  monthlyExpression: monthlyObject.monthlyExpression,
                });
              }
            }}
            fullWidth={false}
            label={'Field'}
            disabled={disabled}
            input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
          >
            {NumberConst.map((field, index) => {
              return (
                <MenuItem key={index} value={field}>
                  {field}
                </MenuItem>
              );
            })}
          </Select>
        </Grid>
        <Grid item style={{ marginTop: '10px', paddingLeft: '10px', marginLeft: '20px' }}>
          <Select
            value={monthlyObject.monthWeeklyFrequencyWeek}
            displayEmpty
            name={'field'}
            placeholder={'Field'}
            onChange={(event) => {
              const value = event.target.value;
              if (typeof value === 'string') {
                monthlyObject.monthWeeklyFrequencyWeek = value;
              }
              setMonthlyObject({
                ...monthlyObject,
                monthWeeklyFrequencyWeek: monthlyObject.monthWeeklyFrequencyWeek,
              });
              if (
                monthlyObject.monthlyExpression &&
                monthlyObject.monthlyFrequencyType === 'monthWeeklyFrequency'
              ) {
                const copyCronExpression = monthlyObject.monthlyExpression;
                if (
                  typeof monthlyObject.monthWeeklyFrequencyNumber === 'string' &&
                  typeof value === 'string'
                ) {
                  copyCronExpression[5] =
                    ' ' +
                    weeklyMap.get(value) +
                    '#' +
                    numberMap.get(monthlyObject.monthWeeklyFrequencyNumber);
                }
                monthlyObject.monthlyExpression = copyCronExpression;
                setMonthlyObject({
                  ...monthlyObject,
                  monthlyExpression: monthlyObject.monthlyExpression,
                });
              }
            }}
            fullWidth={false}
            autoWidth
            label={'Field'}
            disabled={disabled}
            input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
          >
            {WeekConst.map((field, index) => {
              return (
                <MenuItem key={index} value={field}>
                  {field}
                </MenuItem>
              );
            })}
          </Select>
        </Grid>
        <Grid
          item
          style={{
            marginTop: '10px',
            paddingTop: '10px',
            paddingRight: '10px',
            paddingLeft: '10px',
          }}
        >
          of every
        </Grid>
        <Grid item style={{ marginTop: '10px', width: '70px' }}>
          <Select
            value={monthlyObject.monthWeeklyFrequencyMonth}
            displayEmpty
            name={'field'}
            placeholder={'Field'}
            onChange={(event) => {
              const value = event.target.value;
              if (typeof value === 'string') {
                monthlyObject.monthWeeklyFrequencyMonth = value;
              }
              setMonthlyObject({
                ...monthlyObject,
                monthWeeklyFrequencyMonth: monthlyObject.monthWeeklyFrequencyMonth,
              });
              if (
                monthlyObject.monthlyExpression &&
                monthlyObject.monthlyFrequencyType === 'monthWeeklyFrequency'
              ) {
                const copyCronExpression = monthlyObject.monthlyExpression;
                if (typeof value === 'string') {
                  copyCronExpression[4] = ' 1/' + value;
                }
                monthlyObject.monthlyExpression = copyCronExpression;
                setMonthlyObject({
                  ...monthlyObject,
                  monthlyExpression: monthlyObject.monthlyExpression,
                });
              }
            }}
            fullWidth={false}
            label={'Field'}
            disabled={disabled}
            input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
          >
            {MonthConst.map((field, index) => {
              return (
                <MenuItem key={index} value={field}>
                  {field}
                </MenuItem>
              );
            })}
          </Select>
        </Grid>
        <Grid item style={{ marginTop: '10px', paddingTop: '10px', paddingLeft: '10px' }}>
          month(s)
        </Grid>
      </Grid>
    </>
  );
};

export const MonthlyCronExpression: React.FC<CronMonthlyFormProps> = (userProps) => {
  const props = { ...userProps };
  const { monthlyObject, setMonthlyObject, disabled } = props;
  const styles = useStyles();

  return (
    <>
      <FormControl className={styles.formControl}>
        <Grid container item spacing={3} xs={12}>
          <Grid container item spacing={2} xs={12}>
            <Grid item lg={2} xs={12} className={styles.colLabel}>
              FREQUENCY
            </Grid>
            <Grid item lg={6} xs={12}>
              <RadioGroup
                aria-label="metadata-radio"
                row
                name={'FREQUENCY'}
                value={monthlyObject.monthlyFrequencyType}
                onChange={(event, value) => {
                  if (monthlyObject.monthlyExpression) {
                    if (value === 'monthDailyFrequency') {
                      const copyCronExpression = monthlyObject.monthlyExpression;
                      copyCronExpression[3] = ' ' + monthlyObject.monthDailyFrequencyDay;
                      copyCronExpression[4] = ' 1/' + monthlyObject.monthDailyFrequencyMonth;
                      copyCronExpression[5] = ' ?';
                      copyCronExpression[6] = ' *';
                      copyCronExpression[0] = copyCronExpression[0].replace(' ', '');
                      monthlyObject.monthlyExpression = copyCronExpression;
                      setMonthlyObject({
                        ...monthlyObject,
                        monthlyExpression: monthlyObject.monthlyExpression,
                      });
                    } else {
                      const copyCronExpression = monthlyObject.monthlyExpression;
                      copyCronExpression[3] = ' ?';
                      copyCronExpression[4] = ' 1/' + monthlyObject.monthWeeklyFrequencyMonth;
                      if (
                        typeof monthlyObject.monthWeeklyFrequencyWeek === 'string' &&
                        typeof monthlyObject.monthWeeklyFrequencyNumber === 'string'
                      ) {
                        copyCronExpression[5] =
                          ' ' +
                          weeklyMap.get(monthlyObject.monthWeeklyFrequencyWeek) +
                          '#' +
                          numberMap.get(monthlyObject.monthWeeklyFrequencyNumber);
                      }
                      copyCronExpression[6] = ' *';
                      copyCronExpression[0] = copyCronExpression[0].replace(' ', '');
                      monthlyObject.monthlyExpression = copyCronExpression;
                      setMonthlyObject({
                        ...monthlyObject,
                        monthlyExpression: monthlyObject.monthlyExpression,
                      });
                    }
                  }
                  monthlyObject.monthlyFrequencyType = value;
                  setMonthlyObject({
                    ...monthlyObject,
                    monthlyFrequencyType: monthlyObject.monthlyFrequencyType,
                  });
                }}
              >
                <FormControlLabel
                  value={'monthDailyFrequency'}
                  control={<Radio size="small" />}
                  label={
                    <MonthDailyFrequency
                      monthlyObject={monthlyObject}
                      setMonthlyObject={setMonthlyObject}
                      disabled={disabled}
                    />
                  }
                  disabled={disabled}
                  className={styles.optionLabel}
                />

                <FormControlLabel
                  value={'monthWeeklyFrequency'}
                  control={<Radio size="small" />}
                  label={
                    <MonthWeeklyFrequency
                      monthlyObject={monthlyObject}
                      setMonthlyObject={setMonthlyObject}
                      disabled={disabled}
                    />
                  }
                  disabled={disabled}
                  className={styles.optionLabel}
                />
              </RadioGroup>
            </Grid>
          </Grid>
          <Grid container item spacing={2} xs={12}>
            <Grid item lg={2} xs={12} className={styles.titleLabel} style={{ paddingTop: '8px' }}>
              RUN AT
            </Grid>
            <Grid container item spacing={3} xs={8}>
              <Grid item lg={2} xs={8} style={{ maxWidth: '6%' }}>
                <Select
                  value={
                    monthlyObject.monthlyRunAt === undefined ? '' : monthlyObject.monthlyRunAt.hour
                  }
                  displayEmpty
                  name={'field'}
                  placeholder={'Field'}
                  onChange={(event) => {
                    const value = event.target.value;
                    if (typeof value === 'string') {
                      monthlyObject.monthlyRunAt.hour = value;
                    }
                    const copyRunAt = { hour: value, minute: monthlyObject.monthlyRunAt.minute };
                    setMonthlyObject({
                      ...monthlyObject,
                      monthlyRunAt: copyRunAt,
                    });
                    if (monthlyObject.monthlyExpression) {
                      monthlyObject.monthlyExpression[2] = ' ' + value;
                      if (typeof monthlyObject.monthlyExpression[1] === 'string') {
                        if (monthlyObject.monthlyExpression[2].substring(1, 2) === '0') {
                          monthlyObject.monthlyExpression[2] =
                            monthlyObject.monthlyExpression[2].replace('0', '');
                        }
                      }
                      setMonthlyObject({
                        ...monthlyObject,
                        monthlyExpression: monthlyObject.monthlyExpression,
                      });
                    }
                  }}
                  fullWidth={false}
                  label={'Field'}
                  disabled={disabled}
                  input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
                >
                  {HourConst.map((field, index) => {
                    return (
                      <MenuItem key={index} value={field}>
                        {field}
                      </MenuItem>
                    );
                  })}
                </Select>
              </Grid>
              <Grid
                item
                lg={2}
                xs={12}
                style={{
                  marginTop: '8px',
                  maxWidth: '10px',
                  marginLeft: '5px',
                  marginRight: '50px',
                }}
              >
                <b style={{ marginLeft: '50px' }}>:</b>
              </Grid>
              <Grid item lg={4} xs={8} style={{ maxWidth: '10%' }}>
                <Select
                  value={
                    monthlyObject.monthlyRunAt === undefined
                      ? ''
                      : monthlyObject.monthlyRunAt.minute
                  }
                  displayEmpty
                  name={'field'}
                  placeholder={'Field'}
                  onChange={(event) => {
                    const value = event.target.value;
                    if (typeof value === 'string') {
                      monthlyObject.monthlyRunAt.minute = value;
                    }
                    const copyRunAt = { hour: monthlyObject.monthlyRunAt.hour, minute: value };
                    setMonthlyObject({
                      ...monthlyObject,
                      monthlyRunAt: copyRunAt,
                    });
                    if (monthlyObject.monthlyExpression) {
                      monthlyObject.monthlyExpression[1] = ' ' + value;
                      if (typeof monthlyObject.monthlyExpression[1] === 'string') {
                        if (monthlyObject.monthlyExpression[1].substring(1, 2) === '0') {
                          monthlyObject.monthlyExpression[1] =
                            monthlyObject.monthlyExpression[1].replace('0', '');
                        }
                      }
                      setMonthlyObject({
                        ...monthlyObject,
                        monthlyExpression: monthlyObject.monthlyExpression,
                      });
                    }
                  }}
                  fullWidth={false}
                  label={'Field'}
                  disabled={disabled}
                  input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
                >
                  {MinuteConst.map((field, index) => {
                    return (
                      <MenuItem key={index} value={field}>
                        {field}
                      </MenuItem>
                    );
                  })}
                </Select>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </FormControl>
    </>
  );
};

export default MonthlyCronExpression;

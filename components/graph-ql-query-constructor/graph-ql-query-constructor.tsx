import _ from 'lodash';

import type { Ruby<PERSON>ield, RubyObject } from '../metadata/interface';
import { buildExtraRelationFields } from '../ruby-list-view';
import type { Condition, OrderByField, Relation, RubyFilter } from './interface';

export const constructFields = (fields: RubyField[], extraRelationFields?: string) => {
  if (!fields) {
    console.error('Fields is empty: ', fields);
    throw new Error('Invalid object field metadata, fields is empty');
  }
  let query = '{ ';
  fields
    .map((field) => field.apiName)
    .forEach((field: string) => {
      query += `${field} `;
    });
  if (extraRelationFields) {
    query += ` ${extraRelationFields}`;
  }
  query += ' }';
  return query;
};

export const constructFieldsWithExtraRelationsInOrder = (fields: RubyField[]) => {
  if (!fields) {
    console.error('Fields is empty: ', fields);
    throw new Error('Invalid object field metadata, fields is empty');
  }
  let query = '{ ';
  fields
    .map((field) => {
      if (field?.lookupRelation?.relationName) {
        return buildExtraRelationFields([field]);
      }
      return field.apiName;
    })
    .forEach((field: string) => {
      query += `${field} `;
    });
  query += ' }';
  return query;
};

const constructNestedConditions = (conditions?: Condition[]) => {
  let query = '';
  let prevOperand = null;
  let nestedCondition = null;
  let nestedConditionOperand = null;
  if (!conditions) {
    return '';
  }
  for (let i = 0; i < conditions.length; i += 1) {
    const _condition = conditions[i];
    if (_condition.nestedConditions && _condition.nestedConditions!.length > 0) {
      nestedConditionOperand = _condition.nestedConditions![0].operand;
      nestedCondition = constructNestedConditions(_condition.nestedConditions);
    }
    let condition: string;
    if (_condition.operator === ' _is_null ') {
      condition = `{ ${_condition.apiName}: { _is_null : true } }`;
    } else if (_condition.operator === ' _is_not_null ') {
      condition = `{ ${_condition.apiName}: { _is_null : false } }`;
    } else {
      condition = `{ ${_condition.apiName}: { ${_condition.operator} : ${_condition.value} } }`;
    }
    if (
      _condition.type === 'bLookup' ||
      _condition.type === 'bMasterDetail' ||
      _condition.type === 'Reference'
    ) {
      // the field is the sub lookup field user chooses from the second picklist
      const field = _condition.nestedApiName || 'id';
      if (_condition.operator === ' _is_not_null ') {
        condition = `{ ${field}: { _is_null : false } }`;
      } else if (_condition.operator === ' _is_null ') {
        condition = `{ ${field}: { _is_null : true } }`;
      } else {
        condition = `{ ${field} : { ${_condition.operator} : ${_condition.value} } }`;
      }
      // { product : { name: { _like: "${searchResult}" } } }
      query += `{ ${_condition.relation} : ${condition} }`;
    } else if (_condition.relation) {
      condition = `{ name : { ${_condition.operator} : ${_condition.value} } }`;
      // { product : { name: { _like: "${searchResult}" } } }
      query += `{ ${_condition.relation} : ${condition} }`;
    } else {
      query += condition;
    }
    if (i + 1 < conditions.length) {
      if (conditions[i + 1].operand !== prevOperand) {
        query = `{${conditions[i + 1].operand}: [ ${query}`;
        if (i > 0) {
          query += ' ] }';
        }
      }
      prevOperand = conditions[i + 1].operand;
    }
    if (nestedConditionOperand) {
      query = `{ ${nestedConditionOperand}: [ ${query} ${nestedCondition} ] }`;
      nestedConditionOperand = null;
    }
  }
  if (prevOperand != null) {
    query += '] }';
  }
  return query;
};

const constructConditions = (conditions: Condition[]) => {
  if (!conditions) {
    return '';
  }
  let query = '';
  let prevOperand = null;
  let nestedCondition = null;
  let nestedConditionOperand = null;
  for (let i = 0; i < conditions.length; i += 1) {
    const _condition = conditions[i];
    if (_condition.nestedConditions && _condition.nestedConditions!.length > 0) {
      nestedConditionOperand = _condition.nestedConditions![0].operand;
      nestedCondition = constructNestedConditions(_condition.nestedConditions);
    }
    let condition: string;
    if (_condition.operator === ' _is_null ') {
      condition = `{ ${_condition.apiName}: { _is_null : true } }`;
    } else if (_condition.operator === ' _is_not_null ') {
      condition = `{ ${_condition.apiName}: { _is_null : false } }`;
    } else {
      condition = `{ ${_condition.apiName}: { ${_condition.operator} : ${_condition.value} } }`;
    }
    if (
      _condition.type === 'bLookup' ||
      _condition.type === 'lookup' ||
      _condition.type === 'bMasterDetail' ||
      _condition.type === 'Reference'
    ) {
      // the field is the sub lookup field user chooses from the second picklist
      const field = _condition.nestedApiName || 'id';
      if (_condition.operator === ' _is_not_null ') {
        condition = `{ ${field}: { _is_null : false } }`;
      } else if (_condition.operator === ' _is_null ') {
        condition = `{ ${field}: { _is_null : true } }`;
      } else {
        condition = `{ ${field} : { ${_condition.operator} : ${_condition.value} } }`;
      }
      // { product : { name: { _like: "${searchResult}" } } }
      query += `{ ${_condition.relation} : ${condition} }`;
    } else if (_condition.relation) {
      condition = `{ name : { ${_condition.operator} : ${_condition.value} } }`;
      // { product : { name: { _like: "${searchResult}" } } }
      query += `{ ${_condition.relation} : ${condition} }`;
    } else {
      query += condition;
    }
    if (i > 0) {
      if (conditions[i - 1].operand !== prevOperand) {
        query = `{${conditions[i - 1].operand}:[ ${query}`;
      }
      prevOperand = conditions[i - 1].operand;
      if (_condition && _condition.operand !== prevOperand && i < conditions.length - 1) {
        query += ' ] }';
      }
    }
    if (nestedConditionOperand) {
      query = `{ ${nestedConditionOperand}: [ ${query} ${nestedCondition} ] }`;
      nestedConditionOperand = null;
    }
  }
  if (prevOperand != null) {
    query += '] }';
  }
  return query;
};

const concatRelation = (relationOrderConditions: OrderByField, query: string) => {
  if (relationOrderConditions.referenceField) {
    return `${query} ${relationOrderConditions.columnName}: {${relationOrderConditions.referenceField} : ${relationOrderConditions.direction}} `;
  }
  return `${query} ${relationOrderConditions.columnName}: {name : ${relationOrderConditions.direction}} `;
};

const concatField = (orderByFields: OrderByField, query: string) => {
  return `${query} ${orderByFields.columnName}: ${orderByFields.direction} `;
};

const constructOrderByFields = (
  orderByFields: OrderByField[],
  relationOrderConditions?: OrderByField[],
) => {
  let query = '';
  if (orderByFields && orderByFields.length > 0) {
    query = ' orderBy: { ';

    let copyOrderByFields = orderByFields;

    const columnsArr: string[] = [];
    const relationArr: string[] = [];
    copyOrderByFields = copyOrderByFields.filter((orderByField) => {
      if (orderByField.isRelation) {
        relationArr.push(orderByField.columnName);
      }

      if (columnsArr.indexOf(orderByField.columnName) === -1) {
        columnsArr.push(orderByField.columnName);
        return true;
      }
      return false;
    });
    let copyRelationOrderConditions = relationOrderConditions ? [...relationOrderConditions] : null;

    if (copyRelationOrderConditions && copyRelationOrderConditions.length > 0) {
      copyRelationOrderConditions = _.uniqWith(copyRelationOrderConditions, _.isEqual);
    }

    for (const orderByField of copyOrderByFields) {
      if (copyRelationOrderConditions && copyRelationOrderConditions.length > 0) {
        const index = copyRelationOrderConditions.findIndex(
          (_orderByField) => _orderByField.columnName === _orderByField.columnName,
        );
        if (index !== -1) {
          query = concatRelation(copyRelationOrderConditions[index], query);
        }
      }
      if (typeof orderByField.isRelation === 'undefined' || !orderByField.isRelation) {
        if (relationArr.indexOf(orderByField.columnName) === -1) {
          query = concatField(orderByField, query);
        }
      }
    }
    query += ' }';
  }
  return query;
};

// TODO: Right now our where conditions do not support relational nested queries and we need to fix this
const constructQueryConditions = (conditions: Condition[], orderByFields?: OrderByField[]) => {
  if (
    (!conditions && !orderByFields) ||
    conditions.length === 0 ||
    (orderByFields && orderByFields.length === 0 && conditions && conditions.length === 0)
  ) {
    return '';
  }
  let conditionFields = '(';
  if (conditions) {
    conditionFields += `where : ${constructConditions(conditions)}`;
  }
  if (orderByFields) {
    conditionFields += constructOrderByFields(orderByFields);
  }
  conditionFields += ' )';
  return conditionFields;
};

// Right now our where conditions do not support relational nested queries and we need to fix this
// thre are also times where you need to append fields of the current object that you want to query as well
const constructRelationalWhereConditions = (
  relationName: string,
  whereCondition: string,
  additionalFields: string,
  orderByConditions: string,
  pageCondition?: string,
) => {
  let query = '';
  if (!additionalFields) {
    query = `(where : { ${relationName}: ${whereCondition} } ${orderByConditions} ${pageCondition})`;
  } else {
    query = `(where : { _and: [ { ${relationName}: ${whereCondition} } ${additionalFields} ] } ${orderByConditions} ${pageCondition})`;
  }
  return query;
};

const constructRelationFields = (relations: Relation[]) => {
  let relationFields = '';
  relations.forEach((relation: Relation) => {
    switch (relation.relationName) {
      case 'product':
        relationFields += ' product { name }';
        break;
      case 'priceBook':
        relationFields += ' priceBook { name }';
        break;
      case 'uom':
        relationFields += ' uom { name } ';
        break;
      case 'referenceProduct':
        relationFields += ' referenceProduct { name } ';
        break;
      case 'defaultUom':
        relationFields += ' defaultUom { name } ';
        break;
      default:
        break;
    }
  });
  return relationFields;
};

export default {
  construct() {
    return {
      queryTotalCount: (apiName: string, whereCondition: string) => {
        return `query { ${apiName}${whereCondition} { count(field: id, distinct: true) } }`;
      },
      queryWithWhereCondition: (
        objectMetadata: RubyObject,
        whereCondition: string,
        extraRelationFields: string,
      ) => {
        return `query { ${objectMetadata.apiName}${whereCondition}${constructFields(
          objectMetadata.fields || [],
          extraRelationFields,
        )}}`;
      },
      queryWithWhereConditionAndFields: (
        objectMetadata: RubyObject,
        whereCondition: string,
        extraRelationFields: string,
        fields?: RubyField[],
      ) => {
        return `query { ${objectMetadata.apiName}${whereCondition}${constructFields(
          fields ? fields : objectMetadata.fields || [],
          extraRelationFields,
        )}}`;
      },
      queryWithWhereConditionAndFieldsInOrder: (
        objectMetadata: RubyObject,
        whereCondition: string,
        fields: RubyField[],
      ) => {
        return `query { ${
          objectMetadata.apiName
        }${whereCondition}${constructFieldsWithExtraRelationsInOrder(fields)}}`;
      },
      // TODO: refractor extraRelationFields to look cleaner
      query: (
        objectMetadata: RubyObject,
        conditions: Condition[],
        extraRelationFields?: string,
        orderByFields?: OrderByField[],
        fields?: RubyField[],
      ) => {
        return `query { ${objectMetadata.apiName}${constructQueryConditions(
          conditions,
          orderByFields,
        )}${constructFields(fields ? fields : objectMetadata.fields || [], extraRelationFields)}}`;
      },
      whereConditions: (conditions: Condition[]) => {
        return constructQueryConditions(conditions);
      },
      conditions: (conditions: Condition[]) => {
        return constructConditions(conditions);
      },
      relationWhereConditions: (
        relationName: string,
        whereCondition: string,
        additionalFields: string,
        orderByConditions: string,
        pageCondition?: string,
      ) => {
        return constructRelationalWhereConditions(
          relationName,
          whereCondition,
          additionalFields,
          orderByConditions,
          pageCondition,
        );
      },
      findExtraRelations: (relations: Relation[]) => {
        return constructRelationFields(relations);
      },
      fields: (objectMetadata: RubyObject) => {
        return constructFields(objectMetadata.fields || []);
      },
      conditionsFromFilters: (filters: RubyFilter[]) => {
        let whereCondition = filters.length > 1 ? '{ _and:[' : '';
        for (let i = 0; i < filters.length; i += 1) {
          whereCondition += `${constructConditions(filters[i].conditions)} `;
        }
        whereCondition += filters.length > 1 ? '] }' : '';
        return whereCondition;
      },
      pageCondition: (currentCount: number, pageSize: number) => {
        return 'page: {cursor: ' + currentCount + ', limit: ' + pageSize + '}';
      },
      orderByFields: (orderByFields: OrderByField[], relationOrderConditions?: OrderByField[]) => {
        return constructOrderByFields(orderByFields, relationOrderConditions);
      },
    };
  },
};

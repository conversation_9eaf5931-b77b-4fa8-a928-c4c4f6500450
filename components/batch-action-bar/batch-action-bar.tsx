import React, { useState } from 'react';

import { IconButton, Select, Typography } from '@material-ui/core';
import Grid from '@material-ui/core/Grid';
import Paper from '@material-ui/core/Paper';
import { makeStyles } from '@material-ui/core/styles';
import ArrowDropDownIcon from '@material-ui/icons/ArrowDropDown';
import CloseIcon from '@material-ui/icons/Close';

import SplitButton from '../split-button';
import { BatchAction, Props } from './interface';

const useStyles = makeStyles({
  container: {
    background: '#efebfd',
    color: '#6239EB',
    width: '100%',
    position: 'relative',
    zIndex: 1000,
    margin: '8px 0 8px 0',
  },
  closeIconContainer: {
    display: 'flex',
    justifyContent: 'flex-end',
  },
  closeIcon: {
    color: '#6239EB',
  },
  select: {
    minWidth: '200px',
    '&:focus': {
      background: '#FFFFFF',
      borderColor: '#ced4da',
    },
  },
  btnStyles: {
    color: '#ffffff',
    width: '250px',
    backgroundColor: '#6239EB',
    textTransform: 'none',
    borderRadius: '4px 0px 0px 4px',
    '&:hover': {
      opacity: '0.8',
      backgroundColor: '#6239EB',
    },
  },
  btnTitleContainer: {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    padding: '4px',
    justifyContent: 'space-between',
  },
  actionItemStyles: {
    minWidth: '250px',
  },
});

const BatchActionBar: React.FC<Props> = (props) => {
  const {
    rowsSelected,
    actions = [],
    handleClose,
    handleSelectBatchAction,
    allRowsSelectedInCurrentPage,
    selectedAll,
    totalCounts,
    onSelectAll: onHandleSelectAllRecords,
    supportSelectAll,
    selection,
  } = props;

  const classes = useStyles();
  const totalSelected = rowsSelected.length === totalCounts;
  const selectionText = selection ? selection.length : rowsSelected.length;
  return (
    <Paper className={classes.container}>
      <Grid container justifyContent="space-between" alignItems="center" spacing={1}>
        <Grid item xs={10}>
          <div style={{ display: 'flex' }}>
            <SplitButton
              btnStyles={classes.btnStyles}
              actionItemStyles={classes.actionItemStyles}
              title={
                <div className={classes.btnTitleContainer}>
                  <Typography variant="body2" component="p">
                    Batch Actions
                  </Typography>
                  <ArrowDropDownIcon />
                </div>
              }
              actions={actions}
              handleClickAction={(actionApiName: string) => {
                const batchAction = actions.find(
                  (action: BatchAction) => action.apiName === actionApiName,
                );
                if (!batchAction || rowsSelected.length === 0) {
                  return;
                }
                handleSelectBatchAction && handleSelectBatchAction(batchAction, rowsSelected);
              }}
            />
            {allRowsSelectedInCurrentPage ? (
              selectedAll || totalSelected ? (
                <div style={{ marginLeft: 24, padding: 12 }} role="selection-text">
                  All {totalCounts} records are selected.{' '}
                  <span
                    style={{ marginLeft: 8, textDecoration: 'underline', cursor: 'pointer' }}
                    onClick={() => onHandleSelectAllRecords && onHandleSelectAllRecords(false)}
                  >
                    Clear Selection
                  </span>
                </div>
              ) : (
                <div style={{ marginLeft: 24, padding: 12 }} role="selection-text">
                  All {rowsSelected.length} records on this page are selected. (total selected{' '}
                  {selectionText}){' '}
                  {supportSelectAll && (
                    <span
                      style={{ marginLeft: 8, textDecoration: 'underline', cursor: 'pointer' }}
                      onClick={() => onHandleSelectAllRecords && onHandleSelectAllRecords(true)}
                    >
                      Select all {totalCounts} records that meet the criteria
                    </span>
                  )}
                </div>
              )
            ) : (
              <div style={{ marginLeft: 24, padding: 12 }} role="selection-text">
                {supportSelectAll ? (
                  <>
                    {rowsSelected.length} records on this page are selected (total selected{' '}
                    {selectionText})
                  </>
                ) : (
                  <>{selectionText} selected</>
                )}
              </div>
            )}
          </div>
        </Grid>

        <Grid
          container
          xs={2}
          alignItems="center"
          style={{ paddingRight: 20 }}
          justifyContent="flex-end"
        >
          <Grid item>
            <div className={classes.closeIconContainer}>
              <IconButton onClick={handleClose} className={classes.closeIcon} component="span">
                <CloseIcon fontSize="small" />
              </IconButton>
            </div>
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default BatchActionBar;

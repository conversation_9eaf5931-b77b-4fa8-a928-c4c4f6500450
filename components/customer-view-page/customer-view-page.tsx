import { List, ListItem } from '@material-ui/core';
import { MuiThemeProvider, createTheme, makeStyles } from '@material-ui/core/styles';
import type {
  Breadcrumb,
  ChangeGroup,
  ChangeItem,
  TaxIntegrationResponse,
} from '@nue-apps/ruby-ui-component';
import {
  BreadcrumbContext,
  ChangeCartLocalStateContext,
  PageHeader,
  RubyButton,
  RubySnackbarProvider,
  getTotalNumChanges,
  useConfirmDialog,
} from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import React, { useContext, useEffect, useState } from 'react';
import type { AdjustPriceRequest, ChangeItemRequest } from '../change-cart/interface';
import CustomerView from '../customer-view';
import CustomerViewContext from '../customer-view-context';
import type { CustomerViewFacade } from '../customer-view-context/customer-view-context';
import { TrolleyIcon } from '../icons';
import { DocsCenter } from '../index';
import type { ChangeType, InvoicePeriods, Period } from '../revenue-builder-types/interface';
import Theme from '../theme';
import type {
  AddChangeItemToChangeGroup,
  AddSingleChangeItemToExistingChangeGroup,
  BulletListChangeItems,
  CreateChangeGroupWithChangeItem,
  IntegrationResult,
  Props,
} from './interface';
import localforage from 'localforage';

const defaultProps = {};

const theme = createTheme(Theme);

const getChangeTypeLabel = (changeType: ChangeType) => {
  switch (changeType) {
    case 'Renew':
      return 'Renewal';
    case 'RenewWithQuantityUpdate':
      return 'Renewal with Quantity Update';
    case 'ConvertFreeTrial':
      return 'Convert Free Trial';
    case 'CoTerm':
    case 'UpdateTerm':
      return 'Update Term';
    case 'AdjustPrice':
      return 'Adjust Price';
    case 'UpdateQuantity':
      return 'Update Quantity';
    case 'UpdateMilestone':
      return 'Update Milestone';
    default:
      return changeType;
  }
};

const startsWithVowel = (changeType: ChangeType) => {
  // the change type co-term should be percieved in text as update term
  if (changeType === 'CoTerm') {
    return true;
  }
  return /[aeiou]/i.test(changeType[0]);
};

const useStyles = makeStyles({
  btn: {
    backgroundColor: '#6239EB',
    opacity: 1,
    color: '#fff',
    border: '2px solid #FFFFFF',
    boxShadow: '0 8px 24px -2px rgba(0,0,0,0.20)',
    borderRadius: '34.5px',
    '&:hover': {
      opacity: 0.7,
    },
  },
  notification: {
    right: '130px',
    top: '-5px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: 'bold',
    position: 'absolute',
    backgroundColor: '#0FB3D3',
    opacity: 1,
    borderRadius: '25px',
    color: '#FFF',
    height: '32px',
    minWidth: '12px',
    width: '32px',
    border: '1px solid #FFFFFF',
    boxShadow: '0 8px 24px -5px rgba(43,25,102,0.41)',
  },
  btnContainer: {
    position: 'fixed',
    right: 40,
    zIndex: 1300,
  },
  inbox: {
    color: '#fff',
  },
});

const BulletListComponent: React.FC<BulletListChangeItems> = (props) => {
  const { changeItems } = props;

  return (
    <List style={{ listStyleType: 'disc', listStylePosition: 'inside' }}>
      {changeItems.map((changeItem: ChangeItem) => (
        <ListItem style={{ display: 'list-item' }} key={changeItem.id}>
          {changeItem.asset.name}
        </ListItem>
      ))}
    </List>
  );
};

const CustomerViewPage: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const classes = useStyles();

  const {
    customer,
    subscriptionMetadata,
    customerMetadata,
    assetMetadata,
    quoteMetadata,
    orderMetadata,
    orderItemMetadata,
    changeHistoryMetadata,
    opportunityMetadata,
    userMetadata,
    invoiceMetadata,
    transactionHubMetadata,
    currencyIsoCode,
    breadcrumbs,
    executeQuery,
    executeGetCountQuery,
    updateFilters,
    listFilters,
    saveFilter,
    deleteFilter,
    getFieldSetMetadata,
    navigateToQuoteBuilder,
    navigateToCustomerViewPage,
    personPlaceholderImage,
    getChangeCartLocalState,
    setChangeCartLocalState,
    actionEventHandler,
    tabFilters,
    rubySettings,
    getSumOrderProductQuantityAtDate,
    getSubscriptionsForCoterm,
    entitlementMetadata,
    productOptionMetadata,
    getInvoicePeriods,
    loadUsageMetadata,
    loadCreditMetadata,
    getInvoiceIdsForAsset,
    getInvoicesByOrderItemIds,
    getSwapUpgradeDowngradeOptions,
    UpdateMileStonesStartDate,
    getOrderProductIdsByAssetId,
    loadCreditStatistics,
    getPanelStatistics,
    orderId,
    defaultTab,
    creditMemosMetaData,
    paymentMethodsMetaData,
    getRevenueContracts,
    getRevenueIntegration,
    getCreditTypes,
    isInSalesforce,
    getRelatedBillingAccountsIdOfSalesAccount,
    loadInvoicesPreviewData,
    loadSubscriptionStreamData,
    handleSaveInvoicePreview,
    getFutureInvoicePeriods,
    loadChildernAndBillingAccounts,
    loadStripeCardInfo,
    getAccountHierarchy,
    getTaxBreakdown,
    getTaxConfig,
    creditCashOut,
    isFromSalesConsole,
    sync,
    getIntegrations,
    checkOut,
  } = props;

  const [openChangeCart, setOpenChangeCart] = useState(false);
  const [changeGroups, setChangeGroups] = useState<Record<string, ChangeGroup>>({});
  const [existingChangeGroup, setExistingChangeGroup] = useState<ChangeGroup | null>(null);
  const [changeItemsToAdd, setChangeItemsToAdd] = useState<ChangeItem[] | null>(null);
  const [existingChangeItem, setExistingChangeItem] = useState<ChangeItem | null>(null);
  const localStateContext = useContext(ChangeCartLocalStateContext);
  const [changeItemRequests, setChangeItemRequests] = useState<ChangeItemRequest[]>([]);
  const [combinedBreadcrumbs, setConbimedBreadcrumbs] = useState<Breadcrumb[]>([]);
  const [multipleDuplicatedChangeGroups, setMultipleDuplicatedChangeGroups] = useState<any>(null);
  const [taxConfig, setTaxConfig] = useState<TaxIntegrationResponse | null>(null);
  const [integrations, setIntegrations] = useState<IntegrationResult[]>();
  const [showChildrenAccounts, setShowChildrenAccounts] = useState<boolean>(false);
  const [shouldShowChildrenButton, setShouldShowChildrenButton] = useState<boolean>(false);

  React.useEffect(() => {
    setConbimedBreadcrumbs(breadcrumbs);
  }, [breadcrumbs]);

  const { showConfirmDialog, ConfirmDialog } = useConfirmDialog({
    submitButtonText: 'Yes',
    cancelButtonText: 'No',
    onOk: async () => {
      if (multipleDuplicatedChangeGroups) {
        //@ts-ignore
        await multipleDuplicatedChangeGroups.forEach(async (x) => {
          await handleUpdateExistingChangeItemsToChangeGroup(x.changeGroup, [x.changeItem]);
        });

        setMultipleDuplicatedChangeGroups(null);
        return;
      }

      if (!existingChangeGroup || !changeItemsToAdd) {
        return;
      }
      await handleUpdateExistingChangeItemsToChangeGroup(existingChangeGroup, changeItemsToAdd);
    },
    onCancel: () => {
      // do nothing
      setMultipleDuplicatedChangeGroups(null);
    },
  });

  const {
    showConfirmDialog: showConflictsEvergreenChangesDialog,
    ConfirmDialog: ConflictChangeToEvergreenDialog,
  } = useConfirmDialog({
    submitButtonText: 'OK',
    // cancelButtonText: 'No',
    onOk: async () => {},
    // onCancel: () => {},
  });

  const {
    showConfirmDialog: showRemoveAddOnRequestsForReconfigureRequest,
    ConfirmDialog: RemoveAddOnRequestsForReconfigureDialog,
  } = useConfirmDialog({
    submitButtonText: 'Yes',
    cancelButtonText: 'No',
    onOk: async () => {
      if (!changeItemsToAdd || changeItemsToAdd.length !== 1 || !existingChangeGroup) {
        return;
      }
      // right now we only support one change item to reconfigure
      const changeItemToAdd = changeItemsToAdd[0];
      const changeItemsForExistingAddOnAssets = existingChangeGroup.changeItems?.filter(
        (currChangeItem: ChangeItem) => currChangeItem.asset.rootId === changeItemToAdd?.asset.id,
      );
      if (
        !changeItemsForExistingAddOnAssets ||
        (changeItemsForExistingAddOnAssets && changeItemsForExistingAddOnAssets.length === 0)
      ) {
        return;
      }
      const changeGroupId = existingChangeGroup.id;
      const changeCartLocalState = await getChangeCartLocalState();
      const changeCartLocalStateCopy = { ...changeCartLocalState };
      const changeGroupsCopy = { ...changeCartLocalStateCopy.changeGroups };
      let changeItemsCopy = changeGroupsCopy[changeGroupId].changeItems;
      changeItemsCopy = removeChangeItemIdsFromChangeItems(
        changeItemsCopy,
        changeItemsForExistingAddOnAssets.map((changeItem: ChangeItem) => changeItem.id),
      );
      changeGroupsCopy[changeGroupId] = {
        ...changeGroupsCopy[changeGroupId],
        changeItems: changeItemsCopy,
      };
      await addNewChangeItemsToChangeGroup({
        changeGroupIdToChangeGroup: changeGroupsCopy,
        changeGroupId,
        newChangeItemsToAdd: changeItemsToAdd,
        changeCartLocalState: changeCartLocalStateCopy,
      });
    },
    onCancel: () => {
      // do nothing
    },
  });

  const {
    showConfirmDialog: showRemoveRootRequestsForAddOnRequest,
    ConfirmDialog: RemoveRootRequestsForAddOnRequestDialog,
  } = useConfirmDialog({
    submitButtonText: 'Yes',
    cancelButtonText: 'No',
    onOk: async () => {
      if (!changeItemsToAdd || changeItemsToAdd.length !== 1 || !existingChangeGroup) {
        return;
      }
      // right now we only support one change item to reconfigure
      const changeItemToAdd = changeItemsToAdd[0];
      const changeItemsForRootAssets = existingChangeGroup.changeItems?.filter(
        (currChangeItem: ChangeItem) => currChangeItem.asset.id === changeItemToAdd?.asset.rootId,
      );
      if (
        !changeItemsForRootAssets ||
        (changeItemsForRootAssets && changeItemsForRootAssets.length === 0)
      ) {
        return;
      }
      const changeGroupId = existingChangeGroup.id;
      const changeCartLocalState = await getChangeCartLocalState();
      const changeCartLocalStateCopy = { ...changeCartLocalState };
      const changeGroupsCopy = { ...changeCartLocalStateCopy.changeGroups };
      let changeItemsCopy = changeGroupsCopy[changeGroupId].changeItems;
      changeItemsCopy = removeChangeItemIdsFromChangeItems(
        changeItemsCopy,
        changeItemsForRootAssets.map((changeItem: ChangeItem) => changeItem.id),
      );
      changeGroupsCopy[changeGroupId] = {
        ...changeGroupsCopy[changeGroupId],
        changeItems: changeItemsCopy,
      };
      await addNewChangeItemsToChangeGroup({
        changeGroupIdToChangeGroup: changeGroupsCopy,
        changeGroupId,
        newChangeItemsToAdd: changeItemsToAdd,
        changeCartLocalState: changeCartLocalStateCopy,
      });
    },
    onCancel: () => {
      // do nothing
    },
  });

  const { showConfirmDialog: showRenewCancelDialog, ConfirmDialog: RenewCancelDialog } =
    useConfirmDialog({
      submitButtonText: 'Yes',
      cancelButtonText: 'No',
      onOk: async () => {
        if (!changeItemsToAdd || changeItemsToAdd.length !== 1) {
          return;
        }
        // adding conflicting change items (i.e. cancel and renew for example) can only be done for one change item
        const changeItemToAdd = changeItemsToAdd[0];
        const changeCartLocalState = await getChangeCartLocalState();
        if (changeCartLocalState) {
          const changeCartLocalStateCopy = { ...changeCartLocalState };
          if (
            changeCartLocalStateCopy.changeGroups &&
            existingChangeGroup &&
            existingChangeGroup.changeItems &&
            existingChangeItem
          ) {
            const changeItemsIndex = existingChangeGroup.changeItems.findIndex(
              (changeItem: ChangeItem) =>
                isDuplicateChangeItemRequest(changeItem, existingChangeItem),
            );
            if (changeItemsIndex !== -1) {
              existingChangeGroup.changeItems?.splice(changeItemsIndex, 1);
              existingChangeGroup.changeItems.push(changeItemToAdd);
            }
            changeCartLocalStateCopy.changeGroups[existingChangeGroup.id] = existingChangeGroup;
            await setChangeCartLocalState(changeCartLocalStateCopy);
            await setup();
          }
        }
      },
      onCancel: () => {
        // do nothing
      },
    });

  const loadInvoicePeriods = async () => {
    const dateFormat = 'YYYY-MM-DD';
    const value: InvoicePeriods = {};
    if (getInvoicePeriods) {
      const today = dayjs();

      const data = await getInvoicePeriods({
        customerId: customer.id,
        startDate: today.add(-1, 'year').add(1, 'day').format(dateFormat),
        endDate: today.format(dateFormat),
      });

      if (data && data.billingCycles && data.billingCycles.length) {
        const currentPeriod = data.billingCycles.find(
          (x: { endDate: string }) =>
            dayjs(x.endDate).isAfter(today) || dayjs(x.endDate).isSame(today.format(dateFormat)),
        );
        const previousPeriods = data.billingCycles
          .filter((x: { endDate: string }) => dayjs(x.endDate).isBefore(today.format(dateFormat)))
          .sort((a: Period, b: Period) => (dayjs(a.startDate).isBefore(b.startDate) ? 1 : -1));

        value.currentPeriod = currentPeriod;
        value.previousPeriods = previousPeriods;

        if (getFutureInvoicePeriods) {
          const future = await getFutureInvoicePeriods();
          if (future && future.invoices?.length) {
            const futurePeriods = future.invoices.map(
              (x: { startDate: string; endDate: string; id: string }) => {
                return {
                  startDate: x.startDate,
                  endDate: x.endDate,
                  id: x.id,
                };
              },
            );
            value.futurePeriods = futurePeriods;
          }
        }
        return value;
      }
    }

    return null;
  };

  const setup = async () => {
    console.log('setup');
    if (localStateContext) {
      const localState = await getChangeCartLocalState();
      if (localState) {
        const { changeGroups: _changeGroups } = localState;
        if (_changeGroups) setChangeGroups(_changeGroups);
      }
    }
    if (!taxConfig && getTaxConfig) {
      setTaxConfig(await getTaxConfig());
    }
    if (!integrations && getIntegrations) {
      setIntegrations(await getIntegrations());
    }
    const showChildrenAccountsLocal = await localforage.getItem<boolean>('showChildrenAccounts');
    setShowChildrenAccounts(!!showChildrenAccountsLocal);
  };

  useEffect(() => {
    setup();
  }, []);

  const getChangeItemRequestsFromChangeGroups = () => {
    // this needs to be any because this will also be used on line editor
    let changeItems: any[] = [];
    for (const key of Object.keys(changeGroups)) {
      if (changeGroups[key].changeItems) {
        changeItems = changeItems.concat(
          changeGroups[key].changeItems?.map((changeItem: ChangeItem) => changeItem.request),
        );
      }
    }
    return changeItems;
  };

  useEffect(() => {
    const changeItemRequests = getChangeItemRequestsFromChangeGroups();
    setChangeItemRequests(changeItemRequests);
  }, [changeGroups]);

  const handleUpdateExistingChangeItemsToChangeGroupForMultipleChanges = (
    existingChangeGroup: ChangeGroup,
    newChangeItemsToAdd: ChangeItem[],
    changeCartLocalState: any,
  ) => {
    if (changeCartLocalState) {
      const changeCartLocalStateCopy = { ...changeCartLocalState };
      if (changeCartLocalStateCopy.changeGroups && existingChangeGroup && newChangeItemsToAdd) {
        const changeGroupCopy = { ...existingChangeGroup };
        if (!changeGroupCopy.changeItems) {
          return;
        }
        for (let i = 0; i < newChangeItemsToAdd.length; i++) {
          const newChangeItemToAdd = newChangeItemsToAdd[i];
          const changeItemIndex = changeGroupCopy.changeItems.findIndex(
            (changeItem: ChangeItem) =>
              isDuplicateChangeItemRequest(changeItem, newChangeItemToAdd) ||
              isConflictingChangeItemRequest(changeItem, newChangeItemToAdd, 'Renew', 'CoTerm') ||
              isConflictingChangeItemRequest(changeItem, newChangeItemToAdd, 'CoTerm', 'Renew'),
          );
          if (changeItemIndex === -1) {
            changeGroupCopy.changeItems.push(newChangeItemToAdd);
          } else {
            changeGroupCopy.changeItems[changeItemIndex] = newChangeItemToAdd;
          }
        }
        changeCartLocalStateCopy.changeGroups[existingChangeGroup.id] = changeGroupCopy;
        return changeCartLocalStateCopy;
      }
    }
    return null;
  };

  // for bulk-coterm, we have to be able to add multiple change items into a change group at one time
  const handleUpdateExistingChangeItemsToChangeGroup = async (
    existingChangeGroup: ChangeGroup,
    newChangeItemsToAdd: ChangeItem[],
  ) => {
    const changeCartLocalState = await getChangeCartLocalState();
    if (changeCartLocalState) {
      const changeCartLocalStateCopy = { ...changeCartLocalState };
      if (changeCartLocalStateCopy.changeGroups && existingChangeGroup && newChangeItemsToAdd) {
        const changeGroupCopy = { ...existingChangeGroup };
        if (!changeGroupCopy.changeItems) {
          return;
        }
        for (let i = 0; i < newChangeItemsToAdd.length; i++) {
          const newChangeItemToAdd = newChangeItemsToAdd[i];
          const changeItemIndex = changeGroupCopy.changeItems.findIndex(
            (changeItem: ChangeItem) =>
              isDuplicateChangeItemRequest(changeItem, newChangeItemToAdd) ||
              isConflictingChangeItemRequest(changeItem, newChangeItemToAdd, 'Renew', 'CoTerm') ||
              isConflictingChangeItemRequest(changeItem, newChangeItemToAdd, 'CoTerm', 'Renew') ||
              isConflictingChangeItemRequest(changeItem, newChangeItemToAdd, 'CoTerm', 'Cancel') ||
              isConflictingChangeItemRequest(changeItem, newChangeItemToAdd, 'Cancel', 'CoTerm') ||
              isConflictingChangeItemRequest(changeItem, newChangeItemToAdd, 'Renew', 'Cancel') ||
              isConflictingChangeItemRequest(changeItem, newChangeItemToAdd, 'Cancel', 'Renew') ||
              isConflictingChangeItemRequest(
                changeItem,
                newChangeItemToAdd,
                'UpdateTerm',
                'Renew',
              ) ||
              isConflictingChangeItemRequest(
                changeItem,
                newChangeItemToAdd,
                'Renew',
                'UpdateTerm',
              ) ||
              isConflictingChangeItemRequest(
                changeItem,
                newChangeItemToAdd,
                'UpdateTerm',
                'Cancel',
              ) ||
              isConflictingChangeItemRequest(
                changeItem,
                newChangeItemToAdd,
                'Cancel',
                'UpdateTerm',
              ) ||
              isConflictingChangeItemRequest(
                changeItem,
                newChangeItemToAdd,
                'CoTerm',
                'UpdateTerm',
              ) ||
              isConflictingChangeItemRequest(
                changeItem,
                newChangeItemToAdd,
                'UpdateTerm',
                'CoTerm',
              ),
          );
          if (changeItemIndex === -1) {
            changeGroupCopy.changeItems.push(newChangeItemToAdd);
          } else {
            changeGroupCopy.changeItems[changeItemIndex] = newChangeItemToAdd;
          }
        }
        changeCartLocalStateCopy.changeGroups[existingChangeGroup.id] = changeGroupCopy;
        // setChangeGroups(changeCartLocalStateCopy.changeGroups);
        await setChangeCartLocalState(changeCartLocalStateCopy);
        await setup();
      }
    }
  };

  const handleAddToCart = async (changeGroup: ChangeGroup, changeItems: ChangeItem[]) => {
    await updateChangeCartLocalState(changeGroup, changeItems);
  };

  const handleRemoveChangesFromCart = async (id: string) => {
    const changeCartLocalState = await getChangeCartLocalState();
    if (changeCartLocalState) {
      const changeCartLocalStateCopy = { ...changeCartLocalState };
      const changeGroupsCopy = { ...changeCartLocalStateCopy.changeGroups };
      delete changeGroupsCopy[id];
      await setChangeCartLocalState({ changeGroups: changeGroupsCopy });
      setChangeGroups(changeGroupsCopy);
    }
  };

  const handleRemoveMultipleGroupsFromCart = async (ids: string[]) => {
    const changeCartLocalState = await getChangeCartLocalState();
    if (changeCartLocalState) {
      const changeCartLocalStateCopy = { ...changeCartLocalState };
      const changeGroupsCopy = { ...changeCartLocalStateCopy.changeGroups };
      for (const changeGroupId of ids) {
        delete changeGroupsCopy[changeGroupId];
      }
      await setChangeCartLocalState({ changeGroups: changeGroupsCopy });
      setChangeGroups(changeGroupsCopy);
    }
  };

  const removeChangeItemIdsFromChangeItems = (
    changeItems: ChangeItem[],
    changeItemIds: string[],
  ) => {
    for (const changeItemId of changeItemIds) {
      const changeItemIndex = changeItems?.findIndex(
        (changeItem: ChangeItem) => changeItem.id === changeItemId,
      );
      if (changeItemIndex !== -1) {
        changeItems.splice(changeItemIndex, 1);
      }
    }
    return changeItems;
  };

  const handleRemoveChangeItems = async (changeGroupId: string, changeItemIds: string[]) => {
    const changeCartLocalState = await getChangeCartLocalState();
    if (changeCartLocalState) {
      const changeCartLocalStateCopy = { ...changeCartLocalState };
      const changeGroupsCopy = { ...changeCartLocalStateCopy.changeGroups };
      let changeItemsCopy = changeGroupsCopy[changeGroupId].changeItems;
      changeItemsCopy = removeChangeItemIdsFromChangeItems(changeItemsCopy, changeItemIds);
      if (changeItemsCopy?.length === 0) {
        delete changeGroupsCopy[changeGroupId];
      } else {
        changeGroupsCopy[changeGroupId] = {
          ...changeGroupsCopy[changeGroupId],
          changeItems: changeItemsCopy,
        };
      }
      await setChangeCartLocalState({ changeGroups: changeGroupsCopy });
      setChangeGroups(changeGroupsCopy);
    }
  };

  const getExistingChangeGroup = (
    changeCartLocalState: Record<string, any>,
    changeGroup: ChangeGroup,
  ) => {
    let existingChangeGroup;
    if (changeCartLocalState.changeGroups) {
      existingChangeGroup = changeCartLocalState.changeGroups[changeGroup.id];
    }
    return existingChangeGroup;
  };

  const isDuplicateChangeItemRequest = (
    currChangeItem: ChangeItem,
    changeItemToAdd: ChangeItem,
  ) => {
    return currChangeItem.id === changeItemToAdd.id;
  };

  const isConflictingChangeItemRequest = (
    existingChangeItem: ChangeItem,
    changeItemToAdd: ChangeItem,
    firstConflictingChangeType: ChangeType,
    secondConflictingChangeType: ChangeType,
  ) => {
    return (
      existingChangeItem.request.changeType === firstConflictingChangeType &&
      changeItemToAdd.request.changeType === secondConflictingChangeType &&
      existingChangeItem.asset.id === changeItemToAdd.asset.id
    );
  };

  const addMultipleChangeItemsToChangeCart = async (
    newChangeItemsToAdd: ChangeItem[],
    changeGroupIdToChangeGroup: Record<string, ChangeGroup>,
    changeGroupId: string,
    changeCartLocalState: Record<string, any>,
  ) => {
    changeGroupIdToChangeGroup[changeGroupId].changeItems =
      //@ts-ignore
      changeGroupIdToChangeGroup[changeGroupId].changeItems.concat(newChangeItemsToAdd);
    setChangeGroups(changeGroupIdToChangeGroup);
    changeCartLocalState.changeGroups = changeGroupIdToChangeGroup;
    await setChangeCartLocalState(changeCartLocalState);
  };

  const hasDuplicateChangeItemsInChangeGroup = (
    changeGroupIdToChangeGroup: Record<string, any>,
    changeGroupId: string,
    newChangeItemsToAdd: ChangeItem[],
  ) => {
    const existingChangeGroup = changeGroupIdToChangeGroup[changeGroupId];
    const duplicateChangeItems = [];
    for (let i = 0; i < newChangeItemsToAdd.length; i++) {
      const changeItemToAdd = newChangeItemsToAdd[i];
      if (
        existingChangeGroup.changeItems!.find((changeItem: ChangeItem) => {
          return (
            isDuplicateChangeItemRequest(changeItem, changeItemToAdd) ||
            isConflictingChangeItemRequest(changeItem, changeItemToAdd, 'Renew', 'CoTerm') ||
            isConflictingChangeItemRequest(changeItem, changeItemToAdd, 'CoTerm', 'Renew')
          );
        })
      ) {
        duplicateChangeItems.push(changeItemToAdd);
      }
    }
    if (duplicateChangeItems.length > 0) {
      showDuplicateChangeItemsForBulkCotermInChangeGroupDialog(
        existingChangeGroup,
        newChangeItemsToAdd,
        duplicateChangeItems,
      );
      return true;
    }
    return false;
  };

  const addNewChangeItemsToChangeGroup = async ({
    changeGroupIdToChangeGroup,
    changeGroupId,
    newChangeItemsToAdd,
    changeCartLocalState,
  }: AddChangeItemToChangeGroup) => {
    if (
      hasDuplicateChangeItemsInChangeGroup(
        changeGroupIdToChangeGroup,
        changeGroupId,
        newChangeItemsToAdd,
      )
    ) {
      return;
    }
    await addMultipleChangeItemsToChangeCart(
      newChangeItemsToAdd,
      changeGroupIdToChangeGroup,
      changeGroupId,
      changeCartLocalState,
    );
  };

  const addNewChangeItemsToChangeGroupForBulk = ({
    changeGroupIdToChangeGroup,
    changeGroupId,
    newChangeItemsToAdd,
    changeCartLocalState,
  }: AddChangeItemToChangeGroup) => {
    if (
      hasDuplicateChangeItemsInChangeGroup(
        changeGroupIdToChangeGroup,
        changeGroupId,
        newChangeItemsToAdd,
      )
    ) {
      return null;
    }

    const newChange = { ...changeGroupIdToChangeGroup };

    newChange[changeGroupId].changeItems =
      //@ts-ignore
      newChange[changeGroupId].changeItems.concat(newChangeItemsToAdd);
    changeCartLocalState.changeGroups = newChange;
    return newChange;
  };

  const showDuplicateChangeItemsForBulkCotermInChangeGroupDialog = (
    existingChangeGroup: ChangeGroup,
    newChangeItemsToAdd: ChangeItem[],
    duplicateChangeItems: ChangeItem[],
  ) => {
    if (
      !existingChangeGroup.changeItems ||
      existingChangeGroup.changeItems.length < 1 ||
      duplicateChangeItems.length === 0
    ) {
      return;
    }
    const existingChangeItem = existingChangeGroup.changeItems[0];
    const message: JSX.Element = (
      <>
        There are already update or renewal requests for the following{' '}
        {existingChangeItem.request.objectType.toLowerCase()}s in the change cart. Do you want to
        process the action and replace them?
        <BulletListComponent changeItems={duplicateChangeItems} />
      </>
    );
    showConfirmDialog({
      title: `Duplicate ${getChangeTypeLabel(duplicateChangeItems[0].request.changeType)} Request`,
      message,
    });
    setChangeItemsToAdd(newChangeItemsToAdd);
    setExistingChangeGroup(existingChangeGroup);
  };

  const showDuplicateChangeItemInChangeGroupDialog = (
    existingChangeGroup: ChangeGroup,
    newChangeItemToAdd: ChangeItem,
  ) => {
    if (!existingChangeGroup.changeItems) {
      return;
    }
    const existingChangeItem = existingChangeGroup.changeItems.find(
      (changeItem: ChangeItem) => changeItem.id === newChangeItemToAdd.id,
    );
    if (!existingChangeItem) {
      return;
    }
    let message = `There is already ${
      startsWithVowel(existingChangeItem.request.changeType) ? 'an' : 'a'
    } ${getChangeTypeLabel(existingChangeItem.request.changeType).toLowerCase()} request for ${
      existingChangeItem.request.label || existingChangeItem.request.objectType
    } ${existingChangeItem.request.assetName} in the change cart. Do you want to replace it?`;
    if (existingChangeItem.request.changeType === 'UpdateMilestone') {
      message = `There is already an Update Milestone request for the same milestone in the change cart. Do you want to replace it?`;
    }
    showConfirmDialog({
      title: `Duplicate ${getChangeTypeLabel(existingChangeItem.request.changeType)} Request`,
      message,
    });
    setChangeItemsToAdd([newChangeItemToAdd]);
    setExistingChangeGroup(existingChangeGroup);
  };

  const doesChangeGroupHaveConflictingChangeItemsForMultipleChanges = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
    changeType: ChangeType,
    existingChangeType: ChangeType,
  ) => {
    if (changeItem.request.changeType !== changeType) {
      return false;
    }
    const existingChangeItem = changeGroup.changeItems?.find(
      (currChangeItem: ChangeItem) =>
        currChangeItem.request.changeType === existingChangeType &&
        currChangeItem.asset.id === changeItem.asset.id,
    );
    if (!existingChangeItem) {
      return false;
    }

    return true;
  };

  const doesChangeGroupHaveConflictingChangeItemsForMultipleChangesOnRoot = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
    changeType: ChangeType,
    exceptionFilter?: (change: ChangeItem) => boolean,
  ) => {
    if (changeItem.request.changeType !== changeType) {
      return false;
    }
    const rootChangeItem = changeGroup.changeItems
      ?.filter(
        (currChangeItem: ChangeItem) =>
          currChangeItem.asset.id === changeItem.asset.rootId &&
          currChangeItem.asset.id !== changeItem.asset.id,
      )
      .filter((change: ChangeItem) => (exceptionFilter ? exceptionFilter!(change) : true));
    if (!rootChangeItem || !rootChangeItem.length) {
      return false;
    }
    setChangeItemsToAdd([changeItem]);
    setExistingChangeGroup(existingChangeGroup);
    showRemoveRootRequestsForAddOnRequest({
      title: `Remove Root Requests`,
      message: `There are requests for ${rootChangeItem[0].asset.name} in the change cart that conflict with a ${changeType} request for ${changeItem.asset.name}. If you proceed with this change, the root requests will be removed from the cart. Do you want to proceed?`,
    });
    return true;
  };

  const doesChangeGroupHaveConflictingChangeItems = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
    changeType: ChangeType,
    existingChangeType: ChangeType,
  ) => {
    if (changeItem.request.changeType !== changeType) {
      return false;
    }
    const existingChangeItem = changeGroup.changeItems?.find(
      (currChangeItem: ChangeItem) =>
        currChangeItem.request.changeType === existingChangeType &&
        currChangeItem.asset.id === changeItem.asset.id,
    );
    if (!existingChangeItem) {
      return false;
    }
    setExistingChangeItem(existingChangeItem);
    const changeTypeLabel = getChangeTypeLabel(existingChangeType);
    showRenewCancelDialog({
      title: `Remove ${changeTypeLabel} Request`,
      message: `There is a ${changeTypeLabel} Request for ${changeItem.asset.name} in the change cart. If you proceed with this change, the ${changeTypeLabel} Request will be removed from the cart. Do you want to proceed?`,
    });
    return true;
  };

  const findIfThereAreChangeRequestsForAddOnSubscriptionsThatExistWhenAddingReconfigureRequest = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
  ) => {
    const existingChangeItems = changeGroup.changeItems;
    if (changeItem.request.changeType === 'Reconfigure') {
      const changeItemForExistingAddOnAsset = existingChangeItems?.find(
        (currChangeItem) =>
          currChangeItem.asset.rootId === changeItem.asset.id &&
          currChangeItem.asset.id !== changeItem.asset.id,
      );
      if (changeItemForExistingAddOnAsset) {
        setExistingChangeGroup(changeGroup);
        setChangeItemsToAdd([changeItem]);
        showRemoveAddOnRequestsForReconfigureRequest({
          title: 'Reconfigure Request Warning',
          message: `There are change requests for add-on subscriptions of subscription ${changeItem.asset.name} in the change cart.  If you reconfigure the subscription, the change requests for its add-on subscriptions will be removed from the cart.  Do you want to proceed?`,
        });
        return true;
      }
    }
    return false;
  };

  const findIfThereIsRootReconfigureRequestThatAlreadyExistsWhenAddingAddOnSubscriptionRequest = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
  ) => {
    const existingChangeItems = changeGroup.changeItems;
    const existingRootAssetChangeItems = existingChangeItems?.filter(
      (currChangeItem: ChangeItem) =>
        currChangeItem.asset.id === changeItem.asset.rootId &&
        currChangeItem.asset.id !== changeItem.asset.id,
    );
    const reconfigureChangeItem = existingRootAssetChangeItems?.find(
      (currChangeItem: ChangeItem) => currChangeItem.request.changeType === 'Reconfigure',
    );
    if (reconfigureChangeItem) {
      setExistingChangeItem(reconfigureChangeItem);
      setChangeItemsToAdd([changeItem]);
      showRenewCancelDialog({
        title: `Remove ${reconfigureChangeItem.request.changeType} Request`,
        message: `There is a ${reconfigureChangeItem.request.changeType} Request for ${reconfigureChangeItem.asset.name} in the change cart. If you proceed with this change, the ${reconfigureChangeItem.request.changeType} Request will be removed from the cart. Do you want to proceed?`,
      });
      return true;
    }
    return false;
  };

  const findIfThereIsAddOnReconfigureRequestThatAlreadyExistsWhenAddingAddOnReconfigureRequest = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
  ) => {
    if (changeItem.request.changeType !== 'Reconfigure') {
      return false;
    }
    const existingChangeItems = changeGroup.changeItems;
    const existingSameRootAssetChangeItems = existingChangeItems?.filter(
      (currChangeItem: ChangeItem) =>
        currChangeItem.asset.rootId === changeItem.asset.rootId &&
        currChangeItem.asset.id !== changeItem.asset.id,
    );
    const reconfigureChangeItem = existingSameRootAssetChangeItems?.find(
      (currChangeItem: ChangeItem) => currChangeItem.request.changeType === 'Reconfigure',
    );
    if (reconfigureChangeItem) {
      setExistingChangeItem(reconfigureChangeItem);
      setChangeItemsToAdd([changeItem]);
      showRenewCancelDialog({
        title: `Remove ${reconfigureChangeItem.request.changeType} Request`,
        message: `There is a ${reconfigureChangeItem.request.changeType} Request for ${reconfigureChangeItem.asset.name} in the change cart. If you proceed with this change, the ${reconfigureChangeItem.request.changeType} Request will be removed from the cart. Do you want to proceed?`,
      });
      return true;
    }
    return false;
  };

  const findIfThereIsAddOnReconfigureRequestThatAlreadyExistsWhenAddingRootRequest = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
  ) => {
    if (
      changeItem.cardAction.id === 'AdjustPrice' &&
      !(changeItem.request as AdjustPriceRequest).applyToAll
    ) {
      return false;
    }
    const existingChangeItems = changeGroup.changeItems;
    const existingSameRootAssetChangeItems = existingChangeItems?.filter(
      (currChangeItem: ChangeItem) =>
        currChangeItem.asset.rootId === changeItem.asset.id &&
        currChangeItem.asset.id !== changeItem.asset.id,
    );
    const reconfigureChangeItem = existingSameRootAssetChangeItems?.find(
      (currChangeItem: ChangeItem) => currChangeItem.request.changeType === 'Reconfigure',
    );
    if (reconfigureChangeItem) {
      setExistingChangeItem(reconfigureChangeItem);
      setChangeItemsToAdd([changeItem]);
      showRenewCancelDialog({
        title: `Remove ${reconfigureChangeItem.request.changeType} Request`,
        message: `There is a ${reconfigureChangeItem.request.changeType} Request for ${reconfigureChangeItem.asset.name} in the change cart. If you proceed with this change, the ${reconfigureChangeItem.request.changeType} Request will be removed from the cart. Do you want to proceed?`,
      });
      return true;
    }
    return false;
  };

  const doesChangeGroupHaveReconfigureRequestAndChangeRequestsFromAddOns = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
  ) => {
    if (
      findIfThereAreChangeRequestsForAddOnSubscriptionsThatExistWhenAddingReconfigureRequest(
        changeGroup,
        changeItem,
      ) ||
      findIfThereIsRootReconfigureRequestThatAlreadyExistsWhenAddingAddOnSubscriptionRequest(
        changeGroup,
        changeItem,
      ) ||
      findIfThereIsAddOnReconfigureRequestThatAlreadyExistsWhenAddingAddOnReconfigureRequest(
        changeGroup,
        changeItem,
      ) ||
      findIfThereIsAddOnReconfigureRequestThatAlreadyExistsWhenAddingRootRequest(
        changeGroup,
        changeItem,
      )
    ) {
      return true;
    }
    return false;
  };

  const getAddingNewChangeItemWithConflictingRequests = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
  ) => {
    if (
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'CoTerm',
        'Renew',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'Renew',
        'CoTerm',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'Cancel',
        'Renew',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'Renew',
        'Cancel',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'UpdateTerm',
        'Renew',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'Renew',
        'UpdateTerm',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'UpdateTerm',
        'Cancel',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'Cancel',
        'UpdateTerm',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'CoTerm',
        'Cancel',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'Cancel',
        'CoTerm',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'CoTerm',
        'UpdateTerm',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChanges(
        changeGroup,
        changeItem,
        'UpdateTerm',
        'CoTerm',
      ) ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChangesOnRoot(
        changeGroup,
        changeItem,
        'Reconfigure',
        (_changeItem: ChangeItem) => {
          return !(
            _changeItem.cardAction.id === 'AdjustPrice' &&
            !(_changeItem.request as AdjustPriceRequest).applyToAll
          );
        },
      )
    ) {
      return true;
    }
    return false;
  };

  const isAddingNewChangeItemWithConflictingRequests = (
    changeGroup: ChangeGroup,
    changeItem: ChangeItem,
  ) => {
    if (
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'CoTerm', 'Renew') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'Renew', 'CoTerm') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'Cancel', 'Renew') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'Renew', 'Cancel') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'UpdateTerm', 'Renew') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'Renew', 'UpdateTerm') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'UpdateTerm', 'Cancel') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'Cancel', 'UpdateTerm') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'CoTerm', 'Cancel') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'Cancel', 'CoTerm') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'CoTerm', 'UpdateTerm') ||
      doesChangeGroupHaveConflictingChangeItems(changeGroup, changeItem, 'UpdateTerm', 'CoTerm') ||
      doesChangeGroupHaveConflictingChangeItemsForMultipleChangesOnRoot(
        changeGroup,
        changeItem,
        'Reconfigure',
        (_changeItem: ChangeItem) => {
          return !(
            _changeItem.cardAction.id === 'AdjustPrice' &&
            !(_changeItem.request as AdjustPriceRequest).applyToAll
          );
        },
      ) ||
      doesChangeGroupHaveReconfigureRequestAndChangeRequestsFromAddOns(changeGroup, changeItem)
    ) {
      return true;
    }
    return false;
  };

  const initializeMutlipleChangeCart = async (
    changeCartLocalState: Record<string, any>,
    items: { changeGroup: ChangeGroup; changeItems: ChangeItem[] }[],
  ) => {
    if (changeCartLocalState) {
      // somehow this code gets executed even when there is a changeCartLocalState,
      // possibly with some race condition of some sort after you add a change group with a different price book than
      // the existing change group
      return;
    }
    const changeGroups: Record<string, any> = {};
    items.forEach((x) => {
      const newChangeItems = (changeGroups[x.changeGroup.id]?.changeItems || []).concat(
        x.changeItems,
      );
      changeGroups[x.changeGroup.id] = {
        ...x.changeGroup,
        changeItems: newChangeItems,
      };
    });
    const changeCart = {
      changeGroups,
    };

    setChangeGroups(changeCart.changeGroups);
    await setChangeCartLocalState(changeCart);
  };

  const initializeChangeCart = async (
    changeCartLocalState: Record<string, any>,
    changeGroup: ChangeGroup,
    changeItems: ChangeItem[],
  ) => {
    if (changeCartLocalState) {
      // somehow this code gets executed even when there is a changeCartLocalState,
      // possibly with some race condition of some sort after you add a change group with a different price book than
      // the existing change group
      return;
    }
    const changeGroups: Record<string, any> = {};
    changeGroups[changeGroup.id] = { ...changeGroup, changeItems };
    const changeCart = {
      changeGroups,
    };
    setChangeGroups(changeCart.changeGroups);
    await setChangeCartLocalState(changeCart);
  };

  const createChangeGroupWithChangeItems = async ({
    changeGroupIdToChangeGroup,
    changeGroup,
    newChangeItemsToAdd,
    changeCartLocalState,
  }: CreateChangeGroupWithChangeItem) => {
    changeGroup.changeItems = newChangeItemsToAdd;
    if (changeCartLocalState.changeGroups) {
      changeCartLocalState.changeGroups[changeGroup.id] = changeGroup;
      changeGroupIdToChangeGroup = { ...changeGroupIdToChangeGroup, [changeGroup.id]: changeGroup };
    }
    setChangeGroups(changeGroupIdToChangeGroup || {});
    //@ts-ignore
    await setChangeCartLocalState(changeCartLocalState);
  };

  const getChangeGroupWithChangeItems = ({
    changeGroupIdToChangeGroup,
    changeGroup,
    newChangeItemsToAdd,
    changeCartLocalState,
  }: CreateChangeGroupWithChangeItem) => {
    changeGroup.changeItems = newChangeItemsToAdd;
    if (changeCartLocalState.changeGroups) {
      changeCartLocalState.changeGroups[changeGroup.id] = changeGroup;
      return { ...changeGroupIdToChangeGroup, [changeGroup.id]: changeGroup };
    }
    return;
  };

  const isEvergreenToTerm = (changeItem: ChangeItem) => {
    return (
      changeItem.asset.evergreen &&
      (changeItem.request.changeType === 'UpdateTerm' ||
        changeItem.request.changeType === 'CoTerm' ||
        changeItem.request.changeType === 'Cancel' ||
        changeItem.request.changeType === 'CoTermForEvergreen' ||
        changeItem.request.changeType === 'UpdateTermForEvergreen')
    );
  };
  const addSingleChangeItemToExistingChangeGroup = async ({
    changeGroupIdToChangeGroup,
    changeGroup,
    newChangeItemToAdd,
    changeCartLocalState,
  }: AddSingleChangeItemToExistingChangeGroup) => {
    const existingChangeGroup = changeGroupIdToChangeGroup[changeGroup.id];
    const existingChangeItem: ChangeItem | undefined = existingChangeGroup.changeItems!.find(
      (changeItem: ChangeItem) => isDuplicateChangeItemRequest(changeItem, newChangeItemToAdd),
    );
    if (existingChangeItem) {
      if (newChangeItemToAdd.requestWasEdited) {
        newChangeItemToAdd.requestWasEdited = false;
        await handleUpdateExistingChangeItemsToChangeGroup(existingChangeGroup, [
          newChangeItemToAdd,
        ]);
        return;
      }
      showDuplicateChangeItemInChangeGroupDialog(existingChangeGroup, newChangeItemToAdd);
      return;
    }

    // RIM-7327 cannot have updateToEvergreen / renewToEvergreen and other changes in the same change group
    // RIM-12340 cannot have switchToEvergreen and other changes in the same change group
    if (
      ((newChangeItemToAdd.request.updateToEvergreen ||
        // @ts-expect-error Can exist for convert free trial
        newChangeItemToAdd.request.switchToEvergreen) &&
        existingChangeGroup?.changeItems &&
        existingChangeGroup?.changeItems.some((_) => _.asset.id === newChangeItemToAdd.asset.id)) ||
      existingChangeGroup?.changeItems?.some(
        (_) => _.asset.id === newChangeItemToAdd.asset.id && _.request.updateToEvergreen === true,
      )
    ) {
      showConflictsEvergreenChangesDialog({
        title: 'Invalid Change Request',
        message:
          'You cannot make additional changes when converting a termed subscription to evergreen. These changes can be made afterward.',
      });
      return;
    }

    //RIM-8823 cannot change evergreen to term with other changes in the same change group
    if (
      (isEvergreenToTerm(newChangeItemToAdd) &&
        existingChangeGroup?.changeItems?.some(
          (_) => _.asset.id === newChangeItemToAdd.asset.id,
        )) ||
      existingChangeGroup?.changeItems?.some(
        (changeItem) =>
          changeItem.asset.id === newChangeItemToAdd.asset.id && isEvergreenToTerm(changeItem),
      )
    ) {
      showConflictsEvergreenChangesDialog({
        title: 'Invalid Change Request',
        message:
          'You cannot make additional changes when converting an evergreen subscription to a term-based subscription. These changes can be made afterward.',
      });
      return;
    }

    if (isAddingNewChangeItemWithConflictingRequests(existingChangeGroup, newChangeItemToAdd)) {
      setExistingChangeGroup(existingChangeGroup);
      setChangeItemsToAdd([newChangeItemToAdd]);
      return;
    }
    await addNewChangeItemsToChangeGroup({
      changeGroupIdToChangeGroup,
      changeGroupId: existingChangeGroup.id,
      changeCartLocalState,
      newChangeItemsToAdd: [newChangeItemToAdd],
    });
  };

  const addSingleChangeItemToExistingChangeGroupForBulkChanges = ({
    changeGroupIdToChangeGroup,
    changeGroup,
    newChangeItemToAdd,
    changeCartLocalState,
  }: AddSingleChangeItemToExistingChangeGroup) => {
    const existingChangeGroup = changeGroupIdToChangeGroup[changeGroup.id];
    const existingChangeItem: ChangeItem | undefined = existingChangeGroup.changeItems!.find(
      (changeItem: ChangeItem) => isDuplicateChangeItemRequest(changeItem, newChangeItemToAdd),
    );
    if (existingChangeItem) {
      if (newChangeItemToAdd.requestWasEdited) {
        newChangeItemToAdd.requestWasEdited = false;
        const result = handleUpdateExistingChangeItemsToChangeGroupForMultipleChanges(
          existingChangeGroup,
          [newChangeItemToAdd],
          changeCartLocalState,
        );
        return result;
      }
    }

    return addNewChangeItemsToChangeGroupForBulk({
      changeGroupIdToChangeGroup,
      changeGroupId: existingChangeGroup.id,
      changeCartLocalState,
      newChangeItemsToAdd: [newChangeItemToAdd],
    });
  };

  const addChangeItemsToExistingChangeGroupForBulkChanges = ({
    changeGroupIdToChangeGroup,
    changeGroup,
    newChangeItemsToAdd,
    changeCartLocalState,
  }: CreateChangeGroupWithChangeItem) => {
    if (newChangeItemsToAdd.length === 0) {
      return;
    }
    if (newChangeItemsToAdd.length === 1) {
      const newChangeItemToAdd = newChangeItemsToAdd[0];
      const result = addSingleChangeItemToExistingChangeGroupForBulkChanges({
        changeGroupIdToChangeGroup,
        changeGroup,
        changeCartLocalState,
        newChangeItemToAdd,
      });
      return result;
    }

    return null;
  };

  const addChangeItemsToExistingChangeGroup = async ({
    changeGroupIdToChangeGroup,
    changeGroup,
    newChangeItemsToAdd,
    changeCartLocalState,
  }: CreateChangeGroupWithChangeItem) => {
    if (newChangeItemsToAdd.length === 0) {
      return;
    }
    if (newChangeItemsToAdd.length === 1) {
      const newChangeItemToAdd = newChangeItemsToAdd[0];
      await addSingleChangeItemToExistingChangeGroup({
        changeGroupIdToChangeGroup,
        changeGroup,
        changeCartLocalState,
        newChangeItemToAdd,
      });
      return;
    }
    await addNewChangeItemsToChangeGroup({
      changeGroupIdToChangeGroup,
      changeGroupId: changeGroup.id,
      changeCartLocalState,
      newChangeItemsToAdd,
    });
  };

  const updateMultipleChangeCartLocalState = async (
    items: { changeGroup: ChangeGroup; changeItems: ChangeItem[] }[],
  ) => {
    if (!localStateContext) {
      return;
    }
    const changeCartLocalState = await getChangeCartLocalState();
    if (!changeCartLocalState) {
      await initializeMutlipleChangeCart(changeCartLocalState, items);
      return;
    }
    const changeCartLocalStateCopy = { ...changeCartLocalState };
    let changeGroupIdToChangeGroup = { ...changeGroups };

    items.forEach((x) => {
      const newExistingChangeGroup = getExistingChangeGroup(
        changeCartLocalStateCopy,
        x.changeGroup,
      );

      if (!newExistingChangeGroup || !newExistingChangeGroup.changeItems) {
        const cg = getChangeGroupWithChangeItems({
          changeGroupIdToChangeGroup,
          changeGroup: x.changeGroup,
          newChangeItemsToAdd: x.changeItems,
          changeCartLocalState: changeCartLocalStateCopy,
        });
        if (cg) {
          changeGroupIdToChangeGroup = { ...cg };
        }
        return;
      }
      const ad = addChangeItemsToExistingChangeGroupForBulkChanges({
        changeGroupIdToChangeGroup,
        changeGroup: x.changeGroup,
        newChangeItemsToAdd: x.changeItems,
        changeCartLocalState: changeCartLocalStateCopy,
      });
      if (ad) {
        changeGroupIdToChangeGroup = { ...ad };
      }
    });

    setChangeGroups(changeGroupIdToChangeGroup || {});
    //@ts-ignore
    await setChangeCartLocalState(changeCartLocalStateCopy);
  };

  const updateChangeCartLocalState = async (
    changeGroup: ChangeGroup,
    newChangeItemsToAdd: ChangeItem[],
  ) => {
    if (!localStateContext) {
      return;
    }
    const changeCartLocalState = await getChangeCartLocalState();

    if (!changeCartLocalState) {
      await initializeChangeCart(changeCartLocalState, changeGroup, newChangeItemsToAdd);
      return;
    }
    const changeCartLocalStateCopy = { ...changeCartLocalState };
    const changeGroupIdToChangeGroup = { ...changeGroups };
    const existingChangeGroup = getExistingChangeGroup(changeCartLocalStateCopy, changeGroup);
    if (!existingChangeGroup || !existingChangeGroup.changeItems) {
      await createChangeGroupWithChangeItems({
        changeGroupIdToChangeGroup,
        changeGroup,
        newChangeItemsToAdd,
        changeCartLocalState: changeCartLocalStateCopy,
      });
      return;
    }
    await addChangeItemsToExistingChangeGroup({
      changeGroupIdToChangeGroup,
      changeGroup,
      newChangeItemsToAdd,
      changeCartLocalState: changeCartLocalStateCopy,
    });
  };

  const handleOpenChangeCart = (open: boolean) => {
    setOpenChangeCart(open);
  };

  const customerViewFacade: CustomerViewFacade = {
    customerViewService: {
      getCustomerViewConfig: () => {
        return {
          customer,
          customerViewConfigs: {
            getFieldSetMetadata,
            openChangeCart,
            changeGroups,
            changeItemRequests,
            handleOpenChangeCart,
            handleAddToCart,
            handleAddMultipleGroupsToCart,
            handleRemoveChangesFromCart,
            handleRemoveChangeItems,
            handleRemoveMultipleGroupsFromCart,
            currencyIsoCode,
            executeQuery,
            executeGetCountQuery,
            quoteMetadata,
            customerMetadata,
            assetMetadata,
            subscriptionMetadata,
            loadUsageMetadata,
            loadCreditMetadata,
            transactionHubMetadata,
            getPanelStatistics,
            invoiceMetadata,
            loadInvoicePeriods,
            changeHistoryMetadata,
            userMetadata,
            orderMetadata,
            orderItemMetadata,
            opportunityMetadata,
            navigateToQuoteBuilder,
            navigateToCustomerViewPage,
            personPlaceholderImage,
            tabFilters,
            rubySettings,
            getSumOrderProductQuantityAtDate,
            getSubscriptionsForCoterm,
            entitlementMetadata,
            productOptionMetadata,
            getInvoiceIdsForAsset,
            getInvoicesByOrderItemIds,
            getSwapUpgradeDowngradeOptions,
            UpdateMileStonesStartDate,
            getOrderProductIdsByAssetId,
            loadCreditStatistics,
            orderId,
            defaultTab,
            creditMemosMetaData,
            paymentMethodsMetaData,
            getRevenueContracts,
            getRevenueIntegration,
            getCreditTypes,
            getRelatedBillingAccountsIdOfSalesAccount,
            loadInvoicesPreviewData,
            handleSaveInvoicePreview,
            loadSubscriptionStreamData,
            loadChildernAndBillingAccounts,
            loadStripeCardInfo,
            getAccountHierarchy,
            getTaxBreakdown,
            taxConfig: taxConfig,
            creditCashOut,
            sync,
            integrations,
            checkOut,
          },
          searchConfigs: {
            updateFilters: updateFilters,
            saveFilter: saveFilter,
            listFilters: listFilters,
            deleteFilter: deleteFilter,
          },
        };
      },
      actionEventHandler: actionEventHandler,
    },
    showChildrenAccounts: showChildrenAccounts,
    setShowChildrenBtn: setShouldShowChildrenButton,
  };

  const showDuplicateChangeItemsForBulkCotermOrRenewInChangeGroupDialog = (
    duplicateChangeItems: { changeGroup: ChangeGroup; changeItem: ChangeItem }[],
  ) => {
    if (!duplicateChangeItems || duplicateChangeItems.length < 1) {
      return;
    }
    const existingChangeItem = duplicateChangeItems[0].changeItem;
    //@ts-ignore
    const changeType =
      existingChangeItem?.cardAction?.name === 'AdjustPrice' ? 'Adjust Price' : 'Term change';
    const message = (
      <>
        There are already {changeType.toLocaleLowerCase()} requests for the following{' '}
        {existingChangeItem.request.objectType.toLowerCase()}s in the change cart. Do you want to
        replace them?
        <BulletListComponent changeItems={duplicateChangeItems.map((x) => x.changeItem)} />
      </>
    );
    showConfirmDialog({
      title: `Duplicate ${changeType} Change Requests`,
      message,
    });
  };

  const handleAddMultipleGroupsToCart = async (
    items: { changeGroup: ChangeGroup; changeItems: ChangeItem[] }[],
  ) => {
    const changeGroupIdToChangeGroup = { ...changeGroups };
    const newDuplicateChangeItems: any[] = [];
    const validChangeItems: any[] = [];
    const validChangeItemsCancel: any[] = [];

    items.forEach((item) => {
      const newExistingChangeGroup = changeGroupIdToChangeGroup[item.changeGroup.id];
      if (newExistingChangeGroup) {
        for (let i = 0; i < item.changeItems.length; i++) {
          const changeItemToAdd = item.changeItems[i];
          if (
            newExistingChangeGroup.changeItems!.find((changeItem: ChangeItem) => {
              return (
                isDuplicateChangeItemRequest(changeItem, changeItemToAdd) ||
                isConflictingChangeItemRequest(changeItem, changeItemToAdd, 'Renew', 'CoTerm') ||
                isConflictingChangeItemRequest(changeItem, changeItemToAdd, 'CoTerm', 'Renew')
              );
            })
          ) {
            newDuplicateChangeItems.push({
              changeGroup: newExistingChangeGroup,
              changeItem: changeItemToAdd,
            });
          } else {
            if (
              getAddingNewChangeItemWithConflictingRequests(newExistingChangeGroup, changeItemToAdd)
            ) {
              validChangeItemsCancel.push({
                changeGroup: newExistingChangeGroup,
                changeItem: changeItemToAdd,
              });
            } else {
              validChangeItems.push({
                changeGroup: newExistingChangeGroup,
                changeItems: [changeItemToAdd],
              });
            }
          }
        }
      } else {
        validChangeItems.push({
          changeGroup: item.changeGroup,
          changeItems: item.changeItems,
        });
      }
    });

    if (validChangeItems.length > 0) {
      await updateMultipleChangeCartLocalState(validChangeItems);
    }

    if (newDuplicateChangeItems.length > 0 || validChangeItemsCancel.length > 0) {
      const invalidItems = (newDuplicateChangeItems || []).concat(validChangeItemsCancel);
      showDuplicateChangeItemsForBulkCotermOrRenewInChangeGroupDialog(invalidItems);
      setMultipleDuplicatedChangeGroups(invalidItems);
      return;
    }
  };

  const breadcrumbFacade = {
    breadcrumbService: {
      getBreadcrumbConfig: () => {
        return {
          breadcrumbs: combinedBreadcrumbs,
          updateBreadcrumbs: (newBreadcrumbs: Breadcrumb[]) => {
            setConbimedBreadcrumbs([...breadcrumbs].concat(newBreadcrumbs));
          },
        };
      },
    },
  };
  const [isOpen, setIsOpen] = useState(false);
  return (
    <MuiThemeProvider theme={theme}>
      <RubySnackbarProvider>
        <BreadcrumbContext.Provider value={breadcrumbFacade}>
          <CustomerViewContext.Provider value={customerViewFacade}>
            <PageHeader
              title={customer.name}
              breadcrumbs={combinedBreadcrumbs}
              RightOfTitleSlot={
                <div className={classes.btnContainer}>
                  <RubyButton
                    icon={<TrolleyIcon className={classes.inbox} />}
                    text="Changes"
                    classNames={classes.btn}
                    onClick={() => setOpenChangeCart(true)}
                  />
                  <div className={classes.notification}>{getTotalNumChanges(changeGroups)}</div>
                </div>
              }
              isControlShowChildren={shouldShowChildrenButton}
              showChildren={showChildrenAccounts}
              toggleShowChildren={(value: boolean) => {
                setShowChildrenAccounts(value);
                localforage.setItem('showChildrenAccounts', value);
              }}
            />
            {isInSalesforce && (
              <DocsCenter
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                isFromSalesConsole={isFromSalesConsole}
              />
            )}
            <CustomerView />
            <ConfirmDialog />
            <RenewCancelDialog />
            <RemoveAddOnRequestsForReconfigureDialog />
            <ConflictChangeToEvergreenDialog />
            <RemoveRootRequestsForAddOnRequestDialog />
          </CustomerViewContext.Provider>
        </BreadcrumbContext.Provider>
      </RubySnackbarProvider>
    </MuiThemeProvider>
  );
};

export default CustomerViewPage;

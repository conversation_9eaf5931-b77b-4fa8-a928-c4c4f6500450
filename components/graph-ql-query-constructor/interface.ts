import type { RubyField } from '../metadata/interface';

export interface RubyFilter {
  conditions: Condition[];
  name: string;
  isExclusive?: boolean;
  id: string;
  isApplied?: boolean;
  isUnsavedFilter?: boolean;
  isUpdatable?: boolean;
  isSystemField?: boolean;
  ownerId?: string;
}

export interface FilterBuilderField extends RubyField {
  relation: string | null;
  field?: FilterBuilderField;
}

export interface FilterValue {
  value: any;
  translateValue: any;
}

// condition used by filter builder
export interface FilterBuilderCondition {
  id?: string;
  type: 'Simple' | 'Nested';
  value?: FilterValue | null;
  error?: string | null;
  operand?: Operand;
  operator?: FilterBuilderOperator;
  nestedConditions?: FilterBuilderCondition[];
  field: FilterBuilderField | null;
  isRequired?: boolean;
}

export interface Operand {
  id: ' _and ' | ' _or ';
  name: 'And' | 'Or';
}

export interface FilterBuilderOperator {
  id:
    | ' _eq '
    | ' _lt '
    | ' _lte '
    | ' _neq '
    | ' _gt '
    | ' _gte '
    | ' _like '
    | ' _in '
    | ' _is_null '
    | ' _is_not_null ';
  name: string;
}

// existing filter you see constructed with GraphQL Query
export interface Condition {
  apiName: string;
  name: string;
  error?: string;
  type:
    | 'boolean'
    | 'pickList'
    | 'date'
    | 'dateTime'
    | 'integer'
    | 'text'
    | 'number'
    | 'datetime'
    | string;
  value: string | null;
  operand?: ' _and ' | ' _or ';
  operator?:
    | ' _eq '
    | ' _lt '
    | ' _lte '
    | ' _neq '
    | ' _gt '
    | ' _gte '
    | ' _like '
    | ' _in '
    | ' _is_null '
    | ' _is_not_null ';
  nestedConditions?: Condition[];
  relation?: string | null;
  field?: Condition;
  nestedApiName?: string;
  nestedType?: string;
  valueSet?: Object;
  lookupRelation?: any;
  isRequired?: boolean;
}

export interface Operator {
  id: string;
  operator: ' _eq ' | ' _lt ' | ' _lte ' | ' _neq ' | ' _gt ' | ' _gte ' | ' _like ' | ' _in ';
}

export interface OrderByField {
  columnName: string;
  isRelation?: boolean;
  direction: string;
  referenceField?: string;
}

export interface Relation {
  relationName: string;
}

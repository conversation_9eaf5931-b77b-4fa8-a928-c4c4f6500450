export default [
  {
    apiName: 'quantity',
    name: 'Quantity',
    type: 'decimal',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 10,
    scale: 2,
  },
  {
    apiName: 'name',
    name: 'Entitlement Name',
    filterable: true,
    type: 'text',
    required: false,
    updatable: true,
    customField: false,
    xs: 4,
    creatable: true,
    sortable: true,
  },
  {
    apiName: 'assetId',
    name: 'Asset',
    filterable: true,
    type: 'bLookup',
    required: true,
    updatable: true,
    customField: true,
    xs: 4,
    creatable: true,
    sortable: true,
    lookupRelation: {
      relationName: 'asset',
      relationLabel: 'Asset',
    },
  },
  {
    apiName: 'productId',
    name: 'Product',
    filterable: true,
    type: 'bLookup',
    required: false,
    updatable: true,
    customField: true,
    xs: 4,
    creatable: true,
    sortable: true,
    lookupRelation: {
      relationName: 'product',
      relationLabel: 'Product',
    },
  },
];

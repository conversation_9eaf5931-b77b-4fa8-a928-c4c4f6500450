import React from 'react';
import { ArrMomentumChartData, ArrMomentumChartProps } from './interface';
import * as echarts from 'echarts';
import { EChartsOption } from 'echarts';
import { makeStyles } from '@material-ui/core';
import { CallbackDataParams, TopLevelFormatterParams } from 'echarts/types/dist/shared';
import { useResizeDetector } from 'react-resize-detector';
import dayjs from 'dayjs';

const useStyles = makeStyles({
  'legend--bottom': {
    margin: '16px 5%',
    display: 'flex',
    alignItems: 'center',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  legend__item__container: {
    display: 'flex',
    alignItems: 'center',
    margin: '0 10px',
    cursor: 'pointer',
  },
  legend__item__icon: {
    width: '8px',
    height: '8px',
    marginRight: '10px',
    borderRadius: '50%',
  },
  legend__item__text: {
    fontSize: '14px',
    opacity: 0.5,
  },
});

const LegendItem = (props: { name: string; color: string }) => {
  const classes = useStyles();
  const { name, color } = props;
  return (
    <div className={classes.legend__item__container}>
      <div style={{ backgroundColor: color }} className={classes.legend__item__icon} />
      <span className={classes.legend__item__text}>{name}</span>
    </div>
  );
};

export const ArrMomentumChart: React.FC<ArrMomentumChartProps> = (props) => {
  const classes = useStyles();

  const { arrMomentumChartData, currencyIsoCode } = props;

  const browserLocale = navigator.language ? navigator.language : 'en-US';
  const currencyFormatter = new Intl.NumberFormat(browserLocale, {
    style: 'currency',
    currency: currencyIsoCode,
  });

  const shortFormatter = new Intl.NumberFormat(browserLocale, {
    // @ts-ignore
    notation: 'compact',
    compactDisplay: 'short',
  });

  const chartWrapperRef = React.useRef<HTMLDivElement>(null);
  const { width, height } = useResizeDetector({
    refreshRate: 16,
    refreshMode: 'throttle',
    targetRef: chartWrapperRef,
  });
  const chartRef = React.useRef<HTMLDivElement>(null);

  const barsConstructor = (arrMomentumChartData: ArrMomentumChartData) => {
    return arrMomentumChartData.dataItems.map((item, idx) => ({
      name: item.name,
      type: 'bar',
      color: item.color,
      barMaxWidth: 20,
      stack: 'one',
      data: item.data.map((_) => _.ARR),
      emphasis: {
        focus: 'series',
      },
    }));
  };

  const getXAxisLabel = (xaxisValue: string) => {
    return (
      arrMomentumChartData.dateGroups.find((_) => _.date === xaxisValue)?.dateLabel || xaxisValue
    );
  };

  const lineConstructor = (arrMomentumChartData: ArrMomentumChartData) => {
    return {
      name: 'total',
      type: 'line',
      color: '#EF684A',
      smooth: true,
      showSymbol: true,
      symbol: 'circle',
      data: arrMomentumChartData.dataItems[0].data.map((_, i) => {
        const total = arrMomentumChartData.dataItems.reduce(
          (acc, item) => acc + item.data[i].ARR,
          0,
        );
        return total;
      }),
      tooltip: {
        trigger: 'item',
        show: true,
        position: 'bottom',
        axisPointer: {
          type: 'cross',
          axis: 'auto',
          animation: 'auto',
          animationDurationUpdate: 200,
          animationEasingUpdate: 'exponentialOut',
        },
        extraCssText: 'background-color: black; border: none; color: #F5F5F5;',
        formatter: (params: CallbackDataParams) => {
          return `
          <div style="display: flex;">
            <div style="${tooltipStyle}">           
              <div>${currencyFormatter.format(params.value as number)}</div>
              <div style="${tooltipFieldStyle}">TOTAL ARR</div>
            </div>
            <div style="${tooltipStyle}">
                <div>${getXAxisLabel(params.name as string)}</div>
                <div style="${tooltipFieldStyle}">Date</div>
              </div>
          </div>
          `;
        },
      },
    };
  };

  const tooltipStyle =
    'display: flex; flex-direction: column; align-items: center; color: white; padding: 5px; min-width: 40px;';
  const tooltipFieldStyle = 'font-size: 10px; text-decoration: uppercase;';

  React.useEffect(() => {
    const options: EChartsOption = {
      legend: {
        data: arrMomentumChartData.dataItems.map((i) => i.name),
        left: '10%',
        icon: 'circle',
      },
      dataZoom: [
        {
          type: 'inside',
        },
        {
          type: 'slider',
        },
      ],
      tooltip: {
        trigger: 'item',
        show: true,
        extraCssText: 'background-color: black; border: none; color: #F5F5F5;',
        formatter: (params: TopLevelFormatterParams) => {
          const {
            // @ts-ignore
            seriesIndex,
            // @ts-ignore
            seriesName,
            // @ts-ignore
            dataIndex,
          } = params;

          const dateLabel = arrMomentumChartData?.dateGroups?.[dataIndex]?.dateLabel || '';
          const changeInCMRR =
            arrMomentumChartData?.dataItems?.[seriesIndex]?.data?.[dataIndex]?.CMRR || 0;
          const changeInArr =
            arrMomentumChartData?.dataItems?.[seriesIndex]?.data?.[dataIndex]?.ARR || 0;
          return `
            <div style="display: flex;">
              <div style="${tooltipStyle}">
                <div>${dateLabel}</div>
                <div style="${tooltipFieldStyle}">DATE</div>
              </div>
              <div style="${tooltipStyle}">
                <div>${currencyFormatter.format(
                  arrMomentumChartData.dataItems.reduce(
                    (pre, cur) => pre + cur.data[dataIndex].ARR,
                    0,
                  ),
                )}</div>
                <div style="${tooltipFieldStyle}">TOTAL ARR</div>
              </div>
              <div style="${tooltipStyle}">
                <div>${seriesName}</div>
                <div style="${tooltipFieldStyle}">CHANGE TYPE</div>
              </div>
              <div style="${tooltipStyle}">
                <div>${currencyFormatter.format(changeInCMRR)}</div>
                <div style="${tooltipFieldStyle}">CHANGE IN CMRR</div>
              </div>
              <div style="${tooltipStyle}">
                <div>${currencyFormatter.format(changeInArr)}</div>
                <div style="${tooltipFieldStyle}">CHANGE IN ARR</div>
              </div>
            </div>
          `;
        },
      },
      xAxis: {
        data: arrMomentumChartData.dateGroups.map((_) => _.date),
        axisLine: { onZero: true },
        splitLine: { show: false },
        splitArea: { show: false },
        axisLabel: {
          formatter: (value: string) => {
            return getXAxisLabel(value as string);
          },
        },
      },
      yAxis: {
        name: `ARR (${currencyIsoCode})`,
        type: 'value',
        axisLabel: {
          formatter: (value: number) => {
            // @ts-ignore
            return shortFormatter.format(value);
          },
        },
      },
      // @ts-ignore
      series: [...barsConstructor(arrMomentumChartData), lineConstructor(arrMomentumChartData)],
    };
    const chartDiv = chartRef.current;

    if (chartDiv) {
      echarts.dispose(chartDiv);
      // Remove the old chart
      let fc = chartDiv.firstChild;
      while (fc) {
        chartDiv.removeChild(fc);
        fc = chartDiv.firstChild;
      }
    }

    if (chartDiv && chartDiv.children.length === 0) {
      // on salesforce side, avoid being called multiple times
      const chart = echarts.init(chartDiv, undefined, { renderer: 'svg' });
      chart.setOption(options);
      // @ts-ignore
    }
  }, [arrMomentumChartData]);

  React.useEffect(() => {
    const chartDiv = chartRef.current;
    chartDiv && echarts.getInstanceByDom(chartDiv)?.resize();
  }, [width, height]);

  return (
    <div style={{ width: '100%', height: '100%' }} ref={chartWrapperRef}>
      <div
        ref={chartRef}
        style={{
          width: '100%',
          height: '100%',
        }}
      />
    </div>
  );
};

export default ArrMomentumChart;

import React, { useMemo, useState } from 'react';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';
import { Props, ActionItem, SelectedActionItem, ActionType } from './types';
import { initialState } from './constants';
import ActionSection from './ActionSection';
import ActionItems from './ActionItems';
import {
  ButtonBar,
  ChangeGroup,
  ChangeItem,
  ModalAppBar,
  RubyButtonBar,
} from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import {
  convertActionToChangeType,
  createChangeItems,
} from '../../subscription-card/subscription-card-util';
import { convertTerm } from '../../util';
import { useProductRelations } from '../../customer-view/hooks';
import { getBulkActionItems } from './utils';
import { Dialog, DialogContent, DialogTitle, Typography } from '@material-ui/core';
import { useStyles } from './styles';

const SubscriptionBulkActions = ({
  open,
  subscriptions,
  handleClose,
  onSubmit,
  submitButtonText,
  subscriptionMetadata,
  rubySettings,
  setChangeModalProps,
  getSwapUpgradeDowngradeOptions,
}: Props) => {
  const classes = useStyles();
  const [actionItems, setActionItems] = useState<ActionItem[]>([]);
  const [selectedUpgrade, setSelectedUpgrade] = useState<SelectedActionItem>(initialState.UPGRADE);
  const [selectedDowngrade, setSelectedDowngrade] = useState<SelectedActionItem>(
    initialState.DOWNGRADE,
  );
  const [selectedSwap, setSelectedSwap] = useState<SelectedActionItem>(initialState.SWAP);

  const { productRelations, productRelationsByActionType } = useProductRelations(
    subscriptions,
    getSwapUpgradeDowngradeOptions,
  );

  const handleAdd = (type: ActionType) => {
    if (!productRelations) return;
    const selectedRule =
      type === 'upgrade'
        ? selectedUpgrade
        : type === 'downgrade'
          ? selectedDowngrade
          : selectedSwap;

    const newItems: ActionItem[] = [];

    for (const subscription of subscriptions) {
      const relationsBySubscriptionId = productRelations[subscription.id];
      if (!relationsBySubscriptionId) continue;

      const targetProductRelations = relationsBySubscriptionId[type] ?? [];
      for (const targetProductRelation of targetProductRelations) {
        const matchedActionItem = getBulkActionItems(
          subscription,
          selectedRule,
          targetProductRelation,
          type,
        );
        if (matchedActionItem) {
          newItems.push(matchedActionItem);
        }
      }
    }

    setActionItems([...actionItems, ...newItems]);

    // Reset the form
    if (type === 'upgrade') {
      setSelectedUpgrade(initialState.UPGRADE);
    } else if (type === 'downgrade') {
      setSelectedDowngrade(initialState.DOWNGRADE);
    } else {
      setSelectedSwap(initialState.SWAP);
    }
  };

  const handleRemove = (index: number) => {
    setActionItems((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = () => {
    const cartItems = actionItems
      .map((actionItem) => {
        const subscription = subscriptions.find((sub) => sub.id === actionItem.subscriptionId);
        if (!subscription) return;
        const changeGroup: ChangeGroup = {
          id: `${subscription.customerId}-${subscription.priceBookId}-${subscription.currencyIsoCode}`,
          customer: {
            name: subscription.customer.name,
            id: subscription.customerId,
          },
          name: subscription.name,
          priceBook: {
            id: subscription.priceBookId,
            name: subscription.priceBook.name,
          },
          currencyIsoCode: subscription.currencyIsoCode,
        };

        const changeItem: ChangeItem = {
          id: `${subscription.id}-${actionItem.type}`,
          requestWasEdited: false,
          time: dayjs().format('MM/DD/YYYY hh:mm A'),
          asset: subscription,
          subscriptionMetadata,
          cardAction: {
            id: actionItem.type,
            name: actionItem.type,
            description: '',
          },
          request: {
            assetName: subscription.name,
            changeAction: 'add',
            changeType: convertActionToChangeType(actionItem.type),
            objectType: 'Subscription',
            priceBookId: subscription.priceBookId,
            subscriptionEndDate: subscription.subscriptionEndDate,
            subscriptionStartDate: subscription.subscriptionStartDate,
            subscriptionTerm: subscription.product.defaultSubscriptionTerm
              ? convertTerm(
                  subscription.product.defaultSubscriptionTerm,
                  subscription.product.defaultUom?.termDimension,
                  subscription.uom.termDimension,
                )
              : 1,
            renewalTerm: subscription.product.defaultRenewalTerm
              ? convertTerm(
                  subscription.product.defaultRenewalTerm,
                  subscription.product.defaultUom?.termDimension,
                  subscription.uom.termDimension,
                )
              : 1,
          },
        };

        const newChangeItems = createChangeItems({
          isEditingChangeItem: changeItem.requestWasEdited,
          subscriptionMetadata,
          cardAction: changeItem.cardAction,
          subscriptions: [subscription],
          parentSubscription: null,
          cotermDate: '',
          quantityAtStartDate: subscription.quantity || 1,
          handleErrorValidation: (changeItemRequestChanges: Record<string, any>) => {
            return undefined;
          },
          changeItemId: changeItem.id,
          rubySettings,
          values: actionItem,
        });
        return {
          changeGroup,
          changeItems: newChangeItems,
        };
      })
      .filter(
        (item): item is { changeGroup: ChangeGroup; changeItems: ChangeItem[] } =>
          item !== undefined,
      );

    onSubmit(cartItems);
    setChangeModalProps(null);
  };

  const actions = useMemo(() => {
    return [
      {
        type: 'upgrade',
        selected: selectedUpgrade,
        setSelected: setSelectedUpgrade,
        actionItems: actionItems.filter((item) => item.type === 'upgrade'),
      },
      {
        type: 'downgrade',
        selected: selectedDowngrade,
        setSelected: setSelectedDowngrade,
        actionItems: actionItems.filter((item) => item.type === 'downgrade'),
      },
      {
        type: 'swap',
        selected: selectedSwap,
        setSelected: setSelectedSwap,
        actionItems: actionItems.filter((item) => item.type === 'swap'),
      },
    ];
  }, [selectedUpgrade, selectedDowngrade, selectedSwap, actionItems]);

  return (
    <MuiPickersUtilsProvider utils={DateFnsUtils}>
      <Dialog
        open={open}
        fullScreen
        fullWidth
        title="Subscription Bulk Actions"
        onClose={handleClose}
      >
        <DialogTitle className={classes.dialogTitle}>
          <ModalAppBar handleClose={handleClose} />
        </DialogTitle>
        <DialogContent>
          <Typography variant="h2" className={classes.dialogHeader}>
            Subscription Bulk Actions
          </Typography>
          {actions.map((action) => (
            <ActionSection
              key={action.type}
              type={action.type as ActionType}
              selected={action.selected}
              setSelected={action.setSelected}
              actionItems={action.actionItems}
              handleAdd={handleAdd}
              productRelationsByActionType={productRelationsByActionType[action.type]}
            />
          ))}
          <ActionItems actionItems={actionItems} handleRemove={handleRemove} />
          <ButtonBar>
            <RubyButtonBar
              variant="inPlace"
              buttonSize="large"
              leftButtons={[
                {
                  text: 'Cancel',
                  onClick: handleClose,
                },
              ]}
              rightButtons={[
                {
                  text: submitButtonText,
                  onClick: handleSubmit,
                  disabled: actionItems.length === 0,
                },
              ]}
            />
          </ButtonBar>
        </DialogContent>
      </Dialog>
    </MuiPickersUtilsProvider>
  );
};

export default SubscriptionBulkActions;

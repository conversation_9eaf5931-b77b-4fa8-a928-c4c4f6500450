import React from 'react';

import { Table as TableBase } from '@devexpress/dx-react-grid';
import { Table } from '@devexpress/dx-react-grid-material-ui';

import { NSRowEditStateFlag } from './interface';

const styles: {
  [key: string]: React.CSSProperties;
} = {
  deleted: {
    backgroundColor: '#D2D3DE',
    textDecoration: 'line-through',
  },
  changed: {
    backgroundColor: '#E6FCF9',
  },
  added: {
    backgroundColor: '#F1EAFE',
  },
};

const TableRow: React.FC<TableBase.DataRowProps> = ({ row, ...restProps }) => {
  let style: React.CSSProperties = {};
  if (row[NSRowEditStateFlag]) {
    style = styles[row[NSRowEditStateFlag]];
  }
  return <Table.Row {...restProps} row={row} style={{ ...style }} />;
};

export default TableRow;

import React from 'react';

import {
  SubscriptionRevenueStreamDataItem as DataItem,
  TimeDimension,
} from '../subscription-revenue-stream-chart';
import { OrderChangedDateItem as DateGroup } from '../subscription-revenue-stream-chart';

export interface ArrMomentumChartDataItem {
  type: string;
  name: string;
  color: string; //RGB or HEX format
  data: DataItem[];
}

export interface ArrMomentumChartData {
  dataItems: ArrMomentumChartDataItem[];
  dateGroups: DateGroup[];
}

export interface MetricData {
  value: number;
  currencyIsoCode: string;
  change?: number;
}

export interface ArrMomentumChartProps {
  arrMomentumChartData: ArrMomentumChartData;
  currencyIsoCode: string;
}

export interface ArrMomentumDataService {
  getData: (
    params: {
      startDate?: string;
      endDate?: string;
      timeDimension?: TimeDimension;
      currency?: string;
    },
    onLoad?: (startDate: string, endDate: string) => void,
  ) => Promise<ArrMomentumChartData | null>;

  getMetricData: (metricName: string) => Promise<MetricData | null>;
}

export const ArrMomentumDataContext = React.createContext<ArrMomentumDataService | null>(null);

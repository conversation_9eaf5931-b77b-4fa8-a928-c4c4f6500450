import { Theme } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';

const useDateInputStyles = makeStyles((theme: Theme) => ({
  // TODO: There's a bug here where on @nue-apps/ruby-ui-component the border is shown twice on filter panel
  // because of the border and the app theme doesn't match up. Somehow we need to show the styling
  // without using the className here, similar to how it's done in ruby-ui
  root: {
    border: '1px solid #ced4da',
    borderRadius: '4px',
    marginTop: theme.spacing(1),
    '& .Mui-disabled': {
      backgroundColor: '#fafafb',
    },
    '&:focus-within': {
      borderColor: theme.palette.secondary.main,
    },
    '& input': {
      fontSize: '.875rem',
      padding: '12px 20px',
    },
    '& fieldset': {
      border: '0px',
    },
    '& button': {
      color: '#6239EB',
      opacity: 0.7,
      padding: '8px',
    },
    '& button:hover': {
      color: '#6239EB',
      opacity: 1,
    },
  },
}));

export default useDateInputStyles;

import { useCallback } from 'react';

import * as yup from 'yup';

import { Ruby<PERSON>ield, RubyObject } from '../metadata/interface';
import { getTextByLocale } from '../self-service/helpers/translationHelper';

export const useYupValidationResolver = (validationSchema: yup.ObjectSchema) => {
  const validateObject = (data: any) => {
    return validationSchema
      .validate(data, { abortEarly: false })
      .then((values) => {
        console.debug('validation passed');
        return {
          values,
          errors: {},
        };
      })
      .catch((errors) => {
        const errorResult = {
          values: {},
          errors: errors.inner.reduce(
            (allErrors: yup.ValidationError, currentError: yup.ValidationError) => ({
              ...allErrors,
              [currentError.path]: {
                type: currentError.type ?? 'validation',
                message: currentError.message,
              },
            }),
            {},
          ),
        };
        console.debug('validation failed, errors:', errors);
        return errorResult;
      });
  };
  return useCallback(validateObject, [validationSchema]);
};

const buildStringValidator = (field: RubyField, objectValidationSchema: any) => {
  let rule = yup.string().nullable();
  if (field.required) {
    let fieldName;
    if (field.type === 'Reference') {
      fieldName = field?.lookupRelation?.relationLabel;
    } else {
      fieldName = field.name;
    }
    rule = rule.required(`${fieldName} is required`);
  }
  objectValidationSchema[field.apiName] = rule;
};

const buildEmailValidator = (field: RubyField, objectValidationSchema: any) => {
  let rule = yup.string().nullable().email(`${field.name} must be a valid email`);
  if (field.required) {
    rule = rule.required(`${field.name} is required`);
  }
  objectValidationSchema[field.apiName] = rule;
};

export const buildValidationSchema = (rubyObject: RubyObject, includedFields?: string[]) => {
  let objectValidationSchema = {};
  const { fields } = rubyObject;
  fields?.forEach((field) => {
    //todo add validation for other fields
    if (includedFields && includedFields.indexOf(field.apiName) < 0) {
      return;
    }

    switch (field.type) {
      case 'Reference':
      case 'URL':
      case 'Date':
      case 'DateTime':
      case 'Picklist':
      case 'RadioGroup':
      case 'String':
      case 'pickList':
      case 'integer':
      case 'date':
        buildStringValidator(field, objectValidationSchema);
        break;
      case 'Email':
        buildEmailValidator(field, objectValidationSchema);
        break;
    }
  });
  return yup.object(objectValidationSchema);
};

const buildSelfServiceStringValidator = (field: any, objectValidationSchema: any) => {
  let rule = yup.string().nullable();
  if (field.required) {
    //@ts-ignore
    const fieldName = getTextByLocale(field.text);

    rule = rule.required(`${fieldName} is required`);
  }
  objectValidationSchema[field.id] = rule;
};

const buildSelfServiceEmailValidator = (field: any, objectValidationSchema: any) => {
  const fieldName = getTextByLocale(field.text);

  let rule = yup.string().nullable().email(`${fieldName} must be a valid email`);
  if (field.required) {
    rule = rule.required(`${fieldName} is required`);
  }
  objectValidationSchema[field.id] = rule;
};

export const buildSelfServiceValidationSchema = (formFieldMetadata: any) => {
  const objectValidationSchema = {};
  // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
  //@ts-ignore
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  formFieldMetadata?.forEach((field) => {
    if (!field.editable) {
      return;
    }
    switch (field.type) {
      case 'text':
      case 'date':
      case 'phone':
        buildSelfServiceStringValidator(field, objectValidationSchema);
        break;
      case 'email':
        buildSelfServiceEmailValidator(field, objectValidationSchema);
        break;
    }
  });
  return yup.object(objectValidationSchema);
};

export default { buildValidationSchema, useYupValidationResolver };

import React from 'react';
import type { RubyObject } from '../metadata';
import type {
  PaymentRuleSchema,
  InvoiceActionTarget,
  PaymentRule,
  BillSysSetting,
} from './interface';

export interface CollectionsService {
  getPaymentRuleList: (currentPage: number, pageSize: number) => Promise<PaymentRuleSchema[]>;
  deletePaymentRule: (paymentRuleId: string) => Promise<void>;
  activePaymentRule: (paymentRuleId: string) => Promise<void>;
  cancelPaymentRule: (paymentRuleId: string) => Promise<void>;
  duplicatePaymentRule: (paymentRuleId: string) => Promise<PaymentRule>;
  createPaymentRule: (paymentRule: object) => Promise<void>;
  updatePaymentRule: (paymentRule: object) => Promise<void>;
  metadataObjects: Array<RubyObject>;
  getInvoiceActionTargetList: () => Promise<InvoiceActionTarget[]>;
  getBillSystemSettings: () => Promise<BillSysSetting[]>;
  saveBillSystemSettings: (settings: BillSysSetting[]) => Promise<void>;
  getSpecifyRelatedObjects: Record<string, string[]>;
}
export const CollectionsContext = React.createContext<CollectionsService | null>(null);

export default CollectionsContext;

import { buildSubscriptionHierarchy } from '../../utils/utils';

describe('buildSubscriptionHierarchy', () => {
  const mockSubscriptions = [
    {
      id: 'root1',
      name: 'Root 1',
      productName: 'Product 1',
    },
    {
      id: 'root2',
      name: 'Root 2',
      productName: 'Product 2',
    },
    {
      id: 'child1',
      parentId: 'root1',
      name: 'Child 1',
      productName: 'Product 3',
    },
    {
      id: 'child2',
      parentId: 'root1',
      name: 'Child 2',
      productName: 'Product 4',
    },
    {
      id: 'grandchild1',
      parentId: 'child1',
      name: 'Grandchild 1',
      productName: 'Product 5',
    },
  ];

  it('should build correct hierarchy without search result', () => {
    const result = buildSubscriptionHierarchy(mockSubscriptions, '');
    expect(result).toHaveLength(2); // Two root nodes
    expect(result[0].children).toHaveLength(2); // Two children under root1
    expect(result[0].children![0].children).toHaveLength(1); // One grandchild
    expect(result[1].children).toBeUndefined(); // No children under root2
  });

  it('should mark path nodes as disabled when exact match found by name', () => {
    const result = buildSubscriptionHierarchy(mockSubscriptions, 'Grandchild 1');
    expect(result[0].disabled).toBe(true); // Root1 should be disabled
    expect(result[0].children![0].disabled).toBe(true); // Child1 should be disabled
    expect(result[0].children![0].children![0].disabled).toBeUndefined(); // Grandchild1 should not be disabled
  });

  it('should mark path nodes as disabled when exact match found by productName', () => {
    const result = buildSubscriptionHierarchy(mockSubscriptions, 'Product 5');
    expect(result[0].disabled).toBe(true);
    expect(result[0].children![0].disabled).toBe(true);
    expect(result[0].children![0].children![0].disabled).toBeUndefined();
  });

  it('should only include relevant paths when exact match found', () => {
    const result = buildSubscriptionHierarchy(mockSubscriptions, 'Child 1');
    expect(result).toHaveLength(1); // Only root1 should be included
    expect(result[0].children).toHaveLength(1); // Only child1 should be included
  });

  it('should handle empty subscriptions array', () => {
    const result = buildSubscriptionHierarchy([], '');
    expect(result).toHaveLength(0);
  });

  it('should handle single node without children', () => {
    const result = buildSubscriptionHierarchy([mockSubscriptions[0]], '');
    expect(result).toHaveLength(1);
    expect(result[0].children).toBeUndefined();
  });

  it('should handle multiple matches', () => {
    const subscriptionsWithDuplicates = [
      ...mockSubscriptions,
      {
        id: 'child3',
        parentId: 'root2',
        name: 'Child 1', // Same name as child1
        productName: 'Product 6',
      },
    ];

    const result = buildSubscriptionHierarchy(subscriptionsWithDuplicates, 'Child 1');
    // Both roots should be included
    expect(result).toHaveLength(2);
    expect(result[0].children!.some((child) => child.name === 'Child 1')).toBe(true);
    expect(result[1].children!.some((child) => child.name === 'Child 1')).toBe(true);
  });

  it('should preserve all subscription properties', () => {
    const subscriptionWithExtraProps = {
      id: 'test',
      name: 'Test',
      productName: 'Product',
      customField: 'value',
      status: 'active',
    };

    const result = buildSubscriptionHierarchy([subscriptionWithExtraProps], '');
    expect(result[0]).toEqual(expect.objectContaining(subscriptionWithExtraProps));
  });

  it('should handle circular references gracefully', () => {
    const circularSubscriptions = [
      {
        id: 'node1',
        name: 'Node 1',
        productName: 'Product 1',
        parentId: 'node2',
      },
      {
        id: 'node2',
        name: 'Node 2',
        productName: 'Product 2',
        parentId: 'node1',
      },
    ];

    const result = buildSubscriptionHierarchy(circularSubscriptions, '');
    // Should return empty array for circular reference
    expect(result).toHaveLength(0);
  });

  it('should show children of exact match and not disable them', () => {
    // exact match is 'Child 1', its parent is 'Root 1', its child is 'Grandchild 1'
    const result = buildSubscriptionHierarchy(mockSubscriptions, 'Child 1');
    // Only root1 should be included
    expect(result).toHaveLength(1);
    // root1 should be disabled
    const root1 = result.find((r) => r.id === 'root1');
    expect(root1?.disabled).toBe(true);
    // child1 should be included and not disabled
    expect(root1?.children).toHaveLength(1);
    expect(root1?.children![0].name).toBe('Child 1');
    expect(root1?.children![0].disabled).toBeUndefined();
    // child1's child (grandchild1) should also be included and not disabled
    expect(root1?.children![0].children).toHaveLength(1);
    expect(root1?.children![0].children![0].name).toBe('Grandchild 1');
    expect(root1?.children![0].children![0].disabled).toBeUndefined();
  });

  it('should show all direct children of exact match and not disable them', () => {
    // Add more children to 'Child 1'
    const subscriptionsWithMoreChildren = [
      ...mockSubscriptions,
      {
        id: 'grandchild2',
        parentId: 'child1',
        name: 'Grandchild 2',
        productName: 'Product 7',
      },
    ];
    const result = buildSubscriptionHierarchy(subscriptionsWithMoreChildren, 'Child 1');
    // Only root1 should be included
    expect(result).toHaveLength(1);
    // child1 should have two children now
    expect(result[0].children![0].children).toHaveLength(2);
    // Both children should not be disabled
    expect(result[0].children![0].children![0].disabled).toBeUndefined();
    expect(result[0].children![0].children![1].disabled).toBeUndefined();
  });

  it('should not disable exact match node or its children, but disable ancestors', () => {
    // exact match is 'Grandchild 1', its parent is 'Child 1', grandparent is 'Root 1'
    const result = buildSubscriptionHierarchy(mockSubscriptions, 'Grandchild 1');
    // root1 and child1 should be disabled, grandchild1 should not
    expect(result[0].disabled).toBe(true);
    expect(result[0].children![0].disabled).toBe(true);
    expect(result[0].children![0].children![0].disabled).toBeUndefined();
  });

  it('should show direct children of exact match even if they are not on ancestor path', () => {
    // Add a sibling to 'Grandchild 1' under 'Child 1'
    const subscriptionsWithSibling = [
      ...mockSubscriptions,
      {
        id: 'grandchild2',
        parentId: 'child1',
        name: 'Grandchild 2',
        productName: 'Product 7',
      },
    ];
    const result = buildSubscriptionHierarchy(subscriptionsWithSibling, 'Child 1');
    // Both grandchild1 and grandchild2 should be present and not disabled
    const child1 = result[0].children![0];
    expect(child1.children).toHaveLength(2);
    expect(child1.children!.some((c) => c.name === 'Grandchild 1')).toBe(true);
    expect(child1.children!.some((c) => c.name === 'Grandchild 2')).toBe(true);
    expect(child1.children!.every((c) => c.disabled === undefined)).toBe(true);
  });

  it('should show all descendants of exact match and not disable them (deep tree)', () => {
    // Add a deeper descendant to 'Child 1'
    const subscriptionsWithDeepDescendant = [
      ...mockSubscriptions,
      {
        id: 'grandgrandchild1',
        parentId: 'grandchild1',
        name: 'GrandGrandchild 1',
        productName: 'Product 8',
      },
    ];
    const result = buildSubscriptionHierarchy(subscriptionsWithDeepDescendant, 'Child 1');
    // Only root1 should be included
    expect(result).toHaveLength(1);
    // child1 should have children
    expect(result[0].children![0].name).toBe('Child 1');
    // grandchild1 should be present
    expect(result[0].children![0].children!.some((c) => c.name === 'Grandchild 1')).toBe(true);
    // grandgrandchild1 should be present under grandchild1
    const grandchild1 = result[0].children![0].children!.find((c) => c.name === 'Grandchild 1');
    expect(grandchild1).toBeDefined();
    expect(grandchild1!.children!.some((c) => c.name === 'GrandGrandchild 1')).toBe(true);
    // All descendants should not be disabled
    expect(result[0].children![0].disabled).toBeUndefined();
    expect(grandchild1!.disabled).toBeUndefined();
    expect(grandchild1!.children![0].disabled).toBeUndefined();
  });

  it('should show all descendants of exact match even if they are at different levels', () => {
    // Add multiple levels of descendants to 'Child 2'
    const subscriptionsWithMultiLevelDescendants = [
      ...mockSubscriptions,
      {
        id: 'grandchild2',
        parentId: 'child2',
        name: 'Grandchild 2',
        productName: 'Product 9',
      },
      {
        id: 'grandgrandchild2',
        parentId: 'grandchild2',
        name: 'GrandGrandchild 2',
        productName: 'Product 10',
      },
    ];
    const result = buildSubscriptionHierarchy(subscriptionsWithMultiLevelDescendants, 'Child 2');
    // Only root1 should be included
    expect(result).toHaveLength(1);
    // child2 should be present and not disabled
    const child2 = result[0].children!.find((c) => c.name === 'Child 2');
    expect(child2).toBeDefined();
    expect(child2!.disabled).toBeUndefined();
    // grandchild2 should be present and not disabled
    const grandchild2 = child2!.children!.find((c) => c.name === 'Grandchild 2');
    expect(grandchild2).toBeDefined();
    expect(grandchild2!.disabled).toBeUndefined();
    // grandgrandchild2 should be present and not disabled
    expect(
      grandchild2!.children!.find((c) => c.name === 'GrandGrandchild 2')!.disabled,
    ).toBeUndefined();
  });

  it('should not disable exact match node or any of its descendants, but disable ancestors', () => {
    // exact match is 'Child 1', its parent is 'Root 1', its child is 'Grandchild 1'
    // Add a deeper descendant
    const subscriptionsWithDeepDescendant = [
      ...mockSubscriptions,
      {
        id: 'grandgrandchild1',
        parentId: 'grandchild1',
        name: 'GrandGrandchild 1',
        productName: 'Product 8',
      },
    ];
    const result = buildSubscriptionHierarchy(subscriptionsWithDeepDescendant, 'Child 1');
    // root1 should be disabled
    expect(result[0].disabled).toBe(true);
    // child1 and all its descendants should not be disabled
    const child1 = result[0].children![0];
    expect(child1.disabled).toBeUndefined();
    expect(child1.children!.some((c) => c.disabled === undefined)).toBe(true);
    const grandchild1 = child1.children!.find((c) => c.name === 'Grandchild 1');
    expect(grandchild1!.disabled).toBeUndefined();
    expect(
      grandchild1!.children!.find((c) => c.name === 'GrandGrandchild 1')!.disabled,
    ).toBeUndefined();
  });

  it('should show all descendants for multiple exact matches', () => {
    // Add another exact match node with its own descendants
    const subscriptionsWithMultipleMatches = [
      ...mockSubscriptions,
      {
        id: 'child3',
        parentId: 'root2',
        name: 'Child 1', // Same name as child1
        productName: 'Product 6',
      },
      {
        id: 'grandchild3',
        parentId: 'child3',
        name: 'Grandchild 3',
        productName: 'Product 11',
      },
    ];
    const result = buildSubscriptionHierarchy(subscriptionsWithMultipleMatches, 'Child 1');
    // Both roots should be included
    expect(result).toHaveLength(2);
    // root1's child1 and its descendants
    const child1 = result[0].children!.find((c) => c.name === 'Child 1');
    expect(child1).toBeDefined();
    expect(child1!.disabled).toBeUndefined();
    expect(child1!.children!.some((c) => c.name === 'Grandchild 1')).toBe(true);
    // root2's child3 and its descendants
    const child3 = result[1].children!.find((c) => c.name === 'Child 1');
    expect(child3).toBeDefined();
    expect(child3!.disabled).toBeUndefined();
    expect(child3!.children!.some((c) => c.name === 'Grandchild 3')).toBe(true);
  });
});

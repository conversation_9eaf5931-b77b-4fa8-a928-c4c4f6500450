import React, { useEffect, useRef, useState } from 'react';
import { usePopper } from 'react-popper';

import {
  InputAdornment,
  InputBase,
  List,
  ListItem,
  ListItemText,
  ListSubheader,
  Paper,
  Popper,
  TextField,
  withStyles,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Search } from '@material-ui/icons';

import ClickAwayListener from '../click-away-listener';
import { Props, SuggestionItem } from './interface';
import SuggestionItemRenderer from './suggestion-item-renderer';

const defaultProps: Partial<Props> = {
  suggestions: [],
  readOnly: false,
};
const useStyles = makeStyles((theme) => ({
  root: {
    marginTop: '8px',
    backgroundColor: '#EFEEF0',
    boxShadow: 'inset 0 1px 3px 0 rgba(0,0,0,0.05)',
    padding: '10px 22px 5px 12px',
    borderRadius: '4px',
    display: 'flex',
    '&:focus-within': {
      borderColor: theme.palette.secondary.main,
    },
  },
  readOnly: {
    backgroundColor: '#EFEEF0',
  },
  suggestionsWrapper: {
    zIndex: 400,
    top: '12px',
    left: '-12px',
  },
  subheader: {
    fontSize: '12px',
    lineHeight: '1.5',
    padding: theme.spacing(1, 2),
    position: 'sticky',
    top: 0,
    backgroundColor: '#ffffff',
    zIndex: 1,
  },
}));
const InputWrapper = withStyles((theme) => ({
  root: {},
  input: {
    position: 'relative',
    fontSize: '.875rem',
    fontWeight: 500,
    color: '#4d4c50',
    width: '100%',
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    // Use the system font instead of the default Roboto font.
    fontFamily: 'inherit',
  },
}))(InputBase);

const Autocomplete: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const classes = useStyles();
  const {
    placeholder,
    suggestions,
    value,
    onChange,
    getSuggestionItemLabel,
    getSuggestionItemValue,
    getSelectedItemLabel,
    async,
    asyncFetchSuggestions,
    asyncFetchSelectedItem,
    readOnly,
    disabled,
    endAdornment,
    style,
    inputStyle = {},
    setSuggestionValue,
    useDorpdownPopper,
    poperStyle,
    getFocusElement,
    isClickToShowDirectly = false,
  } = props;

  if (async === true && !asyncFetchSuggestions) {
    console.error(`Prop asyncFetchSuggestions is required when async == true`);
  }

  const referenceElement = useRef(null);
  const refPopperElement = useRef<HTMLDivElement | null>(null);
  const refArrowElement = useRef(null);
  const { styles, attributes } = usePopper(referenceElement.current, refPopperElement.current, {
    modifiers: [
      { name: 'arrow', options: { element: refArrowElement.current } },
      {
        name: 'flip',
        options: {
          flipVariations: false,
          fallbackPlacements: ['bottom'],
          allowedAutoPlacements: ['bottom'],
        },
      },
    ],
    placement: 'bottom-start',
  });

  const [inputValue, setInputValue] = useState('');
  const [suggestionItems, setSuggestionItems] = useState<Array<SuggestionItem>>();
  const [loading, setLoading] = useState(false);
  const [lastRequestId, setLastRequestId] = useState<any>(null);
  const [open, setOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(0);

  function updateSuggestionItems(suggestions: Array<string | object> | undefined) {
    let _suggestion: Array<SuggestionItem>;
    if (suggestions) {
      _suggestion = suggestions?.map((suggestion) => {
        if (typeof suggestion === 'string') {
          return { label: suggestion, value: suggestion, category: '' };
        } else {
          const formattedSuggestion = {
            label: getSuggestionItemLabel
              ? getSuggestionItemLabel(suggestion)
              : JSON.stringify(suggestion),
            value: getSuggestionItemValue ? getSuggestionItemValue(suggestion) : suggestion,
            category: '',
            icon: typeof suggestion === 'object' && 'icon' in suggestion ? suggestion.icon : null,
            isDisabled:
              typeof suggestion === 'object' && 'isDisabled' in suggestion
                ? !!suggestion.isDisabled
                : undefined,
          };
          if ('category' in suggestion && typeof suggestion.category === 'string') {
            formattedSuggestion.category = suggestion.category;
          }
          return formattedSuggestion;
        }
      });
    } else {
      _suggestion = [];
    }
    console.debug('updated suggestion items', _suggestion);
    setSuggestionItems(_suggestion);
  }

  useEffect(() => {
    if (async) {
      //todo should fetch the selectedItem by value first
      if (value && asyncFetchSelectedItem) {
        asyncFetchSelectedItem(value).then((selectedItem) => {
          setInputValue((getSelectedItemLabel || getSuggestionItemLabel)(selectedItem));
        });
      }
      setSuggestionItems([]);
    } else {
      updateSuggestionItems(suggestions);
    }

    if (value && suggestions && suggestions.length > 0) {
      const selectedItem = suggestions?.find((x) => getSuggestionItemValue(x) === value);
      if (selectedItem) {
        setInputValue((getSelectedItemLabel || getSuggestionItemLabel)(selectedItem));
      } else {
        console.error(`The value ${value} is not found in suggestion list you provided,`);
      }
    } else {
      setInputValue('');
    }
  }, [value, suggestions]);

  useEffect(() => {
    setSuggestionValue?.(
      open
        ? suggestionItems?.filter(
            ({ label }) => label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0,
          )?.[highlightedIndex]?.value
        : null,
    );
  }, [highlightedIndex, setSuggestionValue, open, inputValue, suggestionItems]);

  function triggerItemSelected(label: string, value: any, index: number) {
    onChange({ label, value, index });
    if (!!value) {
      setOpen(false);
    }
    setInputValue(label);
  }

  function renderSuggestionItems() {
    if (loading) {
      return (
        <List dense={true}>
          <ListItem>
            <ListItemText primary={'Loading ...'} />
          </ListItem>
        </List>
      );
    } else {
      const hasCategory =
        suggestionItems &&
        suggestionItems.some((item) => item.category && item.category.trim() !== '');
      if (hasCategory) {
        const groupedItems = suggestionItems.reduce(
          (acc, item) => {
            const category =
              item.category && item.category.trim() !== '' ? item.category : 'OTHERS';
            if (!acc[category]) {
              acc[category] = [];
            }
            acc[category].push(item);
            return acc;
          },
          {} as { [key: string]: Array<SuggestionItem> },
        );
        let overallIndex = 0;
        return (
          <List dense={true}>
            {Object.keys(groupedItems).map((category, categoryIndex) => (
              <React.Fragment key={categoryIndex}>
                {category !== 'noCategory' && (
                  <ListSubheader className={classes.subheader}>{category}</ListSubheader>
                )}
                {groupedItems[category].map(({ label, value, icon, isDisabled }, index) => {
                  const itemIndex = overallIndex++;
                  return (
                    <SuggestionItemRenderer
                      label={label}
                      value={value}
                      key={`${category}-${index}`}
                      index={itemIndex}
                      highlightedIndex={highlightedIndex}
                      setHighlightedIndex={setHighlightedIndex}
                      currentInput={inputValue}
                      onSelect={({ label, value }) => {
                        triggerItemSelected(label, value, itemIndex);
                      }}
                      icon={icon}
                      isDisabled={isDisabled}
                    />
                  );
                })}
              </React.Fragment>
            ))}
          </List>
        );
      } else {
        return (
          suggestionItems &&
          suggestionItems.length > 0 && (
            <List dense={true}>
              {suggestionItems
                .filter((item) => {
                  return (
                    !inputValue ||
                    props.async ||
                    item.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                  );
                })
                .map(({ label, value, icon, isDisabled }, index) => (
                  <SuggestionItemRenderer
                    label={label}
                    value={value}
                    key={index}
                    index={index}
                    highlightedIndex={highlightedIndex}
                    setHighlightedIndex={setHighlightedIndex}
                    currentInput={inputValue}
                    onSelect={({ label, value }) => {
                      triggerItemSelected(label, value, index);
                    }}
                    icon={icon}
                    isDisabled={isDisabled}
                  />
                ))}
            </List>
          )
        );
      }
    }
  }

  const inputChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    fetchAndShowOptions(newValue);
  };

  const fetchAndShowOptions = (newValue: string) => {
    if (!open) {
      setOpen(true);
    }
    setInputValue(newValue);
    setHighlightedIndex(0);
    if (async && asyncFetchSuggestions) {
      if (lastRequestId !== null) {
        clearTimeout(lastRequestId);
      }
      setLoading(true);
      let _lastRequestId = setTimeout(async () => {
        try {
          const result = await asyncFetchSuggestions(newValue);
          console.debug('async result fetched: ', result);
          updateSuggestionItems(result);
          setLoading(false);
        } catch (error) {
          console.error('Error handling auto-complete input change.', error);
        }
      }, 500);
      setLastRequestId(_lastRequestId);
    } else if (suggestions && suggestions?.length > 0) {
      updateSuggestionItems(suggestions);
    }
  };

  const paperSytle = {
    ...styles.popper,
    zIndex: 410,
    top: '12px',
    left: '-12px',
    maxHeight: 300,
    overflow: 'auto',
    overflowX: 'hidden',
    width: '100%',
  };
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  return (
    <ClickAwayListener onClickAway={() => setOpen(false)}>
      <div
        ref={refPopperElement}
        className={readOnly ? `${classes.root} ${classes.readOnly}` : classes.root}
        style={style}
      >
        <InputWrapper
          style={{ flex: '1', ...inputStyle }}
          placeholder={placeholder}
          value={inputValue}
          onChange={inputChangeHandler}
          onFocus={(event) => {
            if (!readOnly && !disabled && !value && !suggestionItems?.length) {
              fetchAndShowOptions(inputValue);
            } else if (!readOnly && !disabled && !value && isClickToShowDirectly) {
              fetchAndShowOptions(inputValue);
            }
          }}
          onKeyUp={(event) => {
            const key = event.key;
            if (key === 'Backspace' && inputValue.length === 0) {
              // set field to null when pressing backspace and input is empty
              triggerItemSelected('', null, -1);
            }
          }}
          onKeyDown={(event) => {
            //this is a workaround for showing the poper in the dialog. as we are using material ui 4.
            const element = getFocusElement ? getFocusElement(event) : event.currentTarget;
            setAnchorEl(element);
            const key = event.key;
            if (key === 'ArrowUp' && highlightedIndex > 0) {
              // Move focus up & down
              setHighlightedIndex(highlightedIndex - 1);
              event.preventDefault();
            } else if (key === 'ArrowDown') {
              const lastIndex = suggestionItems
                ? suggestionItems.filter(
                    ({ label }) => label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0,
                  ).length - 1
                : -1;
              if (lastIndex && lastIndex >= highlightedIndex + 1) {
                setHighlightedIndex(highlightedIndex + 1);
              }
              event.preventDefault();
            } else if (event.key === 'Enter') {
              const showingItems = suggestionItems?.filter(
                ({ label }) => label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0,
              );
              if (showingItems && showingItems[highlightedIndex]) {
                const { label, value } = showingItems[highlightedIndex];
                triggerItemSelected(label, value, highlightedIndex);
              } else {
                fetchAndShowOptions('');
              }
              event.preventDefault();
            }
          }}
          autoFocus={false}
          ref={referenceElement}
          startAdornment={
            <InputAdornment position="start" style={{ opacity: 0.5 }}>
              <Search />
            </InputAdornment>
          }
          readOnly={readOnly}
        />
        {endAdornment && endAdornment}
        {open && useDorpdownPopper && (
          <Popper open={open} anchorEl={anchorEl} style={poperStyle}>
            <Paper>
              <div ref={refArrowElement} style={styles.arrow} />
              {renderSuggestionItems()}
            </Paper>
          </Popper>
        )}
        {open && !useDorpdownPopper && (
          <Paper
            role="dialog"
            //@ts-ignore
            style={{ ...paperSytle }}
            {...attributes.popper}
          >
            <div ref={refArrowElement} style={styles.arrow} />
            {renderSuggestionItems()}
          </Paper>
        )}
      </div>
    </ClickAwayListener>
  );
};

export default Autocomplete;

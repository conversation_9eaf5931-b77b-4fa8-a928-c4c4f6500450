import React from 'react';

import { Box, DialogTitle, List, ListItem, ListItemText, Paper, Popover } from '@material-ui/core';
import dayjs from 'dayjs';

const InvoiceCommentPopupContent = ({ commentsBtnId, open, handleCommentsClose, invoice }: any) => {
  const id = invoice ? invoice.id : undefined;
  let comments = [];
  try {
    comments = invoice && invoice.comments ? JSON.parse(invoice.comments) : [];
  } catch (error) {
    console.error('Error parsing JSON:', error);
  }
  const popoverId = open ? `popover_comments_${id}` : undefined;
  return (
    <Popover
      id={popoverId}
      open={open}
      //@ts-ignore
      anchorEl={() => document.getElementById(commentsBtnId)}
      onClose={handleCommentsClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'center',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'center',
      }}
    >
      <Paper style={{ maxHeight: '300px', overflowY: 'hidden', width: '400px' }}>
        <DialogTitle>Comments</DialogTitle>
        <div style={{ maxHeight: '235px', overflowY: 'auto', padding: '0px 24px' }}>
          <List style={{ paddingTop: '0px' }} dense>
            {comments.map((item: any, index: any) => (
              <ListItem key={index} style={{ padding: '0px 0px' }}>
                <ListItemText
                  secondaryTypographyProps={{
                    style: {
                      fontWeight: 'normal',
                      color: '#333',
                    },
                  }}
                  primaryTypographyProps={{
                    style: {
                      fontWeight: 'normal',
                      color: '#666',
                    },
                  }}
                  primary={
                    dayjs(item.createTime).format('YYYY/MM/DD hh:mm A') +
                    ' - ' +
                    (item.createUserName ? item.createUserName : '')
                  }
                  secondary={item.comment}
                />
              </ListItem>
            ))}
          </List>
        </div>
      </Paper>
    </Popover>
  );
};

export default InvoiceCommentPopupContent;

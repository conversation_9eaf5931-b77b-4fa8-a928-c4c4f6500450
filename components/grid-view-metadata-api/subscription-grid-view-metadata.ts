import AccessTimeIcon from '@material-ui/icons/AccessTime';
import BarChartOutlinedIcon from '@material-ui/icons/BarChartOutlined';
import BusinessOutlinedIcon from '@material-ui/icons/BusinessOutlined';
import DescriptionOutlinedIcon from '@material-ui/icons/DescriptionOutlined';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import IndeterminateCheckBoxOutlinedIcon from '@material-ui/icons/IndeterminateCheckBoxOutlined';
import MonetizationOnOutlinedIcon from '@material-ui/icons/MonetizationOnOutlined';
import NoteAddOutlinedIcon from '@material-ui/icons/NoteAddOutlined';
import PauseCircleOutlineOutlinedIcon from '@material-ui/icons/PauseCircleOutlineOutlined';
import PlayCircleOutlineOutlinedIcon from '@material-ui/icons/PlayCircleOutlineOutlined';
import SettingsIcon from '@material-ui/icons/Settings';
import SkipNextIcon from '@material-ui/icons/SkipNext';
import TrendingUp from '@material-ui/icons/TrendingUp';
import ZoomInOutlinedIcon from '@material-ui/icons/ZoomInOutlined';
import PublishIcon from '@material-ui/icons/Publish';
import GetAppIcon from '@material-ui/icons/GetApp';
import SwapCallsIcon from '@material-ui/icons/SwapCalls';

import ActiveIcon from '../active-icon';
import DraftIcon from '../draft-icon';
import { AdjustPriceIcon, RenewWithQuantityUpdateIcon } from '../icons';
import type { Subscription } from '../revenue-builder-types';

export const subscriptionActions = [
  {
    id: 'updateQuantity',
    name: 'Update Quantity',
    description: 'Update Quantity',
    icon: FileCopyOutlinedIcon,
    showTooltip: true,
    toolTipText: 'Update Subscription Quantity',
    type: 'dialog',
  },
  {
    id: 'updateTerm',
    name: 'Update Term',
    description: 'Update Term',
    icon: IndeterminateCheckBoxOutlinedIcon,
    showTooltip: true,
    toolTipText: 'Update Subscription Term',
    type: 'dialog',
  },
  {
    id: 'pause',
    name: 'Pause',
    description: 'Pause Subscription',
    icon: PauseCircleOutlineOutlinedIcon,
  },
  {
    id: 'renew',
    name: 'Renew',
    description: 'Renew Subscription',
    icon: PlayCircleOutlineOutlinedIcon,
    showTooltip: true,
    toolTipText: 'Subscription Renewal',
    type: 'dialog',
  },
  {
    id: 'adjustPrice',
    name: 'Adjust Price',
    description: 'Adjust Subscription Price',
    icon: AdjustPriceIcon,
    showTooltip: true,
    toolTipText: 'Adjust Subscription Price',
  },
  {
    id: 'swap',
    name: 'Swap',
    description: 'Swap',
    icon: SwapCallsIcon,
    showTooltip: true,
    toolTipText: 'Swap',
    type: 'dialog',
  },
  {
    id: 'upgrade',
    name: 'Upgrade',
    description: 'Upgrade',
    icon: PublishIcon,
    showTooltip: true,
    toolTipText: 'Upgrade',
    type: 'dialog',
  },
  {
    id: 'downgrade',
    name: 'Downgrade',
    description: 'Downgrade',
    icon: GetAppIcon,
    showTooltip: true,
    toolTipText: 'Downgrade',
    type: 'dialog',
  },
  {
    id: 'skip',
    name: 'Skip',
    description: 'Skip Subscription',
    icon: SkipNextIcon,
  },
  {
    id: 'convertFreeTrial',
    name: 'Convert Free Trial',
    description: 'Convert Free Trial',
    icon: TrendingUp,
    showTooltip: true,
    toolTipText: 'Convert Free Trial',
    type: 'dialog',
  },
  {
    id: 'reconfigure',
    name: 'Reconfigure',
    description: 'Reconfigure',
    icon: SettingsIcon,
    showTooltip: true,
    toolTipText: 'Reconfigure Subscription',
    type: 'dialog',
  },
  {
    id: 'cancel',
    name: 'Cancel',
    description: 'Cancel',
    icon: AccessTimeIcon,
    showTooltip: true,
    toolTipText: 'Cancel Subscription',
    type: 'dialog',
  },
  {
    id: 'renewWithQuantityUpdate',
    name: 'Renew with Quantity Update',
    description: 'Renew with Quantity Update',
    icon: RenewWithQuantityUpdateIcon,
    showTooltip: true,
    toolTipText: 'Renew with Quantity Update',
    type: 'dialog',
  },
  {
    id: 'viewOrder',
    name: 'View Order History',
    description: 'View Order History',
    icon: ZoomInOutlinedIcon,
    type: 'navigation',
  },
  {
    id: 'viewInvoice',
    name: 'View Invoice',
    description: 'View Invoice',
    icon: DescriptionOutlinedIcon,
    type: 'navigation',
  },
  {
    id: 'viewUsage',
    name: 'View Usage',
    description: 'View Usage',
    icon: BarChartOutlinedIcon,
    type: 'navigation',
  },
  {
    id: 'viewAsset',
    name: 'View Asset',
    description: 'View Asset',
    icon: BusinessOutlinedIcon,
    type: 'navigation',
  },
  {
    id: 'viewEntitlement',
    name: 'View Entitlement',
    description: 'View Entitlement',
    icon: NoteAddOutlinedIcon,
    type: 'navigation',
  },
  {
    id: 'viewCredits',
    name: 'View Credits',
    description: 'View Credits',
    icon: MonetizationOnOutlinedIcon,
    type: 'navigation',
  },
];

export default {
  cardHeaderMetadata: {
    title: (subscription: Subscription) => subscription.product?.name || '',
    subtitle: '',
    description: '',
    statusStyle: {
      Draft: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
      Active: {
        backgroundColor: '#6239EB',
        color: '#FFF',
      },
      Cancelled: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
    },
    statusShadowStyle: {
      Draft: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
      Active: {
        borderColor: 'transparent transparent #000000 transparent',
      },
      Cancelled: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
    },
    statusIcon: {
      Active: ActiveIcon,
      Draft: DraftIcon,
    },
    // TODO: switch to using order interface
    status: 'status',
    imageSignedUrl: 'imageSignedUrl',
  },
  cardBodyMetadata: {
    activeBorderStyle: {
      border: '1px solid #6239EB',
      borderRadius: '10px 10px 8px 8px',
      backgroundColor: '#F5F5F5',
    },
    summaryItemPosition: 'top-left' as const,
    actions: {
      Cancelled: [],
      Draft: [],
      Active: subscriptionActions,
    },
  },
};

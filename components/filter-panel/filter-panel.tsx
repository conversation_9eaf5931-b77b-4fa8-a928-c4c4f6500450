import React, { useContext, useEffect, useState } from 'react';

import { Divider } from '@material-ui/core';
import { Grid } from '@material-ui/core';

import DialogComponent from '../dialog-component';
import FilterBuilder from '../filter-builder';
import type { RubyFilter } from '../graph-ql-query-constructor/interface';
import PickList from '../pick-list';
import type { ListViewLocalState } from '../ruby-list-view-local-state-context';
import RubyListViewLocalStateContext from '../ruby-list-view-local-state-context';
import TextInput from '../text-input';
import type { Props } from './interface';
import { FilterOperationPermissionValidatorContext } from './interface';
import { UserContext } from '../user-context';

const defaultProps = {};

const FilterPanel: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const {
    openFilters,
    handleCloseFilters,
    searchResult,
    handleSearch,
    objectMetadata,
    savedFilters,
    updateListOfSavedFilters,
    saveFilter,
    deleteFilter,
    updateFilters,
    warningBeforeUpdate,
    allMetadatas,
  } = props;

  const [referencedFilter, setReferencedFilter] = useState<RubyFilter | null>(null);
  const [filterName, setFilterName] = useState('');
  const localStateContext = useContext(RubyListViewLocalStateContext);
  const filterOperationPermissionValidator = useContext(FilterOperationPermissionValidatorContext);
  const userContext = useContext(UserContext);
  const currentUserId = userContext?.userContextService?.getUserConfig()?.user?.id;

  useEffect(() => {
    if (openFilters) {
      setupReferencedFilter(savedFilters);
    }
    return () => {
      setReferencedFilter(null);
      setFilterName('');
    };
  }, [openFilters]);

  const handleSelectFilter = (newValue: string) => {
    if (!newValue) {
      return;
    }
    const filter = savedFilters.filter(
      (currentFilter: RubyFilter) => currentFilter.id === newValue,
    )[0];
    if (filter) {
      setReferencedFilter(filter);
    } else {
      setReferencedFilter({ id: 'none', name: 'None', conditions: [] });
    }
  };

  const setupReferencedFilter = (filters: RubyFilter[]) => {
    const firstAppliedFilter = filters.find((filter) => filter.isApplied);
    if (firstAppliedFilter) {
      // you may select a filter that you don't want to persiist
      setReferencedFilter(firstAppliedFilter);
    }
  };

  const handleUpdateFilterName = (value: string) => {
    setFilterName(value);
  };

  const handleUpdateReferencedFilter = (value: RubyFilter) => {
    setReferencedFilter(value || null);
  };

  const handleUpdateFilterNameWithValue = (value: string) => {
    setFilterName(value);
  };

  const updateListViewLocalState = async (payload: Partial<ListViewLocalState>) => {
    if (localStateContext) {
      const listViewLocalState = await localStateContext.getLocalState();
      await localStateContext.setLocalState({ ...listViewLocalState, ...payload });
    }
  };

  const disableSaveButton = React.useCallback(() => {
    if (!filterOperationPermissionValidator || !referencedFilter) {
      return false;
    }
    if (filterName && filterName.trim().length > 0) {
      return !filterOperationPermissionValidator.hasPermissionOnFilter(referencedFilter, 'create');
    } else {
      return !filterOperationPermissionValidator.hasPermissionOnFilter(referencedFilter, 'update');
    }
  }, [filterOperationPermissionValidator, referencedFilter, filterName]);

  const disableDeleteButton = React.useCallback(() => {
    if (!filterOperationPermissionValidator || !referencedFilter) {
      return false;
    }
    return (
      !filterOperationPermissionValidator.hasPermissionOnFilter(referencedFilter, 'delete') &&
      referencedFilter.id !== currentUserId
    );
  }, [filterOperationPermissionValidator, referencedFilter]);

  if (!openFilters) {
    return null;
  }

  return (
    <DialogComponent width="md" open={openFilters} handleClose={handleCloseFilters} title="Filters">
      <Grid container spacing={2}>
        <Grid item xs={4}>
          <PickList
            value={referencedFilter?.id}
            label="Saved Filter"
            field={{
              apiName: 'savedFilter',
              name: 'Saved Filter',
              type: 'text',
            }}
            showNone
            name="filter"
            options={savedFilters.map((filter: RubyFilter) => ({
              name: filter.name,
              value: filter.id,
            }))}
            handleInputChange={handleSelectFilter}
          />
        </Grid>
        <Grid item xs={4}>
          <TextInput
            value={filterName}
            label="Save As"
            field={{
              apiName: 'saveAs',
              name: 'Save As',
              type: 'text',
            }}
            placeholder="New Filter Name"
            name="filter-name"
            handleInputChange={handleUpdateFilterName}
          />
        </Grid>
      </Grid>
      <Divider
        style={{
          margin: '20px 0',
        }}
      />
      <FilterBuilder
        filterName={filterName}
        handleUpdateFilterNameWithValue={handleUpdateFilterNameWithValue}
        //@ts-ignore
        referencedFilter={referencedFilter}
        handleUpdateReferencedFilter={handleUpdateReferencedFilter}
        searchResult={searchResult}
        handleSearch={handleSearch}
        handleClose={handleCloseFilters}
        objectMetadata={objectMetadata}
        allMetadatas={allMetadatas}
        savedFilters={savedFilters}
        saveFilter={saveFilter}
        deleteFilter={deleteFilter}
        updateFilters={updateFilters}
        updateListOfSavedFilters={updateListOfSavedFilters}
        updateListViewLocalState={updateListViewLocalState}
        warningBeforeUpdate={warningBeforeUpdate}
        disableSaveButton={disableSaveButton()}
        disableDeleteButton={disableDeleteButton()}
        currentUserId={currentUserId}
      />
    </DialogComponent>
  );
};

export default FilterPanel;

import React from 'react';

import { RubyButtonBarProps } from '../ruby-button';

export interface Props {
  customDialogFooter?: string;
  open: boolean;
  handleClose?: () => void;
  processing?: boolean;
  title: string;
  closeBar?: React.ReactNode;
  width: false | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | undefined;
  children: React.ReactNode;
  /**
   * Deprecated, use actions
   */
  handleSubmit?: () => void;
  loading?: boolean;
  loadingText?: string;
  dialogOptions?: any;
  dontShowCloseIcon?: boolean;
  /**
   * Deprecated, use actions
   */
  cancelButtonText?: string;
  /**
   * Deprecated, use actions
   */
  submitButtonText?: string;

  /**
   * See RubyButtonBar props
   */
  actions?: Partial<RubyButtonBarProps>;

  /**
   * styles to be applied on title text
   */
  titleStyle?: React.CSSProperties;
  className?: string;
  dialogContentStyle?: any;
  customTitleMode?: boolean;
}

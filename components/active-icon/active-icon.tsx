import React from 'react';

import { SvgIcon } from '@material-ui/core';

import { Props } from './interface';

const defaultProps = {};

export const ActiveIcon: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const { width, height, viewBox } = props;

  return (
    <SvgIcon viewBox={viewBox}>
      <svg
        width={width || '24px'}
        height={height || '24px'}
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>F9C4EBC4-D704-49D5-B019-1100624A0303</title>
        <g id="Visual-System" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Visual-System-(Icons)" transform="translate(-398.000000, -439.000000)">
            <g id="focus-2-fill" transform="translate(398.000000, 439.000000)">
              <polygon id="Path" points="0 0 24 0 24 24 0 24"></polygon>
              <path
                d="M12,2 C17.52,2 22,6.48 22,12 C22,17.52 17.52,22 12,22 C6.48,22 2,17.52 2,12 C2,6.48 6.48,2 12,2 Z M11.9999937,19.9999937 C16.427,19.9999937 19.9999937,16.427 19.9999937,11.9999937 C19.9999937,7.573 16.427,3.99999373 11.9999937,3.99999373 C9.8774543,3.99734183 7.84107819,4.83934292 6.34021055,6.34021055 C4.83934292,7.84107819 3.99734183,9.8774543 3.99999373,11.9999937 C3.99999373,16.427 7.573,19.9999937 11.9999937,19.9999937 L11.9999937,19.9999937 Z M12,18 C8.68,18 6,15.32 6,12 C6,8.68 8.68,6 12,6 C15.32,6 18,8.68 18,12 C18,15.32 15.32,18 12,18 Z M12,10 C10.9,10 10,10.9 10,12 C10,13.1 10.9,14 12,14 C13.1,14 14,13.1 14,12 C14,10.9 13.1,10 12,10 Z"
                id="Shape"
                fill="#FFF"
                fillRule="nonzero"
              ></path>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default ActiveIcon;

import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { Props } from './interface';

const defaultProps = {};

const DraftIcon: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const { width, height, viewBox } = props;

  return (
    <SvgIcon viewBox={viewBox}>
      <svg
        width={width || '24px'}
        height={height || '24px'}
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>6DBD3924-5B20-4E3A-82B4-5F8663880555</title>
        <g id="Visual-System" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Visual-System-(Icons)" transform="translate(-1085.000000, -440.000000)">
            <g id="edit-circle-line" transform="translate(1085.000000, 440.000000)">
              <polygon id="Path" points="0 0 24 0 24 24 0 24"></polygon>
              <path
                d="M12.684,4.029 C9.33559541,3.74177405 6.16465494,5.57789922 4.74694464,8.62493213 C3.32923433,11.671965 3.96704255,15.2802083 6.34341711,17.6565829 C8.71979166,20.0329574 12.328035,20.6707657 15.3750679,19.2530554 C18.4221008,17.8353451 20.258226,14.6644046 19.971,11.316 C19.900889,10.4757991 19.6973367,9.65213818 19.368,8.876 L20.868,7.374 C21.6148845,8.80138777 22.0033839,10.3890187 22.0000219,11.9999961 C22.0000219,17.523 17.523,21.9999961 12,21.9999961 C6.477,21.9999961 2,17.523 2,11.9999961 C2,6.477 6.477,1.99999606 12,1.99999606 C13.6107474,1.99856877 15.1979181,2.38695555 16.626,3.132 L15.125,4.632 C14.3488348,4.30274429 13.5251871,4.09919528 12.685,4.029 L12.684,4.029 Z M20.485,2.1 L21.9,3.515 L12.708,12.707 L11.296,12.71 L11.294,11.293 L20.485,2.1 Z"
                id="Shape"
                fill="#6239EB"
                fillRule="nonzero"
              ></path>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default DraftIcon;

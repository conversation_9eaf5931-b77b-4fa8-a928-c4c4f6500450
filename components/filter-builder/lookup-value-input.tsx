import React, { useContext, useEffect, useState } from 'react';
import { FilterBuilderCondition, FilterBuilderField } from '../graph-ql-query-constructor';
import InputLabelComponent from '../input-label-component';
import TextField from '@material-ui/core/TextField';
import { RubyField, RubyObject } from '../metadata/interface';
import { withStyles } from '@material-ui/core/styles';
import Autocomplete from '@material-ui/lab/Autocomplete';
import { AutocompleteInputChangeReason } from '@material-ui/lab/useAutocomplete/useAutocomplete';
import ServiceContext from '../service-context/service-context';

type LookupOption = { name: string; id: string };

interface LookupSelectProps {
  field: RubyField | FilterBuilderField;
  value: any;
  label?: string;
  fetchOptions: (searchText: string, currentValue: string) => Promise<LookupOption[]>;
  handleInputChange?: (e: any, newValue: LookupOption) => void;
  name?: string;
  id?: string;
  disabled?: boolean;
}

const defaultProps = {};

const StyledAutocomplete = withStyles((theme: any) => ({
  root: {
    'label + &': {
      marginTop: theme.spacing(1),
    },
    test: {
      borderColor: 'green',
    },
    '& fieldset': {
      border: 'none',
    },
  },
  inputRoot: {
    backgroundColor: '#ffffff !important',
    border: '1px solid #ced4da',
    fontSize: '.875rem',
    paddingTop: '1px !important',
    paddingBottom: '1px !important',
  },
}))(Autocomplete);

const LookupSelect: React.FC<LookupSelectProps> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const {
    field,
    name,
    label,
    id,
    handleInputChange,
    fetchOptions,
    value: initValue,
    disabled,
  } = props;
  const _name = name || field.apiName;
  const _label = label || field.name || _name;
  const _id = id || field.apiName;
  const [inputValue, setInputValue] = useState('');
  const [options, setOptions] = useState<LookupOption[]>();
  const [value, setValue] = useState<LookupOption | undefined>();
  const [lastRequestId, setLastRequestId] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const _fetchOptions = async (input: string) => {
    if (lastRequestId !== null) {
      clearTimeout(lastRequestId);
    }
    setLoading(true);
    let _lastRequestId = setTimeout(async () => {
      try {
        const fetchedOptions = await fetchOptions(input, initValue);
        setOptions(fetchedOptions);
        setLoading(false);
      } catch (error) {
        console.error('Error handling auto-complete input change.', error);
      }
    }, 200);
    setLastRequestId(_lastRequestId);
  };

  useEffect(() => {
    _fetchOptions(inputValue);
  }, [inputValue]);

  if (!options) {
    return null;
  }

  return (
    <div style={{ width: '100%' }}>
      <InputLabelComponent
        required={field ? field.required : false}
        shrink
        htmlFor={field ? field.apiName : id}
        disabled={disabled}
      >
        {_label}
      </InputLabelComponent>
      <StyledAutocomplete
        id={_id}
        autoComplete
        disabled={disabled}
        options={options}
        //@ts-ignore
        defaultValue={
          initValue
            ? options.find(
                (_) =>
                  _.id === initValue ||
                  //@ts-ignore
                  (field.nestedApiName && _[field.nestedApiName] === initValue),
              )
            : null
        }
        //value={value}
        loading={loading}
        //@ts-ignore
        onChange={(event, value: LookupOption) => {
          setValue(value);
          handleInputChange && handleInputChange(event, value);
        }}
        onInputChange={(
          event: React.ChangeEvent<{}>,
          value: string,
          reason: AutocompleteInputChangeReason,
        ) => {
          setInputValue(value);
        }}
        //@ts-ignore
        getOptionLabel={(option: LookupOption) => {
          //@ts-ignore
          return field.field?.apiName && option[field.field?.apiName]
            ? //@ts-ignore
              option[field.field?.apiName]
            : //@ts-ignore
              field.nestedApiName && option[field.nestedApiName]
              ? //@ts-ignore
                option[field.nestedApiName]
              : option.name
                ? option.name
                : '';
        }}
        renderInput={(params: any) => (
          <TextField
            {...params}
            disabled={disabled}
            // autoComplete="off"
            variant="outlined"
            placeholder={_label}
            inputProps={{
              ...params.inputProps,
              autocomplete: 'off',
            }}
          />
        )}
      />
    </div>
  );
};

interface LookupValueInputProps {
  objectMetadata: RubyObject;
  conditionField: FilterBuilderCondition;
  handleInputChange?: (e: any, newValue: LookupOption) => void;
  disabled?: boolean;
}

const LookupValueInput: React.FC<LookupValueInputProps> = (props) => {
  const { conditionField, handleInputChange, objectMetadata, disabled } = props;
  const serviceFacade = useContext(ServiceContext);

  const field = conditionField.field;
  if (!field) {
    console.error('Field metadata not provided to LookupValueInput');
    return null;
  }
  return (
    <LookupSelect
      // @ts-ignore
      field={field}
      value={conditionField.value ? conditionField.value.value : []}
      fetchOptions={async (searchText, currentValue) => {
        if (
          serviceFacade &&
          serviceFacade.lookupRecordService &&
          !conditionField.field?.field &&
          !conditionField.field?.nestedApiName
        ) {
          return serviceFacade.lookupRecordService.searchLookupRecordOptionsByName(
            objectMetadata,
            field,
            searchText,
            currentValue,
          );
        } else if (
          serviceFacade &&
          serviceFacade.lookupRecordService &&
          (conditionField.field?.field || conditionField.field?.nestedApiName)
        ) {
          return (
            (serviceFacade.lookupRecordService.searchLookupNestedRecordOptionsByNestedName &&
              serviceFacade.lookupRecordService.searchLookupNestedRecordOptionsByNestedName(
                objectMetadata,
                field,
                searchText,
                currentValue,
              )) ||
            []
          );
        } else {
          console.error('serviceFacade.lookupRecordService not provided');
          return [];
        }
      }}
      handleInputChange={handleInputChange}
    />
  );
};

export default LookupValueInput;

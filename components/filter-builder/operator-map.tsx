/**
 * Note: much of this code was taken from https://github.com/logipro/logi-filter-builder and refractored for our case to construct filters into GraphQL
 * TODO: Refractor to make this into a functional component and clean up code
 */
export const operatorTypes = new Map();

const LessThan = { name: 'Less Than', id: ' _lt ' };
const LessThanOrEqualTo = { name: 'Less Than Or Equal To', id: ' _lte ' };
const Equals = { name: 'Equals', id: ' _eq ' };
const NotEquals = { name: 'Not Equals', id: ' _neq ' };
const GreaterThan = { name: 'Greater Than', id: ' _gt ' };
const GreaterThanOrEqualTo = { name: 'Greater Than Or Equal To', id: ' _gte ' };
const Like = { name: 'Like', id: ' _like ' };
const Contains = { name: 'Contains', id: ' _in ' };
const IsNull = { name: 'Is Null', id: ' _is_null ' };
const IsNotNull = { name: 'Is Not Null', id: ' _is_not_null ' };

const numericOperators = [
  <PERSON><PERSON><PERSON>,
  LessThanOrEqualTo,
  Equals,
  NotEquals,
  GreaterThan,
  GreaterThanOrEqualTo,
  IsNull,
  IsNotNull,
];
const textOperators = [Equals, NotEquals, Like, IsNull, IsNotNull];
const pickListOperators = [Equals, NotEquals, Contains, IsNull, IsNotNull];
const lookupOperators = [Equals, NotEquals, IsNull, IsNotNull];
const booleanOperators = [Equals];
const currencyOperators = [LessThan, LessThanOrEqualTo, Equals, GreaterThan, GreaterThanOrEqualTo];
const idOperators = [Equals, NotEquals];
const dateTimeOperators = [
  LessThan,
  LessThanOrEqualTo,
  Equals,
  GreaterThan,
  GreaterThanOrEqualTo,
  IsNull,
  IsNotNull,
];
const dateOperators = [
  LessThan,
  LessThanOrEqualTo,
  Equals,
  GreaterThan,
  GreaterThanOrEqualTo,
  IsNull,
  IsNotNull,
];

operatorTypes
  .set('integer', numericOperators)
  .set('Decimal', numericOperators)
  .set('decimal', numericOperators)
  .set('Double', numericOperators)
  .set('autoNumber', textOperators)
  .set('dateTime', dateTimeOperators)
  .set('date', dateOperators)
  .set('Date', dateOperators)
  .set('text', textOperators)
  .set('TextArea', textOperators)
  .set('longText', textOperators)
  .set('richText', textOperators)
  .set('string', textOperators)
  .set('String', textOperators)
  .set('pickList', pickListOperators)
  .set('Picklist', pickListOperators)
  .set('currency', currencyOperators)
  .set('id', idOperators)
  .set('bId', idOperators)
  .set('bLookup', lookupOperators)
  .set('lookup', lookupOperators)
  .set('bMasterDetail', lookupOperators)
  .set('Reference', lookupOperators)
  .set('phone', textOperators)
  .set('email', textOperators)
  .set('boolean', booleanOperators)
  .set('Boolean', booleanOperators)
  .set('currencyIsoCode', pickListOperators)
  // Hide multipicklist until backend API is ready
  .set('multiPickList', pickListOperators)
  .set('MultiPicklist', pickListOperators);

export const operandTypes = new Map();
operandTypes.set(' _and ', { name: 'And', id: ' _and ' }).set(' _or ', { name: 'Or', id: ' _or ' });

import React from 'react';
import {makeStyles} from '@material-ui/core/styles';
import t from 'prop-types';

import {AlertProps, KindMap} from './interface';
import {Card} from "@material-ui/core";

const useStyles = makeStyles({
    root: {
        padding: "50px",
        background: "white",
        borderRadius: "3px",
        color: "#c8f5be"
    }
})

const kinds: KindMap = {
    info: '#5352ED',
    positive: '#2ED573',
    negative: '#FF4757',
    warning: '#FFA502',
};

const Alert: React.FC<AlertProps> = ({children, kind = 'info', ...rest}) => {
    const classes = useStyles()
    return (
        <Card
            className={classes.root}
            style={{
                background: kinds[kind],
            }}
            {...rest}
        >
            {children}
        </Card>
    );
};

Alert.propTypes = {
    kind: t.oneOf(['info', 'positive', 'negative', 'warning']),
};

export default Alert;
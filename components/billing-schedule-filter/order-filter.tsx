import React, { useState } from 'react';
import { Grid } from '@material-ui/core';
import Accordion from '@material-ui/core/Accordion';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import TextField from '@material-ui/core/TextField';
import Tooltip from '@material-ui/core/Tooltip';
import Typography from '@material-ui/core/Typography';
import { Theme, createStyles, makeStyles } from '@material-ui/core/styles';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import InfoIcon from '@material-ui/icons/Info';
import { RubyFilter } from '@nue-apps/ruby-ui-component';
import FilterBuilder from '../filter-builder';
import MultiSelectionSearchBar from '../multi-selection-search-bar';
import TabView from '../tab-view';
import { OrderProps } from './interface';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      width: '100%',
    },
    heading: {
      fontSize: '12px',
      letterSpacing: 0,
      color: '#999999',
      fontWeight: 700,
    },
    secondaryHeading: {
      fontSize: theme.typography.pxToRem(15),
      color: theme.palette.text.secondary,
    },
    infoIcon: {
      fontSize: '.875rem',
      color: '#6239EB',
      cursor: 'pointer',
    },
  }),
);

export const OrderFilter: React.FC<OrderProps> = (props) => {
  const {
    open,
    setOpen,
    title,
    activeTabIndex,
    setActiveTabIndex,
    objectMetadata,
    executeQuery,
    selectedValues,
    setSelectedValues,
    disabled = false,
    isWebCreated,
    orderFilter,
    orderInvokeObject,
    savedFilters,
  } = props;
  const [value, setValue] = useState('');
  const [referencedFilter, setReferencedFilter] = useState<RubyFilter | null>(null);
  const classes = useStyles();
  const [temporaryFilter, setTemporaryFilter] = useState();

  const tabs = isWebCreated
    ? [
        { id: '0', name: 'Simple', displayName: 'Simple' },
        { id: '1', name: 'Advanced', displayName: 'Advanced' },
      ]
    : [{ id: '0', name: 'GraphQL Filter', displayName: 'GraphQL Filter' }];

  const topTitle =
    'If there are no specific orders selected, all orders meet the filter criteria will be included in this billing schedule.';

  return (
    <>
      <Grid>
        <Accordion
          expanded={open}
          onChange={(event, expanded) => setOpen(expanded)}
          style={{ boxShadow: 'none' }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            style={{ display: '-webkit-inline-flex', padding: '0px' }}
          >
            <Typography className={classes.heading}>{title}</Typography>
          </AccordionSummary>
          <TabView
            tabs={tabs}
            activeTabIndex={activeTabIndex}
            reverseColorStyle={true}
            disabledIndex={
              disabled
                ? tabs.map((tab) => {
                    return Number(tab.id);
                  })
                : []
            }
            tabHeightStyle={true}
            variant={'standard'}
            handleUpdateTabIndex={(newActiveTabIndex) => {
              setActiveTabIndex(newActiveTabIndex);
            }}
          >
            <Grid
              container
              spacing={1}
              xs={12}
              style={{
                margin: '0px',
                border: '1px solid #EEE',
                minHeight: '325px',
                display: 'block',
              }}
            >
              {isWebCreated && activeTabIndex === 0 && (
                <Grid container spacing={2} xs={12}>
                  <Grid
                    item
                    container
                    spacing={2}
                    style={{ marginTop: '20px', marginLeft: '25.5px' }}
                  >
                    <Grid item style={{ fontSize: '14px', color: '#999999', height: '20px' }}>
                      Include Orders:
                    </Grid>
                    <Grid item style={{ marginTop: '1px', marginLeft: '-10px' }}>
                      <Tooltip title={topTitle} arrow placement="top">
                        <InfoIcon className={classes.infoIcon} />
                      </Tooltip>
                    </Grid>
                  </Grid>
                  <Grid item xs={12}>
                    <MultiSelectionSearchBar
                      value={value}
                      setValue={setValue}
                      selectedValues={selectedValues}
                      setSelectedValues={setSelectedValues}
                      fullWidth={true}
                      searchStyle={{
                        marginLeft: '26px',
                        marginTop: '-10px',
                      }}
                      orderByField={{
                        columnName: 'name',
                        direction: 'asc',
                      }}
                      searchField={[
                        {
                          apiName: 'id',
                          name: 'id',
                          type: 'bId',
                        },
                        {
                          apiName: 'name',
                          name: 'Name',
                          type: 'text',
                        },
                      ]}
                      defaultFilter={[
                        {
                          name: 'Activate Status',
                          conditions: [
                            {
                              apiName: 'status',
                              name: 'Status',
                              operand: ' _and ',
                              operator: ' _eq ',
                              type: 'text',
                              value: `"Activated"`,
                              nestedConditions: [],
                              error: '',
                            },
                          ],
                          id: '',
                          isExclusive: false,
                        },
                      ]}
                      executeQuery={executeQuery}
                      objectMetadata={objectMetadata}
                      disabled={disabled}
                    />
                  </Grid>
                </Grid>
              )}

              {isWebCreated && activeTabIndex === 1 && (
                <Grid style={{ marginTop: '20px', marginLeft: '25.5px' }}>
                  <FilterBuilder
                    objectMetadata={objectMetadata}
                    referencedFilter={referencedFilter}
                    filterName={''}
                    searchResult=""
                    hiddenButton={true}
                    graphqlGenerator={true}
                    savedFilters={savedFilters}
                    invokeObject={orderInvokeObject}
                    disabled={disabled}
                    temporaryFilter={temporaryFilter}
                    setTemporaryFilter={setTemporaryFilter}
                  />
                </Grid>
              )}

              {!isWebCreated && (
                <Grid container spacing={1} xs={12}>
                  <Grid item xs={12}>
                    <TextField
                      multiline
                      value={orderFilter}
                      disabled={true}
                      aria-readonly={true}
                      variant="outlined"
                      style={{ width: '890px', marginLeft: '10px', marginTop: '5px' }}
                      inputProps={{
                        style: {
                          height: 275,
                          padding: '0 14px',
                          color: '#4d4c50',
                          fontWeight: 'normal',
                          fontSize: '14px',
                          overflow: 'auto',
                        },
                      }}
                    />
                  </Grid>
                </Grid>
              )}
            </Grid>
          </TabView>
        </Accordion>
      </Grid>
    </>
  );
};

export default OrderFilter;

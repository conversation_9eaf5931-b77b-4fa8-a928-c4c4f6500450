import React from 'react';

import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import dayjs from 'dayjs';

import { ChangeItemText } from './interface';

const useStyles = makeStyles({
  itemText: {
    opacity: 0.7,
    color: '#000000',
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
    opacity: '1 !important',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
});
const AdjustText: React.FC<ChangeItemText> = (props) => {
  const { changeItem } = props;
  const classes = useStyles();

  const getPriceMessages = () => {
    if (changeItem.newValues?.priceRadio === 'customPrice') {
      return (
        <>
          <Typography className={classes.itemText} variant="body2" component="p" display="inline">
            is set to{' '}
          </Typography>
          <Typography
            variant="body2"
            className={classes.textHighlight}
            component="p"
            display="inline"
          >
            ${changeItem.newValues.price}
          </Typography>
          <Typography className={classes.itemText} variant="body2" component="p" display="inline">
            {` ${changeItem.objects.uom.name}`}
          </Typography>
        </>
      );
    } else if (changeItem.newValues?.priceRadio === 'trueUpPrice') {
      return (
        <>
          <Typography className={classes.itemText} variant="body2" component="p" display="inline">
            is adjusted to match the current quantity tier, as determined by the value of the
            quantity tier attribute,
          </Typography>
        </>
      );
    } else if (changeItem.newValues?.priceRadio === 'updatePriceTag') {
      return (
        <>
          <Typography className={classes.itemText} variant="body2" component="p" display="inline">
            will be updated,{' '}
          </Typography>
        </>
      );
    } else {
      return (
        <>
          <Typography className={classes.itemText} variant="body2" component="p" display="inline">
            is
          </Typography>{' '}
          <Typography className={classes.itemText} variant="body2" component="p" display="inline">
            {changeItem.newValues.percentAction === 'priceIncrease' ? 'increased' : 'discounted'}
          </Typography>{' '}
          <Typography variant="body2" component="p" display="inline">
            by
          </Typography>{' '}
          <Typography
            variant="body2"
            className={classes.textHighlight}
            component="p"
            display="inline"
          >
            {changeItem.newValues.percent > 0
              ? changeItem.newValues.percent
              : `(${Math.abs(changeItem.newValues.percent)})`}
            %
          </Typography>
        </>
      );
    }
  };

  return (
    <Typography variant="body2" component="p" display="inline">
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        {changeItem.newValues?.priceRadio === 'updatePriceTag'
          ? 'The price tags of Subscription '
          : 'The price of Subscription '}
      </Typography>
      <Typography variant="body2" className={classes.textUnderline} component="p" display="inline">
        {changeItem.request.assetName}{' '}
      </Typography>
      {changeItem.newValues.applyToAll && changeItem.newValues.priceRadio === 'customPercent' && (
        <Typography className={classes.itemText} variant="body2" component="p" display="inline">
          and its add-on items{' '}
        </Typography>
      )}
      {getPriceMessages()}

      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        {' '}
        effective{' '}
      </Typography>
      <Typography variant="body2" className={classes.textHighlight} component="p" display="inline">
        {dayjs(changeItem.newValues.startDate).format('MM/DD/YYYY')}
      </Typography>
    </Typography>
  );
};

export default AdjustText;

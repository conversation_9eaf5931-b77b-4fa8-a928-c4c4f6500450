import { TermCalculator } from '@nue-apps/ruby-pricing-calculator';
import dayjs from 'dayjs';
import type { ChangeGroup } from '../change-cart/interface';

const getTermDimension = (termDimension: string) => {
  if (termDimension.toLowerCase() === 'year') {
    return 'Year';
  }
  /**
   * Error: Invalid term type month
   * else if (termType != 'Month') {
      throw new InternalCalculationError('Invalid term type ' + termType)
    }
   */
  if (termDimension.toLowerCase() === 'month') {
    return 'Month';
  }
  return termDimension;
};

export const calculateEndDate = (
  subscriptionTerm: number,
  date: Date,
  termDimension: string,
  format: string,
) => {
  const currTermDimension = getTermDimension(termDimension);
  const termCalculator = new TermCalculator();
  const endDate = termCalculator.calculateEndDate(subscriptionTerm, date, currTermDimension);
  return dayjs(endDate).format(format);
};

export const calculateEndDateWithTermBasis = (
  subscriptionTerm: number,
  date: Date,
  termDimension: string,
  termBasis: any,
  format: string,
) => {
  const currTermDimension = getTermDimension(termDimension);
  const termCalculator = new TermCalculator();
  const endDate = termCalculator.calculateEndDateWithTermBasis(
    subscriptionTerm,
    date,
    currTermDimension,
    termBasis,
  );
  return dayjs(endDate).format(format);
};

export const calculateTerm = (startDate: Date, endDate: Date, termDimension: string) => {
  const currTermDimension = getTermDimension(termDimension);
  const termCalculator = new TermCalculator();
  const term = termCalculator.calculateTerm(startDate, endDate, currTermDimension);
  return term;
};

export const getTotalNumChanges = (changeGroups: Record<string, ChangeGroup>) => {
  let sum = 0;
  Object.keys(changeGroups).forEach((key) => {
    //@ts-ignore
    sum += changeGroups[key].changeItems ? changeGroups[key].changeItems.length : 0;
  });
  return sum;
};

import type { Item, SummaryItem } from '../list/interface';
import type { CardA<PERSON>, Uom } from '../metadata/interface';

export interface Props {
  items: Item[];
  status: string;
  imageSignedUrl: string;
  title: string;
  subtitle?: string;
  description?: string;
  statusStyle: React.CSSProperties;
  statusShadowStyle: React.CSSProperties;
  statusHeaderStyle?: React.CSSProperties;
  summaryItem?: SummaryItem;
  currencyIsoCode: string;
  StatusIcon: React.ComponentType<any>;
  actions: CardAction[];
  moreActions?: CardAction[];
  activeBorderStyle?: React.CSSProperties;
  onTitleClick?: () => void;
  actionEventHandler: (action: CardAction) => void | Promise<void>;
  numCardActionsPerRow: number;
  uom: Uom;
  showSubmitBtn?: boolean;
  onShowDetails?: () => void;
  showDetails?: boolean;
  locale?: string;
  summaryView?: boolean;
  transactionHub?: JSX.Element | null;
}

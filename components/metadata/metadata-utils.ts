import type { Item } from '../list/interface';
import type { GetDisplayFieldsFromFieldSet, RubyField } from '../metadata/interface';

export const getFieldsToDisplay = (objectMetadataFields: RubyField[], fieldSet: RubyField[]) => {
  const fieldsToDisplay = fieldSet.reduce((filtered: any, field) => {
    const index = objectMetadataFields.findIndex(
      (metadataField) => metadataField.apiName === field.apiName,
    );
    if (index !== -1) {
      const newField = {
        ...objectMetadataFields[index],
        type: objectMetadataFields[index].type.toLowerCase(),
      };
      filtered.push(newField);
    }
    return filtered;
  }, []);
  return fieldsToDisplay;
};

export const displayValuesFromFieldSet = ({
  objectFieldSet,
  objectMetadata,
  currencyIsoCode,
  object,
  locale,
}: GetDisplayFieldsFromFieldSet) => {
  if (!objectMetadata.fields) {
    return [];
  }
  const metadataFieldsToDisplay: RubyField[] = getFieldsToDisplay(
    objectMetadata.fields,
    objectFieldSet,
  );
  const fieldsForDisplay: Item[] = [];
  for (let i = 0; i < metadataFieldsToDisplay.length; i += 1) {
    let value = object[metadataFieldsToDisplay[i].apiName];
    if (
      metadataFieldsToDisplay[i].type === 'blookup' &&
      metadataFieldsToDisplay[i].lookupRelation
    ) {
      value = object[metadataFieldsToDisplay[i].lookupRelation!.relationName]?.name || '';
    }
    if (metadataFieldsToDisplay[i].type === 'picklist') {
      const valuePair = metadataFieldsToDisplay[i].valueSet?.valuePairs.find(
        (valPair) => valPair.apiName === value,
      );
      value = valuePair?.name;
    }
    fieldsForDisplay.push({
      apiName: metadataFieldsToDisplay[i].apiName,
      name: metadataFieldsToDisplay[i].name,
      type: metadataFieldsToDisplay[i].type,
      value: value,
      xs: metadataFieldsToDisplay[i].xs,
    });
  }
  return fieldsForDisplay;
};

import React from 'react';
import { SvgIcon } from '@material-ui/core';
import { Props } from './interface';

const AccountCircleIcon: React.FC<Props> = ({ width, height, viewBox }) => {
  return (
    <SvgIcon>
      <svg
        width={width || '16px'}
        height={height || '16px'}
        viewBox={viewBox || '0 0 16 16'}
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>DC8CD07E-4A34-4FE3-B56D-54F490C9E3DF</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-5-1-Create-Quote" transform="translate(-1014.000000, -853.000000)">
            <g id="Product-Options" transform="translate(361.000000, 283.000000)">
              <g id="Profile" transform="translate(633.000000, 165.000000)">
                <g id="Group-14" transform="translate(20.000000, 404.000000)">
                  <g id="account-circle-line" transform="translate(0.000000, 1.000000)">
                    <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                    <path
                      d="M8,14.6666667 C4.318,14.6666667 1.********,11.682 1.********,8 C1.********,4.318 4.318,1.******** 8,1.******** C11.682,1.******** 14.6666667,4.318 14.6666667,8 C14.6666667,11.682 11.682,14.6666667 8,14.6666667 Z M4.********,12.1706667 C5.********,12.9251317 6.********,13.3352544 8,13.3333401 C9.********,13.3333401 10.5153333,12.8586667 11.4446667,12.072 C10.5674777,11.1719419 9.********,10.6650433 8.********,10.6666628 C6.********,10.6651625 5.********,11.2107711 4.********,12.1706667 L4.********,12.1706667 Z M3.744,11.2133333 C4.********,10.0114882 6.4553207,9.******** 8.********,9.******** C9.********,9.******** 11.2240445,9.******** 12.348,11.0893333 C13.9321221,8.******** 13.5729436,5.******** 11.5164746,3.******** C9.********,2.******** 6.********,2.******** 4.********,4.******** C2.********,5.95564892 2.09594669,9.03127678 3.744,11.214 L3.744,11.2133333 Z M8,8.******** C6.52724067,8.******** 5.********,7.47275933 5.********,6 C5.********,4.52724067 6.52724067,3.******** 8,3.******** C9.47275933,3.******** 10.6666667,4.52724067 10.6666667,6 C10.6666667,7.47275933 9.47275933,8.******** 8,8.******** Z M8,7.******** C8.********,7.******** 9.********,6.******** 9.********,6 C9.********,5.******** 8.********,4.******** 8,4.******** C7.********,4.******** 6.********,5.******** 6.********,6 C6.********,6.******** 7.********,7.******** 8,7.******** Z"
                      id="Shape"
                      fill="#6239EB"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default AccountCircleIcon;

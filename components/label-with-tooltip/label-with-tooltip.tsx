import React from 'react';

import HelpIcon from '../help-icon';
import InputLabelComponent from '../input-label-component';
import { Props } from './interface';

const defaultProps = {};

const LabelWithTooltip: React.FC<Props> = (userProps) => {
  const {
    required = false,
    showTooltip = false,
    tooltipText = '',
    label,
    applyBottomPadding,
    style = {},
    ...otherProps
  } = { ...defaultProps, ...userProps };
  return (
    <InputLabelComponent
      {...otherProps}
      required={required && !showTooltip}
      style={applyBottomPadding ? { ...style, paddingBottom: 8 } : { ...style }}
    >
      {showTooltip ? (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {label}
          <div style={{ paddingLeft: '8px' }}>
            {required && <span>*</span>}{' '}
            <HelpIcon toolTipText={tooltipText} toolTipPlacement="top" />{' '}
          </div>
        </div>
      ) : (
        label
      )}
    </InputLabelComponent>
  );
};

export default LabelWithTooltip;

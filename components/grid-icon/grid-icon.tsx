import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { Props } from './interface';

const defaultProps = {};

const GridIcon: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  return (
    <SvgIcon style={{fontSize: '1.4rem'}} viewBox={props.viewBox}>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>1E06BCA1-6FD5-4F61-9F65-47BF36C054A3</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-7-1-Product-Configurator" transform="translate(-1673.000000, -456.000000)">
            <g id="Group-8" transform="translate(1673.000000, 455.000000)">
              <g id="menu-line" transform="translate(0.000000, 1.000000)">
                <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                <path
                  d="M2,2.66666667 L14,2.66666667 L14,4 L2,4 L2,2.66666667 Z M2,7.33333333 L14,7.33333333 L14,8.66666667 L2,8.66666667 L2,7.33333333 Z M2,12 L14,12 L14,13.3333333 L2,13.3333333 L2,12 Z"
                  id="Shape"
                  fill={props.styles && props.styles.color ? props.styles.color : '#000000'}
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default GridIcon;

import React from 'react';

import type {
  NetSuiteConfigureDetail,
  NetSuiteIntegration,
  NetSuiteTrayDetail,
  NetSuiteUpdateDetail,
} from './interface';

export interface NetSuiteConnectionService {
  queryNetSuiteConfigure: () => Promise<NetSuiteIntegration | null>;
  createNetSuiteIntegration: () => Promise<NetSuiteTrayDetail>;
  updateNetSuiteIntegration: (configuration: NetSuiteUpdateDetail) => Promise<void>;
  configureNetSuiteIntegration: (
    configuration: NetSuiteConfigureDetail,
  ) => Promise<NetSuiteTrayDetail | NetSuiteIntegration>;
  handleUploadFiles: (data: any, type: string) => Promise<any>;
  loadMappingData: (
    netSuiteMappingType: string,
    env: string,
    pageSize?: number,
    currentPage?: number,
    keyword?: string,
  ) => Promise<any>;
  handleUpdateRows: (rows: any, env: string, type: string) => Promise<void>;
  handleDeleteRow: (rows: any, type: string) => Promise<void>;
}

export const NetSuiteConnectionContext = React.createContext<NetSuiteConnectionService | null>(
  null,
);

export default NetSuiteConnectionContext;

import React, { useState } from 'react';
import { DataTypeProvider } from '@devexpress/dx-react-grid';
import Input from '@material-ui/core/Input';
import MenuItem from '@material-ui/core/MenuItem';
import Select from '@material-ui/core/Select';
import type { RubyField, ValuePair } from '@nue-apps/ruby-ui-component';
import {
  DialogComponent,
  EInvoicingStatusRenderer,
  StatusRenderer,
} from '@nue-apps/ruby-ui-component';
import { Invoice } from '../self-service/models';
import { Grid, IconButton, makeStyles, Tooltip } from '@material-ui/core';
import CopyIcon from '@material-ui/icons/FileCopy';
import dayjs from 'dayjs';

export type EInvoicingMessageProps = {
  invoice: any;
};

const useStyles = makeStyles({
  viewDetails: {
    color: '#6239EB',
    textDecoration: 'underline',
    cursor: 'pointer',
  },
});

const EInvoicingMessage: React.FC<EInvoicingMessageProps> = ({ invoice }) => {
  const classes = useStyles();
  const [viewEInvoicingDialog, setViewEInvoicingDialog] = useState(false);
  const [messageEvents, setMessageEvents] = useState(
    JSON.parse(invoice.einvoicingMessage).events || [],
  );
  const handleViewEInvoicingDetails = () => {
    setViewEInvoicingDialog(true);
  };

  const handleClose = () => {
    setViewEInvoicingDialog(false);
  };

  const copyId = async () => {
    await navigator.clipboard.writeText(JSON.parse(invoice.einvoicingMessage).id);
  };

  return (
    <>
      <div className={classes.viewDetails} onClick={handleViewEInvoicingDetails}>
        View Details
      </div>
      <DialogComponent
        width="md"
        open={viewEInvoicingDialog}
        title={' '}
        handleClose={() => {
          handleClose();
        }}
        dialogContentStyle={{
          padding: '10px 80px 50px 80px',
        }}
      >
        <Grid
          style={{
            display: 'flex',
            padding: '6px 0 7px',
            fontSize: '1.5rem',
            fontWeight: 400,
            alignItems: 'center',
          }}
        >
          <div
            style={{
              marginRight: '10px',
            }}
          >
            {'Document Status'}
          </div>
          <EInvoicingStatusRenderer status={JSON.parse(invoice.einvoicingMessage).status} />
        </Grid>
        <Grid
          style={{
            fontSize: '1rem',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            id: {invoice.einvoicingDocId}
            <Tooltip title="Copy" arrow placement="top">
              <IconButton onClick={copyId}>
                <CopyIcon style={{ height: '20px', width: '20px', overflow: 'visible' }} />
              </IconButton>
            </Tooltip>
          </div>
        </Grid>
        {messageEvents && messageEvents.length > 0 && (
          <Grid
            container
            style={{
              marginTop: '20px',
            }}
          >
            <Grid
              style={{
                fontSize: '1.1rem',
                padding: '15px 0',
              }}
              item
              container
              spacing={2}
            >
              <Grid item xs={6}>
                Event Time
              </Grid>
              <Grid item xs={6}>
                Message
              </Grid>
            </Grid>
            {messageEvents.map((message: any) => {
              return (
                <Grid
                  key={message.eventDateTime}
                  style={{
                    fontSize: '1rem',
                    padding: '15px 0',
                  }}
                  item
                  container
                  spacing={2}
                >
                  <Grid item xs={6}>
                    {dayjs(new Date(message.eventDateTime)).format('MM/DD/YYYY hh:mm A')}
                  </Grid>
                  <Grid item xs={6}>
                    {message.message}
                  </Grid>
                </Grid>
              );
            })}
          </Grid>
        )}
      </DialogComponent>
    </>
  );
};

export default EInvoicingMessage;

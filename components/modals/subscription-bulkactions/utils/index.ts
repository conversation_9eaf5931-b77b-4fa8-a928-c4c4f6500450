import { Subscription } from '../../../revenue-builder-types/interface';
import {
  ActionItem,
  ActionItemOption,
  ProductRelations,
  ActionType,
  SelectedActionItem,
  ProductRelationWithSubscription,
  FromProductOption,
} from '../types';
import { isUpDownSwapEligible } from '../../../subscription-card/subscription-card-util';
import dayjs from 'dayjs';

export const getBulkActionItems = (
  subscription: Subscription,
  selectedRule: SelectedActionItem,
  targetProductRelation: ActionItemOption,
  type: ActionType,
) => {
  const isMatchedRelation = () => {
    const subscriptionProduct = subscription.productName;

    return (
      selectedRule.fromProduct.fromProductName === subscriptionProduct &&
      selectedRule.toProduct.name === targetProductRelation.toProduct.name &&
      selectedRule.fromProduct.subscriptionId === subscription.id
    );
  };

  if (!isMatchedRelation()) return null;

  return {
    type,
    subscriptionId: subscription.id,
    subscriptionName: subscription.name,
    fromProduct: selectedRule.fromProduct.fromProductName,
    toProduct: selectedRule.toProduct,
    fromUom: selectedRule.fromProduct.subscriptionUOM,
    withUom: selectedRule.withUom,
    startDate: dayjs(selectedRule.startDate).format('YYYY-MM-DD'),
    ...(type === 'swap' && { samePriceSwap: selectedRule.samePriceSwap }),
    ...(type === 'upgrade' && { priceTags: targetProductRelation.priceTags }),
  };
};

export const getProductRelationsByActionType = (
  productRelations: ProductRelations | null,
  subscriptions: Subscription[],
) => {
  const today = dayjs().startOf('day');

  const productRelationsForBulkActions: {
    [key: string]: ProductRelationWithSubscription[];
  } = {
    upgrade: [],
    downgrade: [],
    swap: [],
  };

  if (!productRelations || !subscriptions.length) return productRelationsForBulkActions;

  subscriptions
    .filter((sub) => isUpDownSwapEligible(sub))
    .forEach((subscription) => {
      const subscriptionId = subscription.id;
      if (productRelations[subscriptionId]) {
        const productRelationBySubscriptionId = productRelations[subscriptionId];
        Object.keys(productRelationsForBulkActions).forEach((actionType: string) => {
          const relations: ActionItemOption[] = productRelationBySubscriptionId[actionType] ?? [];
          productRelationsForBulkActions[actionType] = productRelationsForBulkActions[
            actionType
          ].concat(
            relations
              .filter((relation) => {
                if (!relation.startDate) return true;
                const startDate = dayjs(relation.startDate);
                if (!startDate.isValid()) return true;
                // This relationship is valid if its start date is on or before today
                return startDate.isSame(today, 'day') || startDate.isBefore(today, 'day');
              })
              .map((relation) => ({
                ...relation,
                subscriptionId,
                subscriptionUOM: subscription.uom.name,
                subscriptionName: subscription.name,
              })),
          );
        });
      }
    });

  return productRelationsForBulkActions;
};

export const getFromProductOptions = (
  productRelationsByActionType: ProductRelationWithSubscription[],
  actionItems: ActionItem[],
) => {
  if (!productRelationsByActionType.length) return [];

  const uniqueOptions = new Map<string, FromProductOption>();

  productRelationsByActionType
    .filter((relation) => {
      const { fromProductUOMs, sameUomOnly, toProductUOMs, subscriptionUOM } = relation;
      return (
        fromProductUOMs.includes(subscriptionUOM) &&
        (sameUomOnly ? toProductUOMs.includes(subscriptionUOM) : true)
      );
    })
    .filter((relation) => {
      const hasSelectedActionItem = actionItems.some((item) => {
        return (
          item.subscriptionId === relation.subscriptionId &&
          item.fromProduct === relation.fromProduct.name
        );
      });

      return !hasSelectedActionItem;
    })
    .forEach((item) => {
      const {
        subscriptionId,
        subscriptionUOM,
        subscriptionName,
        fromProduct: { id, name },
      } = item;

      const key = `${subscriptionId}_${id}`;
      if (!uniqueOptions.has(key)) {
        uniqueOptions.set(key, {
          key,
          subscriptionId,
          subscriptionUOM,
          subscriptionName,
          fromProductId: id,
          fromProductName: name,
        });
      }
    });

  return Array.from(uniqueOptions.entries()).map(([key, value]) => ({
    key,
    value,
  }));
};

export const getToProductOptions = (
  productRelationsByActionType: ProductRelationWithSubscription[],
  selectedFromProduct: FromProductOption,
) => {
  if (!productRelationsByActionType.length || !selectedFromProduct.key) return [];

  return productRelationsByActionType
    .filter((relation) => {
      const {
        subscriptionId,
        fromProduct: { id },
      } = relation;

      const {
        subscriptionId: selectedFromProductSubscriptionId,
        fromProductId: selectedFromProductId,
      } = selectedFromProduct;

      return subscriptionId === selectedFromProductSubscriptionId && id === selectedFromProductId;
    })
    .map((relation) => ({
      value: relation.toProduct.id,
      name: relation.toProduct.name,
    }));
};

export const getTargetProductRelation = (
  productRelationsByActionType: ProductRelationWithSubscription[],
  selectedFromProduct: FromProductOption,
  selectedToProductId: string | null,
) => {
  if (!productRelationsByActionType.length || !selectedFromProduct.key || !selectedToProductId)
    return null;

  const targetRelation = productRelationsByActionType.find((relation) => {
    const {
      subscriptionId,
      fromProduct: { id: fromProductId },
      toProduct: { id: toProductId },
    } = relation;
    const {
      subscriptionId: selectedFromProductSubscriptionId,
      fromProductId: selectedFromProductId,
    } = selectedFromProduct;

    return (
      subscriptionId === selectedFromProductSubscriptionId &&
      fromProductId === selectedFromProductId &&
      toProductId === selectedToProductId
    );
  });

  if (!targetRelation) return null;

  return targetRelation;
};

export const getToUomOptions = (
  productRelationsByActionType: ProductRelationWithSubscription[],
  selectedFromProduct: FromProductOption,
  selectedToProductId: string | null,
) => {
  if (!productRelationsByActionType.length || !selectedFromProduct.key || !selectedToProductId)
    return [];

  const targetRelation = productRelationsByActionType.find((relation) => {
    const {
      subscriptionId,
      fromProduct: { id: fromProductId },
      toProduct: { id: toProductId },
    } = relation;
    const {
      subscriptionId: selectedFromProductSubscriptionId,
      fromProductId: selectedFromProductId,
    } = selectedFromProduct;

    return (
      subscriptionId === selectedFromProductSubscriptionId &&
      fromProductId === selectedFromProductId &&
      toProductId === selectedToProductId
    );
  });

  if (!targetRelation) return [];

  if (targetRelation.sameUomOnly) {
    return [
      {
        value: targetRelation.subscriptionUOM,
        name: targetRelation.subscriptionUOM,
      },
    ];
  }

  return targetRelation.toProductUOMs.map((uom) => ({
    value: uom,
    name: uom,
  }));
};

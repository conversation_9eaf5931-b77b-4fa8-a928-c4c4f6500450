import { ChangeGroup, ChangeItem } from './interface';
import groupBy from '../util/group-by';
import { Subscription } from '../revenue-builder-types';
import { isValidChangeItemRequest } from '../subscription-card/subscription-action-dialog-validation-util';
import { RubySettings } from '../ruby-settings';

export const getValidateChangeItemsResult = (
  changeItems: ChangeItem[] | undefined,
  rubySettings: RubySettings,
) => {
  const errorMessages: string[] = [];
  if (changeItems && changeItems.length) {
    const requests = changeItems.map((x: ChangeItem) => x.request);
    const assets = changeItems.map((x: ChangeItem) => x.asset);

    const groupedValues = groupBy(requests, 'assetName');
    const arrFiltered = assets.reduce((unique: any, o: Subscription) => {
      if (!unique.some((obj: any) => obj.name === o.name)) {
        unique.push(o);
      }
      return unique;
    }, []);

    Object.keys(groupedValues).forEach((key: string) => {
      const subscription = arrFiltered.find((x: any) => x.name === key);
      if (!subscription) {
        errorMessages.push(`Could not find subscription for asset: ${key}`);
        return;
      }

      const validationResult = isValidChangeItemRequest({
        subscription: subscription,
        rubySettings: rubySettings,
        changeItemRequests: groupedValues[key],
      });

      if (validationResult) {
        errorMessages.push(validationResult.message || 'An unknown validation error occurred');
      }
    });
  }

  return errorMessages;
};

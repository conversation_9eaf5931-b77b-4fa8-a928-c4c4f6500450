import React, { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import DialogComponent from '../dialog-component';
import { useYupValidationResolver } from '../form-validation';
import { Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  text: {
    color: '#000000',
    opacity: 0.5,
  },
  formSection: {
    paddingBottom: '12px',
  },
});

const ChangeFormDialog: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const classes = useStyles();

  const {
    title,
    open,
    handleOpen,
    defaultValues,
    FormSection,
    handleAddChangeToChangeCart,
    validationSchema,
    submitButtonText = 'Confirm',
  } = props;

  const resolver = useYupValidationResolver(validationSchema);
  //@ts-ignore
  const methods = useForm({ defaultValues: {}, resolver });
  const { getValues, reset, handleSubmit } = methods;

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues]);

  const onSubmit = async () => {
    const values = getValues();
    await handleAddChangeToChangeCart(values);
  };

  return (
    <DialogComponent
      width="sm"
      open={open}
      title={title}
      dontShowCloseIcon={true}
      actions={{
        rightButtons: [
          {
            text: submitButtonText,
            type: 'submit',
            onClick: () => {
              try {
                handleSubmit(onSubmit)();
              } catch (err) {
                console.log('err: ', err);
              }
            },
          },
        ],
        leftButtons: [
          {
            onClick: () => handleOpen(false),
            text: 'Cancel',
          },
        ],
      }}
    >
      <FormProvider {...methods}>
        <form className={classes.formSection} onSubmit={handleSubmit(onSubmit)}>
          {FormSection}
        </form>
      </FormProvider>
    </DialogComponent>
  );
};

export default ChangeFormDialog;

import React from 'react';
import { ChangeCartLocalState, ChangeCartLocalStateService, Props } from './interface';
import localforage from 'localforage';

const cache = new Map<string, ChangeCartLocalState | null>();

export const ChangeCartLocalStateContext: React.Context<ChangeCartLocalStateService | null> =
  React.createContext<ChangeCartLocalStateService | null>(null);

/**
 * This is a wrapper on-top-of Customer View and allow changes to be stored in local forage.
 *
 * @param props
 * @constructor
 */
export const ChangeCartLocalStateProvider: React.FC<Props> = (props) => {
  const key = 'change-cart';
  const defaultLocalStateProvider: ChangeCartLocalStateService = {
    getLocalState: async () => {
      if (cache.has(key)) {
        return cache.get(key);
      }
      const item = await localforage.getItem<ChangeCartLocalState>(key);
      cache.set(key, item);
      return item;
    },
    setLocalState: async (changeCartLocalState: ChangeCartLocalState) => {
      cache.set(key, changeCartLocalState);
      await localforage.setItem(key, changeCartLocalState);
      return;
    },
  };

  return (
    <ChangeCartLocalStateContext.Provider value={defaultLocalStateProvider}>
      {props.children}
    </ChangeCartLocalStateContext.Provider>
  );
};

export default ChangeCartLocalStateContext;

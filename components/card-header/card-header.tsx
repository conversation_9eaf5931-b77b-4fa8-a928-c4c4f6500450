import React from 'react';

import { Grid, Typography, makeStyles } from '@material-ui/core';

import UploadImgBtn from '../upload-img-btn';
import { Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  cardView: {
    display: 'flex',
    flexDirection: 'column',
  },
  container: {
    // position: 'relative'
    height: '45px',
  },
  img: {
    position: 'relative',
    width: '80px',
    // left: '30px',
    bottom: '55px',
    border: '3px solid #fff',
    borderRadius: '4px',
  },
  titleWithHover: {
    opacity: '0.7',
    fontSize: '20px',
    margin: 0,
    fontWeight: 'bold',
    '&:hover': {
      cursor: 'pointer',
      textDecoration: 'underline',
    },
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  title: {
    opacity: '0.7',
    fontSize: '20px',
    margin: 0,
    fontWeight: 'bold',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  description: {
    opacity: '0.5',
    color: '#000000',
    fontSize: '12px',
    fontWeight: 500,
    paddingTop: '8px',
    paddingBottom: '12px',
  },
  triangle: {
    position: 'absolute',
    width: 0,
    height: 0,
    left: '322px',
    borderStyle: 'solid',
    borderWidth: '0 10px 10px 0',
    borderColor: 'transparent transparent #B095A7 transparent',
    top: '27px',
    zIndex: 90,
  },
  rectangle: {
    position: 'absolute',
    top: '37px',
    left: '217px',
    width: '115px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 90,
    fontSize: '.5rem',
    // borderRadius: '5px',
    textTransform: 'uppercase',
    borderRadius: '115px 0 0 115px',
    height: '24px',
    fontWeight: 'bold',
    boxShadow: '0px 4px 10px 0px gainsboro',
  },
  statusContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  titleContainer: {
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
    padding: '8px 0',
  },
  subtitle: {
    fontWeight: 500,
    fontSize: '12px',
    opacity: 0.3,
    color: '#000000',
  },
});

export const CardHeader: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const {
    status,
    title,
    description,
    imageSignedUrl,
    statusStyle,
    statusShadowStyle,
    statusHeaderStyle,
    StatusIcon,
    subtitle,
    onTitleClick,
    transactionHub,
  } = props;

  const classes = useStyles();

  return (
    <div className={classes.cardView} role="card-header" style={statusHeaderStyle}>
      <div className={classes.container}>
        <div className={classes.triangle} style={statusShadowStyle} />
        <div className={classes.rectangle} style={statusStyle}>
          <div className={classes.statusContainer} role="status">
            {StatusIcon && (
              <StatusIcon
                width="14px"
                height="14px"
                viewBox="0 -5 24 24"
                // in order to use configurable icon here, we pass the color to the icon if user passes the statusStyle
                // the statusStyle color represents the color of the status + the icon besides the status
                style={{ color: statusStyle?.color }}
              />
            )}
            <span>{status}</span>
          </div>
        </div>
        <div className={classes.img}>
          <UploadImgBtn readOnly storedImgUrl={imageSignedUrl} />
        </div>
      </div>
      <div className={classes.titleContainer}>
        <Grid container direction="row" justifyContent="space-between">
          <Grid item xs={11}>
            <Typography
              component="h2"
              className={onTitleClick ? classes.titleWithHover : classes.title}
              onClick={onTitleClick ? onTitleClick : undefined}
            >
              {title}
            </Typography>
          </Grid>
          <Grid item xs={1}>
            {transactionHub}
          </Grid>
        </Grid>

        <Typography className={classes.subtitle} noWrap component="p">
          {subtitle}
        </Typography>
        <Typography className={classes.description} component="p" noWrap>
          {description}
        </Typography>
      </div>
    </div>
  );
};

export default CardHeader;

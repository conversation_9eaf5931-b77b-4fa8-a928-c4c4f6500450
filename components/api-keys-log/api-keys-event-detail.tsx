import React, { useCallback, useEffect, useRef, useState } from 'react';

import { Button, Grid, Typography, makeStyles } from '@material-ui/core';
import InputAdornment from '@material-ui/core/InputAdornment';
import TextField from '@material-ui/core/TextField';
import { styled } from '@material-ui/core/styles';
import { Clear } from '@material-ui/icons';
import LibraryBooksIcon from '@material-ui/icons/LibraryBooks';
import SearchIcon from '@material-ui/icons/Search';
import ZoomOutMapIcon from '@material-ui/icons/ZoomOutMap';
import Autocomplete from '@material-ui/lab/Autocomplete';
import _ from 'lodash';

import DialogComponent from '../dialog-component/dialog-component';
import { Loading } from '../index';
import { useRubySnackbar } from '../ruby-notifier';

const useStyle = makeStyles({
  container: {
    wordBreak: 'break-all',
    flexWrap: 'wrap',
  },
  title: {
    fontSize: '1rem',
    padding: '10px 30px',
    border: 'none',
    borderBottom: '1px solid lightgray',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    fontWeight: 'bold',
    height: '63px',
  },
  request: {
    padding: 30,
    minHeight: '300px',
  },
  response: {
    padding: 30,
    borderBottom: '1px solid lightgray',
    minHeight: '300px',
  },
  resend: {
    color: '#6239eb',
    fontSize: '12px',
  },
  toolBar: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  toolBarTitle: {
    fontSize: '1rem',
    color: 'black',
  },
  code: {
    fontSize: '14px',
    wordBreak: 'break-all',
    flexWrap: 'wrap',
    whiteSpace: 'pre-wrap',
  },
  noSearchResult: {
    margin: 0,
    paddingTop: '5px',
    paddingLeft: '30px',
    paddingBottom: '5px',
    fontSize: '13px',
    background: 'white',
  },
});

interface Props {
  activeLog: any;
  getEventResponseById: any;
  getEventRequestById: any;
  resendWebhook: any;
  setUp: any;
}

const isJSONString = (str: string) => {
  try {
    if (typeof JSON.parse(str) === 'object') {
      return true;
    }
  } catch (e) {}
  return false;
};

const transformJSONString = (str: string) => {
  if (isJSONString(str)) {
    return JSON.stringify(JSON.parse(str), null, 4);
  } else {
    const jsonString = str.slice(str.indexOf(':') + 3, str.length - 1);
    return JSON.stringify(JSON.parse(jsonString), null, 4);
  }
};

export const ApiKeysDetailDialog: React.FC<{
  open: boolean;
  onClose: () => void;
  value: string;
  type: string;
}> = (props) => {
  const classes = useStyle();
  const { open, onClose, value, type } = props;
  return (
    <DialogComponent open={open} title={type} width={'md'} handleClose={onClose}>
      {value && <code className={classes.code}>{transformJSONString(value)}</code>}
    </DialogComponent>
  );
};

const StyledAutoComplete = styled(Autocomplete)({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#eee',
    border: 'none',
    borderBottom: '1px solid #f1f0f2',
    borderRadius: '0',
    height: '43px',
    padding: '0 30px 0 27px !important',
    display: 'flex',
    flexWrap: 'nowrap', // fix search icon position during CSS transition
  },
});

const ApiKeysEventDetail = ({ activeLog, getEventRequestDetails }: any) => {
  const classes = useStyle();
  const { showSnackbar, Snackbar } = useRubySnackbar();
  const [detailDialog, setDetailDialog] = useState(false);
  const [type, setType] = useState('');
  const [detail, setDetail] = useState('');
  const [request, setRequest] = useState('');
  const [response, setResponse] = useState('');
  const [isRequestLoading, setIsRequestLoading] = useState(false);
  const [isResponseLoading, setIsResponseLoading] = useState(false);
  const [searchResult, setSearchResult] = useState('');
  const [noSearchResult, setNoSearchResult] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const currRef = useRef(null);

  const copyContent = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showSnackbar('confirm', 'Success', 'Copy success');
    } catch (err) {
      showSnackbar('error', 'Error', 'Copy failed');
    }
  };

  const copyRequest = () => {
    const requestDom = document.getElementById('request');
    if (requestDom) {
      copyContent(requestDom?.innerHTML);
    }
  };

  const copyResponse = () => {
    const responseDom = document.getElementById('response');
    if (responseDom) {
      copyContent(responseDom?.innerHTML);
    }
  };

  const expandRequest = () => {
    setType('Request');
    setDetailDialog(true);
    setDetail(request);
  };

  const expandResponse = () => {
    setType('Response');
    setDetailDialog(true);
    setDetail(response);
  };

  const getActiveRequestAndResponse = async () => {
    setIsRequestLoading(true);
    setIsResponseLoading(true);
    const result = await getEventRequestDetails(activeLog.id);
    if (result) {
      setResponse(result.responseBody);
      setRequest(result.requestBody);
    }

    setIsRequestLoading(false);
    setIsResponseLoading(false);
  };

  useEffect(() => {
    if (activeLog) {
      getActiveRequestAndResponse();
    }
  }, [activeLog]);

  const onInputChange = useCallback(
    _.debounce((searchResult: any) => {
      const requestDom = document.getElementById('request');
      const responseDom = document.getElementById('response');
      [requestDom, responseDom].forEach((text) => {
        if (searchResult && text?.innerText.includes(searchResult)) {
          setNoSearchResult(false);
          const res = text?.innerText.replace(
            new RegExp(searchResult, 'g'),
            `<span class='hightLightWordClassName' style="background-color: yellow">${searchResult}</span>`,
          );
          text!.innerHTML = res || '';
          const highlightElms = document.getElementsByClassName('hightLightWordClassName');
          if (highlightElms.length > 0 && searchResult) {
            highlightElms[0].scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'center',
            });
          }
        } else if (searchResult && !text?.innerText.includes(searchResult)) {
          setNoSearchResult(true);
          const res = text?.innerText
            .replace(
              /'<span class='hightLightWordClassName' style="background-color: yellow">'/g,
              '',
            )
            .replace(/'<\/span>'/g, '');
          if (text) {
            text!.innerHTML = res || '';
          }
        } else {
          setNoSearchResult(false);
          const res = text?.innerText
            .replace(
              /'<span class='hightLightWordClassName' style="background-color: yellow">'/g,
              '',
            )
            .replace(/'<\/span>'/g, '');
          if (text) {
            text!.innerHTML = res || '';
          }
        }
      });
      const highlightElms = document.getElementsByClassName('hightLightWordClassName');
      if (highlightElms.length > 0) {
        setNoSearchResult(false);
      }
      setIsSearching(false);
    }, 2000),
    [],
  );

  useEffect(() => {
    setIsSearching(true);
    onInputChange(searchResult.trim());
  }, [searchResult]);

  return (
    <>
      <Snackbar />
      <Grid xs={12} container className={classes.container}>
        <Grid item xs={12} style={{ position: 'sticky', top: '0', zIndex: 100 }}>
          <div
            style={{
              fontSize: 16,
              fontWeight: 700,
              backgroundColor: '#fff',
              paddingTop: 29,
              paddingBottom: 19,
              paddingLeft: 20,
            }}
          >
            {activeLog?.url}
          </div>
          <StyledAutoComplete
            options={[]}
            freeSolo
            inputValue={searchResult}
            fullWidth={true}
            onInputChange={(event, value, reason) => {
              setSearchResult(value);
            }}
            renderInput={(params) => {
              return (
                <TextField
                  {...params}
                  inputRef={currRef}
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: (
                      <InputAdornment position="start" color="#6239EB">
                        <SearchIcon style={{ transition: 'none' }} />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end" color="#6239EB">
                        {isSearching && searchResult ? (
                          <Loading size="24px" />
                        ) : (
                          <Clear onClick={() => setSearchResult('')} />
                        )}
                      </InputAdornment>
                    ),
                  }}
                  variant="outlined"
                  placeholder="Search Messages"
                />
              );
            }}
          />
          {noSearchResult && searchResult && (
            <p className={classes.noSearchResult}>Sorry, there's no search result.</p>
          )}
        </Grid>
        <Grid item xs={12} className={classes.response}>
          <Grid item className={classes.toolBar}>
            <Typography className={classes.toolBarTitle}>Response</Typography>
            <Grid>
              <Button
                variant="text"
                className={classes.resend}
                startIcon={<LibraryBooksIcon />}
                onClick={copyResponse}
              >
                Copy
              </Button>
              <Button
                variant="text"
                className={classes.resend}
                startIcon={<ZoomOutMapIcon />}
                onClick={expandResponse}
              >
                Expend
              </Button>
            </Grid>
          </Grid>

          {isResponseLoading ? (
            <Loading style={{ minHeight: '350px' }} />
          ) : (
            response && (
              <code className={classes.code} id="response">
                {transformJSONString(response)}
              </code>
            )
          )}
        </Grid>
        <Grid item xs={12} className={classes.request}>
          <Grid item className={classes.toolBar}>
            <Typography className={classes.toolBarTitle}>Request</Typography>
            <Grid>
              <Button
                variant="text"
                className={classes.resend}
                startIcon={<LibraryBooksIcon />}
                onClick={copyRequest}
              >
                Copy
              </Button>
              <Button
                variant="text"
                className={classes.resend}
                startIcon={<ZoomOutMapIcon />}
                onClick={expandRequest}
              >
                Expend
              </Button>
            </Grid>
          </Grid>
          {isRequestLoading ? (
            <Loading style={{ minHeight: '350px' }} />
          ) : (
            request && (
              <code className={classes.code} id="request">
                {transformJSONString(request)}
              </code>
            )
          )}
        </Grid>
        <ApiKeysDetailDialog
          type={type}
          open={detailDialog}
          onClose={() => setDetailDialog(false)}
          value={detail}
        />
      </Grid>
    </>
  );
};

export default ApiKeysEventDetail;

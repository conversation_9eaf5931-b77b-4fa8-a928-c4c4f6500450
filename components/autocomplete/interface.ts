import React from 'react';

export interface Props {
  placeholder?: string;
  value?: any;
  suggestions?: Array<string | object>;
  async?: boolean;
  asyncFetchSuggestions?: (inputValue: string) => Promise<Array<string | object>>;
  asyncFetchSelectedItem?: (value: any) => Promise<string | object>;
  onChange: (event: { label: string; value: any; index: number }) => void;
  getSuggestionItemLabel: (suggestion: any) => string;
  getSelectedItemLabel?: (suggestion: any) => string;
  getSuggestionItemValue: (suggestion: any) => any;
  readOnly?: boolean;
  disabled?: boolean;
  endAdornment?: React.ReactNode;
  style?: React.CSSProperties;
  inputStyle?: React.CSSProperties;
  setSuggestionValue?: (suggested: string | null) => void;
  useDorpdownPopper?: boolean;
  poperStyle?: any;
  getFocusElement?: (event: any) => any;
  isClickToShowDirectly?: boolean;
}

export type SuggestionItem = {
  label: string;
  value: any;
  category?: string;
  icon?: any;
  isDisabled?: boolean;
};

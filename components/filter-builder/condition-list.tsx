import React from 'react';
import Button from '@material-ui/core/Button';
import FormControl from '@material-ui/core/FormControl';
import MenuItem from '@material-ui/core/MenuItem';
import Select from '@material-ui/core/Select';
import { makeStyles } from '@material-ui/core/styles';
import HighlightOffOutlined from '@material-ui/icons/HighlightOffOutlined';
import PropTypes from 'prop-types';
import type { FilterBuilderCondition } from '../graph-ql-query-constructor/interface';
import type { RubyField, RubyObject } from '../metadata/interface';
import ConditionLine from './condition-line';
import FilterBuilderStyles from './filter-builder-styles';
import { operandTypes } from './operator-map';

// @ts-ignore
const useStyles = makeStyles(FilterBuilderStyles);

/**
 * Note: much of this code was taken from https://github.com/logipro/logi-filter-builder and refractored for our case to construct filters into GraphQL
 * TODO: Refractor to make this into a functional component and clean up code
 */

interface Props {
  conditions: FilterBuilderCondition[];
  fields: RubyField[];
  handleChange: (
    selectedField: FilterBuilderCondition,
    index: number,
    value: string | RubyField,
    changeType: string,
    translateValue: string | null,
    conditions: FilterBuilderCondition[],
    updateConditions: (newConditions: FilterBuilderCondition[]) => void,
  ) => void;
  updateConditions: (conditionsCopy: FilterBuilderCondition[]) => void;
  isFirst: boolean;
  objectMetadata: RubyObject;
  allMetadatas?: RubyObject[];
  disabled?: boolean;
  isNested?: boolean;
  hideNestedCondition?: boolean;
  specifyRelatedObjects?: Record<string, string[]>;
}
const ConditionList = (props: Props) => {
  const classes = useStyles();
  const {
    conditions,
    fields,
    handleChange,
    updateConditions,
    isFirst,
    objectMetadata,
    disabled,
    isNested = false,
    hideNestedCondition,
    allMetadatas,
    specifyRelatedObjects,
  } = props;

  const addNewNestedCondition = (
    existingConditions: FilterBuilderCondition[],
    condition: FilterBuilderCondition,
    index: number,
  ) => {
    // retrieve from props
    const conditionCopy = { ...condition };
    conditionCopy.nestedConditions = [];
    const newNestedCondition: FilterBuilderCondition = {
      id: `${conditionCopy.id}_nested_condition_${conditionCopy.nestedConditions.length}`,
      type: 'Nested',
      field: null,
      value: null,
      operand: operandTypes.get(' _and '),
    };
    conditionCopy.nestedConditions.push(newNestedCondition);
    const conditionsCopy = [...existingConditions];
    conditionsCopy.splice(index, 1, conditionCopy);
    updateConditions(conditionsCopy);
  };

  const addNewCondition = (isFirstCondition: boolean) => {
    const conditionsCopy = [...conditions];
    const conditionNumber = conditionsCopy.length + 1;
    const newCondition: FilterBuilderCondition = {
      id: `condition_${conditionNumber}`,
      error: '',
      field: null,
      value: null,
      type: 'Simple',
      operand: operandTypes.get(' _and '),
      nestedConditions: [],
    };
    if (!isFirstCondition) {
      newCondition.type = 'Nested';
    }
    conditionsCopy.push(newCondition);
    updateConditions(conditionsCopy);
  };

  const removeCondition = (conditionIndex: number) => {
    const conditionsCopy = [...conditions];
    conditionsCopy.splice(conditionIndex, 1);
    updateConditions(conditionsCopy);
  };

  return (
    <div className={classes.root && (!isFirst ? `${classes.nestedCondition}` : undefined)}>
      {
        conditions.map(
          (
            condition: FilterBuilderCondition,
            index: number,
            currentConditions: FilterBuilderCondition[],
          ) => {
            return (
              <div key={index} className={classes.root}>
                <div className={classes.conditionSection}>
                  {index === 0 && isFirst ? (
                    <FormControl className={classes.formControl}>
                      <p className={classes.whereTextField}>Where</p>
                    </FormControl>
                  ) : (
                    <FormControl className={classes.formControl}>
                      <Select
                        disableUnderline
                        classes={{
                          // @ts-ignore
                          root: classes.operatorRoot,
                        }}
                        disabled={disabled}
                        value={
                          isFirst
                            ? currentConditions[index - 1].operand?.id
                            : currentConditions[index].operand?.id // default must be AND
                        }
                        name="filter"
                        onChange={(e: any) => {
                          handleChange(
                            isFirst === true
                              ? currentConditions[index - 1]
                              : currentConditions[index],
                            index,
                            e.target.value,
                            'operand',
                            null,
                            currentConditions,
                            updateConditions,
                          );
                        }}
                      >
                        {Array.from(operandTypes.values()).map((option, i) => (
                          <MenuItem key={i} value={option.id} disabled={disabled}>
                            {option.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                  <ConditionLine
                    isNested={isNested}
                    key={index}
                    classes={classes}
                    condition={condition}
                    index={index}
                    handleChange={handleChange}
                    fields={fields}
                    disabled={disabled}
                    conditions={conditions}
                    updateConditions={updateConditions}
                    objectMetadata={objectMetadata}
                    allMetadatas={allMetadatas}
                    specifyRelatedObjects={specifyRelatedObjects}
                  />
                  {!condition.isRequired && (
                    <FormControl className={classes.formControl}>
                      <Button
                        aria-label="Remove"
                        disableRipple
                        className={classes.button}
                        disabled={disabled}
                        onClick={() => {
                          removeCondition(index);
                        }}
                      >
                        <HighlightOffOutlined />
                      </Button>
                    </FormControl>
                  )}
                </div>
                {condition.error ? (
                  <div className={classes.errorTextField}>{condition.error}</div>
                ) : null}
                <div className={classes.linkSection}>
                  <FormControl className={classes.formControl}>
                    {!disabled ? (
                      <span
                        className={classes.newConditionLink}
                        onClick={() => addNewCondition(isFirst)}
                      >
                        + New Condition
                      </span>
                    ) : (
                      <span className={classes.newConditionLink}>+ New Condition</span>
                    )}
                  </FormControl>
                  {!hideNestedCondition && (
                    <FormControl className={classes.formControl}>
                      {!disabled ? (
                        <span
                          className={classes.newConditionLink}
                          onClick={() => {
                            addNewNestedCondition(conditions, condition, index);
                          }}
                        >
                          + Nested Condition
                        </span>
                      ) : (
                        <span className={classes.newConditionLink}>+ Nested Condition</span>
                      )}
                    </FormControl>
                  )}
                </div>
                {/* Base case */}
                {condition.nestedConditions && condition.nestedConditions.length > 0 && (
                  <ConditionList
                    isNested={true}
                    conditions={condition.nestedConditions}
                    fields={fields}
                    disabled={disabled}
                    handleChange={handleChange}
                    objectMetadata={objectMetadata}
                    updateConditions={(nestedConditions) => {
                      const conditionCopy = { ...condition };
                      conditionCopy.nestedConditions = nestedConditions;
                      const conditionsCopy = [...conditions];
                      conditionsCopy.splice(index, 1, conditionCopy);
                      updateConditions(conditionsCopy);
                    }}
                    isFirst={false}
                    allMetadatas={allMetadatas}
                    specifyRelatedObjects={specifyRelatedObjects}
                  />
                )}
              </div>
            ); // return statement
          }, // conditions.map curly brace
        ) // map statement
      }
    </div>
  );
};

ConditionList.propTypes = {
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      apiName: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      type: PropTypes.oneOf([
        'id',
        'text',
        'integer',
        'decimal',
        'date',
        'dateTime',
        'phone',
        'boolean',
        'email',
        'currency',
        'longText',
        'bId',
        'bLookup',
        'bMasterDetail',
        'richText',
        'pickList',
        'autoNumber',
      ]).isRequired,
    }),
  ).isRequired,
  handleChange: PropTypes.func.isRequired,
  // TODO: add shape for conditions
  conditions: PropTypes.array.isRequired,
  updateConditions: PropTypes.func.isRequired,
  isFirst: PropTypes.bool,
};

export default ConditionList;

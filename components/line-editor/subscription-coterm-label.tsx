import React from 'react';
import Grid from '@material-ui/core/Grid';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import dayjs from 'dayjs';

const useStyles = makeStyles({
  textHighlight: {
    color: '#6239eb',
  },
  title: {
    paddingBottom: '4px',
  },
});

interface Props {
  endDate: string;
  labels: Array<string>;
  title?: string;
}

export const SubscriptionCotermLabel: React.FC<Props> = (props) => {
  const { endDate, labels, title } = props;

  const classes = useStyles();

  return (
    <Grid container>
      {title && (
        <Grid item xs={12} className={classes.title}>
          <Typography component="p" variant="body1">
            {title}
          </Typography>
        </Grid>
      )}
      <Grid item xs={6}>
        {labels.map((label: string, index: number) => (
          <Typography key={`${label}-${index}`} component="p" variant="body1">
            {label}
          </Typography>
        ))}
      </Grid>
      {endDate && (
        <Grid item xs={6}>
          <Typography component="p" variant="body1">
            End Date:{' '}
            <Typography
              component="p"
              variant="body1"
              className={classes.textHighlight}
              display="inline"
            >
              {dayjs(endDate).format('MM/DD/YYYY')}
            </Typography>
          </Typography>
        </Grid>
      )}
    </Grid>
  );
};

export default SubscriptionCotermLabel;

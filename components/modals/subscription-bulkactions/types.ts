import type {
  ChangeGroup,
  ChangeItem,
  ChangeModalProps,
  RubyObject,
} from '@nue-apps/ruby-ui-component';
import type { Subscription } from '../../revenue-builder-types/interface';
import type { RubySettings } from '../../ruby-settings/interface';

export interface ActionSectionProps {
  type: ActionType;
  selected: SelectedActionItem;
  setSelected: (value: SelectedActionItem) => void;
}

export interface SelectedActionItem {
  toProduct: ProductInfoOption;
  fromProduct: FromProductOption;
  withUom: string;
  startDate: string;
  samePriceSwap?: boolean;
  priceTags?: string[];
}

export interface Props {
  open: boolean;
  handleClose: () => void;
  subscriptions: Subscription[];
  onSubmit: (items: { changeGroup: ChangeGroup; changeItems: ChangeItem[] }[]) => void;
  submitButtonText?: string;
  subscriptionMetadata: RubyObject;
  rubySettings: RubySettings;
  setChangeModalProps: (props: ChangeModalProps | null) => void;
  getSwapUpgradeDowngradeOptions: (params: {
    subscriptionIds: string[];
  }) => Promise<ProductRelations>;
}

export interface ActionItem {
  type: ActionType;
  subscriptionId: string;
  subscriptionName: string;
  fromProduct: string;
  toProduct: ProductInfoOption;
  fromUom: string;
  withUom: string;
  startDate: string;
  samePriceSwap?: boolean;
  priceTags?: string[];
}

export type ActionType = 'upgrade' | 'downgrade' | 'swap';

export interface ProductInfo {
  sku: string;
  recordType: string;
  productCategory: string;
  priceModel: string;
  name: string;
  id: string;
  freeTrialUnit: string | null;
  freeTrialType: string;
}

export interface ActionItemOption {
  toProductUOMs: string[];
  toProduct: ProductInfo;
  startDate: string;
  sameUomOnly: boolean;
  samePriceSwap: boolean;
  relationshipType: string;
  fromProductUOMs: string[];
  fromProduct: ProductInfo;
  priceTags?: string[];
}

export interface ProductRelationWithSubscription extends ActionItemOption {
  subscriptionId: string;
  subscriptionUOM: string;
  subscriptionName: string;
}

export type SubscriptionActionOptions = Record<string, ActionItemOption[]>;

export type ProductRelations = Record<string, SubscriptionActionOptions>;

export type ProductInfoOption = Pick<ProductInfo, 'id' | 'name'>;

export type FromProductOption = {
  key: string;
  subscriptionId: string;
  subscriptionName: string;
  subscriptionUOM: string;
  fromProductId: string;
  fromProductName: string;
};

import { ChangeGroup } from '@nue-apps/ruby-ui-component';
import { ChangeItem } from '../change-cart/interface';
import { PriceBook } from '../metadata';

export interface Props {
  changeGroup: ChangeGroup;
  changeGroupNumber: number;
  handleRemoveChangesFromCart: (id: string) => void;
  handleRemoveChangeItems: (changeGroupId: string, changeItemIds: string[]) => void;
  handleCheckout: () => void;
  onAddToCartComplete: (changeGroup: ChangeGroup, changeItems: Array<ChangeItem>) => void;
}

export interface ChangeItemText {
  changeItem: ChangeItem;
}

export interface ChangeCardHeaderProps {
  changeGroupNumber: number;
  priceBook: PriceBook;
  currencyIsoCode: string;
  numberChangeItems: number;
  customer: {
    id: string;
    name: string;
  };
}

export interface ChangeCardHeadingProps {
  title: string;
  value: string;
}

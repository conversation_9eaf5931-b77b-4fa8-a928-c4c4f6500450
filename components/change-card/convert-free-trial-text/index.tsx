import React from 'react';
import { Typography, makeStyles } from '@material-ui/core';
import dayjs from 'dayjs';
import type { ChangeItemText } from '../interface';
import type { ConvertFreeTrialRequest } from '../../change-cart';
import {
  formatDimension,
  formatTerm,
} from '../../subscription-card/info-section-text-registry/convert-free-trial-text/utils';

const useStyles = makeStyles((theme) => ({
  itemText: {
    color: theme.palette.grey[900],
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
}));

const ConvertFreeTrialText: React.FC<ChangeItemText> = (props) => {
  const { changeItem } = props;

  const classes = useStyles();

  const request = changeItem.request as ConvertFreeTrialRequest;
  const {
    objectType,
    assetName,
    term,
    startDate,
    switchToEvergreen,
    cotermToBundleSubscription,
    uomDimension,
  } = request;

  const formattedTerm = formatTerm(term);
  const formattedDimension = formatDimension(uomDimension, term);
  const formattedStartDate = dayjs(startDate).format('MM/DD/YYYY');

  const highlighted = React.useCallback(
    (text: string) => {
      return <span className={classes.textHighlight}>{text}</span>;
    },
    [classes.textHighlight],
  );

  const messageSuffix = React.useMemo(() => {
    if (switchToEvergreen) {
      return <>with an {highlighted('evergreen')} term.</>;
    }

    if (cotermToBundleSubscription) {
      return (
        <>
          starting {highlighted(`${formattedStartDate}, co-termed`)} with the bundle subscription.
        </>
      );
    }

    return (
      <>
        for {highlighted(`${formattedTerm} `)}
        {formattedDimension} starting {highlighted(formattedStartDate)}.
      </>
    );
  }, [
    highlighted,
    switchToEvergreen,
    cotermToBundleSubscription,
    formattedStartDate,
    formattedTerm,
    formattedDimension,
  ]);

  return (
    <Typography variant="body2" component="p" display="inline" className={classes.itemText}>
      The {objectType} <span className={classes.textUnderline}>{assetName}</span> is converted to a
      paid subscription {messageSuffix}
    </Typography>
  );
};

export default ConvertFreeTrialText;

import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';

export interface IconProps {
  style?: React.CSSProperties;
  className?: string | null;
}

export const HintIcon: React.FC<IconProps> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon viewBox="0 0 32 32" style={style} className={userProps.className || ''}>
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <title>Hint</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-6-1-Edit-Lines-(Tutorial)" transform="translate(-1096.000000, -788.000000)">
            <g id="Group-2" transform="translate(1096.000000, 772.000000)">
              <g id="Group-15" transform="translate(0.000000, 16.000000)">
                <circle id="Oval" fill="#FFFFFF" cx="16" cy="16" r="16" />
                <g id="lightbulb-line" transform="translate(6.000000, 6.000000)">
                  <polygon id="Path" points="0 0 20 0 20 20 0 20" />
                  <path
                    d="M8.31083333,15 L9.16666667,15 L9.16666667,10.8333333 L10.8333333,10.8333333 L10.8333333,15 L11.6891667,15 C11.7991667,13.9983333 12.31,13.1716667 13.1391667,12.2691667 C13.2333333,12.1675 13.8325,11.5466667 13.9033333,11.4583333 C15.5396652,9.41405109 15.3216167,6.45356542 13.4034324,4.67108377 C11.4852481,2.88860212 8.51674339,2.8879685 6.59779832,4.66963113 C4.67885324,6.45129375 4.45954096,9.41168607 6.095,11.4566667 C6.16666667,11.5458333 6.7675,12.1675 6.86,12.2683333 C7.69,13.1716667 8.20083333,13.9983333 8.31083333,15 L8.31083333,15 Z M8.33333333,16.6666667 L8.33333333,17.5 L11.6666667,17.5 L11.6666667,16.6666667 L8.33333333,16.6666667 Z M4.795,12.5 C2.61297734,9.77375759 2.90436317,5.82571106 5.46298367,3.44935213 C8.02160416,1.07299321 11.9803889,1.07362712 14.5382482,3.45080534 C17.0961075,5.82798356 17.386229,9.7761232 15.2033333,12.5016667 C14.6866667,13.145 13.3333333,14.1666667 13.3333333,15.4166667 L13.3333333,17.5 C13.3333333,18.4204746 12.5871412,19.1666667 11.6666667,19.1666667 L8.33333333,19.1666667 C7.41285875,19.1666667 6.66666667,18.4204746 6.66666667,17.5 L6.66666667,15.4166667 C6.66666667,14.1666667 5.3125,13.145 4.795,12.5 Z"
                    id="Shape"
                    fill="#000000"
                    fillRule="nonzero"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default HintIcon;

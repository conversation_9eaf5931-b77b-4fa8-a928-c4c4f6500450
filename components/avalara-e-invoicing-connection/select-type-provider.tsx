import React from 'react';
import { DataTypeProvider } from '@devexpress/dx-react-grid';
import Input from '@material-ui/core/Input';
import MenuItem from '@material-ui/core/MenuItem';
import Select from '@material-ui/core/Select';
import type { RubyField, ValuePair } from '@nue-apps/ruby-ui-component';
import { StatusRenderer } from '@nue-apps/ruby-ui-component';

const getSelectTypeReturnValue = (column: RubyField, value: string) => {
  if (
    column &&
    (column.apiName === 'status' ||
      column.apiName === 'publishStatus' ||
      column.apiName === 'recordType')
  ) {
    return <StatusRenderer status={value} />;
  }
  return <>{value}</>;
};

const SelectEditor: React.ComponentType<DataTypeProvider.ValueEditorProps> = ({
  value,
  onValueChange,
  column,
  disabled,
  onBlur,
  row,
}) => {
  const onClose = () => {
    setTimeout(() => {
      onBlur();
    }, 50);
  };

  const columnField = column as RubyField;
  // if(row.elrXpath === 'Invoice/cac:InvoiceLine/cbc:InvoicedQuantity/@unitCode') {
  //   const index = columnField.valueSet?.valuePairs.findIndex((pair: any) => pair.apiName === 'UOM')
  //   if(index === -1) {
  //     columnField.valueSet?.valuePairs.push({
  //       name: 'Uom',
  //       apiName: 'UOM',
  //       active: true
  //     })
  //   }
  // } else {
  //   const index = columnField.valueSet?.valuePairs.findIndex((pair: any) => pair.apiName === 'UOM')
  //   if(index !== -1 && index) {
  //     columnField.valueSet?.valuePairs.splice(index, 1)
  //   }
  // }
  const isColumnNotUpdatable = row.pickListUpdatable === false;
  const isNotNewAddedRow = row.id && !row.id.endsWith('_temp');

  return (
    <Select
      disabled={
        ((column.name === 'nueObject' || column.name === 'nueField') && row.disableNueMappings) ||
        column.name === 'elrXpath'
      }
      input={<Input />}
      value={value}
      onBlur={onClose}
      onChange={(event) => onValueChange(event.target.value)}
      style={{ width: '100%' }}
    >
      {columnField.required !== true && <MenuItem value={''}>None</MenuItem>}
      {column.name === 'nueField' && row.valueSet && row.valueSet.valuePairs ? (
        row.valueSet.valuePairs.map((valuePair: any, index: number) => (
          <MenuItem key={index} value={valuePair.apiName}>
            {valuePair.name}
          </MenuItem>
        ))
      ) : columnField.valueSet ? (
        columnField.valueSet.valuePairs.map((valuePair: any, index: number) => (
          <MenuItem key={index} value={valuePair.apiName}>
            {valuePair.name}
          </MenuItem>
        ))
      ) : (
        <MenuItem value={value}>{value}</MenuItem>
      )}
    </Select>
  );
};

const SelectTypeFormatter: React.ComponentType<DataTypeProvider.ValueFormatterProps> = ({
  value,
  column,
  row,
}) => {
  const _val = value || row[column.name];
  if (_val === undefined || _val === null) {
    return null;
  }

  const columnField = column as RubyField;
  if (columnField.valueSet) {
    const displayValue = (columnField?.valueSet?.valuePairs || []).find(
      (_: ValuePair) => _.apiName === _val,
    )?.name;
    return getSelectTypeReturnValue(columnField, displayValue || _val);
  }
  return getSelectTypeReturnValue(columnField, _val);
};

export const SelectTypeProvider = (props: any) => (
  <DataTypeProvider
    {...props}
    editorComponent={SelectEditor}
    formatterComponent={SelectTypeFormatter}
  />
);

export default SelectTypeProvider;

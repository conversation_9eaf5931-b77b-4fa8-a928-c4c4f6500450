import React, { ChangeEvent, useContext, useEffect, useState } from 'react';
import { Grid, IconButton, Paper, TextField, Tooltip } from '@material-ui/core';
import FormControl from '@material-ui/core/FormControl';
import { makeStyles } from '@material-ui/core/styles';
import Autocomplete from '@material-ui/lab/Autocomplete';
import { SquareDownloadIcon } from '../icons';
import Loading from '../loading';
import MultiSelectionSearchBar, { SelectedObject } from '../multi-selection-search-bar';
import { PickListOption } from '../pick-list';
import TemplateBuilderContext from '../template-builder-context';
import { PreviewRecordListProps } from './interface';

export const useStyles = makeStyles({
  recordListContainer: {
    padding: '50px 20px',
  },
  inputRoot: {
    background: 'white',
    border: '1px solid #ced4da',
    borderRadius: '4px',
    '&>div': {
      padding: '0!important',
      fontSize: '.875rem',
    },
    '& input': {
      fontSize: '.875rem',
      padding: '12px 20px!important',
      color: '#4d4c50',
    },
    '& fieldset': {
      border: 0,
      color: '#4d4c50',
    },
  },
  autocompleteOption: {
    fontSize: '.875rem',
    color: '#4d4c50',
  },
});

export const PreviewRecordList = (props: PreviewRecordListProps) => {
  const classes = useStyles();

  const templateBuilderContext = useContext(TemplateBuilderContext);
  const { templateBuilderService } = templateBuilderContext;
  if (!templateBuilderService) {
    throw Error(
      'TemplateSetting component requires templateBuilderService to be declared in context provider',
    );
  }
  const {
    getTemplateBuilderConfig,
    setTemplateBuilderConfig,
    getTemplateRecord,
    setTemplateRecord,
  } = templateBuilderService;
  const { templateConfig } = getTemplateBuilderConfig();
  const { templateRecord } = getTemplateRecord();

  const { download, invoiceList, getInvoiceById, setLoading, loaded, mode } = props;
  const [downloading, setDownloading] = useState(false);

  const downloadPdf = async () => {
    setDownloading(true);
    await download(setDownloading);
  };

  useEffect(() => {
    if (invoiceList) {
      handleUpdateRecord(invoiceList[0]);
    }
  }, []);

  const handleUpdateRecord = async (invoice: any) => {
    if (getInvoiceById && invoice) {
      setLoading(true);
      const data = await getInvoiceById(mode || 'invoice', invoice.id, templateConfig);
      if (data.data.company) {
        if (data.data.Invoice) {
          data.data.Invoice[0].company = data.data.company;
          setTemplateRecord(data.data.Invoice[0]);
        }
        if (data.data.Quote) {
          data.data.Quote[0].company = data.data.company;
          setTemplateRecord(data.data.Quote[0]);
        }
        if (data.data.Order && data.data.Order.length > 0) {
          data.data.Order[0].company = data.data.company;
          setTemplateRecord(data.data.Order[0]);
        }
        if (data.data.CreditMemo && data.data.CreditMemo.length > 0) {
          data.data.CreditMemo[0].company = data.data.company;
          setTemplateRecord(data.data.CreditMemo[0]);
        }
      } else {
        if (data.data.Invoice) {
          setTemplateRecord(data.data.Invoice[0]);
        }
        if (data.data.Quote) {
          setTemplateRecord(data.data.Quote[0]);
        }
        if (data.data.Order && data.data.Order.length > 0) {
          setTemplateRecord(data.data.Order[0]);
        }
        if (data.data.CreditMemo && data.data.CreditMemo.length > 0) {
          setTemplateRecord(data.data.CreditMemo[0]);
        }
      }
      if (data.template) {
        setTemplateBuilderConfig(data.template);
      }
      setLoading(false);
    }
  };

  return (
    <Grid
      container
      justifyContent="space-between"
      alignItems="center"
      className={classes.recordListContainer}
    >
      <Grid item container xs={8} alignItems="center">
        <Grid item>Previewing Record</Grid>
        <Grid item xs={6}>
          <Autocomplete
            id="combo-box-demo"
            options={invoiceList || []}
            disableClearable={true}
            getOptionLabel={(option) => option.name || option.orderNumber}
            style={{
              width: 200,
              padding: 0,
              marginLeft: '20px',
              background: 'white',
              borderRadius: '5px',
            }}
            classes={{ option: classes.autocompleteOption }}
            onChange={(event: object, value: any, reason: string) => {
              const newValue = value;
              handleUpdateRecord(newValue);
            }}
            renderInput={(params: any) => (
              <TextField {...params} label="" variant="outlined" className={classes.inputRoot} />
            )}
            defaultValue={invoiceList && invoiceList[0]}
          />
        </Grid>
      </Grid>
      {/* {loaded && (
        <Grid item>
          {downloading ? (
            <Loading size="25px" />
          ) : (
            <Tooltip title="Download PDF" arrow placement="bottom">
              <IconButton style={{ padding: '5px', marginTop: '-15px' }} onClick={downloadPdf}>
                <SquareDownloadIcon />
              </IconButton>
            </Tooltip>
          )}
        </Grid>
      )} */}
    </Grid>
  );
};

export default PreviewRecordList;

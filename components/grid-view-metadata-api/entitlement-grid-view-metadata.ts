import BarChartOutlinedIcon from '@material-ui/icons/BarChartOutlined';
import CheckCircleOutlineIcon from '@material-ui/icons/CheckCircleOutline';
import DescriptionOutlinedIcon from '@material-ui/icons/DescriptionOutlined';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import MoreHorizOutlinedIcon from '@material-ui/icons/MoreHorizOutlined';
import ZoomInIcon from '@material-ui/icons/ZoomIn';

import ActiveIcon from '../active-icon';
import DraftIcon from '../draft-icon';
import { Entitlement } from '../revenue-builder-types';
import type { CardAction } from '../metadata/interface';

const [updateQuantity, more, manageMilestones]: CardAction[] = [
  {
    id: 'updateQuantity',
    name: 'Update Quantity',
    description: 'Update Quantity',
    icon: FileCopyOutlinedIcon,
    showTooltip: true,
    toolTipText: 'Update Entitlement Quantity',
  },
  {
    id: 'more',
    name: 'More...',
    description: 'More',
    icon: MoreHorizOutlinedIcon,
  },
  {
    id: 'manageMilestones',
    name: 'Manage Milestones',
    description: 'Manage Milestones',
    icon: ZoomInIcon,
  },
];

const entitlementCardActions: (args: { object: Record<string, any> }) => CardAction[] = ({
  object,
}) => {
  if (object.bundled === true || object.quantityEditable === false) {
    return [more, manageMilestones];
  } else {
    return [updateQuantity, more, manageMilestones];
  }
};

export default {
  cardHeaderMetadata: {
    title: 'name',
    description: '',
    statusStyle: {
      Active: {
        backgroundColor: '#6239EB',
        color: '#FFF',
      },
      Inactive: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
      Expired: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
    },
    statusShadowStyle: {
      Active: {
        borderColor: 'transparent transparent #000000 transparent',
      },
      Inactive: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
      Expired: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
    },
    statusIcon: {
      Active: ActiveIcon,
      Expired: DraftIcon,
    },
    // TODO: switch to using asset interface
    status: 'status',
    imageSignedUrl: (entitlement: Entitlement) => entitlement.product?.imageSignedUrl,
  },
  cardBodyMetadata: {
    numCardActionsPerRow: 2,
    summaryItemPosition: 'top-left',
    moreActions: [
      {
        id: 'changeLogs',
        name: 'View Change Logs',
        description: 'View Change Logs',
        icon: CheckCircleOutlineIcon,
      },
      {
        id: 'viewOrder',
        name: 'View Order History',
        description: 'View Order History',
        icon: BarChartOutlinedIcon,
        type: 'navigation',
      },
      {
        id: 'viewInvoice',
        name: 'View Invoice',
        description: 'View Invoice',
        icon: DescriptionOutlinedIcon,
        type: 'navigation',
      },
    ],
    popUpMoreActions: [
      {
        id: 'changeLogs',
        name: 'View Change Logs',
        description: 'View Change Logs',
        icon: CheckCircleOutlineIcon,
      },
      {
        id: 'viewOrder',
        name: 'View Order History',
        description: 'View Order History',
        icon: BarChartOutlinedIcon,
        type: 'navigation',
      },
      {
        id: 'viewInvoice',
        name: 'View Invoice',
        description: 'View Invoice',
        icon: DescriptionOutlinedIcon,
        type: 'navigation',
      },
    ],
    actions: {
      Active: entitlementCardActions,
      Purchased: entitlementCardActions,
    },
  },
};

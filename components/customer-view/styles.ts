import { makeStyles } from '@material-ui/core';

export const useStyles = makeStyles({
  btn: {
    color: '#6239EB',
    textTransform: 'none',
  },
  fieldLabel: {
    opacity: '0.4',
    color: '#000000',
    fontSize: '12px',
    fontWeight: 'bold',
    letterSpacing: 0,
    lineHeight: '15px',
    marginRight: '22px',
    textTransform: 'uppercase',
    whiteSpace: 'nowrap',
    marginLeft: 40,
  },
  amountLabel: {
    fontSize: 22,
    color: '#6239EB',
    letterSpacing: '0.69px',
    fontWeight: 700,
  },
  balanceIcon: {
    color: '#FF7D00',
    marginRight: 12.85,
  },
  selectorContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  statsBarTotal: {
    marginTop: '8px',
    marginRight: '8px',
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'baseline',
  },
  statsBarTotalLabel: {
    color: '#BBBBBB',
    fontSize: '12px',
    fontWeight: 'bold',
    letterSpacing: 0,
    lineHeight: '15px',
    marginRight: '12px',
    textTransform: 'uppercase',
    whiteSpace: 'nowrap',
  },
  statsBarTotalValue: {
    fontSize: 30,
    color: '#6239EB',
    letterSpacing: '0.94px',
    fontWeight: 600,
  },
  statsBarRoot: {
    padding: '8px',
  },
  cashOutButton: {
    backgroundColor: '#6239EB',
    opacity: 1,
    color: '#fff',
    boxShadow: '0 8px 24px -2px rgba(0,0,0,0.20)',
    '&:hover': {
      opacity: 0.7,
    },
  },
  gridIcon: {
    background: 'transparent',
    border: 'none',
    color: '#6239eb',
    cursor: 'pointer',
    width: '18px',
    height: '18px',
  },
  cardInfoCard: {
    color: 'rgb(110, 110, 110)',
    padding: '20px',
    minWidth: '300px',
  },
  cardInfoTitle: {
    color: 'rgb(110, 110, 110)',
    fontWeight: 500,
    fontSize: '20px',
    marginBottom: 8,
    display: 'flex',
    alignItems: 'center',
    gap: 6,
  },
  cardInfoLabel: {
    color: 'rgb(110, 110, 110)',
    fontWeight: 500,
    fontSize: '15px',
  },
});

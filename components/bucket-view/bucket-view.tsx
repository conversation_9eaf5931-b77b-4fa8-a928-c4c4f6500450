import React, { useEffect, useState } from 'react';
import Grid from '@material-ui/core/Grid';
import { BucketObject, RubyGrid } from '@nue-apps/ruby-ui-component';
import { BucketViewProps } from './interface';

const BucketView: React.FC<BucketViewProps> = (props) => {
  const { getBucketItems, quoteId, objectMetadata, fieldSetMetadata, currencyIsoCode, BucketItem } =
    props;

  const [buckets, setBuckets] = useState<BucketObject[]>([]);

  const setup = async () => {
    const objects = await getBucketItems(quoteId);
    setBuckets(objects);
  };

  useEffect(() => {
    setup();
  }, []);

  return (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        {buckets.map((bucket: BucketObject, index: number) => (
          <div style={{ marginTop: '32px' }} key={index}>
            <BucketItem
              bucketIndex={index}
              object={bucket}
              fieldSetMetadata={fieldSetMetadata}
              objectMetadata={objectMetadata}
              currencyIsoCode={currencyIsoCode}
            />
          </div>
        ))}
      </Grid>
    </Grid>
  );
};

export default BucketView;

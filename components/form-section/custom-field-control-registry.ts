import React from 'react';

import { CustomFormControlProps } from './interface';

const registry = new Map<String, React.FC<CustomFormControlProps>>();

const CustomFieldControlRegistry = {
  register: (fieldApiName: string, control: React.FC<CustomFormControlProps>) => {
    registry.set(fieldApiName, control);
  },

  unRegister: (fieldApiName: string) => {
    registry.delete(fieldApiName);
  },

  get: (fieldApiName: string) => {
    return registry.get(fieldApiName);
  },
};

export default CustomFieldControlRegistry;

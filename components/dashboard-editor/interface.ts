import React from 'react';
import { Layout } from 'react-grid-layout';

export interface Widget {
  title: string;
  info: string;
  preview: React.ReactNode;
  node: React.ReactNode;
  h?: number;
  w?: number;
}

export interface DashboardEditorProps {
  widgets: Map<string, Widget>;
  defaultLayout?: Layout[];
  onEditMode?: () => void;
  onReadMode?: () => void;
  isInList?: boolean;
  objectName?: string;
  hiddenSetting?: boolean;
}

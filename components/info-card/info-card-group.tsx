import React from 'react';

import { Grid } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';

import { InfoCardGroupProps } from './interface';

const useStyles = makeStyles((theme) => ({
  groupWrapper: {
    '& .card-container:only-child': {
      borderRadius: '10px',
    },
    '& .card-container:first-child': {
      borderTopLeftRadius: '10px',
    },
    '& .card-container:nth-child(2)': {
      borderTopRightRadius: '10px',
    },
    '& .card-container:nth-last-child(2):nth-child(odd)': {
      borderBottomLeftRadius: '10px',
    },
    '& .card-container:last-child:nth-child(even)': {
      borderBottomRightRadius: '10px',
    },
    '& .card-container:nth-last-child(2):nth-child(even)': {
      borderBottomRightRadius: '10px',
    },
    '& .card-container:last-child:nth-child(odd)': {
      borderBottomLeftRadius: '10px',
    },
    '& *': {
      boxSizing: 'border-box',
    },
  },
}));

const InfoCardGroup: React.FC<InfoCardGroupProps> = (props) => {
  const classes = useStyles();
  const cards = props.children;
  return !cards ? (
    <>
      <h1>No data</h1>
    </>
  ) : (
    <>
      <Grid className={classes.groupWrapper} container spacing={1}>
        {React.Children.map(cards, (card, index) => (
          <Grid key={index} item xs={6} className="card-container">
            {card}
          </Grid>
        ))}
      </Grid>
    </>
  );
};

export default InfoCardGroup;

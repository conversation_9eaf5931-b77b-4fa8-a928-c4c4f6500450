export interface LoginDialogProps {
  title: string;
  open: boolean;
  handleOpen: (open: boolean) => void;
  handleLogin: (values: Login) => Promise<void>;
}

export interface Login {
  userName: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  refreshToken: string;
  expiresIn: number;
  challenge: Challenge;
  tenants: UserMappedTenantResponse[];
}

export interface Challenge {
  session: string;
  name: string;
}

export interface UserMappedTenantResponse {
  apiAccessKey: string;
  tenantNumber: number;
  tenantName: string;
  tenantId: string;
  orgId: string;
  orgName: string;
}

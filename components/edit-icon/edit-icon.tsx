import React from 'react';

import SvgIcon from '@material-ui/core/SvgIcon';

import { Props } from './interface';

const EditIcon: React.FC<Props> = ({
  viewBox,
  width,
  height,
  style,
}: React.PropsWithChildren<Props>) => {
  return (
    <SvgIcon>
      <svg
        viewBox={viewBox || '0 0 19 16'}
        style={style}
        width={width || '23px'}
        height={height || '23px'}
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>02BDFFC8-4429-4D77-92E8-092D23B1760D</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-1-1-Subscribers" transform="translate(-453.000000, -666.000000)">
            <g id="Product-Options" transform="translate(360.000000, 288.000000)">
              <g id="Group-10" transform="translate(45.000000, 246.000000)">
                <g id="row" transform="translate(0.500000, 116.000000)">
                  <g id="Actions" transform="translate(24.500000, 16.000000)">
                    <g id="edit-line" transform="translate(24.000000, 0.000000)">
                      <polygon id="Path" points="0.5 0 16.5 0 16.5 16 0.5 16" />
                      <path
                        d="M5.276,10.6666667 L12.0373333,3.90533333 L11.0946667,2.96266667 L4.33333333,9.724 L4.33333333,10.6666667 L5.276,10.6666667 Z M5.82866667,12 L3,12 L3,9.17133333 L10.6233333,1.548 C10.8836666,1.28774537 11.3056668,1.28774537 11.566,1.548 L13.452,3.434 C13.7122546,3.69433323 13.7122546,4.11633343 13.452,4.37666667 L5.82866667,12 Z M3,13.3333333 L15,13.3333333 L15,14.6666667 L3,14.6666667 L3,13.3333333 Z"
                        id="Shape"
                        fill="#6239EB"
                        fillRule="nonzero"
                      />
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default EditIcon;

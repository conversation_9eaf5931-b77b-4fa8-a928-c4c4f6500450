import { Customer } from '../revenue-builder-types/interface';
import ActiveIcon from '../active-icon';
import DraftIcon from '../draft-icon';

export default {
  cardHeaderMetadata: {
    title: 'name',
    // TODO: figure out a metadata-driven way to retrieve status and description
    // may need to do it dynamically since they depend on dynamic values
    description: (customer: Customer) => {
      let description = '';
      if (customer.billingStreet) {
        description += `${customer.billingStreet}, `;
      }
      if (customer.billingCity) {
        description += `${customer.billingCity}, `;
      }
      if (customer.billingStateCode) {
        description += `${customer.billingStateCode}, `;
      }
      if (customer.billingPostalCode) {
        description += `${customer.billingPostalCode}, `;
      }
      if (description.length > 0) {
        description = description.substring(0, description.length - 2);
      }
      return description;
    },
    // TODO when we implement orders, get status by orders active
    statusStyle: {
      Draft: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
      Active: {
        backgroundColor: '#6239EB',
        color: '#FFF',
      },
      Prospecting: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
    },
    statusShadowStyle: {
      Draft: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
      Active: {
        borderColor: 'transparent transparent #000000 transparent',
      },
      Prospecting: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
    },
    statusIcon: {
      Active: ActiveIcon,
      Draft: DraftIcon,
    },
    status: (customer: Customer) => 'Active',
    imageSignedUrl: 'imageSignedUrl',
  },
  cardBodyMetadata: {
    summaryItemPosition: 'top-left',
    actions: [],
  },
};

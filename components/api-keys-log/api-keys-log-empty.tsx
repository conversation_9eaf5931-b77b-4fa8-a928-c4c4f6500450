import React from 'react';

import Grid from '@material-ui/core/Grid';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';

import WebhookEventLogsEmpty from '../../static/images/webhook-eventlogs-empty.png';

const useStyles = makeStyles({
  gridWrapper: {
    height: '75vh',
    width: 'auto',
  },
  bucket: {
    width: '100px',
    height: '100px',
  },
  empty: {
    marginBottom: '-20px',
  },
});

const ApiKeysLogEmpty: React.FC = () => {
  const classes = useStyles();
  return (
    <Grid
      spacing={5}
      className={classes.gridWrapper}
      container
      direction="column"
      justifyContent="center"
      alignItems="center"
    >
      <Grid direction="row" container justifyContent="center" alignItems="center">
        <img alt="image" className={classes.bucket} src={WebhookEventLogsEmpty} />
      </Grid>
      <Grid item className={classes.empty}>
        <Typography>The event log is currently empty.</Typography>
      </Grid>
      <Grid item>
        <Typography />
      </Grid>
    </Grid>
  );
};

export default ApiKeysLogEmpty;

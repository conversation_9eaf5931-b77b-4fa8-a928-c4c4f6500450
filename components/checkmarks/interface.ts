import React from 'react';

import { SelectInputProps } from '@material-ui/core/Select/SelectInput';

import Metadata from '../metadata';

export interface CheckmarksProps {
  labelClassName?: string | null | undefined;
  labelStyles?: Record<string, any>;
  toolTipStyles?: Record<string, any>;
  style?: React.CSSProperties;
  options: string[];
  value: any;
  setValue?: Function;
  selectBorderColor?: boolean;
  setSelectBorderColor?: Function;
  name?: string;
  label?: string;
  fullWidth?: boolean;
  placeholder?: string;
  field: Metadata.RubyField;
  onChange: SelectInputProps['onChange'];
}

export interface Props extends CheckmarksProps {}

import { RubyObject, RubyField, CardAction } from '../metadata';
import type { ColumnConfig } from '../metadata/interface';
import { Condition, RubyFilter } from '../graph-ql-query-constructor/interface';
import { CountQueryExecutor } from '../query-executor';

export interface Props {
  navigateToNewCustomerViewPage: (id: string) => void;
  customerMetadata: RubyObject;
  fieldSetMetadata: Array<RubyField>;
  currencyIsoCode: string;
  updateFilters: (
    objectApiName: string,
    referencedFilterId: string,
    newFilterName: string,
    conditions: Array<Condition>,
  ) => Promise<RubyFilter>;
  saveFilter: (
    objectApiName: string,
    filterName: string,
    conditions: Array<Condition>,
  ) => Promise<RubyFilter>;
  listFilters: (objectApiName: string) => Promise<Array<RubyFilter>>;
  getFieldSetMetadata: (fieldSetName: string, objectApiName: string) => Promise<Array<RubyField>>;
  executeQuery: (query: string, objectMetadata: RubyObject) => Promise<Array<any>>;
  executeGetCountQuery: CountQueryExecutor;
  actionEventHandler: (argument: {
    object: any;
    objectMetadata: RubyObject;
    action: CardAction;
  }) => void | Promise<void>;
  activeTabIndex: number;
  setActiveTabIndex: Function;
  idsCondition?: string;
  jumpFrom?: string;
  getTransactionHubStatus?: Function;
  transactionHubColumn?: ColumnConfig;
}

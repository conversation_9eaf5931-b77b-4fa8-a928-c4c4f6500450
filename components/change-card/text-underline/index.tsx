import makeStyles from '@material-ui/core/styles/makeStyles';
import React from 'react';

export interface TextUnderlineProps {
  children: React.ReactNode;
}

const useStyles = makeStyles((theme) => ({
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
}));

const TextUnderline: React.FC<TextUnderlineProps> = ({ children }) => {
  const classes = useStyles();

  return <span className={classes.textUnderline}>{children}</span>;
};

export default TextUnderline;

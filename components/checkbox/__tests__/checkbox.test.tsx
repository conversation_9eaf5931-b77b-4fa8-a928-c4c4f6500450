import React from 'react';
import '@testing-library/jest-dom';
import { render, screen, fireEvent } from '@testing-library/react';
import RubyCheckbox from '../checkbox';
import { RubyField } from '../../metadata/interface';

describe('Checkbox Tests', () => {
  it('should pass always', () => {
    expect(1 + 1).toEqual(2);
  });
  it('should render checkbox', () => {
    let fieldMetadata: RubyField = {
      name: 'Test Checkbox',
      apiName: 'testCheckbox',
      type: 'boolean',
    };
    render(<RubyCheckbox field={fieldMetadata} value={false} />);
    fireEvent.click(screen.getByText('Test Checkbox'));
  });
});

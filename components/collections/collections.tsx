import React, { useContext, useEffect, useState } from 'react';

import { Grid, Typography, makeStyles, Box } from '@material-ui/core';

import { SubmitHandler } from 'react-hook-form';
import { useRubySnackbar } from '../ruby-notifier';
import WarningIcon from '@material-ui/icons/Warning';

import DialogComponent from '../dialog-component/dialog-component';

import { RubyButtonBar } from '../ruby-button';
import { User } from '../metadata';
import { RubySettingEditorPanel } from '../ruby-settings/ruby-setting-editor-panel';
import { Condition } from '../graph-ql-query-constructor';
import { UserContext } from '../user-context';
import type { RubyObject, UserRole } from '../metadata/interface';
import type {
  PaymentRule,
  PaymentRuleStatus,
  PaymentRuleConditionItem,
  BillSysSetting,
} from './interface';
import CollectionsContext from './collections-context';
import PaymentRuleList from './payment-rule-list';
import PaymentRuleForm from './payment-rule-form';
import _ from 'lodash';
import { OrderSettingEditorSwitchControl } from '../ruby-settings/setting-editors/order-setting-editor-switch-control';

interface Props {}

const useStyle = makeStyles({
  papper: {
    flexGrow: 2,
    paddingLeft: 32,
    paddingTop: 34,
    paddingBottom: 24,
    paddingRight: 24,
  },
  subHeader: {
    marginBottom: 30,
    color: '#000',
    opacity: 0.8,
    fontSize: '16px !important',
    marginTop: '20px',
  },
  linkBreadcrumb: {
    cursor: 'pointer',
    '&:hover': {
      color: '#896af0',
      textDecoration: 'none',
    },
  },
  activeBreadcrumb: {
    color: '#000000',
  },
  breadCrumbsContainer: {
    padding: '12px 0',
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    fontSize: '26px',
    letterSpacing: '-0.3px',
    marginTop: '20px',
    marginBottom: '20px',
  },
  buttonBarContainer: {
    marginTop: '20px',
  },
  sectionTitle: {
    opacity: 0.7,
    color: '#000000',
    fontSize: '20px',
    fontWeight: 500,
    letterSpacing: '-0.77px',
    lineHeight: '25px',
  },
  settingItemWrapper: {
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
    paddingTop: '0px !important',
    paddingBottom: '0px !important',
  },
});

const Collections: React.FC<Props> = ({}: Props) => {
  const classes = useStyle();

  const collectionsContext = useContext(CollectionsContext);
  if (!collectionsContext) {
    throw Error(
      'CollectionsContext is not available. Make sure the component is wrapped with CollectionsContext.Provider.',
    );
  }

  const {
    getPaymentRuleList,
    deletePaymentRule,
    activePaymentRule,
    cancelPaymentRule,
    duplicatePaymentRule,
    createPaymentRule,
    updatePaymentRule,
    metadataObjects,
    getBillSystemSettings,
    saveBillSystemSettings,
  } = collectionsContext;

  const [loading, setLoading] = useState(true);
  const [refreshFlag, setRefreshFlag] = useState(1);
  const [paymentRuleEnabled, setPaymentRuleEnabled] = useState(false);
  const [newPaymentRuleEnabled, setNewPaymentRuleEnabled] = useState(false);

  const { userContextService } = useContext(UserContext);

  if (!userContextService) {
    throw Error(
      'CustomerView component requires userContextService to be declared in context provider',
    );
  }
  const user = userContextService.getUserConfig().user as User;
  const canManagePaymentRules = user.roles
    .find((role: UserRole) => !!role.effective)
    ?.functions.includes('ManagePaymentRules');
  let canViewPaymentRules = false;
  if (canManagePaymentRules) {
    canViewPaymentRules = true;
  } else {
    canViewPaymentRules = user.roles
      .find((role: UserRole) => !!role.effective)
      ?.functions.includes('ViewPaymentRules');
  }
  useEffect(() => {
    setUp();
  }, []);
  const setUp = async () => {
    setLoading(true);
    const billSystemSettings = await getBillSystemSettings();
    if (billSystemSettings) {
      billSystemSettings.forEach((setting) => {
        if (setting.key === 'PaymentRuleEnabled') {
          setPaymentRuleEnabled(setting.value === 'true');
        }
      });
    }
    setLoading(false);
  };
  const defaultValues = {
    id: '',
    name: '',
    startDate: '',
    endDate: '',
    status: 'Draft' as PaymentRuleStatus,
    description: '',
    conditions: [],
  };
  const [selectedRow, setSelectedRow] = useState(defaultValues);
  const [selectPaymentRuleId, setSelectPaymentRuleId] = useState('');
  const [activeType, setActiveType] = useState('');
  const [duplicatePaymentRuleModel, setDuplicatePaymentRuleModel] = useState<PaymentRule | null>(
    null,
  );
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmDialogTitle, setConfirmDialogTitle] = useState('');
  const [confirmDialogLoading, setConfirmDialogLoading] = useState(false);
  const [confirmDialogLoadingText, setConfirmDialogLoadingText] = useState('');
  const [confirmDialogSubmitLabel, setConfirmDialogSubmitLabel] = useState('');
  const [confirmDialogCancelLabel, setConfirmDialogCancelLabel] = useState('');
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [showPaymentRuleWarningMessage, setShowPaymentRuleWarningMessage] = useState(false);
  const showConfirmDialog = (
    paymentRuleId: string,
    activeType: string,
    title: string,
    message: string,
    loadingText: string,
    submitLabel: string,
    cancelLabel: string,
  ) => {
    setSelectPaymentRuleId(paymentRuleId);
    setActiveType(activeType);
    setConfirmDialogTitle(title);
    setConfirmDialogMessage(message);
    setConfirmDialogLoadingText(loadingText);
    setConfirmDialogSubmitLabel(submitLabel);
    setConfirmDialogCancelLabel(cancelLabel);
    setConfirmDialogOpen(true);
  };

  const showPaymentRuleEnabledConfirmDialog = (
    paymentRuleEnabled: boolean,
    activeType: string,
    title: string,
    message: string,
    loadingText: string,
    submitLabel: string,
    cancelLabel: string,
  ) => {
    setNewPaymentRuleEnabled(paymentRuleEnabled);
    setSelectPaymentRuleId('');
    setActiveType(activeType);
    setConfirmDialogTitle(title);
    setConfirmDialogMessage(message);
    setConfirmDialogLoadingText(loadingText);
    setConfirmDialogSubmitLabel(submitLabel);
    setConfirmDialogCancelLabel(cancelLabel);
    setConfirmDialogOpen(true);
  };
  type ActionType = 'delete' | 'active' | 'cancel' | 'duplicate';

  const handleConfirmSubmit = async () => {
    if (selectPaymentRuleId && selectPaymentRuleId !== '') {
      const actionMap: Record<ActionType, (paymentRuleId: string) => Promise<PaymentRule | void>> =
        {
          delete: deletePaymentRule,
          active: activePaymentRule,
          cancel: cancelPaymentRule,
          duplicate: async () => {},
        };

      const action = actionMap[activeType as ActionType];
      if (action) {
        if (activeType === 'duplicate' && duplicatePaymentRuleModel) {
          handleEditPaymentRule(duplicatePaymentRuleModel);
        } else {
          await action(selectPaymentRuleId);
        }
        setRefreshFlag(refreshFlag + 1);
        setConfirmDialogOpen(false);
      }
    } else {
      if (activeType === 'paymentRuleEnabled') {
        const bsSettings: BillSysSetting[] = [
          {
            key: 'PaymentRuleEnabled',
            value: newPaymentRuleEnabled ? 'true' : 'false',
          },
        ];
        await saveBillSystemSettings(bsSettings)
          .then(() => {
            setPaymentRuleEnabled(newPaymentRuleEnabled);
          })
          .catch(() => {
            setPaymentRuleEnabled(!newPaymentRuleEnabled);
            setNewPaymentRuleEnabled(!newPaymentRuleEnabled);
          });
      }
      setConfirmDialogOpen(false);
    }
  };

  const handleDeletePaymentRule = async (row: any) => {
    showConfirmDialog(
      row.id,
      'delete',
      'Delete Payment Rule',
      'Do you want to delete payment rule ' + row.name + '?',
      'Deleting',
      'Yes',
      'No',
    );
  };

  const handleActivePaymentRule = async (row: any) => {
    showConfirmDialog(
      row.id,
      'active',
      'Activate Payment Rule',
      'Are you sure you want to activate the payment rule: ' + row.name + '?',
      'Activating',
      'Yes',
      'No',
    );
  };

  const handleCancelPaymentRule = async (row: any) => {
    showConfirmDialog(
      row.id,
      'cancel',
      'Deactivate Payment Rule',
      'If you deactivate this payment rule, the invoices may not be transferred to any payment system. Do you want to continue?',
      'Deactivating',
      'Yes',
      'No',
    );
  };
  const handleDuplicatePaymentRule = async (row: any) => {
    const result = await duplicatePaymentRule(row.id);
    setDuplicatePaymentRuleModel(result);
    if (result) {
      showConfirmDialog(
        result.id,
        'duplicate',
        'Duplicate Payment Rule',
        'A new payment rule is replicated from ' +
          row.name +
          '.  Do you want to open the payment rule for edit?',
        'Duplicating',
        'Edit',
        'Close',
      );
    }
  };

  const getAllPaymentRuleList = async (currentPage: any, pageSize: any) => {
    const paymentRuleList = await getPaymentRuleList(currentPage, pageSize);
    return paymentRuleList;
  };

  const handleEditPaymentRule = (row: any) => {
    setMode('edit');
    try {
      if (row.conditions && typeof row.conditions === 'string') {
        row.conditions = JSON.parse(row.conditions);
      }
      setSelectedRow(_.cloneDeep(row));
    } catch (error) {
      console.error('Invalid JSON string:', error);
    }
    setOpenPaymentRuleFormDialog(true);
  };
  const handleViewPaymentRule = (row: any) => {
    setMode('view');
    try {
      if (row.conditions && typeof row.conditions === 'string') {
        row.conditions = JSON.parse(row.conditions);
      }
      setSelectedRow(row);
    } catch (error) {
      console.error('Invalid JSON string:', error);
    }
    setOpenPaymentRuleFormDialog(true);
  };

  const { showSnackbar, Snackbar } = useRubySnackbar();

  useEffect(() => {
    if (selectedRow) {
      setPaymentRuleFormValues(selectedRow);
    }
  }, [selectedRow]);

  const paymentRuleMetadata = metadataObjects.find(
    (metadataObject) => metadataObject.apiName === 'PaymentRule',
  ) as RubyObject;

  const [openPaymentRuleFormDialog, setOpenPaymentRuleFormDialog] = useState(false);
  const [mode, setMode] = useState<'create' | 'edit' | 'view'>('create');

  const [paymentRuleFormValues, setPaymentRuleFormValues] = useState<PaymentRule>(defaultValues);
  useState<PaymentRule>(defaultValues);
  const closePaymentRuleFormDialog = () => {
    setOpenPaymentRuleFormDialog(false);
    setSelectedRow(defaultValues);
  };

  const validateEmptyFilters = (paymentRule: PaymentRule): PaymentRuleConditionItem | undefined => {
    let emptyFilters = undefined;
    const itemIndex = paymentRule.conditions.findIndex(
      (c) => !c.filters || c.filters?.length === 0,
    );
    if (itemIndex !== -1 && itemIndex !== paymentRule.conditions.length - 1) {
      emptyFilters = paymentRule.conditions[itemIndex];
    }
    return emptyFilters;
  };

  const updateConditions = (values: PaymentRule) => {
    if (values.id === '') {
      //@ts-ignore
      values.id = null;
    }
    if (values.conditions.length > 0) {
      values.conditions.forEach((c: PaymentRuleConditionItem) => {
        //@ts-ignore
        if (c.filters === '') {
          c.filters = [];
        }
        if (c.id?.includes('temp')) {
          c.id = null;
        }
        if (c.filters && c.filters?.length > 0) {
          c.filters.forEach((f: Condition) => {
            delete f.error;
            delete f.nestedConditions;
            delete f.relation;
          });
        }
      });
    }
  };

  const handlePaymentRuleEnabled = (newPaymentRuleEnabled: boolean) => {
    if (newPaymentRuleEnabled) {
      showPaymentRuleEnabledConfirmDialog(
        newPaymentRuleEnabled,
        'paymentRuleEnabled',
        'Confirmation',
        'Once payment rules are enabled, invoices will be sent to external collection systems according to the configured rules. Do you want to proceed?',
        'Saving',
        'Confirm',
        'Cancel',
      );
    } else {
      showPaymentRuleEnabledConfirmDialog(
        newPaymentRuleEnabled,
        'paymentRuleEnabled',
        'Confirmation',
        'Once payment rules are disabled, invoices will be sent to the default external collection system. Do you want to proceed?',
        'Saving',
        'Confirm',
        'Cancel',
      );
    }
  };
  return (
    <RubySettingEditorPanel title="Collections">
      {!loading && (canManagePaymentRules || canViewPaymentRules) && (
        <>
          <Typography component="h3" className={classes.sectionTitle}>
            General
          </Typography>
          <OrderSettingEditorSwitchControl
            label="Enable Payment Rules"
            value={paymentRuleEnabled}
            style={{ paddingRight: '2px', marginTop: '16px', marginBottom: '16px' }}
            tooltip="Determines whether invoices will be sent to external systems based on configured rules. If this is set to false, invoices will be transferred to the default connected collection system."
            handleInputChange={(newValue: boolean) => {
              handlePaymentRuleEnabled(newValue);
            }}
          />
          {paymentRuleEnabled && (
            <>
              <Typography component="h3" className={classes.sectionTitle}>
                Payment Rules
              </Typography>
              <div className={classes.subHeader}>
                <span>
                  The transactions will be sent to the payment systems based on the payment rules
                  configured below.
                </span>
              </div>
              {showPaymentRuleWarningMessage && (
                <Grid item xs={12} style={{ marginTop: '-15px' }}>
                  <Box
                    component="blockquote"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      margin: '16px 0',
                      padding: '16px',
                      backgroundColor: 'rgba(255,229,100,0.2)',
                      borderLeft: '5px solid #ffe564',
                    }}
                  >
                    <WarningIcon
                      style={{ color: '#ff9800', marginRight: '8px', fontSize: '1.5rem' }}
                    />
                    <Typography component="p" variant="body1" style={{ margin: 0 }}>
                      Currently, there are no active payment rules, so invoices will not be sent to
                      any external collection systems.
                    </Typography>
                  </Box>
                </Grid>
              )}
              {canManagePaymentRules && (
                <Grid item xs={12} className={classes.buttonBarContainer}>
                  <RubyButtonBar
                    style={{ marginLeft: '50px' }}
                    variant="inPlace"
                    processing={false}
                    leftButtons={[]}
                    rightButtons={[
                      {
                        text: 'New Payment Rule',
                        onClick: async () => {
                          setMode('create');
                          setSelectedRow(defaultValues);
                          setOpenPaymentRuleFormDialog(true);
                        },
                      },
                    ]}
                  />
                </Grid>
              )}

              <PaymentRuleList
                getAllPaymentRuleList={getAllPaymentRuleList}
                handleDeletePaymentRule={handleDeletePaymentRule}
                handleEditPaymentRule={handleEditPaymentRule}
                handleViewPaymentRule={handleViewPaymentRule}
                handleActivePaymentRule={handleActivePaymentRule}
                handleCancelPaymentRule={handleCancelPaymentRule}
                handleCopyPaymentRule={handleDuplicatePaymentRule}
                refreshFlag={refreshFlag}
                paymentRuleMetadata={paymentRuleMetadata}
                canManagePaymentRules={canManagePaymentRules}
                setShowPaymentRuleWarningMessage={setShowPaymentRuleWarningMessage}
              />
            </>
          )}

          {openPaymentRuleFormDialog && (
            <>
              <PaymentRuleFormDialog
                mode={mode}
                open={openPaymentRuleFormDialog}
                onClose={closePaymentRuleFormDialog}
                values={paymentRuleFormValues}
                onSubmit={async (values) => {
                  if (values.conditions.length > 0 && validateEmptyFilters(values)) {
                    showSnackbar('error', 'Error', `Please add filters to all conditions.`);
                    return Promise.reject();
                  }

                  if (
                    values.conditions == undefined ||
                    values.conditions == null ||
                    values.conditions.length == 0
                  ) {
                    showSnackbar('error', 'Error', `Please add filters to all conditions.`);
                    return Promise.reject();
                  } else {
                    updateConditions(values);
                  }
                  if (values.id && values.id.length > 1) {
                    await updatePaymentRule(values);
                    setRefreshFlag(refreshFlag + 1);
                  } else {
                    await createPaymentRule(values);
                    setRefreshFlag(refreshFlag + 1);
                  }
                  closePaymentRuleFormDialog();
                }}
              />
              <Snackbar />
            </>
          )}

          <DialogComponent
            open={confirmDialogOpen}
            handleClose={() => {
              if (activeType == 'duplicate') {
                setRefreshFlag(refreshFlag + 1);
              }
              setConfirmDialogOpen(false);
            }}
            title={confirmDialogTitle}
            width={'sm'}
            loading={confirmDialogLoading}
            loadingText={confirmDialogLoadingText}
            handleSubmit={handleConfirmSubmit}
            submitButtonText={confirmDialogSubmitLabel}
            cancelButtonText={confirmDialogCancelLabel}
          >
            <p>{confirmDialogMessage}</p>
          </DialogComponent>
        </>
      )}
    </RubySettingEditorPanel>
  );
};
export const PaymentRuleFormDialog: React.FC<{
  open: boolean;
  onClose: () => void;
  onSubmit: SubmitHandler<PaymentRule>;
  mode: 'create' | 'edit' | 'view';
  values: PaymentRule;
}> = (props) => {
  const { open, onClose, onSubmit, values, mode } = props;
  return (
    <DialogComponent
      open={open}
      title={
        mode === 'create'
          ? 'New Payment Rule'
          : mode === 'edit'
            ? 'Edit Payment Rule'
            : 'View Payment Rule'
      }
      width={'sm'}
      handleClose={onClose}
    >
      <PaymentRuleForm values={values} onCancel={onClose} mode={mode} onSubmit={onSubmit} />
    </DialogComponent>
  );
};
export default Collections;

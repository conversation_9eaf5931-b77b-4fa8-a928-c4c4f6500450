import React, { useEffect, useState } from 'react';
import { CronWeeklyFormProps, WeeklyFrequencyProps } from './interface';
import { Grid, MenuItem, Select } from '@material-ui/core';
import FormControl from '@material-ui/core/FormControl';
import { makeStyles } from '@material-ui/core/styles';
import SelectInputBase from '../select-input-base';
import { HourConst, MinuteConst } from '../common/cron-const';
import { PickListOption } from '../pick-list';
import RubyCheckbox from '../checkbox';

const useStyles = makeStyles({
  titleLabel: {
    textTransform: 'uppercase',
    color: '#9c9c9c',
    fontWeight: 'bold',
    fontSize: '.80rem',
    alignItems: 'baseline',
  },
  formControl: {
    flexDirection: 'row',
    width: '100%',
  },
  optionLabel: {
    '& > span': {
      color: '#6e6e6e',
      fontSize: '.9rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
  },
});

const DEFAULT_SETTING_VALUES = {
  Monday: 'false',
  Tuesday: 'false',
  Wednesday: 'false',
  Thursday: 'false',
  Friday: 'false',
  Saturday: 'false',
  Sunday: 'false',
};

interface SettingWeeklyControlProps {
  weeklyFrequencyProps: WeeklyFrequencyProps;
  onChange: (weeklyFrequency: WeeklyFrequencyProps) => void;
  disabled?: boolean;
}

const ChangeWeeklySettings: React.FC<SettingWeeklyControlProps> = ({
  disabled,
  onChange,
  weeklyFrequencyProps,
}) => {
  const options: Array<PickListOption> = [
    {
      name: 'Monday',
      value: 'Monday',
    },
    {
      name: 'Tuesday',
      value: 'Tuesday',
    },
    {
      name: 'Wednesday',
      value: 'Wednesday',
    },
    {
      name: 'Thursday',
      value: 'Thursday',
    },
    {
      name: 'Friday',
      value: 'Friday',
    },
    {
      name: 'Saturday',
      value: 'Saturday',
    },
    {
      name: 'Sunday',
      value: 'Sunday',
    },
  ];

  return (
    <>
      <Grid
        container
        spacing={2}
        item
        lg={10}
        xs={12}
        style={{ maxWidth: '2000px', paddingTop: '10px' }}
      >
        {options.map((option: PickListOption) => (
          <RubyCheckbox
            labelStyles={{
              color: '#000000',
              width: '80px',
            }}
            field={{
              apiName: option.value,
              name: option.name,
              type: 'boolean',
            }}
            disabled={disabled}
            //@ts-ignore
            value={weeklyFrequencyProps[option.value] === 'true'}
            handleInputChange={(newValue: boolean) => {
              onChange({
                ...weeklyFrequencyProps,
                //@ts-ignore
                [option.value]: newValue ? 'true' : 'false',
              });
            }}
          />
        ))}
      </Grid>
    </>
  );
};

export const WeeklyCronExpression: React.FC<CronWeeklyFormProps> = (userProps) => {
  const props = { ...userProps };
  const [loading, setLoading] = useState(false);
  const { weeklyObject, setWeeklyObject, disabled } = props;
  const styles = useStyles();

  useEffect(() => {
    setup();
  }, []);

  const setup = async () => {
    const weeklyValue = Object.values(weeklyObject.weeklyFrequency);
    weeklyValue.map((val) => {
      const weekday = weeklyMap.get(val);
      for (let value in weeklyFrequencyProps) {
        if (value === weekday) {
          (weeklyFrequencyProps as any)[value] = 'true';
        }
      }
    });
    if (weeklyObject.weeklyExpression) {
      if (
        typeof weeklyObject.weeklyExpression[3] === 'string' &&
        weeklyObject.weeklyExpression[3] !== ' ?'
      ) {
        weeklyObject.weeklyExpression[3] = ' ?';
      }
      if (
        typeof weeklyObject.weeklyExpression[5] === 'string' &&
        weeklyObject.weeklyExpression[5] !== ' *'
      ) {
        weeklyObject.weeklyExpression[5] = ' ' + weeklyValue.join(',');
      }
    }
    setWeeklyObject({
      ...weeklyObject,
      weeklyExpression: weeklyObject.weeklyExpression,
    });
    setWeeklyFrequencyProps(weeklyFrequencyProps);
    setLoading(true);
  };

  let weeklyMap = new Map([
    ['MON', 'Monday'],
    ['TUE', 'Tuesday'],
    ['WED', 'Wednesday'],
    ['THU', 'Thursday'],
    ['FRI', 'Friday'],
    ['SAT', 'Saturday'],
    ['SUN', 'Sunday'],
  ]);

  const settings = { ...DEFAULT_SETTING_VALUES };

  const [weeklyFrequencyProps, setWeeklyFrequencyProps] = useState<WeeklyFrequencyProps>({
    Monday: settings.Monday,
    Tuesday: settings.Tuesday,
    Wednesday: settings.Wednesday,
    Thursday: settings.Thursday,
    Friday: settings.Friday,
    Saturday: settings.Saturday,
    Sunday: settings.Sunday,
  });

  const handleWeeklyChange = (newValue: WeeklyFrequencyProps) => {
    setWeeklyFrequencyProps(newValue);
    let values = [];
    for (let value in newValue) {
      if ((newValue as any)[value] === 'true') {
        value = value.substring(0, 3).toUpperCase();
        values.push(value);
      }
    }

    weeklyObject.weeklyFrequency = values;
    setWeeklyObject({
      ...weeklyObject,
      weeklyFrequency: weeklyObject.weeklyFrequency,
    });

    if (weeklyObject.weeklyExpression) {
      if (Array.isArray(weeklyObject.weeklyExpression)) {
        if (typeof weeklyObject.weeklyExpression[3] === 'string') {
          weeklyObject.weeklyExpression[3] = ' ?';
        }
        if (typeof weeklyObject.weeklyExpression[5] === 'string') {
          if (values.length > 0) {
            weeklyObject.weeklyExpression[5] = ' ' + values.join(',');
          } else {
            weeklyObject.weeklyExpression[5] = ' *';
          }
        }
      }
      setWeeklyObject({
        ...weeklyObject,
        weeklyExpression: weeklyObject.weeklyExpression,
      });
    }
  };

  return (
    <>
      <FormControl className={styles.formControl}>
        {{ loading } && (
          <Grid container item spacing={3} xs={12}>
            <Grid container item spacing={2} xs={12} style={{ marginTop: '5px' }}>
              <Grid
                item
                lg={2}
                xs={12}
                className={styles.titleLabel}
                style={{ paddingTop: '15px' }}
              >
                FREQUENCY
              </Grid>
              <Grid item lg={10} xs={12} style={{ alignItems: 'baseline' }}>
                <ChangeWeeklySettings
                  weeklyFrequencyProps={weeklyFrequencyProps}
                  onChange={handleWeeklyChange}
                  disabled={disabled}
                />
              </Grid>
            </Grid>
            <Grid container item spacing={2} xs={12}>
              <Grid
                item
                lg={2}
                xs={12}
                className={styles.titleLabel}
                style={{ paddingTop: '15px' }}
              >
                RUN AT
              </Grid>
              <Grid container item spacing={3} xs={8}>
                <Grid item lg={2} xs={8} style={{ maxWidth: '6%', alignItems: 'baseline' }}>
                  <Select
                    value={
                      weeklyObject.weeklyRunAt === undefined ? '' : weeklyObject.weeklyRunAt.hour
                    }
                    displayEmpty
                    name={'field'}
                    placeholder={'Field'}
                    onChange={(event) => {
                      const value = event.target.value;
                      if (typeof value === 'string') {
                        weeklyObject.weeklyRunAt.hour = value;
                      }
                      const copyRunAt = { hour: value, minute: weeklyObject.weeklyRunAt.minute };
                      setWeeklyObject({
                        ...weeklyObject,
                        weeklyRunAt: copyRunAt,
                      });
                      if (weeklyObject.weeklyExpression) {
                        weeklyObject.weeklyExpression[2] = ' ' + value;
                        if (typeof weeklyObject.weeklyExpression[1] === 'string') {
                          if (weeklyObject.weeklyExpression[2].substring(1, 2) === '0') {
                            weeklyObject.weeklyExpression[2] =
                              weeklyObject.weeklyExpression[2].replace('0', '');
                          }
                        }
                        setWeeklyObject({
                          ...weeklyObject,
                          weeklyExpression: weeklyObject.weeklyExpression,
                        });
                      }
                    }}
                    fullWidth={false}
                    label={'Field'}
                    disabled={disabled}
                    input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
                  >
                    {HourConst.map((field, index) => {
                      return (
                        <MenuItem key={index} value={field}>
                          {field}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </Grid>
                <Grid
                  item
                  lg={2}
                  xs={12}
                  style={{
                    marginTop: '8px',
                    maxWidth: '10px',
                    marginLeft: '5px',
                    marginRight: '50px',
                  }}
                >
                  <b style={{ marginLeft: '50px' }}>:</b>
                </Grid>
                <Grid item lg={4} xs={8} style={{ maxWidth: '10%' }}>
                  <Select
                    value={
                      weeklyObject.weeklyRunAt === undefined ? '' : weeklyObject.weeklyRunAt.minute
                    }
                    displayEmpty
                    name={'field'}
                    placeholder={'Field'}
                    onChange={(event) => {
                      const value = event.target.value;
                      if (typeof value === 'string') {
                        weeklyObject.weeklyRunAt.minute = value;
                      }
                      const copyRunAt = { hour: weeklyObject.weeklyRunAt.hour, minute: value };
                      setWeeklyObject({
                        ...weeklyObject,
                        weeklyRunAt: copyRunAt,
                      });
                      if (weeklyObject.weeklyExpression) {
                        weeklyObject.weeklyExpression[1] = ' ' + value;
                        if (typeof weeklyObject.weeklyExpression[1] === 'string') {
                          if (weeklyObject.weeklyExpression[1].substring(1, 2) === '0') {
                            weeklyObject.weeklyExpression[1] =
                              weeklyObject.weeklyExpression[1].replace('0', '');
                          }
                        }
                        setWeeklyObject({
                          ...weeklyObject,
                          weeklyExpression: weeklyObject.weeklyExpression,
                        });
                      }
                    }}
                    fullWidth={false}
                    label={'Field'}
                    disabled={disabled}
                    input={<SelectInputBase placeholder={'Field'} disabled={disabled} />}
                  >
                    {MinuteConst.map((field, index) => {
                      return (
                        <MenuItem key={index} value={field}>
                          {field}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        )}
      </FormControl>
    </>
  );
};

export default WeeklyCronExpression;

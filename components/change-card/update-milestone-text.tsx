import React from 'react';

import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';

import type { UpdateMilestoneRequest } from '../change-cart';
import type { ChangeItemText } from './interface';

const useStyles = makeStyles({
  itemText: {
    opacity: 0.7,
    color: '#000000',
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
    opacity: '1 !important',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
});
const UpdateMilestoneText: React.FC<ChangeItemText> = (props) => {
  const { changeItem } = props;

  const classes = useStyles();

  const changeItemTitle = changeItem.request.label || changeItem.request.objectType;
  return (
    <Typography variant="body2" component="p" display="inline">
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        The milestone {(changeItem.request as UpdateMilestoneRequest).name || ''} of{' '}
        {changeItemTitle}{' '}
      </Typography>
      <Typography variant="body2" component="p" display="inline">
        {changeItem.request.assetName} is requested to change.
      </Typography>
    </Typography>
  );
};

export default UpdateMilestoneText;

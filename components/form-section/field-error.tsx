import React from 'react';
import { DeepMap, FieldError } from 'react-hook-form';

import { makeStyles } from '@material-ui/core/styles';

import { RubyField } from '../metadata/interface';

const useStyles = makeStyles({
  submitError: {
    fontSize: '14px',
    fontWeight: 400,
    color: '#ca0035',
    width: '100%',
    paddingTop: '5px',
  },
});
const RubyFieldError: React.FC<{
  errors: DeepMap<Record<string, any>, FieldError>;
  field: RubyField;
}> = (props) => {
  const { errors, field } = props;
  const errorMsg = errors?.[field.apiName]?.message;
  const classes = useStyles();
  if (errorMsg) {
    return (
      <div role="alert" className={classes.submitError}>
        {errorMsg}
      </div>
    );
  } else {
    return null;
  }
};

export default RubyFieldError;

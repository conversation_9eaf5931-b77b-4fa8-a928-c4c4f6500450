export interface Language {
  code: string;
  name: string;
}

export const LanguageList: Language[] = [
  { code: 'ar_AE', name: 'Arabic (United Arab Emirates)' },
  { code: 'ar_JO', name: 'Arabic (Jordan)' },
  { code: 'ar_SY', name: 'Arabic (Syria)' },
  { code: 'hr_HR', name: 'Croatian (Croatia)' },
  { code: 'fr_BE', name: 'French (Belgium)' },
  { code: 'es_PA', name: 'Spanish (Panama)' },
  { code: 'mt_MT', name: 'Maltese (Malta)' },
  { code: 'es_VE', name: 'Spanish (Venezuela)' },
  { code: 'zh_TW', name: 'Chinese (Taiwan)' },
  { code: 'da_DK', name: 'Danish (Denmark)' },
  { code: 'es_PR', name: 'Spanish (Puerto Rico)' },
  { code: 'vi_VN', name: 'Vietnamese (Vietnam)' },
  { code: 'en_US', name: 'English (United States)' },
  { code: 'sr_ME', name: 'Serbian (Montenegro)' },
  { code: 'sv_SE', name: 'Swedish (Sweden)' },
  { code: 'es_BO', name: 'Spanish (Bolivia)' },
  { code: 'en_SG', name: 'English (Singapore)' },
  { code: 'ar_BH', name: 'Arabic (Bahrain)' },
  { code: 'ar_SA', name: 'Arabic (Saudi Arabia)' },
  { code: 'ar_YE', name: 'Arabic (Yemen)' },
  { code: 'hi_IN', name: 'Hindi (India)' },
  { code: 'en_MT', name: 'English (Malta)' },
  { code: 'fi_FI', name: 'Finnish (Finland)' },
  { code: 'sr_BA', name: 'Serbian (Latin,Bosnia and Herzegovina)' },
  { code: 'uk_UA', name: 'Ukrainian (Ukraine)' },
  { code: 'fr_CH', name: 'French (Switzerland)' },
  { code: 'es_AR', name: 'Spanish (Argentina)' },
  { code: 'ar_EG', name: 'Arabic (Egypt)' },
  { code: 'ja_JP', name: 'Japanese (Japan,JP)' },
  { code: 'es_SV', name: 'Spanish (El Salvador)' },
  { code: 'pt_BR', name: 'Portuguese (Brazil)' },
  { code: 'is_IS', name: 'Icelandic (Iceland)' },
  { code: 'cs_CZ', name: 'Czech (Czech Republic)' },
  { code: 'pl_PL', name: 'Polish (Poland)' },
  { code: 'ca_ES', name: 'Catalan (Spain)' },
  { code: 'sr_CS', name: 'Serbian (Serbia and Montenegro)' },
  { code: 'ms_MY', name: 'Malay (Malaysia)' },
  { code: 'es_ES', name: 'Spanish (Spain)' },
  { code: 'es_CO', name: 'Spanish (Colombia)' },
  { code: 'bg_BG', name: 'Bulgarian (Bulgaria)' },
  { code: 'sr_BA', name: 'Serbian (Bosnia and Herzegovina)' },
  { code: 'es_PY', name: 'Spanish (Paraguay)' },
  { code: 'es_EC', name: 'Spanish (Ecuador)' },
  { code: 'es_US', name: 'Spanish (United States)' },
  { code: 'ar_SD', name: 'Arabic (Sudan)' },
  { code: 'ro_RO', name: 'Romanian (Romania)' },
  { code: 'en_PH', name: 'English (Philippines)' },
  { code: 'ar_TN', name: 'Arabic (Tunisia)' },
  { code: 'sr_ME', name: 'Serbian (Latin,Montenegro)' },
  { code: 'es_GT', name: 'Spanish (Guatemala)' },
  { code: 'ko_KR', name: 'Korean (South Korea)' },
  { code: 'el_CY', name: 'Greek (Cyprus)' },
  { code: 'es_MX', name: 'Spanish (Mexico)' },
  { code: 'ru_RU', name: 'Russian (Russia)' },
  { code: 'es_HN', name: 'Spanish (Honduras)' },
  { code: 'zh_HK', name: 'Chinese (Hong Kong)' },
  { code: 'no_NO', name: 'Norwegian (Norway,Nynorsk)' },
  { code: 'hu_HU', name: 'Hungarian (Hungary)' },
  { code: 'th_TH', name: 'Thai (Thailand)' },
  { code: 'ar_IQ', name: 'Arabic (Iraq)' },
  { code: 'es_CL', name: 'Spanish (Chile)' },
  { code: 'ar_MA', name: 'Arabic (Morocco)' },
  { code: 'ga_IE', name: 'Irish (Ireland)' },
  { code: 'tr_TR', name: 'Turkish (Turkey)' },
  { code: 'et_EE', name: 'Estonian (Estonia)' },
  { code: 'ar_QA', name: 'Arabic (Qatar)' },
  { code: 'pt_PT', name: 'Portuguese (Portugal)' },
  { code: 'fr_LU', name: 'French (Luxembourg)' },
  { code: 'ar_OM', name: 'Arabic (Oman)' },
  { code: 'sq_AL', name: 'Albanian (Albania)' },
  { code: 'es_DO', name: 'Spanish (Dominican Republic)' },
  { code: 'es_CU', name: 'Spanish (Cuba)' },
  { code: 'en_NZ', name: 'English (New Zealand)' },
  { code: 'sr_RS', name: 'Serbian (Serbia)' },
  { code: 'de_CH', name: 'German (Switzerland)' },
  { code: 'es_UY', name: 'Spanish (Uruguay)' },
  { code: 'el_GR', name: 'Greek (Greece)' },
  { code: 'iw_IL', name: 'Hebrew (Israel)' },
  { code: 'en_ZA', name: 'English (South Africa)' },
  { code: 'th_TH', name: 'Thai (Thailand,TH)' },
  { code: 'fr_FR', name: 'French (France)' },
  { code: 'de_AT', name: 'German (Austria)' },
  { code: 'no_NO', name: 'Norwegian (Norway)' },
  { code: 'en_AU', name: 'English (Australia)' },
  { code: 'nl_NL', name: 'Dutch (Netherlands)' },
  { code: 'fr_CA', name: 'French (Canada)' },
  { code: 'lv_LV', name: 'Latvian (Latvia)' },
  { code: 'de_LU', name: 'German (Luxembourg)' },
  { code: 'es_CR', name: 'Spanish (Costa Rica)' },
  { code: 'ar_KW', name: 'Arabic (Kuwait)' },
  { code: 'ar_LY', name: 'Arabic (Libya)' },
  { code: 'it_CH', name: 'Italian (Switzerland)' },
  { code: 'de_DE', name: 'German (Germany)' },
  { code: 'ar_DZ', name: 'Arabic (Algeria)' },
  { code: 'sk_SK', name: 'Slovak (Slovakia)' },
  { code: 'lt_LT', name: 'Lithuanian (Lithuania)' },
  { code: 'it_IT', name: 'Italian (Italy)' },
  { code: 'en_IE', name: 'English (Ireland)' },
  { code: 'zh_SG', name: 'Chinese (Singapore)' },
  { code: 'en_CA', name: 'English (Canada)' },
  { code: 'nl_BE', name: 'Dutch (Belgium)' },
  { code: 'zh_CN', name: 'Chinese (China)' },
  { code: 'ja_JP', name: 'Japanese (Japan)' },
  { code: 'de_GR', name: 'German (Greece)' },
  { code: 'sr_RS', name: 'Serbian (Latin,Serbia)' },
  { code: 'en_IN', name: 'English (India)' },
  { code: 'ar_LB', name: 'Arabic (Lebanon)' },
  { code: 'es_NI', name: 'Spanish (Nicaragua)' },
  { code: 'mk_MK', name: 'Macedonian (Macedonia)' },
  { code: 'be_BY', name: 'Belarusian (Belarus)' },
  { code: 'sl_SI', name: 'Slovenian (Slovenia)' },
  { code: 'es_PE', name: 'Spanish (Peru)' },
  { code: 'in_ID', name: 'Indonesian (Indonesia)' },
  { code: 'en_GB', name: 'English (United Kingdom)' },
];

export default LanguageList;

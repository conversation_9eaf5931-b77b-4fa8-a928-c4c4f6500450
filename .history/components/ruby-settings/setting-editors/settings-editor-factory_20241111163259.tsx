import React from 'react';
import { ApiKeysEditor } from '../../api-key-editor/api-keys-editor';
import AvalaraConnection from '../../avalara-connection/avalara-connection';
import ObjectList from '../../object-list';
import QuickBooksConnection from '../../quickbooks-connection/quickbooks-connection';
import RightRevConnection from '../../rightrev-connection/rightrev-connection';
import SalesforceConnection from '../../salesforce-connection/salesforce-connection';
import StripeConnection from '../../stripe-connection/stripe-connection';
import WebhookConnection from '../../webhook-connection/webhook-connection';
import { type SubcategoryEditorProps } from '../interface';
import AnalyticsSettingsEditor from './analytics-settings-editor';
import BillingSettingsEditor from './billing-settings-editor';
import CreditsSettingsEditor from './credits-settings-editor';
import CustomerLifecycleSettingsEditor from './customer-lifecycle-settings-editor';
import DataFlowEditor from './data-flow-editor';
import GraphqlGeneratorSettingEditor from './graphql-generator-settings-editor';
import IntegrationOverviewSettingsEditor from './integration-overview-settings-editor';
import InvoicesSettingsEditor from './invoices-settings-editor';
import OrderFormsSettingsEditor from './order-forms-settings-editor';
import OrderSettingsEditor from './order-settings-editor';
import OrganizationSettingsEditor from './organization-settings-editor';
import PricingAttributeEditor from './pricing-attribute-editor';
import ProductCategoriesEditor from './product-categories-editor';
import RenewalSettingsEditor from './renewal-settings-editor';
import RevenueRulesSettingsEditor from './revenue-settings-editor';
import { RolesSettingEditor } from './roles-settings-editor';
import SecuritySettingsEditor from './security-settings-editor';
import SubscriptionSettingsEditor from './subscription-settings-editor';
import UsageSettingsEditor from './usage-settings-editor';
import QuantityTierAttributeEditor from './quantity-tier-attribute-editor';

type GetSettingEditor = (settingItemApiName: string) => React.FC<SubcategoryEditorProps>;

const registry = new Map<string, React.FC<SubcategoryEditorProps>>();
registry.set('Organization Settings', OrganizationSettingsEditor);
registry.set('Subscriptions', SubscriptionSettingsEditor);
registry.set('Renewals', RenewalSettingsEditor);
registry.set('Quotes and Orders', OrderSettingsEditor);
registry.set('Salesforce Integration', SalesforceConnection);
registry.set('Roles', RolesSettingEditor);
registry.set('GraphQL Generator', GraphqlGeneratorSettingEditor);
registry.set('Billing', BillingSettingsEditor);
registry.set('Invoices', InvoicesSettingsEditor);
registry.set('Usage', UsageSettingsEditor);
registry.set('Credits', CreditsSettingsEditor);
registry.set('Revenue Settings', RevenueRulesSettingsEditor);
registry.set('Pricing Attributes', PricingAttributeEditor);
registry.set('Data Flow Manager', DataFlowEditor);
registry.set('API Keys', ApiKeysEditor);
registry.set('Business Objects', ObjectList);
registry.set('Integration Overview', IntegrationOverviewSettingsEditor);
registry.set('Customer Lifecycle', CustomerLifecycleSettingsEditor);
registry.set('Order Forms', OrderFormsSettingsEditor);
registry.set('Stripe Integration', StripeConnection);
registry.set('Product Categories', ProductCategoriesEditor);
registry.set('Avalara Integration', AvalaraConnection);
registry.set('RightRev Integration', RightRevConnection);
registry.set('QuickBooks Integration', QuickBooksConnection);
registry.set('NetSuite Integration', QuickBooksConnection);
registry.set('Security', SecuritySettingsEditor);
registry.set('Webhook Endpoints', WebhookConnection);
registry.set('Analytics', AnalyticsSettingsEditor);
registry.set('Quantity Tier Attributes', QuantityTierAttributeEditor);

const UnknownSetting: React.FC<SubcategoryEditorProps> = (props) => {
  const { category } = props;
  return <div>Unknown setting category {category.name}, please make sure it is registered.</div>;
};

const EmptyPanel: React.FC<SubcategoryEditorProps> = (props) => {
  return <div></div>;
};

const getSettingEditor: GetSettingEditor = (settingItemApiName) => {
  if (!settingItemApiName) {
    return EmptyPanel;
  }
  return registry.get(settingItemApiName) || UnknownSetting;
};

export default getSettingEditor;

import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import { PDFDocument } from 'pdf-lib';

import { PdfType } from './interface';
import { TYPES } from './template-config';
import printHtml from './utils/print';

const createCanvas = (el: HTMLElement, noClass: string[]) => {
  return html2canvas(el, {
    backgroundColor: '#ffffff',
    scale: 3,
  });
};

const generatePdf = (
  index: any,
  mergedPdf: any,
  pdfsToMerges: any,
  removed: any,
  title: any,
  setDownloading: any,
) => {
  PDFDocument.load(pdfsToMerges[index]).then((pdf) => {
    mergedPdf.copyPages(pdf, pdf.getPageIndices()).then((copiedPages: any) => {
      copiedPages.forEach((page: any) => {
        // console.log('page', page.getWidth(), page.getHeight());
        // if (mergedPdf.getPageCount() > 0 && !removed) {
        //   removed = true;
        //   mergedPdf.removePage(0);
        // }
        mergedPdf.addPage(page);
      });
      if (index === pdfsToMerges.length - 1) {
        mergedPdf.save().then((mergedPdfFile: any) => {
          let file = mergedPdfFile;
          if (file) {
            downloadFile(new Blob([file]), `${title}.pdf`, 'application/pdf');
            setDownloading(false);
          }
        });
      } else {
        index = index + 1;
        generatePdf(index, mergedPdf, pdfsToMerges, removed, title, setDownloading);
      }
    });
  });
};

const mergePdfs = (pdfsToMerges: ArrayBuffer[], title: any, setDownloading: any) => {
  let removed = false;
  PDFDocument.create().then((mergedPdf) => {
    let index = 0;
    generatePdf(index, mergedPdf, pdfsToMerges, removed, title, setDownloading);
    // const ations = pdfsToMerges.map((pdfBuffer) => {
    //   return PDFDocument.load(pdfBuffer).then((pdf) => {
    //     mergedPdf.copyPages(pdf, pdf.getPageIndices()).then((copiedPages) => {
    //       copiedPages.forEach((page) => {
    //         // console.log('page', page.getWidth(), page.getHeight());
    //         // page.setWidth(210);
    //         if (mergedPdf.getPageCount() > 0 && !removed) {
    //           removed = true;
    //           mergedPdf.removePage(0);
    //         }
    //         mergedPdf.addPage(page);
    //       });
    //     });
    //   });
    // });
    // return Promise.all(ations).then((res) => {
    //   return mergedPdf.save().then((mergedPdfFile) => {
    //     let file = mergedPdfFile;
    //     if (file) {
    //       return Promise.resolve(file);
    //     }
    //   });
    // });
  });
};

const downloadFile = (blob: any, fileName: string, type: string) => {
  const link = document.createElement('a');
  // create a blobURI pointing to our Blob
  link.href = URL.createObjectURL(blob);
  link.download = fileName;
  // some browser needs the anchor to be in the doc
  document.body.append(link);
  link.click();
  link.remove();
  // in case the Blob uses a lot of memory
  setTimeout(() => URL.revokeObjectURL(link.href), 7000);
};

const initialForRolePaper = (
  index: any,
  noClass: any,
  type: any,
  w: any,
  h: any,
  pdfArry: any,
  childPaperElements: any,
  setDownloading: any,
  title: any,
) => {
  html2canvas(childPaperElements[index] as HTMLElement, {
    backgroundColor: '#ffffff',
    scale: 3,
    allowTaint: true,
  }).then((canvas) => {
    let PDF = new jsPDF(undefined, 'pt', type);
    const dataURL = canvas.toDataURL('image/jpeg', 1.0);
    const { width, height } = canvas;
    let pageHeight = (width / w) * h;
    let leftHeight = height;
    let position = 0;
    let imgHeight = (w / width) * height;
    if (leftHeight < pageHeight) {
      PDF.addImage(dataURL, 'JPEG', 0, 0, w, imgHeight);
    } else {
      while (leftHeight > 0) {
        PDF.addImage(dataURL, 'JPEG', 0, position, w, imgHeight);
        leftHeight -= pageHeight;
        position -= h;
        if (leftHeight > 0) PDF.addPage();
      }
    }
    pdfArry.push(PDF.output('arraybuffer'));
    console.log('index', index);
    if (index === childPaperElements.length - 1) {
      if (pdfArry.length === childPaperElements.length) {
        mergePdfs(pdfArry, title, setDownloading);
        const tableWrapper = document.getElementById('tableWrapper');
        if (
          navigator.userAgent.match(
            /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i,
          )
        ) {
          tableWrapper!.style.maxWidth = '90%';
        } else {
          tableWrapper!.style.maxWidth = '80%';
        }
        return;
      }
    } else {
      index = index + 1;
      initialForRolePaper(
        index,
        noClass,
        type,
        w,
        h,
        pdfArry,
        childPaperElements,
        setDownloading,
        title,
      );
    }
  });
};

const start = (
  title: string,
  type: PdfType,
  el: HTMLElement,
  noClass: string[],
  withDesign: boolean,
  setDownloading: Function,
) => {
  const [w, h] = TYPES[type];
  if (!el) throw new Error('Missing target element!');
  const childPaperElements = el.querySelectorAll('[role="paper"]');
  const pdfArry: ArrayBuffer[] = [];
  const promiseArray: any[] = [];
  let index = 0;
  const tableWrapper = document.getElementById('tableWrapper');
  tableWrapper!.style.maxWidth = '';
  initialForRolePaper(
    index,
    noClass,
    type,
    w,
    h,
    pdfArry,
    childPaperElements,
    setDownloading,
    title,
  );
};

export const downloadHtmlPdf = (
  title: string,
  type: PdfType,
  el: HTMLElement,
  noClass: string[],
  withDesign: boolean,
  setDownloading: Function,
) => {
  printHtml(el!);
  setDownloading(false);
};

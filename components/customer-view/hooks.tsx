import { useState, useEffect, useCallback, useMemo } from 'react';
import dayjs from 'dayjs';

import type {
  ProductInfoOption,
  ProductRelations,
  ProductRelationWithSubscription,
  SubscriptionActionOptions,
} from '../modals/subscription-bulkactions/types';
import type { Subscription } from '../revenue-builder-types/interface';
import {
  getProductRelationsByActionType,
  getTargetProductRelation,
} from '../modals/subscription-bulkactions/utils/index';
import { FieldValues, UseFormMethods } from 'react-hook-form';

const filterProductRelations = (relations: ProductRelations) => {
  const filtered: ProductRelations = {};
  const today = dayjs().startOf('day');

  Object.entries(relations).forEach(([id, subscriptionActionOptions]) => {
    const filteredActionOptions: SubscriptionActionOptions = {};

    Object.entries(subscriptionActionOptions).forEach(([type, actionItemOptions]) => {
      filteredActionOptions[type] = actionItemOptions.filter((actionItemOption) => {
        const startDate = dayjs(actionItemOption.startDate);
        // This relationship is valid if its start date is on or before today
        return startDate.isSame(today, 'day') || startDate.isBefore(today, 'day');
      });
    });

    filtered[id] = filteredActionOptions;
  });

  return filtered;
};

export const useProductRelations = (
  subscriptions: Subscription[],
  getSwapUpgradeDowngradeOptions?: (params: {
    subscriptionIds: string[];
  }) => Promise<ProductRelations>,
) => {
  const [productRelations, setProductRelations] = useState<ProductRelations | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const productRelationsByActionType: Record<string, ProductRelationWithSubscription[]> =
    useMemo(() => {
      return getProductRelationsByActionType(productRelations, subscriptions);
    }, [productRelations, subscriptions]);

  useEffect(() => {
    let isMounted = true;

    const fetchProductRelations = async () => {
      if (!subscriptions.length || !getSwapUpgradeDowngradeOptions) return;

      setIsLoading(true);
      try {
        const relations = await getSwapUpgradeDowngradeOptions({
          subscriptionIds: subscriptions.map((sub) => sub.id),
        });
        const filteredRelations = filterProductRelations(relations);
        if (isMounted) {
          setProductRelations(filteredRelations);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error fetching swap upgrade downgrade options', err);
          setError(err instanceof Error ? err : new Error('Failed to fetch product relations'));
          setProductRelations(null);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchProductRelations();

    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line
  }, [subscriptions]);

  return { productRelations, error, isLoading, productRelationsByActionType };
};

export const useSelectedToProduct = (
  subscription: Subscription,
  productRelationsByActionType: ProductRelationWithSubscription[],
  initialToProductId: string,
  methods?: UseFormMethods<FieldValues>,
) => {
  const [selectedToProduct, setSelectedToProduct] = useState<ProductInfoOption>({
    id: '',
    name: '',
  });

  const fromProductInfo = useMemo(() => {
    return {
      key: `${subscription.id}_${subscription.productId}`,
      subscriptionId: subscription.id,
      subscriptionName: subscription.name ?? '',
      subscriptionUOM: subscription.uom?.name ?? '',
      fromProductId: subscription.productId ?? '',
      fromProductName: subscription.productName ?? '',
    };
  }, [subscription]);

  useEffect(() => {
    const targetProductRelation = getTargetProductRelation(
      productRelationsByActionType,
      fromProductInfo,
      initialToProductId,
    );
    if (targetProductRelation) {
      setSelectedToProduct({
        id: targetProductRelation.toProduct.id,
        name: targetProductRelation.toProduct.name,
      });
    }
  }, [initialToProductId, productRelationsByActionType, fromProductInfo]);

  const handleFieldChange = useCallback(
    (fieldApiName: string, fieldNewValue: string) => {
      if (fieldApiName === 'toProduct') {
        const targetProductRelation = getTargetProductRelation(
          productRelationsByActionType,
          fromProductInfo,
          fieldNewValue,
        );
        if (targetProductRelation) {
          setSelectedToProduct({
            id: targetProductRelation.toProduct.id,
            name: targetProductRelation.toProduct.name,
          });
          if (methods) {
            methods.setValue('samePriceSwap', targetProductRelation.samePriceSwap);
          }
        }
      }
    },
    [productRelationsByActionType, fromProductInfo],
  );

  return { selectedToProduct, handleFieldChange };
};

export default [
  {
    apiName: 'totalTCV',
    name: 'Total TCV',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    precision: 15,
    scale: 3,
  },
  {
    apiName: 'totalPrice',
    name: 'Total Price',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    precision: 15,
    scale: 3,
  },
  {
    apiName: 'salesPrice',
    name: 'Sales Price',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    precision: 15,
    scale: 3,
  },
  {
    apiName: 'status',
    name: 'Status',
    type: 'pickList',
    creatable: false,
    updatable: false,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    defaultValue: 'Draft',
    required: false,
    unique: false,
    valueSet: {
      apiName: 'SubscriptionStatus',
      name: 'Subscription Status',
      defaultValue: 'Draft',
      alphabeticallyOrdered: true,
      valuePairs: [
        {
          apiName: 'Draft',
          name: 'Draft',
          orderNumber: 0,
        },
        {
          apiName: 'Active',
          name: 'Active',
          orderNumber: 0,
        },
        {
          apiName: 'Cancelled',
          name: 'Cancelled',
          orderNumber: 0,
        },
      ],
    },
  },
  {
    apiName: 'subscriptionEndDate',
    name: 'Subscription End Date',
    type: 'date',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'subscriptionInternalNumber',
    name: 'Subscription Internal Number',
    type: 'text',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    minLength: 0,
    maxLength: 80,
  },
  {
    apiName: 'subscriptionNumber',
    name: 'Subscription Number',
    type: 'autoNumber',
    creatable: false,
    updatable: false,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    autoNumberSetting: {
      displayFormat: 'SUB-{00000000}',
      startValue: 0,
    },
  },
];

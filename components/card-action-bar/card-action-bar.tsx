import React, { useEffect, useState } from 'react';

import Button from '@material-ui/core/Button';
import ButtonGroup from '@material-ui/core/ButtonGroup';
import Typography from '@material-ui/core/Typography';
import { makeStyles, useTheme } from '@material-ui/core/styles';
import MoreHorizIcon from '@material-ui/icons/MoreHoriz';

import { CardAction } from '../metadata/interface';
import useConfirmDialog from '../use-confirm-dialog';
import { Action, Props } from './interface';

const useStyles = makeStyles({
  root: {
    width: '100%',
    backgroundColor: '#f2ecfe',
    '&:hover:not([disabled])': {
      textDecoration: 'solid',
      backgroundColor: '#f2ecfe',
      color: '#6239eb',
    },
    '&:disabled': {
      borderTop: 'none',
      opacity: '0.7',
    },
  },
  iconButtonContainer: {
    padding: '6px 0',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },
  text: {
    color: '#6239EB',
    fontWeight: 500,
    fontSize: '.625rem',
    textTransform: 'none',
    minWidth: '54px',
  },
  submitBtn: {
    padding: '8px 0',
    borderBottomLeftRadius: '4px !important',
    borderBottomRightRadius: '4px !important',
    borderTopRightRadius: 0,
    borderTopLeftRadius: 0,
    backgroundColor: '#6239EB',
    color: '#FFF',
    borderColor: '#6239EB',
    fontSize: '.8rem',
    textTransform: 'none',
    '&:hover:not([disabled])': {
      textDecoration: 'solid',
      backgroundColor: '#6239eb',
      color: '#FFF',
    },
    '&:disabled': {
      backgroundColor: '#6239eb',
      color: '#FFF',
      opacity: '0.7',
    },
  },
  group: {
    width: '100%',
  },
  lastBtnGroup: {
    '& button': {
      borderBottom: 0,
    },
  },
  container: {
    // maxWidth: '275px',
    width: '100%',
    '& button': {
      borderColor: '#CCBFF2',
      borderTop: 0,
    },
    '& div:first-child': {
      '& button:last-child': {
        borderRight: 0,
        borderBottomRightRadius: 0,
      },
      '& button:first-child': {
        borderLeft: 0,
        borderBottomRightRadius: 0,
        borderBottomLeftRadius: 0,
      },
    },
    '& div:not(:first-child)': {
      '& button:first-child': {
        borderLeft: 0,
        borderTopLeftRadius: 0,
        borderBottomLeftRadius: 0,
      },
      '& button:last-child': {
        borderRight: 0,
        borderTopRightRadius: 0,
        borderBottomRightRadius: 0,
      },
    },
  },
  buttonGroup: {
    display: 'flex',
    justifyContent: 'space-around',
  },
});

const defaultProps = {
  showSubmitBtn: true,
};

const toGrid = (array: Array<CardAction>, size: number, numRows: number) => {
  const result = [];
  for (let i = 0; i < array.length; i += size) {
    if (array.length - i < size) {
      return result;
    }
    if (numRows === 1) {
      result.push(array.slice(i, i + size + 1));
    } else {
      result.push(array.slice(i, i + size));
    }
  }
  return result;
};

export const CardActionBar: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const theme = useTheme();
  const {
    actions,
    numActionsPerRow,
    maxNumActionsToShow,
    numRows,
    hideMoreAction,
    handleClickAction,
    showSubmitBtn,
    isProcessing = false,
  } = props;

  useEffect(() => {
    const actionsGrid = toGrid(actions, numActionsPerRow, numRows);
    setActionsGrid(actionsGrid);
  }, [actions]);

  const classes = useStyles();
  const [pendingConfirmAction, setPendingConfirmAction] = useState<CardAction | null>(null);
  const [cardActions, setCardActions] = useState<Array<Action>>(actions);
  const [actionsGrid, setActionsGrid] = useState<Array<Array<CardAction>>>([[]]);
  const { showConfirmDialog, ConfirmDialog } = useConfirmDialog({
    submitButtonText: 'Yes',
    cancelButtonText: 'No',
    onOk: async () => {
      pendingConfirmAction && (await handleClickAction(pendingConfirmAction));
    },
    onCancel: () => {},
  });

  useEffect(() => {
    if (actions.length > maxNumActionsToShow && !hideMoreAction) {
      // TODO: temporarily add this prop in - once we complete subscription actions, we will remove this prop
      const cardActionsCopy = [...cardActions];
      cardActionsCopy.splice(maxNumActionsToShow, 1, {
        id: 'more',
        name: 'More',
        description: 'More',
        icon: MoreHorizIcon,
      });
      setCardActions(cardActionsCopy);
    } else {
      setCardActions(actions);
    }
  }, [actions]);

  return (
    <div className={classes.container} role="buttonbar">
      {actionsGrid.map((actionsRow: Array<CardAction>, index: number) => {
        return (
          <ButtonGroup
            key={index}
            className={
              index === actionsGrid.length - 1 && !showSubmitBtn
                ? `${classes.group} ${classes.lastBtnGroup}`
                : `${classes.group}`
            }
          >
            {actionsRow.map((action: CardAction) => {
              return (
                <Button
                  key={action.id}
                  disabled={isProcessing}
                  classes={{
                    root: classes.root,
                  }}
                  onClick={() => {
                    if (action.confirmation) {
                      setPendingConfirmAction(action);
                      showConfirmDialog(action.confirmation);
                    } else {
                      handleClickAction(action);
                    }
                  }}
                  fullWidth
                >
                  <span className={classes.iconButtonContainer} style={action?.style}>
                    {/* as we want to confirgure the color of the icon, so we add style props here */}
                    {action.icon && (
                      <action.icon
                        color="secondary"
                        //used for non-material icons
                        style={{ color: theme.palette.secondary.main }}
                      />
                    )}
                    <Typography className={classes.text} component="p" variant="body2">
                      {!showSubmitBtn ? (isProcessing ? 'Submitting' : action.name) : action.name}
                    </Typography>
                  </span>
                </Button>
              );
            })}
          </ButtonGroup>
        );
      })}
      {showSubmitBtn && cardActions && cardActions.length > 0 && (
        <Button
          variant="outlined"
          disabled={isProcessing}
          classes={{
            root: classes.submitBtn,
          }}
          fullWidth
          onClick={() => {
            handleClickAction(cardActions[cardActions.length - 1]);
          }}
        >
          {isProcessing ? 'Submitting' : cardActions[cardActions.length - 1].name}
        </Button>
      )}
      <ConfirmDialog />
    </div>
  );
};

export default CardActionBar;

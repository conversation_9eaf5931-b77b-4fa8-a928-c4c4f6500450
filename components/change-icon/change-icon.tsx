import { SvgIcon } from '@material-ui/core';
import React from 'react';
import { Props } from './interface';

const defaultProps = {};

const ChangeIcon: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const { viewBox } = props;

  return (
    <SvgIcon viewBox={viewBox}>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Change</title>
        <g
          id="08-Orders"
          stroke="none"
          strokeWidth="1"
          fill="none"
          fillRule="evenodd"
          opacity="0.200000003"
        >
          <g id="8-1-1-4-Create-Quote" transform="translate(-380.000000, -1343.000000)">
            <g id="Product-Options" transform="translate(157.000000, 290.000000)">
              <g id="Group-5" transform="translate(46.000000, 469.000000)">
                <g id="Table" transform="translate(0.000000, 37.000000)">
                  <g id="row" transform="translate(23.500000, 546.000000)">
                    <g id="exchange-fill" transform="translate(153.500000, 1.000000)">
                      <polygon id="Path" points="0 0 16 0 16 16 0 16"></polygon>
                      <path
                        d="M8,14.6666667 C4.318,14.6666667 1.33333333,11.682 1.33333333,8 C1.33333333,4.318 4.318,1.33333333 8,1.33333333 C11.682,1.33333333 14.6666667,4.318 14.6666667,8 C14.6666667,11.682 11.682,14.6666667 8,14.6666667 Z M8,6 L5.33333333,6 L5.33333333,7.33333333 L11.3333333,7.33333333 L8,4 L8,6 Z M4.66666667,8.66666667 L8,12 L8,10 L10.6666667,10 L10.6666667,8.66666667 L4.66666667,8.66666667 Z"
                        id="Shape"
                        fill="#000000"
                        fillRule="nonzero"
                      ></path>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default ChangeIcon;

import React, { useContext, useEffect, useState } from 'react';

import { Button, Grid, useMediaQuery } from '@material-ui/core';
import IconButton from '@material-ui/core/IconButton';
import { Theme, makeStyles } from '@material-ui/core/styles';
import ClearIcon from '@material-ui/icons/Clear';

import FilterIcon from '../filter-icon';
import FilterPanel from '../filter-panel';
import { OrderByField, RubyFilter } from '../graph-ql-query-constructor/interface';
import MultiSortBuilder, { SortField } from '../multi-sort-builder';
import RubyListViewLocalStateContext, {
  ListViewLocalState,
} from '../ruby-list-view-local-state-context';
import SavedFiltersList from '../saved-filters-list';
import TextInput from '../text-input';
import { Props } from './interface';

const defaultProps = {
  predefinedFilters: [],
  useMultiSort: true,
  searchBtnLabel: 'Search',
  sortingList: [],
  showFiltersLabel: true,
};

const useStyles = makeStyles({
  searchBarWrapper: {
    display: 'flex',
  },
  searchBarInput: {
    borderRightColor: 'whitesmoke',
    // borderRadius: '4px 0px 0px 4px !important',
    fontSize: '.875rem',
    height: '100%',
    marginTop: '0 !important',
    backgroundColor: '#f1f0f2 !important',
    border: 'none',
    minHeight: '22px',
  },
  searchBarRoot: {
    overflow: 'hidden',
    borderRadius: '4px 0 0 4px !important',
    backgroundColor: '#f1f0f2 !important',
  },
  clearInputIcon: {
    padding: '0px',
    margin: '12px',
    '& svg': {
      fontSize: '1rem',
    },
  },
  filterBtn: {
    marginLeft: '8px',
    minHeight: '46px',
    padding: '6px 12px',
    color: '#6239eb',
    border: '1px solid hsl(260, 82%, 98%)',
    backgroundColor: '#f2ecfe',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '.875rem',
    fontWeight: 500,
    textTransform: 'none',
    '&:hover': {
      backgroundColor: '#f2ecfe',
    },
  },
  searchBtn: {
    width: '100%',
    maxWidth: '200px',
    fontWeight: 500,
    borderRadius: '0px 4px 4px 0px',
    height: '100%',
    backgroundColor: '#6239eb',
    fontSize: '.875rem',
    color: '#fff',
    textTransform: 'none',
    cursor: 'pointer',
    padding: '0px 8px',
    '&:hover': {
      // you want this to be the same as the backgroundColor above
      backgroundColor: '#6239eb',
    },
  },
  filterItem: {
    borderTopColor: 'rgba(0, 0, 0, 0.23)',
    borderRight: 'none',
    borderTop: 'none',
    borderBottom: 'none',
    borderLeft: '1px solid #e5e5e5',
    borderRadius: 0,
    height: '100%',
    color: '#6239eb',
    width: '100%',
    maxWidth: '200px',
    justifyContent: 'center',
    textAlign: 'center',
    textTransform: 'capitalize',
    backgroundColor: '#f1f0f2',
    boxShadow: 'none',
    fontSize: '.8rem',
    fontWeight: 500,
    display: 'flex',
    alignItems: 'center',
    '&:hover:not([disabled])': {
      color: '#6239eb',
      backgroundColor: '#f1f0f2',
    },
  },
});

export const GridSearchBar: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const {
    handleSearch,
    placeholder,
    predefinedFilters,
    objectMetadata,
    updateFilters,
    saveFilter,
    listFilters,
    deleteFilter,
    useMultiSort,
    sortingList,
    onSortingListChange,
    searchBtnLabel = 'Search',
    showFiltersLabel,
    layoutColumns = 3,
    showFilters = true,
    showSearchBar = true,
    searchBarWidth = 'half',
    showFilterHistory = true,
    isSettingSearch = false,
    allFilter,
    setAllFilter,
    filterToDelete,
    completeDeleteFilter,
    onAppliedFiltersChange,
    allMetadatas,
  } = props;

  const classes = useStyles();
  const isXS = useMediaQuery((theme: Theme) => {
    if (!theme?.breakpoints) {
      return 'lg';
    }
    return theme.breakpoints.down('xs');
  });

  const [search, setSearch] = useState('');
  const [openFilters, setOpenFilters] = useState(false);
  const localStateContext = useContext(RubyListViewLocalStateContext);
  const [allFiltersState, setAllFiltersState] = useState<Array<RubyFilter>>([]);

  const handleUpdateAllFilters = (newFilters: Array<RubyFilter>) => {
    setAllFiltersState(newFilters);
    if (typeof setAllFilter === 'function') {
      setAllFilter(newFilters);
    }
  };

  const getOrderByFields = (sortFields: SortField[]) => {
    return sortFields
      .filter((_) => !!_.field)
      .map(({ field, order }) => {
        return {
          columnName: field,
          direction: order.toLowerCase(),
        };
      });
  };

  React.useEffect(() => {
    if (filterToDelete && allFiltersState?.length) {
      const newFilters = [...allFiltersState].filter((x: any) => x.id !== filterToDelete);
      setAllFiltersState(newFilters);
      if (typeof setAllFilter === 'function') {
        setAllFilter(newFilters);
      }
      if (completeDeleteFilter) {
        handleSearch(search, newFilters, getOrderByFields(sortingList));
        completeDeleteFilter();
      }
    }
  }, [
    allFiltersState,
    filterToDelete,
    setAllFilter,
    completeDeleteFilter,
    handleSearch,
    search,
    sortingList,
  ]);

  const handleOpenFilters = () => {
    setOpenFilters(true);
  };

  const getAppliedFilters = () => {
    return allFilter
      ? allFilter.filter((_) => _.isApplied)
      : allFiltersState.filter((_) => _.isApplied);
  };

  const updateListViewLocalState = async (payload: Partial<ListViewLocalState>) => {
    if (localStateContext) {
      const listViewLocalState = await localStateContext.getLocalState();
      await localStateContext.setLocalState({ ...listViewLocalState, ...payload });
    }
  };

  const doSearch = () => {
    const orderByFields = getOrderByFields(sortingList);
    const appliedFilters = getAppliedFilters();
    handleSearch(search, appliedFilters, orderByFields);
    updateListViewLocalState({ lastSearching: search });
  };

  const onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      doSearch();
    }
  };

  const filtersBtnLabel = () => {
    let filterLabel: string;
    const appliedFilters = allFilter
      ? allFilter.filter((f) => f.isApplied === true)
      : allFiltersState.filter((f) => f.isApplied === true);

    if (appliedFilters.length === 0) {
      filterLabel = 'Filters';
    } else if (appliedFilters.length === 1) {
      filterLabel = '1 Filter Active';
    } else {
      filterLabel = `${appliedFilters.length} Filters Active`;
    }
    return filterLabel;
  };

  const retainSearchKeywords = async () => {
    if (localStateContext) {
      const localState = await localStateContext.getLocalState();
      if (localState?.lastSearching) {
        setSearch(localState?.lastSearching);
      }
    }
  };

  useEffect(() => {
    retainSearchKeywords();
  }, []);

  const filterButton = (
    <Button
      className={classes.filterItem}
      style={!showFiltersLabel ? { maxWidth: '30px' } : undefined}
      type="button"
      onClick={handleOpenFilters}
    >
      <FilterIcon />
      {showFiltersLabel && filtersBtnLabel()}
    </Button>
  );
  return (
    <Grid container alignItems="flex-start" spacing={2}>
      <Grid
        container
        item
        xs={12}
        md={searchBarWidth === 'full' ? 12 : 6}
        style={{ marginTop: '7px' }}
      >
        {showSearchBar && (
          <Grid item xs={12} className={classes.searchBarWrapper}>
            <TextInput
              value={search}
              handleInputChange={(value) => {
                setSearch(value);
              }}
              disableLabel
              field={{
                apiName: 'search',
                type: 'text',
                name: 'Search',
              }}
              onKeyDown={onKeyDown}
              placeholder={placeholder}
              classes={{
                input: classes.searchBarInput,
                root: classes.searchBarRoot,
              }}
              endAdornment={
                search ? (
                  <IconButton className={classes.clearInputIcon} onClick={() => setSearch('')}>
                    <ClearIcon />
                  </IconButton>
                ) : null
              }
            />
            {showFilters && filterButton}
            <Button type="button" onClick={() => doSearch()} className={classes.searchBtn}>
              {searchBtnLabel}
            </Button>
          </Grid>
        )}
        {showFilterHistory && !isSettingSearch && (
          <Grid item xs={12}>
            <SavedFiltersList
              objectApiName={objectMetadata.apiName}
              predefinedFilters={predefinedFilters || []}
              allFilters={allFilter ? allFilter : allFiltersState}
              handleUpdateAllFilters={handleUpdateAllFilters}
              listFilters={listFilters}
              onAppliedFiltersChange={async (filtersToApply) => {
                if (onAppliedFiltersChange) {
                  onAppliedFiltersChange();
                }
                await handleSearch(search, filtersToApply, getOrderByFields(sortingList));
              }}
            />
          </Grid>
        )}
        <FilterPanel
          saveFilter={saveFilter}
          updateFilters={updateFilters}
          savedFilters={allFilter ? allFilter : allFiltersState}
          deleteFilter={deleteFilter}
          updateListOfSavedFilters={handleUpdateAllFilters}
          openFilters={openFilters}
          handleCloseFilters={() => setOpenFilters(false)}
          searchResult={search}
          handleSearch={handleSearch}
          objectMetadata={objectMetadata}
          allMetadatas={allMetadatas}
        />
      </Grid>
      {useMultiSort && (
        <Grid
          item
          xs={12}
          md={isSettingSearch ? 12 : 6}
          style={
            !isSettingSearch
              ? isXS
                ? { marginTop: '8px' }
                : { marginTop: '-17px' }
              : { marginTop: '8px' }
          }
        >
          <MultiSortBuilder
            sortingList={sortingList}
            sortableFields={objectMetadata.fields?.filter((_) => _.sortable === true) || []}
            onChange={(newSortingList) => {
              const orderBys: OrderByField[] = getOrderByFields(newSortingList);
              onSortingListChange(newSortingList);
              handleSearch(search, getAppliedFilters(), orderBys, undefined, undefined, true);
              updateListViewLocalState({ orderByFields: orderBys });
            }}
            layoutColumns={layoutColumns}
          />
        </Grid>
      )}
      {showFilterHistory && isSettingSearch && (
        <Grid item xs={12}>
          <SavedFiltersList
            objectApiName={objectMetadata.apiName}
            predefinedFilters={predefinedFilters || []}
            allFilters={typeof allFilter !== 'undefined' ? allFilter : allFiltersState}
            handleUpdateAllFilters={handleUpdateAllFilters}
            listFilters={listFilters}
            onAppliedFiltersChange={async (filtersToApply) => {
              await handleSearch(search, filtersToApply, getOrderByFields(sortingList));
            }}
          />
        </Grid>
      )}
    </Grid>
  );
};

export default GridSearchBar;

const Theme = {
  overrides: {
    PrivateNotchedOutline: {
      root: {
        borderColor: 'transparent !important',
      },
    },
    MuiOutlinedInput: {
      root: {
        borderRadius: 4,
        position: 'relative',
        fontSize: '.875rem',
        color: '#4d4c50',
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: 'transparent !important',
        },
        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          borderColor: '#f1f0f2 !important',
        },
      },
      input: {
        padding: '12px 20px',
      },
    },
    MuiAutocomplete: {
      inputRoot: {
        borderRadius: 4,
        position: 'relative',
        backgroundColor: '#f1f0f2',
        border: '2px solid #f1f0f2',
        fontSize: '.875rem',
        color: '#4d4c50',
        paddingTop: '6px !important',
        maxHeight: '47px',
        width: '100%',
        padding: '12px 20px',
      },
      input: {
        fontSize: '.8rem',
      },
    },
  },
  palette: {
    secondary: {
      main: '#6239eb',
    },
  },
};

export default Theme;

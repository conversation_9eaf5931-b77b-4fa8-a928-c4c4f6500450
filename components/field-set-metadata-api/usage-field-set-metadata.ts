import { RubyField } from '../metadata/interface';

export default [
  {
    apiName: 'name',
  },
  {
    apiName: 'subscriptionNumber',
  },
  {
    apiName: 'invoiceItemNumber',
  },
  {
    apiName: 'status',
  },
  {
    apiName: 'quantity',
  },
  {
    apiName: 'termedTotalQuantity',
  },
  {
    apiName: 'ratedAmount',
  },
  {
    apiName: 'startTime',
  },
  {
    apiName: 'termStartDate',
  },
  {
    apiName: 'termEndDate',
  },
] as RubyField[];

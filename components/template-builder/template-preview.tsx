import React, { useContext, useEffect, useState } from 'react';
import { createUseStyles } from 'react-jss';
import { Grid } from '@material-ui/core';
import Paper from '@material-ui/core/Paper';
import ColorThief from '@neutrixs/colorthief';
import { get, set } from 'lodash';
import ImagePlaceholer from '../../static/images/image-placeholder.png';
import Loading from '../loading';
import TemplateBuilderContext from '../template-builder-context';
import { TemplateSchema } from '../template-builder-context/template-builder-context';
import { PreviewGenerator } from './generator/preview-generator';
import { PdfGenerator } from './generator/pdf-generator';
import { TemplatePreviewProps } from './interface';

const defaultProps = {};

export const useStyles = createUseStyles({
  PaperContainer: {
    padding: '2px 0',
    boxShadow: 'none',
    minHeight: '120px',
    width: '100%',
    boxSizing: 'border-box',
  },
  //@ts-ignore
  previewPaper: ({ templateConfig, activateComponent }) => ({
    minHeight: '100vh',
    padding: '35px 30px',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
    boxSizing: 'border-box',
    background: 'white',
    boxShadow: '0px 2px 10px 0px rgba(200,200,200,0.5);',
    borderRadius: 0,
    marginBottom: '30px',
    breakBefore: 'page',
    position: 'relative',
    '&:before': {
      content: templateConfig.details.settings.waterMarkProps?.text
        ? `'${templateConfig.details.settings.waterMarkProps?.text}'`
        : '',
      position: 'absolute',
      zIndex: 9999,
      color: templateConfig.details.settings.waterMarkProps?.fontColor || '#000000',
      fontSize: (templateConfig.details.settings.waterMarkProps?.fontSize || 200) + 'px',
      fontWeight: '500',
      justifyContent: 'center',
      alignContent: 'center',
      opacity:
        (templateConfig.details.settings.waterMarkProps?.fontOpacity ||
        templateConfig.details.settings.waterMarkProps?.fontOpacity === 0
          ? 100 - templateConfig.details.settings.waterMarkProps?.fontOpacity
          : 20) / 100,
      transform: `rotate(${templateConfig.details.settings.waterMarkProps?.orientation === 'Diagonal' ? -45 : templateConfig.details.settings.waterMarkProps?.orientation === 'Horizontal' ? 0 : -45}deg)`,
      width: '100%',
      height: '100%',
      textAlign: 'center',
      pointerEvents: 'none',
      fontFamily: templateConfig.details.settings.waterMarkProps?.fontStyle || 'AvenirNextMedium',
      display:
        templateConfig.details.settings.waterMarkProps?.visible !== null &&
        templateConfig.details.settings.waterMarkProps?.visible === false
          ? 'none'
          : 'grid',
      margin: '-35px -30px',
    },
  }),
  SmallSizeImg: {
    width: '80px',
    objectFit: 'contain',
  },
  MiddleSizeImg: {
    width: '100px',
    objectFit: 'contain',
  },
  LargeSizeImg: {
    width: '120px',
    objectFit: 'contain',
  },
  //@ts-ignore
  CustomizeSizeImg: () => ({
    objectFit: 'contain',
  }),
  //@ts-ignore
  fieldItem: ({ templateConfig, content }) => ({
    padding: '13px',
    overflowWrap: 'anywhere',
    flexDirection: 'column',
    display: content?.visible !== null && content?.visible === false ? 'none' : 'flex',
  }),
  //@ts-ignore
  imageItem: ({ templateConfig, content }) => ({
    overflowWrap: 'anywhere',
    display: content?.visible !== null && content?.visible === false ? 'none' : 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-start',
  }),
  dividerItem: {
    height: '3px',
    margin: '5px auto',
    background: '#eee',
  },
  fieldLabel: {
    fontSize: '14px',
    fontWeight: 'bolder',
    marginBottom: '10px',
    minWidth: '30%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  fieldContent: {
    fontSize: '14px',
    minWidth: '30%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  //@ts-ignore
  summaryContainer: ({ templateConfig, activateComponent }) => ({
    fontSize: '0.875rem',
    color: get(activateComponent, 'styleSetting.color'),
    '& > div:last-child': {
      borderTop: `2px solid ${get(activateComponent, 'styleSetting.borderColor')}`,
      borderBottom: `2px solid ${get(activateComponent, 'styleSetting.borderColor')}`,
      color: `${get(templateConfig, 'details.settings.themeColor')}`,
    },
    '& > div': {
      padding: '5px 10px',
    },
  }),
  //@ts-ignore
  relatedListTable: ({ templateConfig, activateComponent }) => ({
    transformOrigin: 'left',
    // '@media print': {
    //   'transform': 'scale(0.5)'
    // }
  }),
  //@ts-ignore
  summaryItem: ({ templateConfig, activateComponent }) => ({
    color: get(activateComponent, 'styleSetting.color'),
    '&:last-child': {
      borderTopWidth: '2px',
      borderBottomWidth: '2px',
      borderColor: get(activateComponent, 'styleSetting.borderColor'),
    },
  }),
  activatePreviewComponent: {
    animation: '$shakeBackgroundColor 2s',
  },
  activatePreviewContainerComponent: {
    animation: '$shake 2s',
  },
  '@keyframes shakeBackgroundColor': {
    '0%': {
      backgroundColor: '#F8F5FF',
      transform: 'scale(1)',
    },
    '50%': {
      backgroundColor: 'rgba(98,57,235, .15)',
      transform: 'scale(1.005)',
    },
    '100%': {
      transform: 'scale(1)',
    },
  },
  '@keyframes shake': {
    '0%': {
      transform: 'scale(1)',
    },
    '50%': {
      transform: 'scale(1.005)',
    },
    '100%': {
      transform: 'scale(1)',
    },
  },
  watermark: {},
});

export const TemplatePreview: React.FC<TemplatePreviewProps> = (
  userProps: TemplatePreviewProps,
) => {
  const props = { ...defaultProps, ...userProps };

  const { mode, setLoaded, tenant, isDownload, insideEditor } = props;

  const templateBuilderContext = useContext(TemplateBuilderContext);
  const { templateBuilderService } = templateBuilderContext;
  if (!templateBuilderService) {
    throw Error(
      'TemplateSetting component requires templateBuilderService to be declared in context provider',
    );
  }
  const {
    getTemplateBuilderConfig,
    setTemplateBuilderConfig,
    getActivateComponent,
    setActivateComponent,
    getTemplateOrderProduct,
    getTemplateRecord,
  } = templateBuilderService;
  const { templateConfig } = getTemplateBuilderConfig();
  const { activateComponent } = getActivateComponent();
  const { templateOrderProduct } = getTemplateOrderProduct();
  const { templateRecord } = getTemplateRecord();

  const rgbToHex = (a: any, b: any, c: any) => {
    const r = /^\d{1,3}$/;
    const hexs = [a.toString(16), b.toString(16), c.toString(16)];
    for (var i = 0; i < 3; i++) if (hexs[i].length == 1) hexs[i] = '0' + hexs[i];
    return hexs.join('');
  };

  const updateThemeColor = (img: any) => {
    const colorThief = new ColorThief();
    let color = colorThief.getColor(img);
    const hexColor = rgbToHex(color[0], color[1], color[2]);
    set(templateConfig, 'details.settings.themeColor', `#${hexColor}`);
    setTemplateBuilderConfig(Object.assign({}, templateConfig));
  };

  const updateTemplateConfigThemeColor = (imgSrc: string) => {
    const colorThief = new ColorThief();
    const img = new Image();
    img.addEventListener('load', () => {
      let color = colorThief.getColor(img);
      const hexColor = rgbToHex(color[0], color[1], color[2]);
      set(templateConfig, 'details.settings.themeColor', `#${hexColor}`);
      setTemplateBuilderConfig(Object.assign({}, templateConfig));
      setTimeout(() => {
        if (setLoaded) {
          console.log('page is load successfully!');
          setLoaded(true);
        }
      }, 200);
    });
    img.addEventListener('error', (e) => {
      console.log(`load ${imgSrc} error:`, e);
    });
    img.crossOrigin = 'anonymous';
    img.src = imgSrc;
  };

  useEffect(() => {
    if (
      templateConfig &&
      get(templateConfig, 'details.settings.logo') &&
      mode !== 'view' &&
      get(templateConfig, 'details.settings.themeColor') === '#6239EB'
    ) {
      updateTemplateConfigThemeColor(get(templateConfig, 'details.settings.logo'));
    } else {
      if (setLoaded) {
        setLoaded(true);
      }
    }
  }, []);

  //@ts-ignore
  const classes = useStyles({ templateConfig, activateComponent });

  const pageBreakIndex: number[] = [];
  templateConfig.details.data.forEach((c: any, index: number) => {
    if (c.type === 'pageBreak') {
      pageBreakIndex.push(index);
    }
  });
  if (pageBreakIndex.length > 0) {
    pageBreakIndex.push(templateConfig.details.data.length);
  }

  return (
    <>
      {isDownload
        ? PdfGenerator(
            templateConfig,
            classes,
            templateRecord,
            templateOrderProduct,
            activateComponent,
            tenant,
            isDownload,
          )
        : PreviewGenerator(
            templateConfig,
            classes,
            templateRecord,
            templateOrderProduct,
            activateComponent,
            tenant,
            isDownload,
            insideEditor,
          )}
      <div
        id="hidden-indicator"
        style={{
          opacity: 0,
        }}
      />
    </>
  );
};

export default TemplatePreview;

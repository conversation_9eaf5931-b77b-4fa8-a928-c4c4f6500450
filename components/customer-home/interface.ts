import { RubyField, RubyObject, ColumnConfig } from '../metadata';
import QueryExecutor, { CountQueryExecutor } from '../query-executor';

export interface Props {
  activeTabIndex: number;
  setActiveTabIndex: Function;
  idsCondition?: string;
  jumpFrom?: boolean;
}

export interface CustomerHomeProps {
  customerMetadata: RubyObject;
  executeQuery: QueryExecutor;
  executeGetCountQuery?: CountQueryExecutor;
  getFieldSetMetadata: (fieldSetName: string, objectApiName: string) => Promise<Array<RubyField>>;
  currencyIsoCode: string;
  navigateToNewCustomerViewPage: (customerId: string) => void;
  getTransactionHubStatus?: Function;
  transactionHubColumn?: ColumnConfig;
}

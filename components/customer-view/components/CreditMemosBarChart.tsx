import React, { memo } from 'react';
import { StatsBarChart } from '../../stats-bar-chart/stats-bar-chart';
import { formatNumberForDisplay } from '../../list/list';
import { calculateBarItemWidth } from '../utils/utils';

interface CreditMemosBarChartProps {
  objects: any | null;
  currencyIsoCode: string;
  locale: string | undefined;
}

const CreditMemosBarChart = memo((props: CreditMemosBarChartProps) => {
  const { objects, currencyIsoCode, locale } = props;

  if (!objects || !objects.length) return <></>;

  const draft = objects
    .filter((x: any) => x.status === 'Draft')
    .reduce((x: number, y: any) => x + Number(y.balance), 0);

  const active = objects
    .filter((x: any) => x.status === 'Active')
    .reduce((x: number, y: any) => x + Number(y.balance), 0);

  const applied = objects
    .filter((x: any) => x.status === 'Active')
    .reduce((x: number, y: any) => x + (Number(y.amount) - Number(y.balance)), 0);

  const totalAmountWithDraft = draft + active + applied;

  let defaultWidth = null;

  if (totalAmountWithDraft === 0) {
    defaultWidth = '33.33%';
  }

  const items = [
    {
      text: { value: formatNumberForDisplay(draft, currencyIsoCode, true, false, locale) },
      desc: { value: 'DRAFT' },
      width: defaultWidth || calculateBarItemWidth(draft, totalAmountWithDraft),
      backgroundColor: '#D6D6D6',
    },
    {
      text: { value: formatNumberForDisplay(active, currencyIsoCode, true, false, locale) },
      desc: { value: 'ACTIVE' },
      width: defaultWidth || calculateBarItemWidth(active, totalAmountWithDraft),
      backgroundColor: '#0CB2D2',
    },
    {
      text: { value: formatNumberForDisplay(applied, currencyIsoCode, true, false, locale) },
      desc: { value: 'APPLIED' },
      width: defaultWidth || calculateBarItemWidth(applied, totalAmountWithDraft),
      backgroundColor: '#B09CF5',
    },
  ];

  return <StatsBarChart items={items} />;
});

export default CreditMemosBarChart;

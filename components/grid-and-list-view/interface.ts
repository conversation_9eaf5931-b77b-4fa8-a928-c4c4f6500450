import React from 'react';

import { ChangeGroup, ChangeItem } from '../change-cart';
import { GeneralInformationAccount } from '../general-information-summary-view/interface';
import { RubyFilter } from '../graph-ql-query-constructor/interface';
import { Props as GridSearchConfigs } from '../grid-search-bar';
import { Props as GridViewItemProps } from '../grid-view-item';
import {
  CardAction,
  GridViewMetadata,
  RubyField,
  RubyObject,
  SelectedSubscription,
  ColumnConfig,
} from '../metadata/interface';
import { TabFilter } from '../panel-with-tabs';
import QueryExecutor, { CountQueryExecutor } from '../query-executor';
import { Customer } from '../revenue-builder-types';
import { CustomerViewPageTab } from '../ruby-list-view-local-state-context';
import { RubySettings } from '../ruby-settings';
import { ProductRelations } from '../modals/subscription-bulkactions/types';

export interface Props {
  customer: Customer;
  onDeleteSelectedSubscription?: (key: string) => void;
  selectedSubscriptions: SelectedSubscription[];
  objectMetadata: RubyObject;
  executeQuery: QueryExecutor;
  executeGetCountQuery?: CountQueryExecutor;
  fieldSetMetadata: Array<RubyField>;
  tabFilters: Array<TabFilter>;
  handleUpdateObjects: (objects: Array<any> | null, searchResult: string) => void;
  activeTabDetail: CustomerViewPageTab;
  getInvoiceIdsForAsset: (params: { assetNumbers: string[]; assetType: string }) => any;
  activeTabIndex: number;
  handleUpdateActiveTabIndex: (activeTabIndex: number) => void;
  searchConfigs: Partial<GridSearchConfigs>;
  objects: Array<any> | null;
  gridViewMetadata: GridViewMetadata;
  currencyIsoCode: string;
  GridViewItem: React.ComponentType<GridViewItemProps>;
  handleAddToCart?: (changeGroup: ChangeGroup, changeItems: Array<ChangeItem>) => Promise<void>;
  RightOfTitleSlot?: React.ReactNode;
  handleCardClick?: (object: any) => void;
  personPlaceholderImage?: string;
  orderGridViewMetadata?: GridViewMetadata | null;
  orderFieldSetMetadata?: Array<RubyField> | null;
  subscriptionPricingFieldSet?: Array<RubyField> | null;
  subscriptionMetadata?: RubyObject;
  assetMetadata?: RubyObject;
  entitlementMetadata?: RubyObject;
  orderMetadata?: RubyObject;
  subscriptionDefaultFilter?: RubyFilter;
  actionEventHandler: (argument: {
    object: any;
    objectMetadata: RubyObject;
    action: CardAction;
    callBack?: () => void;
  }) => void | Promise<void>;
  refreshDataFlag?: number;
  getFieldSetMetadata?: (
    fieldSetApiName: string,
    objectApiName: string,
  ) => Promise<Array<RubyField>>;
  listViewMetadata: Array<RubyField>;
  loading?: boolean;
  rubySettings?: RubySettings;
  scrollButtons?: 'auto' | 'desktop' | 'on' | 'off';
  locale?: string;
  selectedCreditPoolId?: string;
  selectedObject?: any;
  setSelectedObject?: any;
  shouldDisableRowSelection?: (row: any) => boolean;
  disableSelectAll?: boolean;
  currentAccountIdANdChildrenAccountIds?: string[];
  loadChildernAndBillingAccounts?: () => Promise<GeneralInformationAccount | undefined>;
  loadChildernAndBillingAccountsByIds?: (
    ids: string[],
    billingAccountIds: any,
    childAccountIds: any,
    accountDetails: any,
  ) => Promise<{
    billingAccountResult: any;
    childAccountResult: any;
    expandedIds: any;
  } | null>;
  getTransactionHubStatus?: Function;
  transactionHubColumn?: ColumnConfig;
  getSwapUpgradeDowngradeOptions?: (params: {
    subscriptionIds: string[];
  }) => Promise<ProductRelations>;
}

import React, { ChangeEvent, useContext, useEffect, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { FormControl, Typography, Grid, TextField, Chip } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import AddCircleOutlineIcon from '@material-ui/icons/AddCircleOutline';
import HighlightOffIcon from '@material-ui/icons/HighlightOff';
import shortUUID from 'short-uuid';
import * as yup from 'yup';
import ConditionsEmpty from '../../static/images/webhook-eventlogs-empty.png';
import ClickAwayListener from '../click-away-listener';
import DialogComponent from '../dialog-component';
import FilterBuilder, { InvokeObject } from '../filter-builder';
import FilterIcon from '../filter-icon';
import FormSection, { CustomFieldControlRegistry, CustomFormControlProps } from '../form-section';
import { useYupValidationResolver } from '../form-validation';
import { RubyFilter } from '../graph-ql-query-constructor';
import { EditBucketIcon as EditIcon } from '../icons';
import LabelWithTooltip from '../label-with-tooltip';
import { RubyObject } from '../metadata';
import PickList, { PickListOption } from '../pick-list';
import { RubyButtonBar } from '../ruby-button';
import { useRubySnackbar } from '../ruby-notifier';
import { CollectionsContext } from '../collections/collections-context';
import {
  PaymentRule,
  PaymentRuleProps,
  EmptyPaymentRuleConditionsProps,
  PaymentRuleConditionItemProps,
  PaymentRuleConditionItem,
  PaymentRuleFilterProps,
} from './interface';

const useStyles = makeStyles({
  sectionTitle: {
    opacity: 0.7,
    color: '#000000',
    fontSize: '18px',
    fontWeight: 500,
    letterSpacing: '-0.77px',
    lineHeight: '25px',
    marginBottom: '10px',
  },
  emptyImg: {
    width: '100px',
    height: '100px',
    marginBottom: '20px',
  },
  emptyContainer: {
    marginTop: '20px',
  },
  addConditions: {
    color: '#6239eb',
    textDecoration: 'underline',
    cursor: 'pointer',
  },
  conditionItemWrapper: {
    border: '1px solid #E5E5E5',
    padding: '20px 25px',
    borderRadius: '5px',
    overflow: 'hidden',
    minHeight: '60px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-around',
    marginTop: '8px',
    marginBottom: '8px',
    position: 'relative',
  },
  statusBar: {
    width: '5px',
    position: 'absolute',
    left: '0',
    top: '0',
    bottom: '0',
    borderRadius: '5px 0 0 5px',
    backgroundColor: '#6239EB',
  },
  conditionItemContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  conditionName: {},
  flex: {
    flex: 1,
  },
  conditionLabel: {
    width: '150px',
    textTransform: 'uppercase',
    fontWeight: 'bold',
  },
  conditionLabelName: {
    fontWeight: 'bold',
    fontSize: '0.8rem',
  },
  deleteConditionItem: {
    position: 'absolute',
    top: '0',
    right: '-7px',
    cursor: 'pointer',
    color: 'red',
    background: 'white',
  },
  addNewConditionContainer: {
    display: 'flex',
    justifyContent: 'center',
  },
  inputText: {
    borderRadius: 0,
    border: 0,
    borderBottom: '1px solid #6239eb',
    fontSize: '1.5rem',
    fontWeight: 400,
    lineHeight: 1.5,
    '&:focus': {
      borderRadius: 0,
    },
  },
});

const defaultProps = {
  value: {
    id: '',
    name: '',
    startDate: '',
    endDate: '',
    description: '',
    status: 'Draft',
    conditions: [],
  },
  mode: 'create',
};

const paymentRuleMetadata: RubyObject = {
  apiName: 'PaymentRule',
  name: 'Payment Rule',
  fields: [
    {
      name: 'Id',
      type: 'String',
      apiName: 'id',
    },
    {
      name: 'Name',
      apiName: 'name',
      type: 'text',
      required: true,
      updatable: true,
      creatable: true,
      xs: 6,
    },
    {
      name: 'Status',
      apiName: 'status',
      type: 'pickList',
      required: true,
      creatable: true,
      updatable: true,
      valueSet: {
        defaultValue: 'Draft',
        valuePairs: [
          { apiName: 'Draft', name: 'Draft', orderNumber: 1 },
          { apiName: 'Active', name: 'Active', orderNumber: 2 },
        ],
      },
      xs: 6,
    },
    {
      name: 'Start Date',
      apiName: 'startDate',
      type: 'Date',
      required: true,
      updatable: true,
      creatable: true,
      xs: 6,
      clearable: true,
    },
    {
      name: 'End Date',
      apiName: 'endDate',
      type: 'Date',
      required: false,
      updatable: true,
      creatable: true,
      xs: 6,
      clearable: true,
    },
    {
      name: 'Description',
      apiName: 'description',
      type: 'longText',
      required: false,
      updatable: true,
      creatable: true,
    },
    {
      name: 'Conditions',
      apiName: 'conditions',
      type: 'conditions',
      updatable: true,
      creatable: true,
    },
  ],
};

interface Condition {
  property: string;
  value: string;
}

interface AdvancedFilter {
  id: string;
  name: string;
  isApplied: boolean;
  conditions: Condition[];
}

interface FilterForUi {
  simple: any[];
  advanced: AdvancedFilter[];
}

interface Values {
  customer: {
    filter: string;
    filterForUi: FilterForUi;
  };
}

const validationSchema = yup.object().shape({
  name: yup.string().required(),
  status: yup.string().required(),
  startDate: yup.string().required(),
  description: yup.string().nullable(),
});

export const EmptyGroupingAttributeConditions: React.FC<EmptyPaymentRuleConditionsProps> = (
  props,
) => {
  const classes = useStyles();
  const { initPaymentRuleConditions } = props;
  return (
    <Grid
      className={classes.emptyContainer}
      container
      direction="column"
      justifyContent="center"
      alignItems="center"
    >
      <Grid direction="row" container justifyContent="center" alignItems="center">
        <img alt="image" src={ConditionsEmpty} className={classes.emptyImg} />
      </Grid>
      <Grid item>
        <Typography className={classes.addConditions} onClick={() => initPaymentRuleConditions()}>
          Add Conditions
        </Typography>
      </Grid>
    </Grid>
  );
};

export const PaymentRuleFilter = (props: PaymentRuleFilterProps) => {
  const { open, onClose, condition, handleChange, conditionArray, field, hiddenColumns } = props;
  const classes = useStyles();
  const [label, setLabel] = useState(condition.name);
  const notAllowedApiNames = new Set([]);
  useEffect(() => {
    if (!open) {
      setLabel('');
    } else {
      setLabel(condition.name);
    }
  }, [open]);

  const collectionsCtx = useContext(CollectionsContext);
  let invoiceMetadata: RubyObject;
  let specifyRelatedObjects;
  if (collectionsCtx) {
    //@ts-ignore
    invoiceMetadata = _.cloneDeep(
      collectionsCtx.metadataObjects.find(
        (metadataObject: any) => metadataObject.apiName === 'Invoice',
      ),
    );
    if (hiddenColumns && hiddenColumns.length > 0) {
      invoiceMetadata.fields = invoiceMetadata?.fields?.filter((f) => !hiddenColumns.includes(f.apiName));
    }
    
    // invoiceMetadata.fields = invoiceMetadata?.fields?.filter((field) =>
    //   allowedApiNames.has(field.apiName),
    // );
    specifyRelatedObjects = collectionsCtx.getSpecifyRelatedObjects;
  }

  const [isSubmitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const { Snackbar, showSnackbar } = useRubySnackbar();

  const [referencedCustomerFilter, setReferencedCustomerFilter] = useState<RubyFilter | null>(null);
  const [customerSavedFilters, setCustomerSavedFilters] = useState<Array<RubyFilter>>([
    {
      name: condition.name,
      id: condition.id || shortUUID().generate().toString() + '_temp',
      conditions: condition.filters || [],
      isApplied: true,
    },
  ]);
  const [isEditing, setIsEditing] = useState(false);

  const [customerFilter, setCustomerFilter] = useState({
    simple: [],
    advanced: [],
  });
  const customerInvokeObject: InvokeObject = {
    validateAllConditions: () => {},
    graphqlGenerator: () => {},
  };
  const [temporaryCustomerFilter, setTemporaryCustomerFilter] = useState();
  return (
    <DialogComponent
      open={open}
      handleClose={onClose}
      width="md"
      //@ts-ignore
      title={' '}
      loading={open && loading}
      loadingText="Retrieving..."
    >
      <Grid item xs={12} lg={12}>
        {isEditing ? (
          <ClickAwayListener onClickAway={() => setIsEditing(false)}>
            <TextField
              value={label}
              inputProps={{
                style: {
                  fontSize: '1.5rem',
                  fontWeight: 400,
                  lineHeight: 1.5,
                },
                maxLength: 60,
              }}
              autoFocus={true}
              onChange={(e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
                setLabel(e.target.value);
              }}
              maxRows={5}
              placeholder="Filter Name"
            />
          </ClickAwayListener>
        ) : (
          <Grid
            style={{
              display: 'flex',
              padding: '6px 0 7px',
              fontSize: '1.5rem',
              fontWeight: 400,
            }}
          >
            <span style={{ color: label ? 'black' : 'grey' }}>{label || 'Filter Name'}</span>
            <span
              onClick={() => {
                if (field.creatable) {
                  setIsEditing(true);
                }
              }}
              style={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                marginLeft: '10px',
              }}
            >
              <EditIcon />
            </span>
          </Grid>
        )}
        <FilterBuilder
          objectMetadata={invoiceMetadata!}
          allMetadatas={collectionsCtx?.metadataObjects}
          referencedFilter={referencedCustomerFilter}
          filterName={''}
          savedFilters={customerSavedFilters}
          searchResult=""
          hiddenButton={true}
          graphqlGenerator={true}
          setGraphqlGeneratorResult={setCustomerFilter}
          disabled={!field.creatable}
          setTemporaryFilter={setTemporaryCustomerFilter}
          temporaryFilter={temporaryCustomerFilter}
          invokeObject={customerInvokeObject}
          hideNestedCondition={true}
          specifyRelatedObjects={specifyRelatedObjects}
        />
      </Grid>
      <Grid item xs={12}>
        <RubyButtonBar
          variant="inPlace"
          processing={isSubmitting}
          leftButtons={[
            {
              text: 'Cancel',
              onClick: onClose,
            },
          ]}
          rightButtons={[
            {
              text: 'Save and Close',
              disabled: isSubmitting,
              onClick: async () => {
                if (!label) {
                  showSnackbar('error', 'Error', 'The filter name cannot be empty.');
                  setIsEditing(true);
                  return;
                }
                const customerConditions = customerInvokeObject.validateAllConditions();
                if (customerConditions?.length === 0) {
                  showSnackbar('error', 'Error', 'The filter conditions cannot be empty.');
                  return;
                }
                setSubmitting(true);
                condition.name = label;
                const graphql = customerInvokeObject.graphqlGenerator();
                condition.filters = customerConditions;
                try {
                  handleChange([...conditionArray]);
                } catch (e) {
                } finally {
                  setSubmitting(false);
                  onClose();
                }
              },
            },
          ]}
        />
      </Grid>
      <Snackbar />
    </DialogComponent>
  );
};

export const PaymentRuleConditionItemPage: React.FC<PaymentRuleConditionItemProps> = (props) => {
  const { condition, conditionArray, index, onChange, field, options } = props;
  const [open, setOpen] = useState(false);
  const { name, filters, action, externalSystemType, externalSystemId } = condition;
  const optionValue =
    (action || '') + '|' + (externalSystemType || '') + '|' + (externalSystemId || '');
  const classes = useStyles();
  const [isEditing, setIsEditing] = useState(false);
  const isFirstCondition = index === 0;
  const isLastCondition = index === conditionArray.length - 1;
  const isMiddleCondition = index !== 0 && index !== conditionArray.length - 1;
  const getConditionLabel = () => {
    if (isFirstCondition) {
      return ['if', 'then'];
    }
    if (isMiddleCondition) {
      return ['else if', 'then'];
    }
    if (isLastCondition) {
      return ['otherwise'];
    }
    return ['else if', 'then'];
  };
  const addNewCondition = () => {
    if (field.creatable) {
      conditionArray.splice(index + 1, 0, {
        name: '',
        filters: [],
        id: shortUUID().generate().toString() + '_temp',
        action: 'DoNothing',
        externalSystemType: '',
        externalSystemId: '',
      });
      onChange([...conditionArray]);
    }
  };

  const deleteCondition = () => {
    if (field.creatable) {
      if (index === 0) {
        onChange([]);
        return;
      }
      conditionArray.splice(index, 1);
      if (conditionArray.length === 1) {
        onChange([]);
      } else {
        onChange([...conditionArray]);
      }
    }
  };

  const openFiltersPage = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  return (
    <div
      style={{
        position: 'relative',
      }}
    >
      <Grid className={classes.conditionItemWrapper}>
        <PaymentRuleFilter
          open={open}
          onClose={onClose}
          condition={condition}
          handleChange={onChange}
          conditionArray={conditionArray}
          field={field}
          hiddenColumns={['source']}
        />
        <Grid className={classes.statusBar} />
        <Grid container className={classes.conditionItemContainer}>
          <Grid className={classes.conditionLabel}>
            <Typography className={classes.conditionLabelName}>{getConditionLabel()[0]}</Typography>
          </Grid>
          <Grid className={classes.flex}>
            {name === 'default' ? (
              <PickList
                value={optionValue}
                label=" "
                readOnly={!field.creatable}
                field={{
                  apiName: 'externalSystemId',
                  type: 'text',
                  name: 'External System',
                }}
                disableLabel
                options={options}
                handleInputChange={(sValue: string) => {
                  const [action, externalSystemType, externalSystemId] = sValue.split('|');
                  condition.action = action;
                  condition.externalSystemType = externalSystemType;
                  condition.externalSystemId = externalSystemId;
                  onChange([...conditionArray]);
                }}
              />
            ) : (
              <Typography className={classes.conditionName}>
                {name || '< Please add filters >'}
              </Typography>
            )}
          </Grid>
          {index !== conditionArray.length - 1 && (
            <Grid
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
              }}
            >
              <Chip
                variant="outlined"
                size="small"
                avatar={<FilterIcon />}
                label="Filters"
                style={{
                  order: 2,
                  fontSize: '.75rem',
                  fontWeight: 'bold',
                  padding: '0 8px',
                  marginTop: '-4px',
                  marginLeft: '5px',
                }}
                onClick={openFiltersPage}
              />
            </Grid>
          )}
        </Grid>
        {getConditionLabel().length > 1 && (
          <Grid container className={classes.conditionItemContainer} style={{ marginTop: '10px' }}>
            <Grid className={classes.conditionLabel}>
              <Typography className={classes.conditionLabelName}>
                {getConditionLabel()[1]}
              </Typography>
            </Grid>
            <Grid className={classes.flex}>
              <PickList
                value={optionValue}
                label=" "
                readOnly={!field.creatable}
                field={{
                  apiName: 'externalSystemId',
                  type: 'text',
                  name: 'External System',
                }}
                disableLabel
                options={options}
                handleInputChange={(sValue: string) => {
                  const [action, externalSystemType, externalSystemId] = sValue.split('|');
                  condition.action = action;
                  condition.externalSystemType = externalSystemType;
                  condition.externalSystemId = externalSystemId;
                  onChange([...conditionArray]);
                }}
              />
            </Grid>
            <Grid></Grid>
          </Grid>
        )}
      </Grid>
      {getConditionLabel().length > 1 && field.creatable && (
        <HighlightOffIcon className={classes.deleteConditionItem} onClick={deleteCondition} />
      )}
      {!isLastCondition && field.creatable && (
        <Grid className={classes.addNewConditionContainer}>
          <AddCircleOutlineIcon
            style={{
              color: '#6239eb',
              cursor: 'pointer',
            }}
            onClick={addNewCondition}
          />
        </Grid>
      )}
    </div>
  );
};

export const PaymentRuleConditionsControl: React.FC<CustomFormControlProps> = (props) => {
  const { field, formContext, mode } = props;

  const { Snackbar, showSnackbar } = useRubySnackbar();
  const [loading, setLoading] = useState<boolean>(true);
  const [options, setOptions] = useState<PickListOption[]>([]);
  const collectionsContext = useContext(CollectionsContext);

  useEffect(() => {
    setUp();
  }, []);

  const setUp = async () => {
    try {
      if (!collectionsContext) return;
      const invoiceActionTargetList = await collectionsContext.getInvoiceActionTargetList();
      const transformedOptions = invoiceActionTargetList.map((item) => ({
        name: item.externalSystemName || '',
        value:
          (item.action || '') +
          '|' +
          (item.externalSystemType || '') +
          '|' +
          (item.externalSystemId || ''),
      }));
      setOptions(transformedOptions);
    } catch (error) {
      console.error('Failed to fetch invoice action target list:', error);
    } finally {
      setLoading(false);
    }
  };
  if (loading) {
    return null;
  }
  return (
    <Controller
      name={field.apiName}
      render={({ value, onChange }) => {
        const label = field.name;
        const handleInitConditions = () => {
          if (field.creatable && value.length === 0) {
            onChange([
              {
                id: shortUUID().generate().toString() + '_temp',
                name: '',
                filters: '',
                action: 'DoNothing',
                externalSystemType: '',
                externalSystemId: '',
              },
              {
                id: shortUUID().generate().toString() + '_temp',
                name: 'default',
                filters: '',
                action: 'DoNothing',
                externalSystemType: '',
                externalSystemId: '',
              },
            ]);
          }
        };

        useEffect(() => {
          handleInitConditions();
        }, []);
        return (
          <FormControl style={{ width: '100%' }}>
            <Snackbar />
            <LabelWithTooltip
              label={label}
              required={field.required}
              shrink
              showTooltip={field.showTooltip}
              tooltipText={field.toolTipText}
              htmlFor={field.apiName}
            />
            {value.length > 0 ? (
              value.map((v: PaymentRuleConditionItem, index: number) => (
                <PaymentRuleConditionItemPage
                  condition={v}
                  conditionArray={value}
                  key={v.id}
                  index={index}
                  onChange={onChange}
                  field={field}
                  options={options}
                />
              ))
            ) : (
              <EmptyGroupingAttributeConditions
                initPaymentRuleConditions={() => {
                  if (field.creatable) {
                    onChange([
                      {
                        id: shortUUID().generate().toString() + '_temp',
                        name: '',
                        filters: '',
                        action: 'DoNothing',
                        externalSystemType: '',
                        externalSystemId: '',
                      },
                      {
                        id: shortUUID().generate().toString() + '_temp',
                        name: 'default',
                        filters: '',
                        action: 'DoNothing',
                        externalSystemType: '',
                        externalSystemId: '',
                      },
                    ]);
                  }
                }}
              />
            )}
          </FormControl>
        );
      }}
    />
  );
};

export const PaymentRuleForm: React.FC<PaymentRuleProps> = (userProps) => {
  const { onSubmit, mode, values, onCancel } = {
    ...defaultProps,
    ...userProps,
  };
  if (!values.conditions) {
    values.conditions = [];
  }
  const resolver = useYupValidationResolver(validationSchema);
  const methods = useForm<PaymentRule>({
    defaultValues: values,
    //@ts-ignore
    resolver,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const classes = useStyles();
  if (mode === 'view') {
    paymentRuleMetadata?.fields?.forEach((field) => {
      if (field.hasOwnProperty('creatable')) {
        field.creatable = false;
      }
    });
  } else {
    paymentRuleMetadata?.fields?.forEach((field) => {
      if (field.hasOwnProperty('creatable')) {
        field.creatable = true;
      }
    });
  }
  CustomFieldControlRegistry.register('conditions', PaymentRuleConditionsControl);
  return (
    <FormProvider {...methods}>
      <form>
        <Grid item xs={12}>
          <Typography component="h3" className={classes.sectionTitle}>
            General
          </Typography>
        </Grid>
        <FormSection
          {...(mode === 'create' || mode === 'edit' ? { mode: mode as 'create' | 'edit' } : {})}
          fields={paymentRuleMetadata?.fields}
          values={methods.getValues()}
          hiddenFields={['id']}
          showDivider={false}
          setHiddenFields={() => {}}
        />
        <br />
        <RubyButtonBar
          variant={'inPlace'}
          processing={isSubmitting}
          leftButtons={[
            {
              text: 'Cancel',
              onClick: onCancel,
            },
          ]}
          rightButtons={
            mode === 'view'
              ? []
              : [
                  {
                    text: 'Save',
                    onClick: async () => {
                      setIsSubmitting(true);
                      try {
                        const isValid = await methods.trigger();
                        if (!isValid) {
                          setIsSubmitting(false);
                          return;
                        }
                        await onSubmit(methods.getValues());
                      } catch (e) {
                        setIsSubmitting(false);
                      }
                    },
                  },
                ]
          }
        />
      </form>
    </FormProvider>
  );
};

export default PaymentRuleForm;

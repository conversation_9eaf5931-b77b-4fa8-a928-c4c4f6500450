import React from 'react';

import Tooltip from '@material-ui/core/Tooltip';
import InfoIcon from '@material-ui/icons/Info';

import { Props } from './interface';

const HelpIcon: React.FC<Props> = ({
  toolTipText,
  toolTipPlacement,
  style,
}: React.PropsWithChildren<Props>) => {
  return (
    <Tooltip role="tooltip" title={toolTipText} arrow placement={toolTipPlacement}>
      <InfoIcon style={style || { fontSize: '1rem', color: '#000000', opacity: '0.2' }} />
    </Tooltip>
  );
};

export default HelpIcon;

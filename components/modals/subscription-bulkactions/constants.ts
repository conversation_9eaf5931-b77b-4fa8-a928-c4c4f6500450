import dayjs from 'dayjs';
import { SelectedActionItem } from './types';
import { defaultDateFormat } from '../../customer-view/constant';

export const initialState: Record<string, SelectedActionItem> = {
  UPGRADE: {
    toProduct: { id: '', name: '' },
    withUom: '',
    fromProduct: {
      key: '',
      subscriptionId: '',
      subscriptionName: '',
      subscriptionUOM: '',
      fromProductId: '',
      fromProductName: '',
    },
    startDate: dayjs().format(defaultDateFormat),
  },
  DOWNGRADE: {
    toProduct: { id: '', name: '' },
    withUom: '',
    fromProduct: {
      key: '',
      subscriptionId: '',
      subscriptionName: '',
      subscriptionUOM: '',
      fromProductId: '',
      fromProductName: '',
    },
    startDate: dayjs().format(defaultDateFormat),
  },
  SWAP: {
    toProduct: { id: '', name: '' },
    withUom: '',
    fromProduct: {
      key: '',
      subscriptionId: '',
      subscriptionName: '',
      subscriptionUOM: '',
      fromProductId: '',
      fromProductName: '',
    },
    startDate: dayjs().format(defaultDateFormat),
    samePriceSwap: true,
  },
};

import React, { SyntheticEvent, useEffect, useRef, useState } from 'react';

import {
  Button,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  TextField,
  Typography,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';

import backgroundGraphic from '../../static/images/ask-nue-bg.svg';
import { AskNueGradientIcon, CleanupIcon, CloseMenuIcon, SendIcon } from '../icons';
import PromptResponse from '../prompt-response/prompt-response';
import type { Props } from './interface';

const useStyles = makeStyles({
  container: {
    backgroundColor: '#fff',
    height: 'calc(100vh - 112px)',
    width: '648px',
    position: 'fixed',
    bottom: '0px',
    display: 'flex',
    flexDirection: 'column',
    borderRadius: '8px',
    right: '20px',
  },
  formContainer: {
    display: 'flex',
    marginTop: 'auto',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: 'inset 0px 1px 0px 0px',
    color: 'rgba(229,229,229,1)',
  },
  inputField: {
    display: 'flex',
    flexDirection: 'column',
    margin: '15px auto',
    minHeight: '40px',
    width: '570px',
    borderRadius: '30px',
    border: '2px solid #6239EB',
    wordWrap: 'break-word',
    whiteSpace: 'normal',
    overflowWrap: 'break-word',
    wordBreak: 'break-word',
  },
  disabledFieldset: {
    [`& fieldset`]: {
      display: 'none',
    },
  },
  responsesContainer: {
    display: 'flex',
    flexDirection: 'column',
    overflowY: 'hidden',
    paddingBottom: '20px',
    scrollbarGutter: 'stable',
    '&:hover': {
      overflowY: 'overlay',
    },
  },
  panelHeader: {
    display: 'flex',
    backgroundImage: `url(${backgroundGraphic})`,
    height: '90px',
    minHeight: '90px',
    width: '100%',
    borderRadius: '8px 8px 0px 0px',
    boxShadow: '0px 4px 10px 0px rgba(0,0,0,0.22)',
    alignItems: 'center',
    zIndex: 1,
  },
  askNueIcon: {
    display: 'inline-block',
    marginLeft: '40px',
    fill: 'url(#linearGradient-1)',
    verticalAlign: 'baseline',
  },
  title: {
    display: 'inline-block',
    fontSize: '1.25rem',
    fontWeight: 'bold',
    marginLeft: '16px',
  },
  clearConvoButton: {
    marginLeft: '187px',
    borderRadius: '19.5px',
    backgroundColor: '#fff',
    width: '200px',
    height: '40px',
    fontSize: '.875rem',
    textTransform: 'none',
  },
  closeButton: {
    display: 'inline-block',
    marginLeft: '30px',
  },

  textFieldNueIcon: {
    alignSelf: 'flex-start',
    marginTop: '10px',
  },
  sendButton: {
    alignSelf: 'flex-end',
    marginBottom: '10px',
  },
});

export const AskNue: React.FC<Props> = (userProps) => {
  const props = { ...userProps };

  const {
    projectKey,
    profilePicUrl,
    provider,
    model = 'gpt-3.5-turbo',
    completionsUrl = 'https://api.markprompt.com/v1/completions',
    iDontKnowMessage = 'Sorry, I am not sure how to answer that.',
    docsSiteUrl = 'https://docs.nue.io/docs/',
    onClose,
  } = props;
  const [prompt, setPrompt] = useState<string>('');
  const [promptArray, setPromptArray] = useState<string[]>([]);
  const [responseArray, setResponseArray] = useState<string[]>([]);
  const [mendableConvoId, setMendableConvoId] = useState<number>(0);
  const classes = useStyles();
  const containerRef = useRef<HTMLDivElement>(null);

  const clearConversation = () => {
    setPromptArray([]);
    setResponseArray([]);
  };

  useEffect(() => {
    if (provider !== 'mendable') {
      return;
    }
    const createMendableConversation = async () => {
      const res = await fetch('https://api.mendable.ai/v0/newConversation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          api_key: projectKey,
        }),
      });
      const id = await res.json();
      setMendableConvoId(id.conversation_id);
    };

    createMendableConversation();
  }, []);

  const addResponseHistory = (response: string) => {
    setResponseArray([...responseArray, response]);
  };

  const zipPromptResponse = () => {
    return responseArray.map((response, index) => {
      return { prompt: promptArray[index], response: response };
    });
  };

  const handleSubmit = (event: SyntheticEvent<EventTarget>) => {
    event.preventDefault();
    if (!prompt) {
      return;
    }
    setPromptArray([...promptArray, prompt]);
    setPrompt('');
  };

  const scrollToBottom = () => {
    const containerNode = containerRef.current;
    if (!containerNode) {
      return;
    }

    containerNode.scrollTop = containerNode.scrollHeight;
  };

  return (
    <Paper className={classes.container}>
      <Paper className={classes.panelHeader} elevation={3}>
        <AskNueGradientIcon className={classes.askNueIcon} />
        <Typography className={classes.title} component="h1">
          Ask Nue
        </Typography>
        <Button
          variant={'contained'}
          className={classes.clearConvoButton}
          startIcon={<CleanupIcon style={{ height: '24px', width: '24px' }} />}
          onClick={clearConversation}
        >
          Clear Conversation
        </Button>
        <IconButton className={classes.closeButton} onClick={onClose}>
          <CloseMenuIcon style={{ height: '20px', width: '20px' }} />
        </IconButton>
      </Paper>
      <div className={classes.responsesContainer} ref={containerRef}>
        {promptArray.map((p, index) => (
          <PromptResponse
            key={index}
            prompt={p}
            provider={provider}
            projectKey={projectKey}
            profilePicUrl={profilePicUrl}
            scrollCallback={scrollToBottom}
            model={model}
            completionsUrl={completionsUrl}
            iDontKnowMessage={iDontKnowMessage}
            docsSiteUrl={docsSiteUrl}
            mendableConvoId={mendableConvoId}
            history={zipPromptResponse()}
            addResponseHistory={addResponseHistory}
          />
        ))}
      </div>
      <form onSubmit={handleSubmit} className={classes.formContainer}>
        <Grid container className={classes.inputField}>
          <TextField
            variant="outlined"
            placeholder="Ask Nue!"
            onChange={(e) => setPrompt(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSubmit(e);
              }
            }}
            value={prompt}
            autoFocus
            multiline
            maxRows={10}
            className={classes.disabledFieldset}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" className={classes.textFieldNueIcon}>
                  <AskNueGradientIcon
                    style={prompt ? { fill: 'url(#linearGradient-1)' } : { fill: '#d6d6d6' }}
                  />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end" className={classes.sendButton}>
                  <IconButton onClick={handleSubmit} disabled={!prompt}>
                    <SendIcon
                      style={{
                        height: '24px',
                        width: '24px',
                        fill: prompt ? '#6239eb' : '#d6d6d6',
                      }}
                    />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Grid>
      </form>
    </Paper>
  );
};

export default AskNue;

import React from 'react';
import NumberFormat from 'react-number-format';

import FormControl from '@material-ui/core/FormControl';

import InputBaseComponent from '../input-base-component';
import LabelWithTooltip from '../label-with-tooltip';
import type { Props } from './interface';

const CURRENCY_PRECISION = 2;

export const NumberFormatCustom = (props: any) => {
  const { inputRef, onChange, ...other } = props;
  return (
    <NumberFormat
      {...other}
      getInputRef={inputRef}
      isAllowed={(values) => {
        const { formattedValue, floatValue } = values;
        return formattedValue === '' || formattedValue === '-' || floatValue! <= 2147483647;
      }}
      onValueChange={(values) => {
        onChange(values.value);
      }}
      decimalScale={CURRENCY_PRECISION}
      allowNegative={true}
    />
  );
};

const DecimalInput: React.FC<Props> = (props) => {
  const {
    value,
    label,
    field,
    handleInputChange,
    disabled,
    className,
    readOnly,
    name,
    currencyIsoCode,
    disableLabel,
    required,
    ...other
  } = props;

  const showTooltip = props.showTooltip || !!field?.toolTipText;
  const toolTipText = props.toolTipText || field?.toolTipText || '';
  return (
    <FormControl style={{ width: '100%' }}>
      {!disableLabel && (
        <LabelWithTooltip
          label={label || ''}
          required={required}
          shrink
          showTooltip={showTooltip}
          tooltipText={toolTipText}
          htmlFor={name || field.apiName}
        />
      )}
      <InputBaseComponent
        {...other}
        placeholder={label}
        value={value === null || value === undefined ? '' : value}
        name={field ? field.apiName : name}
        inputComponent={NumberFormatCustom}
        required={field ? field.required : false}
        onChange={handleInputChange}
        fullWidth={true}
        disabled={disabled}
        readOnly={readOnly}
      />
    </FormControl>
  );
};

export default DecimalInput;

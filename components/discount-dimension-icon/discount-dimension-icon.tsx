import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { Props } from './interface';

const DiscountDimensionIcon: React.FC<Props> = ({ viewBox, width, height }) => {
  return (
    <SvgIcon viewBox={viewBox} style={{height: '40px', width: '48px'}}>
      <svg
        height='40px' 
        width='40px'
        viewBox="0 0 40 40"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>2F567895-D971-4E25-9743-BB151869E163</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="6-1-6-3-Edit-Lines-(Price-Tags)"
            transform="translate(-609.000000, -322.000000)"
            fill="#6239EB"
          >
            <g id="Group-15" transform="translate(531.000000, 77.000000)">
              <g id="Group-11" transform="translate(40.000000, 211.000000)">
                <g id="Group-18" transform="translate(38.000000, 34.000000)">
                  <circle id="Oval" fillOpacity="0.1" cx="20" cy="20" r="20" />
                  <g
                    id="shopping-bag"
                    transform="translate(11.000000, 10.000000)"
                    fillRule="nonzero"
                  >
                    <path
                      d="M17.4762109,19.2745703 L15.1862109,5.21117188 C15.1369571,4.90875663 14.8757359,4.68663914 14.5693359,4.68664063 L12.3710937,4.68664063 L12.3710937,3.62109375 C12.3710937,1.62113262 10.7498049,-0.000156196039 8.74984375,-0.000156196039 C6.74988262,-0.000156196039 5.12859378,1.62113262 5.12859375,3.62109375 L5.12859375,4.68648438 L2.93066406,4.68648438 C2.62426413,4.68648289 2.36304285,4.90860038 2.31378906,5.21101563 L0.0237890625,19.2745703 C-0.00566528046,19.4554904 0.0457868898,19.6402203 0.164526813,19.7798644 C0.283266736,19.9195084 0.457322985,20 0.640625,20 L16.859375,20 C17.0426838,20 17.2167509,19.9195248 17.3354996,19.7798792 C17.4542483,19.6402337 17.5057054,19.4554971 17.47625,19.2745703 L17.4762109,19.2745703 Z M6.37890625,3.62109375 C6.37890625,2.31148854 7.44055104,1.24984375 8.75015625,1.24984375 C10.0597615,1.24984375 11.1214062,2.31148854 11.1214062,3.62109375 L11.1214062,4.68648438 L6.37890625,4.68648438 L6.37890625,3.62109375 Z M1.37566406,18.75 L3.46214844,5.93664063 L5.12890625,5.93664063 L5.12890625,6.97570313 C5.12890625,7.32088109 5.40872828,7.60070313 5.75390625,7.60070313 C6.09908422,7.60070313 6.37890625,7.32088109 6.37890625,6.97570313 L6.37890625,5.93664063 L11.1210937,5.93664063 L11.1210937,6.97570313 C11.1210937,7.32088109 11.4009158,7.60070313 11.7460937,7.60070313 C12.0912717,7.60070313 12.3710937,7.32088109 12.3710937,6.97570313 L12.3710937,5.93664063 L14.0376953,5.93664063 L16.1243359,18.75 L1.37566406,18.75 Z M10.8610547,13.1835869 C9.96687864,13.1835869 9.19722466,13.8152392 9.0227798,14.6922339 C8.84833495,15.5692287 9.31768421,16.447323 10.1437939,16.7895125 C10.9699035,17.1317019 11.9226922,16.8426826 12.4194755,16.0992062 C12.9162589,15.3557298 12.8186768,14.3648635 12.1864062,13.7325781 C11.8356691,13.3799247 11.3584269,13.1822424 10.8610547,13.1835869 L10.8610547,13.1835869 Z M11.3024609,15.4993359 C11.1239218,15.6779061 10.8553921,15.7313381 10.622095,15.6347152 C10.388798,15.5380922 10.2366812,15.3104441 10.2366812,15.0579297 C10.2366812,14.8054153 10.388798,14.5777672 10.622095,14.4811442 C10.8553921,14.3845213 11.1239218,14.4379533 11.3024609,14.6165234 C11.546213,14.8603178 11.546213,15.2555416 11.3024609,15.4993359 L11.3024609,15.4993359 Z M7.96421875,12.1610547 C8.59798577,11.5286506 8.69664074,10.536573 8.19982993,9.79174104 C7.70301912,9.04690912 6.74917272,8.75686111 5.92182139,9.09903848 C5.09447006,9.44121585 4.62414503,10.3202751 4.7985751,11.1984378 C4.97300517,12.0766005 5.74358755,12.7091889 6.63890625,12.7091889 C7.13616649,12.7107421 7.61339428,12.5133471 7.96425781,12.1609766 L7.96421875,12.1610547 Z M6.19746094,10.3942969 C6.37600007,10.2157268 6.64452979,10.1622947 6.87782684,10.2589177 C7.11112389,10.3555406 7.26324067,10.5831888 7.26324067,10.8357031 C7.26324067,11.0882175 7.11112389,11.3158656 6.87782684,11.4124886 C6.64452979,11.5091115 6.37600007,11.4556795 6.19746094,11.2771094 C6.07988703,11.1602919 6.01378131,11.0013966 6.01378131,10.8356559 C6.01378131,10.6699153 6.07991575,10.5110258 6.1975,10.3942188 L6.19746094,10.3942969 Z M11.8932422,10.6872656 L6.490625,16.0898438 C6.33306057,16.2493331 6.10214121,16.3122105 5.8854685,16.254623 C5.66879579,16.1970354 5.49956617,16.0278058 5.4419786,15.8111331 C5.38439103,15.5944604 5.4472685,15.363541 5.60675781,15.2059766 L11.0094141,9.8034375 C11.1673366,9.64554983 11.3974951,9.58390629 11.6131912,9.64172741 C11.8288872,9.69954852 11.9973513,9.86804988 12.0551247,10.0837587 C12.1128982,10.2994674 12.0512038,10.5296123 11.8932812,10.6875 L11.8932422,10.6872656 Z"
                      id="Shape"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default DiscountDimensionIcon;

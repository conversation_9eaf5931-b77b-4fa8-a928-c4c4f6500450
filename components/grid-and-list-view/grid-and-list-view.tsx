import { makeStyles } from '@material-ui/core/styles';
import _ from 'lodash';
import React, { useContext, useEffect, useState } from 'react';
import { processQueryForIndividualTabMode } from '../customer-view/customer-view-helper';
import DialogComponent from '../dialog-component';
import type { GeneralInformationAccount } from '../general-information-summary-view/interface';
import GraphQLQueryConstructor from '../graph-ql-query-constructor';
import type { OrderByField, RubyFilter } from '../graph-ql-query-constructor/interface';
import GridSearchBar from '../grid-search-bar';
import GridView from '../grid-view';
import LoadingScreen from '../loading-screen/loading-screen';
import type { SortField } from '../multi-sort-builder';
import PanelWithTabs from '../panel-with-tabs';
import RubyListViewContainer from '../ruby-list-view-container';
import type { ListViewLocalState } from '../ruby-list-view-local-state-context';
import RubyListViewLocalStateContext from '../ruby-list-view-local-state-context';
import {
  buildExtraRelationFields,
  buildRubyListColumns,
} from '../ruby-list-view/ruby-list-view-util';
import UpdateChangeHistoryCard from '../subscription-card/update-change-history-card';
import { TabViewPanel } from '../tab-view-panel/tab-view-panel';
import ToggleListGridView from '../toggle-list-grid-view';
import type { Props } from './interface';

const defaultProps = {};

const defaultSearchConfigs = {
  objectMetadata: {
    apiName: 'unknown',
    name: 'Unknown',
    customizable: false,
    objectType: '',
  },
  predefinedFilters: [],
  placeholder: '',
  saveFilter: async () => {
    throw new Error('saveFilter is not defined');
  },
  listFilters: async () => {
    throw new Error('listFilters is not defined');
  },
  deleteFilter: async (objectApiName: string, filterId: string) => {
    throw new Error('listFilters is not defined');
  },
  updateFilters: async () => {
    throw new Error('updateFilters is not defined');
  },
  executeQuery: async (query: string) => {
    throw new Error('executeQuery is not defined');
  },
  executeGetCountQuery: async (query: string) => {
    throw new Error('executeGetCountQuery is not defined');
  },
};

const useStyles = makeStyles({
  dividerStyle: {
    opacity: 0.1,
    border: '0.1px solid #727171',
    margin: '-12px -32px 32px',
  },
  fieldLabel: {
    opacity: '0.4',
    color: '#000000',
    fontSize: '12px',
    fontWeight: 'bold',
    letterSpacing: 0,
    lineHeight: '15px',
    marginRight: '22px',
    textTransform: 'uppercase',
    whiteSpace: 'nowrap',
    marginLeft: 40,
  },
  amountLabel: {
    fontSize: 22,
    color: '#6239EB',
    letterSpacing: '0.69px',
    fontWeight: 700,
  },
});

const gridListViewTabs = { 'Grid View': 0, 'List View': 1 };

const GridAndListView: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const {
    tabFilters,
    executeQuery,
    executeGetCountQuery,
    objectMetadata,
    handleUpdateObjects,
    activeTabIndex,
    handleUpdateActiveTabIndex,
    searchConfigs,
    objects,
    gridViewMetadata,
    fieldSetMetadata,
    currencyIsoCode,
    GridViewItem,
    RightOfTitleSlot,
    handleCardClick,
    orderFieldSetMetadata,
    subscriptionPricingFieldSet,
    orderGridViewMetadata,
    subscriptionMetadata,
    subscriptionDefaultFilter,
    actionEventHandler,
    refreshDataFlag = 0,
    getFieldSetMetadata,
    listViewMetadata,
    loading = false,
    onDeleteSelectedSubscription,
    activeTabDetail,
    getInvoiceIdsForAsset,
    customer,
    assetMetadata,
    entitlementMetadata,
    orderMetadata,
    rubySettings,
    scrollButtons,
    locale,
    selectedCreditPoolId,
    selectedObject,
    setSelectedObject,
    shouldDisableRowSelection,
    disableSelectAll,
    currentAccountIdANdChildrenAccountIds,
    loadChildernAndBillingAccounts,
    loadChildernAndBillingAccountsByIds,
    getTransactionHubStatus,
    transactionHubColumn,
    getSwapUpgradeDowngradeOptions,
  } = props;
  const classes = useStyles();

  const [gridListViewTabIndex, setGridListViewTabIndex] = useState<number>(0);
  const [sortingList, setSortingList] = useState<SortField[]>([]);
  const localeStateContext = useContext(RubyListViewLocalStateContext);
  const [searchResultTitle, setSearchResultTitle] = useState<string>('');
  const [existingFilterConditions, setExistingFilterConditions] = useState<RubyFilter[]>([]);
  const [orderByIncludeRelationObj, setOrderByIncludeRelationObj] = useState(false);
  const [extraFilterConditions, setExtraFilterConditions] = useState<RubyFilter[]>([]);
  const [totalCount, setTotalCount] = useState<number | undefined>();
  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState<number>(20);
  const [filterToDelete, setFilterToDelete] = useState<string>('');

  useEffect(() => {
    setExistingFilterConditions(
      tabFilters[activeTabIndex].predefinedFilters?.filter((filter) => filter.isApplied) || [],
    );
    setup();
  }, [listViewMetadata]);

  useEffect(() => {
    console.debug('refreshing grid view data');
    const currentTabFilters = tabFilters[activeTabIndex];
    retainLocalState(currentTabFilters.orderByFilters);
  }, [refreshDataFlag]);

  const setup = async () => {
    const currentTabFilters = tabFilters[activeTabIndex];
    const defaultSorting = (currentTabFilters.orderByFilters || [])?.map((_) => {
      return {
        field: _.columnName,
        order: _.direction.toLowerCase() === 'asc' ? 'ASC' : 'DESC',
      };
    });
    //@ts-ignore
    setSortingList(defaultSorting);
    await retainLocalState(currentTabFilters.orderByFilters);
  };

  const handleSort = async (orderByFields: OrderByField[]) => {
    setSortingList(
      orderByFields.map((_) => {
        return {
          field: _.columnName,
          order: _.direction?.toLowerCase() === 'desc' ? 'DESC' : 'ASC',
        };
      }),
    );
    await updateListViewLocalState({ orderByFields: orderByFields });
    if (existingFilterConditions.length > 0) {
      await handleSearch(
        searchResultTitle,
        existingFilterConditions,
        extraFilterConditions,
        orderByFields,
        {
          newPageNumber: pageNumber,
          newPageSize: pageSize,
        },
      );
    } else {
      await handleSearch(searchResultTitle, [], extraFilterConditions, orderByFields, {
        newPageNumber: pageNumber,
        newPageSize: pageSize,
      });
    }
  };

  const updateListViewLocalState = async (payload: Partial<ListViewLocalState>) => {
    if (localeStateContext) {
      const listViewLocalState = await localeStateContext.getLocalState();
      await localeStateContext.setLocalState({ ...listViewLocalState, ...payload });
    }
  };

  const retainLocalState = async (defaultOrderBy?: OrderByField[]) => {
    if (localeStateContext) {
      const localState = await localeStateContext.getLocalState();
      const orderByFields = localState?.orderByFields;
      const lastSearchedField = localState?.lastSearching || '';
      const definedFilterCondiditions =
        tabFilters[activeTabIndex].predefinedFilters?.filter((filter) => filter.isApplied) || [];
      const filtersToApply = localState?.filtersToApply || definedFilterCondiditions;
      if (orderByFields) {
        setSortingList(
          orderByFields.map((_) => {
            return {
              field: _.columnName,
              order: _.direction.toLowerCase() === 'desc' ? 'DESC' : 'ASC',
            };
          }),
        );
      }

      await handleSearch(
        lastSearchedField,
        filtersToApply,
        extraFilterConditions,
        orderByFields || defaultOrderBy,
      );
    }
  };

  const columns = [...(buildRubyListColumns(objectMetadata, fieldSetMetadata) || [])];

  /**
   * There is a one-off usecase for subscription grid view where we apply default filter when there is no search or filters applied,
   * but if there is a search or filter, we apply an another type of default filter
   * @param searchText
   * @param filters
   * @param objectApiName
   * @param defaultObjectsFilter
   * @returns
   */
  const applyDefaultFilter = (
    searchText: string,
    filters: RubyFilter[],
    objectApiName: string,
    defaultObjectsFilter: RubyFilter[],
  ) => {
    const _filters = [...filters];
    // TODO: find a better way to disable / enable applying default filter. this is a one-off case for subscriptions where if we do any searching,
    // we want to disable the default object filter to show all the subscriptions that fit this criteria
    if (
      ((!_.isNil(searchText) && searchText !== '') ||
        _filters.findIndex((filter: RubyFilter) => filter.name !== 'Object Filter') !== -1) &&
      subscriptionMetadata?.apiName === objectApiName
    ) {
      if (subscriptionDefaultFilter) {
        _filters.push(subscriptionDefaultFilter);
      }
    } else {
      _filters.push(...defaultObjectsFilter);
    }
    return _filters;
  };

  const handleSearch = async (
    searchResult: string,
    filters: RubyFilter[],
    extraFilters: RubyFilter[],
    orderByFields?: OrderByField[],
    pageDetails?: {
      newPageNumber: number;
      newPageSize: number;
    },
  ) => {
    let extraQuery = null;
    let shouldContinue = true;
    if (activeTabDetail && activeTabDetail.tabMode !== 'All' && activeTabDetail.argument?.object) {
      const filterTabName = tabFilters[activeTabIndex].name.toLocaleLowerCase();
      const result = await processQueryForIndividualTabMode(
        filterTabName,
        activeTabDetail,
        executeQuery,
        customer.id,
        getInvoiceIdsForAsset,
        //@ts-ignore
        subscriptionMetadata,
        orderMetadata,
        assetMetadata,
        entitlementMetadata,
      );
      extraQuery = result.extraQuery;
      shouldContinue = result.shouldContinue;
    }

    if (!shouldContinue) {
      handleUpdateObjects([], searchResult);
      return;
    }

    const currentTabFilters = tabFilters[activeTabIndex];
    const defaultOrderByFields = currentTabFilters.orderByFilters;

    let constructedOrderbByFields =
      orderByFields && orderByFields.length > 0 ? orderByFields : defaultOrderByFields || [];
    if (extraQuery && extraQuery.orderByFields && extraQuery.orderByFields.length) {
      constructedOrderbByFields = constructedOrderbByFields.concat(extraQuery.orderByFields);
    }

    if (searchResult !== searchResultTitle) {
      setSearchResultTitle(searchResult);
    }
    const appliedFilters: RubyFilter[] = [];

    if (filters) {
      for (let i = 0; i < filters.length; i++) {
        if (filters[i].isApplied) {
          appliedFilters.push(filters[i]);
        }
      }
    }

    let filtersCopy = appliedFilters;

    if (extraQuery && extraQuery.filtersToApply && extraQuery.filtersToApply.length) {
      filtersCopy = filtersCopy.concat(extraQuery.filtersToApply);
    }

    const defaultObjectsFilterIndex = filtersCopy.findIndex(
      (filter: RubyFilter) => filter.name === 'Object Filter',
    );
    const defaultObjectsFilter = tabFilters[activeTabIndex].filter;
    const joinObjectName = tabFilters[activeTabIndex].joinObjectName;
    const extraRelationFields = tabFilters[activeTabIndex].extraRelationFields;

    const searchFilterIndex = filtersCopy.findIndex((filter) => filter.name === 'Search Filter');
    if (defaultObjectsFilter && defaultObjectsFilterIndex === -1) {
      filtersCopy = applyDefaultFilter(
        searchResult,
        filtersCopy,
        objectMetadata.apiName,
        defaultObjectsFilter,
      );
    }
    if (
      !joinObjectName &&
      searchFilterIndex === -1 &&
      objectMetadata.apiName !== subscriptionMetadata?.apiName
    ) {
      filtersCopy.push({
        name: 'Search Filter',
        conditions: [
          {
            apiName: 'name',
            name: 'Name',
            operand: ' _and ',
            operator: ' _like ',
            type: 'text',
            value: `"${searchResult}"`,
            nestedConditions: [],
            error: '',
          },
        ],
        id: '',
        isExclusive: false,
      });
    }
    //@ts-ignore
    if (
      activeTabDetail &&
      activeTabDetail.tabMode.includes('ServiceMilestone') &&
      tabFilters[activeTabDetail.tabIndex].name === 'Invoices'
    ) {
      const milestoneInvoiceNumber = activeTabDetail.tabMode.slice(
        activeTabDetail.tabMode.indexOf('_') + 1,
        activeTabDetail.tabMode.length,
      );
      extraFilters.push({
        name: 'Milestone Invoice Filter',
        conditions: [
          {
            apiName: 'name',
            name: 'Invoice Number',
            type: 'text',
            value: `"${milestoneInvoiceNumber}"`,
            operator: ' _eq ',
            operand: ' _and ',
            nestedConditions: [],
          },
        ],
        id: 'invoiceNumber',
        isSystemField: true,
        isExclusive: false,
        isApplied: true,
      });
    }

    if (extraFilters && extraFilters.length) {
      filtersCopy = filtersCopy.concat(extraFilters);
    }

    if (
      tabFilters[activeTabIndex].name === 'Credits' &&
      (!extraFilters || !extraFilters.find((x) => x.id === 'creditPool'))
    ) {
      filtersCopy = filtersCopy.concat([
        {
          name: 'Credit pool Filter',
          conditions:
            currentAccountIdANdChildrenAccountIds &&
            currentAccountIdANdChildrenAccountIds.length > 0
              ? [
                  {
                    apiName: 'accountPoolId',
                    name: 'Account Pool Id',
                    type: 'text',
                    value: `"${selectedCreditPoolId ? selectedCreditPoolId : '********-0000-0000-0000-************'}"`,
                    operator: ' _eq ',
                    operand: ' _and ',
                    nestedConditions: [
                      {
                        apiName: 'customerId',
                        name: 'Customer Id',
                        type: 'text',
                        value: `"${customer.id}"`,
                        operator: ' _eq ',
                        operand: ' _and ',
                        nestedConditions: [],
                      },
                      {
                        apiName: 'salesAccountId',
                        name: 'Sales Account Id',
                        type: 'text',
                        value: JSON.stringify(currentAccountIdANdChildrenAccountIds),
                        operator: ' _in ',
                        operand: ' _or ',
                        nestedConditions: [],
                      },
                    ],
                  },
                ]
              : [
                  {
                    apiName: 'accountPoolId',
                    name: 'Account Pool Id',
                    type: 'text',
                    value: `"${selectedCreditPoolId ? selectedCreditPoolId : '********-0000-0000-0000-************'}"`,
                    operator: ' _eq ',
                    operand: ' _and ',
                    nestedConditions: [],
                  },
                  {
                    apiName: 'customerId',
                    name: 'Customer Id',
                    type: 'text',
                    value: `"${customer.id}"`,
                    operator: ' _eq ',
                    operand: ' _or ',
                    nestedConditions: [],
                  },
                ],
          id: 'creditPool',
          isSystemField: true,
          isExclusive: false,
          isApplied: true,
        },
      ]);
    }

    // Remove duplicate filters using the last added
    filtersCopy = filtersCopy.filter((filter: RubyFilter) => {
      const filtersById = filtersCopy.filter((_filter) => filter.id === _filter.id);
      return filter === filtersById[filtersById?.length - 1];
    });

    const filterConditions = GraphQLQueryConstructor.construct().conditionsFromFilters(filtersCopy);
    let orderByConditions = '';

    if (constructedOrderbByFields && constructedOrderbByFields.length > 0) {
      const relationOrderConditions: OrderByField[] = [];
      if (
        //the metadata for usage is coming from NUE api and doesn't contains relations
        objectMetadata.apiName === 'Usage' ||
        (objectMetadata.relations && objectMetadata.relations.length > 0)
      ) {
        if (objectMetadata.fields && objectMetadata.fields.length > 0) {
          const fieldsLength = objectMetadata.fields.length;
          objectMetadata.fields.map((_) => {
            let index = constructedOrderbByFields.findIndex(
              (field) => _.apiName === field.columnName,
            );
            if (index === -1 && _.lookupRelation) {
              const lookupRelation = _.lookupRelation;
              index = constructedOrderbByFields.findIndex(
                (field) => lookupRelation.relationName === field.columnName,
              );
            }
            if (index !== -1 && index < fieldsLength + 1) {
              const indexField = constructedOrderbByFields[index];
              const relation = objectMetadata.fields?.filter((field) => {
                if (field.lookupRelation) {
                  return indexField.columnName === field.lookupRelation.relationName
                    ? indexField.columnName === field.lookupRelation.relationName
                    : indexField.columnName === field.apiName;
                }
              });
              if (relation && relation.length > 0) {
                setOrderByIncludeRelationObj(true);
                const lookupRelation = relation[0].lookupRelation;
                if (lookupRelation) {
                  const relationName = lookupRelation.relationName;
                  relationOrderConditions.push({
                    columnName: relationName,
                    isRelation: true,
                    direction: constructedOrderbByFields[index].direction,
                    referenceField: lookupRelation.referenceField,
                  });
                  constructedOrderbByFields.push({
                    columnName: relationName,
                    isRelation: true,
                    direction: constructedOrderbByFields[index].direction,
                    referenceField: lookupRelation.referenceField,
                  });
                  constructedOrderbByFields.splice(index, 1);
                }
              }
            }
          });
        }
      }
      orderByConditions = GraphQLQueryConstructor.construct().orderByFields(
        constructedOrderbByFields,
        relationOrderConditions,
      );
    }
    let pageCondition = '';

    if (tabFilters[activeTabIndex].enablePagination && tabFilters[activeTabIndex].remotePaging) {
      if (pageDetails === null || pageDetails === undefined) {
        const dPageSize = tabFilters[activeTabIndex].defaultPageSize || 20;
        pageCondition = GraphQLQueryConstructor.construct().pageCondition(0 * dPageSize, dPageSize);
        setPageNumber(0);
      } else {
        pageCondition = GraphQLQueryConstructor.construct().pageCondition(
          pageDetails.newPageNumber * pageDetails.newPageSize,
          pageDetails.newPageSize,
        );
        setPageNumber(pageDetails.newPageNumber);
        setPageSize(pageDetails.newPageSize);
      }
    } else {
      ///RIM-6727 by default give 1000 limit to avoid query all data
      pageCondition = GraphQLQueryConstructor.construct().pageCondition(0, 1000);
    }

    console.debug('filtersCopy: ', filtersCopy);
    const conditions = joinObjectName
      ? GraphQLQueryConstructor.construct().relationWhereConditions(
          joinObjectName,
          `{ name: { _like: "${searchResult}" } } `,
          filterConditions,
          orderByConditions,
          pageCondition,
        )
      : `( where: ${filterConditions} ${orderByConditions} ${pageCondition})`;

    const whereCondition = filtersCopy.length === 0 && !orderByConditions ? '' : conditions;
    const relationFields = buildExtraRelationFields(columns);

    const query = GraphQLQueryConstructor.construct().queryWithWhereCondition(
      objectMetadata,
      whereCondition,
      relationFields,
    );
    console.debug('query constructed: ', query);
    let queriedObjects = await executeQuery(
      query,
      objectMetadata,
      filtersCopy,
      extraRelationFields,
      constructedOrderbByFields,
    );
    if (tabFilters[activeTabIndex].name === 'General Information') {
      await loadBillingAccounts(queriedObjects);
    }
    if (transactionHubColumn && !transactionHubColumn.hidden && getTransactionHubStatus) {
      const queriedObjectsTransactions = await getTransactionHubStatus(
        queriedObjects.map((o) => o.id),
      );
      if (queriedObjectsTransactions.length > 0) {
        queriedObjects = queriedObjects.map((object) => {
          const transactionData = queriedObjectsTransactions.find(
            (o: any) => o.nueId === object.id,
          );
          if (transactionData) {
            const updatedObject = { ...object };
            updatedObject.transactionHub = updatedObject.transactionHub || [];
            updatedObject.transactionHub.push(transactionData);
            return updatedObject;
          }
          return object;
        });
      }
    }
    handleUpdateObjects(queriedObjects || [], searchResult);
    setExistingFilterConditions(appliedFilters);

    if (tabFilters[activeTabIndex].enablePagination && executeGetCountQuery) {
      const countApiKey = `aggregate__${objectMetadata.apiName}`;

      const pageQuery = GraphQLQueryConstructor.construct().queryTotalCount(
        countApiKey,
        whereCondition,
      );
      const count = await executeGetCountQuery(pageQuery, countApiKey);
      setTotalCount(count);
    } else {
      if (tabFilters[activeTabIndex].enablePagination) {
        setTotalCount(queriedObjects.length);
      } else {
        setTotalCount(undefined);
      }
    }
  };

  const getChildAccountIds = (
    accounts: GeneralInformationAccount,
    skipItSelf: boolean,
    level: number,
  ) => {
    let result: string[] = skipItSelf ? [] : [accounts.id];
    let childStructures: any = skipItSelf
      ? []
      : [
          {
            id: accounts.id,
            children: accounts.children?.map((c) => c.id) || [],
            level: level,
          },
        ];
    if (accounts && accounts.children?.length) {
      const newLevel = level + 1;
      accounts.children.forEach((a) => {
        const newResult = getChildAccountIds(a, false, newLevel);
        result = result.concat(newResult.ids);
        childStructures = childStructures.concat(newResult.childStructures);
      });
    }

    return { ids: result, childStructures: childStructures };
  };

  const loadBillingAccounts = async (accountObjects: any[]) => {
    if (loadChildernAndBillingAccounts) {
      const accounts = await loadChildernAndBillingAccounts();
      if (accounts) {
        const accountDetails = getChildAccountIds(accounts, true, 0);
        const childAccountIds = accountDetails?.ids || [];
        const billingAccountIds = accounts?.billingAccounts?.map((x: any) => x.id) || [];

        if (
          (childAccountIds?.length > 0 || billingAccountIds?.length > 0) &&
          loadChildernAndBillingAccountsByIds
        ) {
          //doing query
          const loadResult = await loadChildernAndBillingAccountsByIds(
            childAccountIds.concat(billingAccountIds),
            billingAccountIds,
            childAccountIds,
            accountDetails,
          );
          if (loadResult && loadResult.billingAccountResult?.length > 0) {
            loadResult.billingAccountResult.forEach((b: any) => {
              b.type = 'BillingAccount';
              accountObjects.push(b);
            });
          }
        }
      }
    }
  };

  const handleCurrentPageChange = async (newPage: number) => {
    if (!tabFilters[activeTabIndex].remotePaging) {
      setPageNumber(newPage);
      return;
    }
    if (localeStateContext) {
      const localState = await localeStateContext.getLocalState();

      const orderByFields = localState?.orderByFields;
      const lastSearchedField = localState?.lastSearching || '';
      const filtersToApply = localState?.filtersToApply || [];

      await handleSearch(lastSearchedField, filtersToApply, extraFilterConditions, orderByFields, {
        newPageNumber: newPage,
        newPageSize: pageSize,
      });
    }
  };

  const handlePageSizeChange = async (newPageSize: number) => {
    if (!tabFilters[activeTabIndex].remotePaging) {
      setPageSize(newPageSize);
      return;
    }
    if (localeStateContext) {
      const localState = await localeStateContext.getLocalState();

      const orderByFields = localState?.orderByFields;
      const lastSearchedField = localState?.lastSearching || '';
      const filtersToApply = localState?.filtersToApply || [];

      await handleSearch(lastSearchedField, filtersToApply, extraFilterConditions, orderByFields, {
        newPageNumber: pageNumber,
        newPageSize: newPageSize,
      });
    }
  };

  const handleSearchButtonClicked = async (
    searchResult: string,
    filters: RubyFilter[],
    orderByFields?: OrderByField[],
    updateFields?: string[],
    relationOrderConditions?: OrderByField[],
    stayOnExistingPage?: boolean,
  ) => {
    if (stayOnExistingPage) {
      await handleSearch(searchResult, filters, extraFilterConditions, orderByFields, {
        newPageNumber: pageNumber,
        newPageSize: pageSize,
      });
    } else {
      await handleSearch(searchResult, filters, extraFilterConditions, orderByFields);
    }
  };
  const tabs = tabFilters[activeTabIndex].renderSummaryView
    ? { ...gridListViewTabs, 'Summary View': 2 }
    : gridListViewTabs;

  useEffect(() => {
    if (tabFilters[activeTabIndex].renderSummaryView) {
      setGridListViewTabIndex(2);
    }
  }, [activeTabIndex, tabFilters]);

  return (
    <>
      <PanelWithTabs
        scrollButtons={scrollButtons}
        tabs={tabFilters}
        RightOfTitleSlot={RightOfTitleSlot}
        activeTabIndex={activeTabIndex}
        handleUpdateActiveTabIndex={handleUpdateActiveTabIndex}
        onDeleteFilter={(id: string, key: string) => {
          if (id) {
            if (onDeleteSelectedSubscription) {
              onDeleteSelectedSubscription(key);
            }
            setFilterToDelete(id);
          }
        }}
      >
        <div style={{ paddingBottom: '32px' }}>
          <GridSearchBar
            {...{
              ...defaultSearchConfigs,
              ...searchConfigs,
              executeQuery,
            }}
            completeDeleteFilter={() => {
              setFilterToDelete('');
            }}
            onAppliedFiltersChange={() => {
              if (onDeleteSelectedSubscription) {
                onDeleteSelectedSubscription(tabFilters[activeTabIndex].name.toLocaleLowerCase());
              }
            }}
            filterToDelete={filterToDelete}
            handleSearch={handleSearchButtonClicked}
            placeholder={`Search ${objectMetadata.name}`}
            predefinedFilters={tabFilters[activeTabIndex].predefinedFilters || []}
            objectMetadata={objectMetadata}
            sortingList={sortingList}
            onSortingListChange={(newSortingList: SortField[]) => setSortingList(newSortingList)}
            showFilters={!tabFilters[activeTabIndex].hideFilters}
            showSearchBar={!tabFilters[activeTabIndex].hideSearch}
          />
        </div>
        {tabFilters[activeTabIndex].renderExtraContents &&
          tabFilters[activeTabIndex].renderExtraContents?.(
            objects,
            async (extraFilters: RubyFilter[]) => {
              const localState = await localeStateContext?.getLocalState();
              const orderByFields = localState?.orderByFields;

              setExtraFilterConditions(extraFilters);

              if (existingFilterConditions.length > 0) {
                await handleSearch(
                  searchResultTitle,
                  existingFilterConditions,
                  extraFilters,
                  orderByFields,
                );
              } else {
                await handleSearch(searchResultTitle, [], extraFilters, orderByFields);
              }
            },
          )}
        {tabFilters[activeTabIndex].isListViewOnly ? (
          <>
            {loading && !objects && <LoadingScreen loadingText="Loading..." />}
            {!loading && objects && (
              <RubyListViewContainer
                defaultPage={pageNumber}
                handlePageSizeChange={handlePageSizeChange}
                handleCurrentPageChange={handleCurrentPageChange}
                totalCount={totalCount}
                enablePaging={tabFilters[activeTabIndex].enablePagination}
                remotePaging={tabFilters[activeTabIndex].remotePaging}
                addActionColumn={tabFilters[activeTabIndex].addActionColumn}
                addTransactionHubColumn={tabFilters[activeTabIndex].addTransactionHubColumn}
                objectMetadata={objectMetadata}
                listViewMetadata={listViewMetadata}
                objects={objects}
                sortingList={sortingList}
                handleSort={handleSort}
                multiSelect={tabFilters[activeTabIndex].showListViewMultiSelect}
                batchActions={tabFilters[activeTabIndex].batchActions}
                showBatchActionBar={tabFilters[activeTabIndex].showBatchActionBar}
                handleSelectBatchAction={tabFilters[activeTabIndex].handleSelectBatchAction}
                orderByIncludeRelationObj={orderByIncludeRelationObj}
                currencyIsoCode={currencyIsoCode}
                locale={locale}
                transactionHubColumn={transactionHubColumn}
                currentTabName={tabFilters[activeTabIndex].name}
              />
            )}
          </>
        ) : (
          <>
            <ToggleListGridView
              listViewBox="0 -3 24 24"
              gridViewBox="0 -3 24 24"
              dividerStyle={classes.dividerStyle}
              tabIndex={gridListViewTabIndex}
              handleTabChange={(newTabIndex: number) => setGridListViewTabIndex(newTabIndex)}
              tabs={tabs}
            />
            {loading && !objects && <LoadingScreen loadingText="Loading..." />}
            {!loading &&
              objects &&
              Object.entries(tabs).map(([key, val]) => {
                return (
                  <TabViewPanel
                    value={gridListViewTabIndex}
                    index={val}
                    key={key}
                    greyBackground={key === 'Summary View'}
                  >
                    {key === 'Summary View' ? (
                      tabFilters[activeTabIndex]?.renderSummaryView?.({
                        objects: objects,
                        rubySettings: rubySettings,
                        fieldSetMetadata: fieldSetMetadata,
                        gridViewMetadata: gridViewMetadata,
                        objectMetadata: objectMetadata,
                        locale: locale,
                        currencyIsoCode: currencyIsoCode,
                        listViewMetadata: listViewMetadata,
                      })
                    ) : key === 'List View' ? (
                      <RubyListViewContainer
                        disableSelectAll={disableSelectAll}
                        shouldDisableRowSelection={shouldDisableRowSelection}
                        defaultPage={pageNumber}
                        defaultPageSize={pageSize}
                        handlePageSizeChange={handlePageSizeChange}
                        handleCurrentPageChange={handleCurrentPageChange}
                        totalCount={totalCount}
                        enablePaging={tabFilters[activeTabIndex].enablePagination}
                        remotePaging={tabFilters[activeTabIndex].remotePaging}
                        addActionColumn={tabFilters[activeTabIndex].addActionColumn}
                        addTransactionHubColumn={tabFilters[activeTabIndex].addTransactionHubColumn}
                        objectMetadata={objectMetadata}
                        listViewMetadata={listViewMetadata}
                        objects={objects}
                        sortingList={sortingList}
                        handleSort={handleSort}
                        multiSelect={tabFilters[activeTabIndex].showListViewMultiSelect}
                        batchActions={tabFilters[activeTabIndex].batchActions}
                        showBatchActionBar={tabFilters[activeTabIndex].showBatchActionBar}
                        handleSelectBatchAction={tabFilters[activeTabIndex].handleSelectBatchAction}
                        currencyIsoCode={currencyIsoCode}
                        locale={locale}
                        transactionHubColumn={transactionHubColumn}
                        currentTabName={tabFilters[activeTabIndex].name}
                      />
                    ) : (
                      <GridView
                        enablePagination={tabFilters[activeTabIndex].enablePagination}
                        totalCount={totalCount}
                        pageSize={pageSize}
                        currentPage={pageNumber}
                        handleCurrentPageChange={(currentPage: number) => {
                          setPageNumber(currentPage);
                        }}
                        GridViewItem={GridViewItem}
                        handlePageSizeChange={handlePageSizeChange}
                        rubySettings={rubySettings}
                        objectMetadata={objectMetadata}
                        objects={objects}
                        subscriptionPricingFieldSet={subscriptionPricingFieldSet}
                        currencyIsoCode={currencyIsoCode}
                        gridViewMetadata={gridViewMetadata}
                        fieldSetMetadata={fieldSetMetadata}
                        handleCardClick={handleCardClick}
                        orderGridViewMetadata={orderGridViewMetadata}
                        orderFieldSetMetadata={orderFieldSetMetadata}
                        actionEventHandler={(args: any) =>
                          actionEventHandler({ ...args, callBack: retainLocalState })
                        }
                        getFieldSetMetadata={getFieldSetMetadata}
                        showDetails={tabFilters[activeTabIndex].showDetails}
                        onShowDetails={(object: any) => {
                          setSelectedObject(object);
                        }}
                        locale={locale}
                        addTransactionHubColumn={tabFilters[activeTabIndex].addTransactionHubColumn}
                        currentTabName={tabFilters[activeTabIndex].name}
                        getSwapUpgradeDowngradeOptions={getSwapUpgradeDowngradeOptions}
                      />
                    )}
                  </TabViewPanel>
                );
              })}
          </>
        )}
        {selectedObject && (
          <DialogComponent
            open={!!selectedObject}
            title={' '}
            width={'md'}
            handleClose={() => {
              setSelectedObject(null);
            }}
          >
            <UpdateChangeHistoryCard object={selectedObject} metadata={objectMetadata} />
          </DialogComponent>
        )}
      </PanelWithTabs>
    </>
  );
};

export default GridAndListView;

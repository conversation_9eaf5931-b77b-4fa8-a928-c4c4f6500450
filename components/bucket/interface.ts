import { GridViewMetadata, <PERSON><PERSON>ield, RubyObject } from '../metadata/interface';
import { Order } from '../revenue-builder-types';

declare type AddressType = Record<string, string | number | undefined>;

export interface BucketOrder extends Order {
  subscriptionStartDate?: string;
  totalPrice?: number;
  totalAmount?: number;
  orderPlacedDate?: Date;
  discountAmount?: number;
  acv?: number;
  imageSignedUrl?: string;
}

export interface FieldProps {
  apiName: string;
  value: string;
}

export interface Bucket {
  id: string;
  name: string;
  totalAmount?: number;
  totalPrice?: number;
  status: 'Draft' | 'Activated' | 'Finalized';
  primaryContactId?: string;
  primaryContact?: Record<string, any>;
  accountId?: string;
  account?: Record<string, any>;
  billingAccountId?: string;
  billingAccount?: Record<string, any>;
  shippingAddress?: AddressType;
  billingAddress?: AddressType;
  isDefaultBucket: boolean;
  billStreet?: string;
  billingCity?: string;
  billingCountry?: string;
  billingPostalCode?: string;
  order?: BucketOrder;
  startDate: string;
  modified?: boolean;
  number?: number;
  splitQuantity?: number;
  originalSplitQuantity?: number;
  splitPrice?: number;
  originalSplitPrice?: number;
  TCV?: number;
  ACV?: number;
  splitIncludedUnits?: number;
  originalSplitIncludedUnits?: number;
}

export interface Props {
  activeBucket?: Bucket;
  object: Bucket;
  fieldSetMetadata: RubyField[];
  objectMetadata: RubyObject;
  currencyIsoCode: string;
  handleDeleteBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  handlePreviewBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  handleEditBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  handleCreateBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  handleActivatedBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  bucketIndex: number;
  readOnly?: boolean;
  splitProgress?: number;
}

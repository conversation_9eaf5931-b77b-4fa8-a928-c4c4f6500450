export interface Props {
  rowsSelected: Array<any>;
  actions?: Array<BatchAction>;
  handleClose?: () => void;
  handleSelectBatchAction?: (action: BatchAction, rowsSelected: Array<any>) => void;
  allRowsSelectedInCurrentPage?: boolean;
  selectedAll?: boolean;
  totalCounts?: number;
  onSelectAll?: (isSelectedAll: boolean) => void;
  supportSelectAll?: boolean;
  selection: any;
}

export interface BatchAction {
  apiName: string;
  name: string;
}

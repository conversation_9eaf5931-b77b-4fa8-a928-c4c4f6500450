export default [
  {
    apiName: 'name',
    name: 'Name',
    type: 'text',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    minLength: 0,
    maxLength: 200,
  },
  {
    apiName: 'defaultUomId',
    name: 'Default UOM Id',
    type: 'bLookup',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: false,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    lookupRelation: {
      referenceTo: 'UOM',
      referenceField: 'id',
      relationName: 'defaultUom',
      relationLabel: 'Default UOM',
    },
  },
  {
    apiName: 'sku',
    name: 'Sku',
    type: 'text',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    minLength: 0,
    maxLength: 200,
  },
];

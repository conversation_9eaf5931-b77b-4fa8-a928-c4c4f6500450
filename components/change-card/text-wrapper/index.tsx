import Typography from '@material-ui/core/Typography';
import makeStyles from '@material-ui/core/styles/makeStyles';
import React from 'react';

export interface TextWrapperProps {
  children: React.ReactNode;
}

const useStyles = makeStyles((theme) => ({
  textWrapper: {
    color: theme.palette.grey[900],
    fontWeight: 500,
    fontSize: '.85rem',
  },
}));

const TextWrapper: React.FC<TextWrapperProps> = ({ children }) => {
  const classes = useStyles();

  return (
    <Typography variant="body2" component="p" display="inline" className={classes.textWrapper}>
      {children}
    </Typography>
  );
};

export default TextWrapper;

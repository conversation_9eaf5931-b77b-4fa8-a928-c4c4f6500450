import React, { useState } from 'react';

import { Grid } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';

import DialogComponent from '../dialog-component';
import { useRubySnackbar } from '../ruby-notifier';
import TextInput from '../text-input';
import type { Login, LoginDialogProps } from './interface';

const useStyles = makeStyles({
  input: {
    width: '50%',
    marginTop: '8px',
  },
});

const LoginDialog: React.FC<LoginDialogProps> = (loginDialogProps) => {
  const { title, open, handleOpen, handleLogin } = loginDialogProps;

  const [login, setLogin] = useState<Login>({ userName: '', password: '' });

  const classes = useStyles();

  const { showSnackbar, Snackbar } = useRubySnackbar();

  return (
    <>
      <DialogComponent
        width="xs"
        open={open}
        title={title}
        dontShowCloseIcon={true}
        actions={{
          rightButtons: [
            {
              text: 'Continue',
              type: 'submit',
              onClick: () => {
                try {
                  handleLogin(login);
                } catch (e) {
                  showSnackbar('error', 'Error', 'Could not authenticate');
                }
              },
              disabled: !login.password || !login.userName,
            },
          ],
          leftButtons: [
            {
              onClick: () => handleOpen(false),
              text: 'Cancel',
            },
          ],
        }}
      >
        <form>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextInput
                value={login.userName}
                required={true}
                label="Username"
                field={{
                  apiName: 'userName',
                  name: 'Username',
                  type: 'email',
                }}
                placeholder={'Enter your email'}
                name="Username"
                handleInputChange={(value) => {
                  setLogin((_login) => ({ ..._login, userName: value }));
                }}
                className={classes.input}
              />
            </Grid>
            <Grid item xs={12}>
              <TextInput
                value={login.password}
                required={true}
                label="Password"
                field={{
                  apiName: 'password',
                  name: 'Password',
                  type: 'text',
                }}
                placeholder={'Enter your password'}
                name="Password"
                handleInputChange={(value) => {
                  setLogin((_login) => ({ ..._login, password: value }));
                }}
                subType="password"
                className={classes.input}
              />
            </Grid>
          </Grid>
        </form>
      </DialogComponent>
      <Snackbar />
    </>
  );
};

export default LoginDialog;

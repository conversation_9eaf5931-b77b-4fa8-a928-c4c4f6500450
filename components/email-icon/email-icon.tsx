import React from 'react';
import { Props } from './interface';

const EmailIcon: React.FC<Props> = () => {
  return (
    <svg
      width="16px"
      height="16px"
      viewBox="0 0 16 16"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <title>7CAFFD62-AB1F-49DB-BDC3-B273A9AD34DD</title>
      <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="6-1-4-1-Create-Business" transform="translate(-1344.000000, -1039.000000)">
          <g id="add-new-products" transform="translate(1279.000000, 283.000000)">
            <g id="Profile-Card" transform="translate(45.000000, 618.000000)">
              <g id="Group-4" transform="translate(20.000000, 137.000000)">
                <g id="mail-line" transform="translate(0.000000, 1.000000)">
                  <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                  <path
                    d="M2,2 L14,2 C14.3681898,2 14.6666667,2.29847683 14.6666667,2.66666667 L14.6666667,13.3333333 C14.6666667,13.7015232 14.3681898,14 14,14 L2,14 C1.63181017,14 1.33333333,13.7015232 1.33333333,13.3333333 L1.33333333,2.66666667 C1.33333333,2.29847683 1.63181017,2 2,2 Z M13.3333333,4.82533333 L8.048,9.55866667 L2.66666667,4.81066667 L2.66666667,12.6666667 L13.3333333,12.6666667 L13.3333333,4.82533333 Z M3.00733333,3.33333333 L8.04066667,7.77466667 L13.0013333,3.33333333 L3.00733333,3.33333333 Z"
                    id="Shape"
                    fill="#6239EB"
                    fillRule="nonzero"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
};

export default EmailIcon;

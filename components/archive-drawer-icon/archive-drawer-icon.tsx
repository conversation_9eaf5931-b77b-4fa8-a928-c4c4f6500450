import React from 'react';
import { SvgIcon } from '@material-ui/core';
import { Props } from './interface';

const ArchiveDrawerIcon: React.FC<Props> = ({ width, height, viewBox }) => {
  return (
    <SvgIcon>
      <svg
        width={width || '16px'}
        height={height || '16px'}
        viewBox={viewBox || '0 0 16 16'}
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>E7BA1F65-15A1-4202-B1B7-89369A0D6E84</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-5-1-Create-Quote" transform="translate(-1014.000000, -825.000000)">
            <g id="Product-Options" transform="translate(361.000000, 283.000000)">
              <g id="Profile" transform="translate(633.000000, 165.000000)">
                <g id="archive-drawer-line" transform="translate(20.000000, 377.000000)">
                  <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                  <path
                    d="M2,1.99466667 C2,1.62933333 2.29666667,1.33333333 2.662,1.33333333 L13.338,1.33333333 C13.7022963,1.33587188 13.9970944,1.63037311 14,1.99466667 L14,14.0053333 C13.9996321,14.3706855 13.7033523,14.6666667 13.338,14.6666667 L2.662,14.6666667 C2.2977037,14.6641281 2.0029056,14.3696269 2,14.0053333 L2,1.99466667 Z M12.6666667,7.33333333 L12.6666667,2.66666667 L3.33333333,2.66666667 L3.33333333,7.33333333 L12.6666667,7.33333333 Z M12.6666667,8.66666667 L3.33333333,8.66666667 L3.33333333,13.3333333 L12.6666667,13.3333333 L12.6666667,8.66666667 Z M6,4 L10,4 L10,5.33333333 L6,5.33333333 L6,4 Z M6,10 L10,10 L10,11.3333333 L6,11.3333333 L6,10 Z"
                    id="Shape"
                    fill="#6239EB"
                    fillRule="nonzero"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default ArchiveDrawerIcon;

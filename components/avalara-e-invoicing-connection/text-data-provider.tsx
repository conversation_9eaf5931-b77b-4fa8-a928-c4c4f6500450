import React from 'react';
import { DataTypeProvider } from '@devexpress/dx-react-grid';
import { Input } from '@material-ui/core';

const TextFormatter = (props: DataTypeProvider.ValueFormatterProps) => {
  return <div>{props.value}</div>;
};

const TextEditor: React.ComponentType<DataTypeProvider.ValueEditorProps> = ({
  value,
  onValueChange,
  column,
  disabled,
  onBlur,
  row,
}) => {
  return (
    <Input
      value={value}
      disabled={
        (column.name === 'staticValue' && row.disableStaticValue) ||
        column.name === 'elrXpath' ||
        column.name === 'dataType'
      }
      onChange={(event) => onValueChange(event.target.value)}
    />
  );
};

export const TextDataProvider = (props: any) => {
  return (
    <DataTypeProvider {...props} formatterComponent={TextFormatter} editorComponent={TextEditor} />
  );
};

import ActiveIcon from '../active-icon';
import { Order } from '../revenue-builder-types';
import DraftIcon from '../draft-icon';
import EditIcon from '../edit-icon';
import DeleteOutlineIcon from '@material-ui/icons/DeleteOutline';
import { PreviewIcon, FinalizeIcon, FinalizeAndActivateIcon } from '../icons';
import PlayForWorkIcon from '@material-ui/icons/PlayForWork';
import { CancelIcon } from '../icons';
import CheckCircleOutlineIcon from '@material-ui/icons/CheckCircleOutline';
import ZoomInIcon from '@material-ui/icons/ZoomIn';

export default {
  cardHeaderMetadata: {
    title: 'name',
    subtitle: (order: Order) => order.owner?.name || '',
    // TODO: figure out a metadata-driven way to retrieve status and description
    // may need to do it dynamically since they depend on dynamic values
    description: () => '',
    // TODO when we implement orders, get status by orders active
    status: (order: Order) => order.status,
    imageSignedUrl: 'imageSignedUrl',
    statusStyle: {
      Draft: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
      Activated: {
        backgroundColor: '#6239EB',
        color: '#FFF',
      },
      Preview: {
        color: '#6239EB',
        backgroundColor: '#f2ecfe',
      },
    },
    statusShadowStyle: {
      Preview: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
      Activated: {
        borderColor: 'transparent transparent #000000 transparent',
      },
      Draft: {
        borderColor: 'transparent transparent #babac5 transparent',
      },
    },
    statusIcon: {
      Activated: '',
      Draft: DraftIcon,
      Preview: '',
    },
  },
  cardBodyMetadata: {
    numCardActionsPerRow: 2,
    activeBorderStyle: {
      border: '1px solid #6239EB',
      borderRadius: '10px 10px 8px 8px',
      backgroundColor: '#F5F5F5',
    },
    subtitle: (order: Order) => order.owner?.name || '',
    summaryItemPosition: 'top-left',
    actions: {
      Draft: [
        {
          id: 'edit',
          name: 'Edit',
          description: 'Edit',
          icon: EditIcon,
        },
        {
          id: 'delete',
          name: 'Delete',
          description: 'Delete',
          icon: DeleteOutlineIcon,
          confirmation: {
            title: 'Are you sure you want to delete this order?',
            message: 'This operation cannot be revoked.',
          },
        },
        {
          id: 'activateOrder',
          name: 'Activate Order',
          description: 'Activate order',
          icon: PlayForWorkIcon,
        },
      ],
      Preview: [
        {
          id: 'details',
          name: 'Details',
          description: 'View order details',
          icon: ZoomInIcon,
          style: {
            opacity: 0.5,
            cursor: 'not-allowed',
          },
        },
        {
          id: 'finalize',
          name: 'Finalize',
          description: 'Finalize',
          icon: FinalizeIcon,
          style: {
            color: '#6239EB',
          },
        },
        {
          id: 'finalizeAndActivate',
          name: 'Finalize and Activate',
          description: 'Finalize and Activate',
          icon: FinalizeAndActivateIcon,
          confirmation: {
            title: ' ',
            message: 'Are you sure you want to activate the order?',
          },
          // this props used to configure the style of the icon or button
          style: {
            color: '#6239EB',
          },
        },
      ],
      Activated: [
        {
          id: 'cancel',
          name: 'Cancel',
          description: 'Cancel order',
          icon: CancelIcon,
        },
        {
          id: 'details',
          name: 'Details',
          description: 'View order details',
          icon: ZoomInIcon,
        },
        {
          id: 'changeOrder',
          name: 'Change Order',
          description: 'Change order',
          icon: CheckCircleOutlineIcon,
        },
      ],
    },
  },
};

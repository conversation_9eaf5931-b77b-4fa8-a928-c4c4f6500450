import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import { SubmitHandler } from 'react-hook-form';
import { Condition } from '../graph-ql-query-constructor';
import { RubyField } from '../metadata';

export type GroupingAttribute = {
  conditions: any[];
  description: string;
  id: string;
  name: string;
  groupingField: string;
  status: 'Active' | 'Inactive';
  isStandard: true | false;
};
export interface Props {
  values: {
    id?: string;
    name?: string;
    groupingField?: string;
    status?: 'Active' | 'Inactive';
    isStandard?: true | false;
  };
  mode?: 'create' | 'edit' | 'view';
  onCancel: MouseEventHandler;
  onSubmit: SubmitHandler<GroupingAttribute>;
}

export interface EmptyGroupingAttributeConditionsProps {
  initGroupingAttributeConditions: any;
}

export interface GroupingConditionItem {
  name: string;
  id?: string | null;
  filters?: Condition[];
}

export interface GroupingAttributeConditionItemProps {
  condition: GroupingConditionItem;
  conditionArray: GroupingConditionItem[];
  index: number;
  onChange: Function;
  groupingField: string;
  field: RubyField;
  customFieldsOfAssetData?: any;
}

export interface GroupingAttributeFilterProps {
  open: boolean;
  onClose: () => void;
  groupingField: string;
  condition: GroupingConditionItem;
  handleChange: Function;
  conditionArray: GroupingConditionItem[];
  field: RubyField;
  customFieldsOfAssetData?: any;
}

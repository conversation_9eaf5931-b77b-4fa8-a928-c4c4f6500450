import { BucketObject, BucketProps, RubyField, RubyObject } from '@nue-apps/ruby-ui-component';

export interface BucketViewProps {
  getBucketItems: (quoteId: string) => Promise<BucketObject[]>;
  objectMetadata: RubyObject;
  quoteId: string;
  accountId: string;
  fieldSetMetadata: RubyField[];
  bucketViewMetadata: RubyObject;
  BucketItem: React.ComponentType<BucketProps>;
  currencyIsoCode: string;
}

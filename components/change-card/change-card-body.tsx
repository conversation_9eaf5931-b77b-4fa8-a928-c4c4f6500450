import Grid from '@material-ui/core/Grid';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import DeleteOutlineIcon from '@material-ui/icons/DeleteOutline';
import type { ChangeGroup, RadioItem } from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import React, { useContext, useState } from 'react';
import { flushSync } from 'react-dom';
import type {
  BulkCotermRequest,
  CancellationRequest,
  ChangeItem,
  ConvertFreeTrialRequest,
  DowngradeRequest,
  ReconfigureRequest,
  RenewalRequest,
  RenewalUpdateQuantityRequest,
  SwapRequest,
  UpdateMilestoneRequest,
  UpdateQuantityRequest,
  UpdateTermRequest,
  UpgradeRequest,
} from '../change-cart/interface';
import { CustomerViewContext } from '../customer-view-context/customer-view-context';
import EditIcon from '../edit-icon';
import { useEventBus } from '../event-bus';
import IconBtn from '../icon-btn';
import type { Subscription } from '../revenue-builder-types/interface';
import SubscriptionActionDialog from '../subscription-action-dialog/subscription-action-dialog';
import type { UpdateDialogProps } from '../subscription-card';
import { AdjustPriceDialog } from '../subscription-card/adjust-price-dialog/adjust-price-dialog';
import {
  getCancelSubscriptionFields,
  getValidationSchema,
} from '../subscription-card/subscription-card-util';
import UpdateQuantityActionDialog from '../update-quantity-action-dialog/update-quantity-action-dialog';
import ChangeCardTextRegistry from './change-card-text-registry';
import {
  COTERM_VALUE,
  EVERGREEN_VALUE,
  SUBSCRIPTION_VALUE,
} from '../subscription-card/info-section-text-registry/convert-free-trial-text/constants';

interface ChangeCardBodyProps {
  changeItems: ChangeItem[];
  changeGroup: ChangeGroup;
  handleRemoveChangeItems: (changeGroupId: string, changeItemId: string[]) => void;
  onAddToCartComplete: (changeGroup: ChangeGroup, changeItems: ChangeItem[]) => void;
}

interface ChangeItemActionBarProps {
  changeItem: ChangeItem;
  changeGroup: ChangeGroup;
  getSubscriptionsForCoterm: (
    lineEditorStartDate: Date,
    customerId: string | undefined,
  ) => Promise<Subscription[]>;
  handleSetOpenChangeModal: (newOpen: boolean) => void;
  handleSetUpdateModel: (newOpen: boolean, changeItem: ChangeItem) => void;
  handleUpdateTermOptions: (newRadioOptions: RadioItem[] | null) => void;
}

const useStyles = makeStyles({
  container: {
    padding: '28px 40px 12px 40px',
  },
  time: {
    fontWeight: 'bold',
    fontSize: '.8rem',
    opacity: 0.4,
    color: '#000000',
    paddingRight: 4,
  },
  timeContainer: {
    minHeight: '56px',
  },
  itemContainer: {
    padding: '4px 0',
    display: 'flex',
    alignItems: 'flex-start',
  },
  actionBar: {
    marginLeft: '-35px',
    display: 'flex',
    marginRight: '16px',
    justifyContent: 'space-evenly',
    backgroundColor: '#f2ecfe',
    paddingLeft: '28px',
    paddingRight: '16px',
    alignItems: 'center',
    borderRadius: '0 10px 10px 0',
  },
  changeItemContainer: {
    display: 'flex',
    width: '100%',
  },
});

const getDefaultValues = (changeItem: ChangeItem) => {
  const request = changeItem.request;
  let requestCopy;
  switch (request.changeType) {
    case 'UpdateQuantity':
      requestCopy = request as UpdateQuantityRequest;
      return {
        startDate: requestCopy.subscriptionStartDate,
        quantity: requestCopy.quantity > 0 ? requestCopy.quantity : requestCopy.quantity * -1,
        action: requestCopy.quantity > 0 ? 'add' : 'reduce',
      };
    case 'Reconfigure':
      requestCopy = request as ReconfigureRequest;
      return {
        startDate: requestCopy.startDate,
      };
    case 'Renew':
      requestCopy = request as RenewalRequest;
      return {
        renewalTerm: requestCopy.renewalTerm,
        renewalDate: requestCopy?.renewalDate,
        termDimension: requestCopy.termDimension,
      };
    case 'RenewWithQuantityUpdate': {
      requestCopy = request as RenewalUpdateQuantityRequest;
      const quantity = requestCopy.quantity > 0 ? requestCopy.quantity : requestCopy.quantity * -1;
      const action = requestCopy.quantity > 0 ? 'add' : 'reduce';
      return {
        renewalTerm: requestCopy.renewalTerm,
        renewalDate: requestCopy?.renewalDate,
        termDimension: requestCopy.termDimension,
        startDate: requestCopy.subscriptionStartDate,
        quantity,
        action,
        renewalEnabled: true,
        renewalQuantity: quantity,
        renewalAction: action,
      };
    }
    case 'UpdateTerm':
      requestCopy = request as UpdateTermRequest;
      return {
        term:
          requestCopy.subscriptionTerm > 0
            ? requestCopy.subscriptionTerm
            : requestCopy.subscriptionTerm * -1,
        action: requestCopy.subscriptionTerm > 0 ? 'add' : 'reduce',
        shouldProrateCredits: requestCopy.shouldProrateCredits,
        renewalTerm: requestCopy.renewalTerm,
        updateRenewalTerm: requestCopy.updateRenewalTerm,
        termDimension: requestCopy.termDimension,
      };
    case 'CoTerm':
      requestCopy = request as UpdateTermRequest;

      return {
        term: changeItem.asset.subscriptionTerm,
        action: 'add',
        shouldProrateCredits: requestCopy.shouldProrateCredits,
      };

    case 'Cancel':
      requestCopy = request as CancellationRequest;
      return {
        action: requestCopy.action,
        selectDay: requestCopy.cancellationDate,
        shouldProrateCredits: requestCopy.shouldProrateCredits,
      };
    case 'UpdateMilestone':
      requestCopy = request as UpdateMilestoneRequest;
      return {
        name: requestCopy.name,
        milestoneIds: [request.assetNumber],
      };
    case 'ConvertFreeTrial':
      requestCopy = request as ConvertFreeTrialRequest;
      return {
        convertFreeTrialDate: requestCopy.startDate,
        convertFreeTrialDuration: requestCopy.term,
        convertFreeTrialDimension: requestCopy.termDimension,
        convertFreeTrialTermType: requestCopy.switchToEvergreen
          ? EVERGREEN_VALUE
          : requestCopy.cotermToBundleSubscription
            ? COTERM_VALUE
            : SUBSCRIPTION_VALUE,
        convertFreeTrialOverrideTrialEnd: requestCopy.overrideTrialEnd,
      };
    case 'Upgrade':
      requestCopy = request as UpgradeRequest;
      return {
        toProduct: requestCopy.toProduct,
        withUom: requestCopy.withUom,
        startDate: requestCopy.subscriptionStartDate,
      };
    case 'Downgrade':
      requestCopy = request as DowngradeRequest;
      return {
        toProduct: requestCopy.toProduct,
        withUom: requestCopy.withUom,
        startDate: requestCopy.subscriptionStartDate,
      };
    case 'Swap':
      requestCopy = request as SwapRequest;
      return {
        toProduct: requestCopy.toProduct,
        withUom: requestCopy.withUom,
        startDate: requestCopy.subscriptionStartDate,
        samePriceSwap: requestCopy.samePriceSwap,
      };

    default:
      throw Error('Change type of: ' + request.changeType + ' is not supported.');
  }
};

const ChangeCardBody = ({
  changeItems,
  handleRemoveChangeItems,
  changeGroup,
  onAddToCartComplete,
}: ChangeCardBodyProps) => {
  const classes = useStyles();

  const [changeItemToEdit, setChangeItemToEdit] = useState<ChangeItem | null>(null);
  const [openChangeModal, setOpenChangeModal] = useState(false);
  const [updateTermOptions, setUpdateTermOptions] = useState<RadioItem[] | null>(null);
  const [updateTermRadioValue, setUpdateTermRadioValue] = useState<string>('');
  const [subscriptionRenewalTerm, setSubscriptionRenewalTerm] = useState<number | null>(null);
  const dayAfterSubscriptionEndDate = changeItems?.[0].asset.subscriptionEndDate
    ? dayjs(changeItems?.[0].asset.subscriptionEndDate)
    : null;
  const [subscriptionRenewalDate, setSubscriptionRenewalDate] = useState<string | null>(
    dayAfterSubscriptionEndDate ? dayAfterSubscriptionEndDate.format('YYYY-MM-DD') : null,
  );
  const [subscriptionRenewalQuantity, setSubscriptionRenewalQuantity] = useState<any | null>(null);
  const [cotermDate, setCotermDate] = useState<string>('');
  const [updateDialogProps, setUpdateDialogProps] = useState<UpdateDialogProps>();

  const { customerViewService } = useContext(CustomerViewContext);

  if (!customerViewService) {
    throw Error(
      'ChangeCardBody component requires customerViewService to be declared in context provider',
    );
  }
  const { customerViewConfigs } = customerViewService.getCustomerViewConfig();

  const {
    getSubscriptionsForCoterm,
    handleAddToCart,
    getSumOrderProductQuantityAtDate,
    rubySettings,
  } = customerViewConfigs;

  const getChangeItemTextComponent = (changeItem: ChangeItem) => {
    const ChangeItemTextComponent = ChangeCardTextRegistry.getChangeCardText(
      changeItem.request.changeType,
    );

    return <ChangeItemTextComponent changeItem={changeItem} />;
  };

  const getSelectedUpdateTermRadioValueFromChangeItem = (changeItem: ChangeItem) => {
    if (changeItem.request.changeType === 'CoTerm') {
      const requestCopy = changeItem.request as UpdateTermRequest;
      return requestCopy.subscriptionEndDate;
    }
    if (changeItem.request.changeType === 'UpdateTerm') {
      return 'updateSubscriptionTerm';
    }
    return '';
  };

  const handleSetOpenChangeModal = (newOpen: boolean) => setOpenChangeModal(newOpen);

  const handleUpdateTermOptions = (newRadioOptions: RadioItem[] | null) =>
    setUpdateTermOptions(newRadioOptions);

  const ChangeItemActionBar = ({
    changeItem,
    changeGroup,
    getSubscriptionsForCoterm,
    handleSetOpenChangeModal,
    handleUpdateTermOptions,
    handleSetUpdateModel,
  }: ChangeItemActionBarProps) => {
    const subscription = changeItem.asset;
    const cardAction = changeItem.cardAction;
    const subscriptionMetadata = changeItem.subscriptionMetadata;
    const { publishEvent } = useEventBus();

    return (
      <div className={classes.actionBar}>
        <div style={{ paddingRight: '4px' }}>
          <IconBtn
            handleClick={async () => {
              handleRemoveChangeItems(changeGroup.id, [changeItem.id]);
            }}
            Icon={DeleteOutlineIcon}
            iconAlt="Delete"
            fontSize="small"
          />
        </div>
        <div>
          {!changeItem.asset.milestoneIds && (
            <IconBtn
              viewBox="0 -1 24 24"
              handleClick={async () => {
                if (
                  changeItem.request.objectType === 'Subscription' &&
                  [
                    'updateTerm',
                    'cancel',
                    'updateQuantity',
                    'upgrade',
                    'downgrade',
                    'swap',
                  ].includes(changeItem.cardAction.id)
                ) {
                  publishEvent('openActionDialog', {
                    changeItem: { ...changeItem, requestWasEdited: true },
                    changeGroup,
                    submitButtonText: 'Update Cart',
                  });
                  return;
                }

                if (
                  changeItem.request.objectType === 'Asset' ||
                  //@ts-ignore
                  changeItem.request.objectType === 'Entitlement__c'
                ) {
                  handleSetUpdateModel(true, changeItem);
                  return;
                }
                handleSetOpenChangeModal(true);
              }}
              Icon={() => <EditIcon />}
              iconAlt="Continue"
              fontSize="small"
            />
          )}
        </div>
      </div>
    );
  };

  const formatChangeTime = (time: string) => {
    const dateSection = time.substr(0, time.indexOf(' '));
    const hoursSection = time.substr(time.indexOf(' ') + 1);
    if (dayjs().isSame(dayjs(dateSection), 'day')) {
      return (
        <div>
          <div>Today</div>
          <div>{hoursSection}</div>
        </div>
      );
    } else {
      return (
        <div>
          <div>{dateSection}</div>
          <div>{hoursSection}</div>
        </div>
      );
    }
  };

  const onAddToCart = (changeGroup: ChangeGroup, changeItems: ChangeItem[]) => {
    onAddToCartComplete(changeGroup, changeItems);
    handleAddToCart(changeGroup, changeItems);
  };

  const handleOpenUpdateDialog = (newOpen: boolean, changeItem: ChangeItem) => {
    const request = changeItem.request as UpdateQuantityRequest;
    setUpdateDialogProps({
      open: newOpen,
      quantityDimension: changeItem.asset.uom.quantityDimension,
      defaultValues: {
        action: request.quantity > 0 ? 'add' : 'reduce',
        startDate: request.subscriptionStartDate,
        quantity: request.quantity > 0 ? request.quantity : request.quantity * -1,
      },
      object: changeItem.objects,
      apiName: changeItem.request.objectType,
      label: changeItem.request.label,
    });
  };

  // Need to memoize this since it is used as a dependency in ChangeFormDialog
  const subscriptionActionDialogDefaultValues = React.useMemo(
    () =>
      changeItemToEdit ? changeItemToEdit.newValues || getDefaultValues(changeItemToEdit) : null,
    [changeItemToEdit],
  );

  return (
    <Grid container spacing={1} className={classes.container} role="card-body">
      {changeItems.map((changeItem: ChangeItem) => (
        <div
          key={changeItem.id}
          className={classes.changeItemContainer}
          onMouseEnter={() => {
            if (openChangeModal) {
              return;
            }
            if (changeItem.request.changeType === 'CoTerm') {
              if (changeItem.cardAction.id === 'bulkCoterm') {
                const request = changeItem.request as BulkCotermRequest;
                setCotermDate(request.subscriptionEndDate);
              } else {
                setUpdateTermRadioValue(getSelectedUpdateTermRadioValueFromChangeItem(changeItem));
              }
            }
            if (changeItem.request.changeType === 'UpdateTerm') {
              setUpdateTermRadioValue(getSelectedUpdateTermRadioValueFromChangeItem(changeItem));
            }
            if (changeItem.request.changeType === 'Renew') {
              const request = changeItem.request as RenewalRequest;
              setSubscriptionRenewalTerm(request.renewalTerm);
            }
            flushSync(() => {
              setChangeItemToEdit(changeItem);
            });
          }}
          onMouseLeave={() => {
            if (!openChangeModal) {
              flushSync(() => {
                setChangeItemToEdit(null);
              });
            }
          }}
        >
          <Grid className={classes.timeContainer} item xs={2} key={changeItem.id}>
            {changeItemToEdit?.id === changeItem.id ? (
              <ChangeItemActionBar
                changeItem={changeItem}
                changeGroup={changeGroup}
                getSubscriptionsForCoterm={getSubscriptionsForCoterm}
                handleSetOpenChangeModal={handleSetOpenChangeModal}
                handleUpdateTermOptions={handleUpdateTermOptions}
                handleSetUpdateModel={handleOpenUpdateDialog}
              />
            ) : (
              <Typography className={classes.time} variant="subtitle1">
                {formatChangeTime(changeItem.time)}
              </Typography>
            )}
          </Grid>
          <Grid item xs={10}>
            {getChangeItemTextComponent(changeItem)}
          </Grid>
        </div>
      ))}
      {changeItemToEdit && openChangeModal && (
        <SubscriptionActionDialog
          isEditingChangeItem={true}
          changeItemId={changeItemToEdit.id}
          openSubscriptionActionDialog={openChangeModal}
          handleSetOpenSubscriptionActionDialog={handleSetOpenChangeModal}
          handleSetChangeModalProps={(value, newValues) => {
            if (
              value.objectMetadata.apiName === 'Subscription__c' &&
              value.cardAction.id === 'cancel'
            ) {
              let newFieldValue = { ...newValues };
              if (!newValues.selectDay) {
                newFieldValue = { ...newFieldValue, selectDay: dayjs() };
              }
              const fields = getCancelSubscriptionFields(newFieldValue.action);
              setChangeItemToEdit({
                ...changeItemToEdit,
                newValues: newFieldValue,
                subscriptionMetadata: {
                  ...changeItemToEdit.subscriptionMetadata,
                  fields: fields,
                },
              });
            }
          }}
          subscriptionCotermOptions={updateTermOptions || []}
          handleSetSubscriptionCotermOptions={handleUpdateTermOptions}
          parentSubscription={changeItemToEdit.parentSubscription}
          handleAddToCart={onAddToCart}
          cotermDate={cotermDate}
          handleSetCotermDate={setCotermDate}
          getSumOrderProductQuantityAtDate={getSumOrderProductQuantityAtDate}
          changeModalProps={{
            cardAction: changeItemToEdit.cardAction,
            objectMetadata: changeItemToEdit.subscriptionMetadata,
            initValues: {},
            defaultValues: subscriptionActionDialogDefaultValues,
            handleOpen: handleSetOpenChangeModal,
            validationSchema: getValidationSchema(
              changeItemToEdit.cardAction.id,
              changeItemToEdit.asset,
            ),
            subscriptions: [changeItemToEdit.asset],
          }}
          updateTermRadioValue={updateTermRadioValue}
          handleUpdateTermRadioValue={setUpdateTermRadioValue}
          subscriptionRenewalTerm={subscriptionRenewalTerm}
          handleUpdateSubscriptionRenewalTerm={setSubscriptionRenewalTerm}
          subscriptionRenewalDate={subscriptionRenewalDate}
          handleUpdateSubscriptionRenewalDate={setSubscriptionRenewalDate}
          subscriptionRenewalQuantity={subscriptionRenewalQuantity}
          handleUpdateSubscriptionRenewalQuantity={setSubscriptionRenewalQuantity}
          rubySettings={rubySettings}
          handleErrorValidation={(changeItemRequestChanges: Record<string, any>) => {
            return undefined;
          }}
          updateToEvergreen={changeItemToEdit.request?.updateToEvergreen}
          submitButtonText="Update Cart"
        />
      )}
      {changeItemToEdit && updateDialogProps && (
        <UpdateQuantityActionDialog
          apiName={updateDialogProps.apiName || ''}
          defaultValues={
            updateDialogProps.defaultValues || {
              action: 'add',
              startDate: subscriptionRenewalDate || dayjs(),
            }
          }
          object={updateDialogProps.object}
          open={updateDialogProps.open}
          quantityDimension={updateDialogProps.quantityDimension || ''}
          onClose={() => {
            setUpdateDialogProps({
              open: false,
            });
          }}
          onFormSubmit={(values) => {
            const changeGroup: ChangeGroup = {
              id: `${changeItemToEdit?.asset.customerId}-${updateDialogProps.object.priceBook.id}-${updateDialogProps.object.currencyIsoCode}`,
              customer: {
                name: changeItemToEdit?.asset.customer.name,
                id: changeItemToEdit?.asset.customerId,
              },
              name: updateDialogProps.object.name,
              priceBook: {
                name: updateDialogProps.object.priceBook.name,
                id: updateDialogProps.object.priceBook.id,
              },
              currencyIsoCode: updateDialogProps.object.currencyIsoCode,
            };
            const time = dayjs().format('MM/DD/YYYY hh:mm A');
            const changeDate = dayjs(values.startDate).format('YYYY-MM-DD');
            const changeItem: ChangeItem = {
              id:
                changeItemToEdit.id ||
                `${updateDialogProps.object.id}-UpdateQuantity-${changeDate}`,
              time,
              asset: {
                name: updateDialogProps.object.name,
                subscriptionStartDate: updateDialogProps.object.startDate,
                uom: updateDialogProps.object.uom,
                quantity: updateDialogProps.object.quantity,
                customerId: changeItemToEdit?.asset.customerId,
                // @ts-ignore
                customer: {
                  name: changeItemToEdit?.asset.customer.name,
                },
              },
              cardAction: changeItemToEdit.cardAction,
              subscriptionMetadata: changeItemToEdit.subscriptionMetadata,
              requestWasEdited: true,
              parentSubscription: null,
              objects: updateDialogProps.object,
              request: {
                assetName: updateDialogProps.object.name,
                assetNumber:
                  updateDialogProps.apiName === 'Asset'
                    ? updateDialogProps.object.assetNumber
                    : updateDialogProps.object.entitlementNumber,
                changeType: 'UpdateQuantity',
                //@ts-ignore
                objectType: updateDialogProps.apiName,
                label: updateDialogProps.label,
                priceBookId: updateDialogProps.object.priceBook.id,
                quantity: values.action === 'add' ? values.quantity : values.quantity * -1,
                subscriptionStartDate: changeDate,
                startDate: changeDate,
              },
            };
            onAddToCartComplete(changeGroup, [changeItem]);
            handleAddToCart(changeGroup, [changeItem]);
            setUpdateDialogProps({
              open: false,
            });
          }}
          submitButtonText="Update Cart"
        />
      )}
      {changeItemToEdit && openChangeModal && changeItemToEdit.cardAction.id === 'AdjustPrice' && (
        <AdjustPriceDialog
          open={openChangeModal}
          onClose={() => {
            setOpenChangeModal(false);
            setChangeItemToEdit(null);
          }}
          subscription={changeItemToEdit.objects}
          onSubmit={async (result: any) => {
            const newSubscription = changeItemToEdit.objects;

            const changeGroup: ChangeGroup = {
              id: `${newSubscription.customerId}-${newSubscription.priceBookId}-${newSubscription.currencyIsoCode}`,
              customer: {
                name: newSubscription.customer.name,
                id: newSubscription.customerId,
              },
              name: newSubscription.name,
              priceBook: {
                name: newSubscription.priceBook.name,
                id: newSubscription.priceBookId,
              },
              currencyIsoCode: newSubscription.currencyIsoCode,
            };

            let request = {
              ...changeItemToEdit.request,
              ...result,
            };

            if (result.endDateRadio === 'untilSpecificDate') {
              request = {
                ...request,
                endDate: dayjs(result.specificDate).format('YYYY-MM-DD'),
              };
            } else if (result.endDateRadio === 'untilMonthLater') {
              request = {
                ...request,
                term: result.untilAmount,
              };
            }

            const newChangeItem: ChangeItem = {
              id: `${newSubscription.id}-adjustPrice`,
              time: dayjs().format('MM/DD/YYYY hh:mm A'),
              asset: newSubscription,
              //@ts-ignore
              cardAction: {
                id: 'AdjustPrice',
              },
              subscriptionMetadata: changeItemToEdit.subscriptionMetadata,
              requestWasEdited: true,
              parentSubscription: changeItemToEdit.parentSubscription,
              request: request,
              newValues: result,
              objects: newSubscription,
            };

            if (handleAddToCart) {
              await handleAddToCart(changeGroup, [newChangeItem]);
            }

            setOpenChangeModal(false);
          }}
          defaultValues={changeItemToEdit.newValues}
          submitButtonText="Update Cart"
        />
      )}
    </Grid>
  );
};

export default ChangeCardBody;

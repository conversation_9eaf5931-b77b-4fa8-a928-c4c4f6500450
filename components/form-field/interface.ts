import React from 'react';
import { UseFormMethods } from 'react-hook-form';

import Metadata from '../metadata';
import { GetLookupOptionByValue, LookupOptionsLoader, RubyField } from '../metadata/interface';

export type CreateLookupRecordArgs = {
  lookupField: RubyField;
};

export type CreateLookupRecordHandler = (args: CreateLookupRecordArgs) => void;

export interface LookupRecordContextService {
  showCreateOnLookupField: (lookupField: RubyField) => boolean;
  handleCreateLookupRecord: CreateLookupRecordHandler;
  showLookupRecordPreview?: (lookupField: RubyField) => boolean;
  getLookupRecordPreviewComponent?: (
    lookupField: RubyField,
  ) => React.FC<{ lookupField: RubyField; lookupRecordId: string }>;
  getSelectedItemLabel?: (lookupField: <PERSON>Field, item: Record<any, any>) => string;
  getSuggestionItemLabel?: (lookupField: RubyField, item: Record<any, any>) => string;
}

export const LookupRecordContext = React.createContext<LookupRecordContextService | null>(null);

export interface RubyFormControlProps {
  styleOverrides?: React.CSSProperties;
  field: Metadata.RubyField;
  inputWrapperStyles?: React.CSSProperties;
  value: any;
  label?: string;
  required?: boolean;
  placeholder?: string;
  handleInputChange?: (newValue: any, displayValue?: any, fieldFieldName?: string) => void;
  disabled?: boolean;
  readOnly?: boolean;
  name?: string;
  classes?: object;
  className?: string;
  lookupOptionsLoader?: LookupOptionsLoader;
  getLookupOptionByValue?: GetLookupOptionByValue;
  formContext?: UseFormMethods<any>;
  dateFormat?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  disableLabel?: boolean;
  compoundedFields?: Array<Metadata.RubyField>;
  mode?: 'create' | 'edit';
  currencyIsoCode?: string;
  locale?: string;
  withCreateLookupRecord?: boolean;
  // withClearIcon: if X(clear) will be shown at the end of lookup input
  // if you really need this feature, just add the props to other input cmpt
  withClearIcon?: boolean;
  onClickCreateLookupRecord?: CreateLookupRecordHandler;
  showTooltip?: boolean;
  toolTipText?: string;
  showNone?: boolean;
  allowOnlyPositiveNumbers?: boolean;
  subType?: string;
  format?: string;
  hidden?: boolean;
  minDate?: any;
  clearable?: boolean;
  onBlur?: React.FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>;
  autoFocus?: boolean;
}

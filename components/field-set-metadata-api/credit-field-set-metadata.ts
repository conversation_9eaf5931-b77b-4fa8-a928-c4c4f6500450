import { RubyField } from '../metadata/interface';

export default [
  {
    apiName: 'name',
    width: 200,
  },
  {
    apiName: 'creditNumber',
  },
  {
    apiName: 'recordType',
  },
  {
    apiName: 'transactionType',
  },
  {
    apiName: 'transactionDate',
  },
  {
    apiName: 'transactionAmount',
    width: 200,
  },
  {
    apiName: 'creditStartDate',
  },
  {
    apiName: 'creditEndDate',
  },
  {
    apiName: 'transactionSource',
    width: 200,
  },
  {
    apiName: 'transactionSourceId',
    width: 200,
  },
] as RubyField[];

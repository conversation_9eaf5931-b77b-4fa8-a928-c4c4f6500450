export interface NetSuiteIntegration {
  authenticationId?: string;
  solutionInstanceId?: string;
  lastModifiedDate: string;
  enabled: boolean;
  companyName: string;
  trayEmbeddedUrl?: string;
  environment?: string;
}

export interface NetSuiteTrayDetail {
  trayUsername: string;
  trayEmbeddedUrl: string;
  webhookPublicUrl?: string;
}

export interface NetSuiteUpdateDetail {
  appCategory: string;
  integrationType: string;
  configuration: Record<string, string>;
  enabled: boolean;
}

export interface NetSuiteConfigureDetail {
  configuration: {
    endConfig: boolean;
    environment: string;
  };
}
export const NSRowEditStateFlag = Symbol.for('RowState');

export interface EditColumnProps {
  onDeleteRow?: (payload: { row: any }) => void;
  onCommitRow?: (payload: { row: any }) => any;
}

export type ValidationStatusType = Map<
  string | number,
  {
    [col: string]: {
      isValid: boolean;
      error: string;
    };
  }
>;

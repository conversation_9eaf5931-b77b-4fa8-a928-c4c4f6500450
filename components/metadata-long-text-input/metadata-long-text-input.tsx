import React from 'react';

import FormControl from '@material-ui/core/FormControl';
import Tooltip from '@material-ui/core/Tooltip';
import InfoIcon from '@material-ui/icons/Info';

import InputBaseComponent from '../input-base-component';
import InputLabelComponent from '../input-label-component';

export const MetadataLongTextInput = (props: any) => {
  const {
    value,
    field,
    disabled,
    label,
    placeHolder,
    handleInputChange,
    className,
    readOnly,
    rowNum = 5,
  } = props;

  const showTooltip = props.showTooltip || !!field?.inlineHelpText;
  const toolTipText = props.toolTipText || field?.inlineHelpText;
  return (
    <FormControl style={{ width: '100%' }}>
      <InputLabelComponent required={field.required} shrink htmlFor={field.apiName}>
        {label}
        {showTooltip && (
          <Tooltip title={toolTipText} arrow>
            <InfoIcon />
          </Tooltip>
        )}
      </InputLabelComponent>
      <InputBaseComponent
        style={{ paddingTop: '0px', paddingBottom: '0px' }}
        multiline
        rows={rowNum}
        value={value}
        name={field.apiName}
        type="text"
        placeholder={placeHolder ? placeHolder : field.name}
        required={field.required}
        onChange={handleInputChange}
        fullWidth={true}
        disabled={disabled}
        className={className ? className : ''}
        readOnly={readOnly}
      />
    </FormControl>
  );
};

export default MetadataLongTextInput;

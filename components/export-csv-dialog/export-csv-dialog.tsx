import React, { useEffect, useState } from 'react';
import { Typography } from '@material-ui/core';
import RubyCheckbox from '../checkbox/checkbox';
import DialogComponent from '../dialog-component/dialog-component';
import ErrorText from '../self-service/subscription/dialogs/error-text';
import type { ExportCsvDialogFormData, ExportCsvDialogProps } from './interface';

export const ExportCsvDialog: React.FC<ExportCsvDialogProps> = (props) => {
  const [formData, setFormData] = useState<ExportCsvDialogFormData>([...props.fields]);
  const [isError, setIsError] = useState(false);

  const handleSubmit = () => {
    if (isError) return;
    props.onSubmit(formData);
  };

  useEffect(() => {
    const checkIfAtLeastOneFieldIsSelected = () => {
      if (!formData.some((field) => field.isSelected)) {
        setIsError(true);
      } else {
        setIsError(false);
      }
    };
    checkIfAtLeastOneFieldIsSelected();
  }, [formData]);

  return (
    <DialogComponent
      open={props.open}
      title={props.title || 'Export Options'}
      handleClose={props.onClose}
      handleSubmit={handleSubmit}
      width="xs"
    >
      <div style={{ marginBottom: '1.5rem' }}>
        <Typography variant="body1" style={{ marginBottom: '1rem' }}>
          Please select the types of records you&#39;d like to export. Each selected type of record
          will be exported into an individual CSV file.
        </Typography>
        {props.fields.map((field, idx) => {
          return (
            <RubyCheckbox
              key={field.name}
              value={formData[idx].isSelected}
              field={{ apiName: field.apiName, name: field.name, type: 'Checkbox' }}
              handleInputChange={(newValue) => {
                setFormData((prevFormData) => {
                  const newFormData = [...prevFormData];
                  newFormData[idx] = { ...newFormData[idx], isSelected: newValue };
                  return newFormData;
                });
              }}
            />
          );
        })}
        {isError && (
          <ErrorText
            text={`At least one record type must be selected.`}
            styles={{ marginTop: '1.5rem' }}
          />
        )}
      </div>
    </DialogComponent>
  );
};

import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Typography from '@material-ui/core/Typography';
import Grid from '@material-ui/core/Grid';
import { ChangeCardHeaderProps, ChangeCardHeadingProps } from '../change-card/interface';

const useStyles = makeStyles({
  container: {
    borderRadius: '9px 10px 0 0',
    backgroundColor: '#F5F5F5',
    boxShadow: '0 12px 24px 0 rgba(0,0,0,0.08)',
    padding: '20px 40px',
  },
  title: {
    opacity: '0.7',
    color: '#000000',
    fontWeight: 500,
    fontSize: '1.5rem',
  },
  itemTitle: {
    display: 'flex',
    fontWeight: 500,
    alignItems: 'center',
    color: '#000000',
    opacity: 0.5,
    fontSize: '.8rem',
  },
  itemValue: {
    opacity: 0.7,
    color: '#000000',
    fontSize: '.85rem',
  },
  itemsContainer: {
    paddingTop: '8px',
  },
  heading: {
    paddingRight: '24px',
  },
});

const Heading = ({ title, value }: ChangeCardHeadingProps) => {
  const classes = useStyles();

  return (
    <>
      <Typography variant="subtitle1" className={classes.itemTitle} noWrap>
        {title}
      </Typography>
      <Typography className={classes.itemValue} variant="subtitle1" noWrap>
        {value}
      </Typography>
    </>
  );
};

const ChangeCardHeader = ({
  changeGroupNumber,
  priceBook,
  currencyIsoCode,
  customer,
  numberChangeItems,
}: ChangeCardHeaderProps) => {
  const classes = useStyles();

  return (
    <Grid container className={classes.container} justifyContent="space-between">
      <Grid item xs={12}>
        <Typography className={classes.title} variant="h2">
          Change Set {changeGroupNumber}
        </Typography>
      </Grid>
      <Grid item container className={classes.itemsContainer}>
        <Grid item className={classes.heading}>
          <Heading title="PRICE BOOK" value={priceBook.name} />
        </Grid>
        <Grid item className={classes.heading}>
          <Heading title="CURRENCY" value={currencyIsoCode} />
        </Grid>
        <Grid item>
          <Heading title="CUSTOMER" value={customer.name} />
        </Grid>
      </Grid>
    </Grid>
  );
};

export default ChangeCardHeader;

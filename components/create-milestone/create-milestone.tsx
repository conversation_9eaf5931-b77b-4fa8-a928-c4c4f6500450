import React, { ChangeEvent, useEffect, useRef, useState } from 'react';
import { <PERSON>rid, TextField, Typography, setRef } from '@material-ui/core';
import InputAdornment from '@material-ui/core/InputAdornment';
import Popover from '@material-ui/core/Popover';
import { makeStyles } from '@material-ui/core/styles';
import AddIcon from '@material-ui/icons/Add';
import NavigateNextIcon from '@material-ui/icons/NavigateNext';
import { GridViewItem, RubyButton, RubyButtonBar } from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import * as _ from 'lodash';
import ClickAwayListener from '../click-away-listener';
import DateInput from '../date-input';
import DecimalInput from '../decimal-input';
import DialogComponent from '../dialog-component';
import { DeleteBucketIcon as DeleteIcon, EditBucketIcon as EditIcon } from '../icons';
import type { LineBucketItem, LineItem } from '../revenue-builder-types';
import { useRubySnackbar } from '../ruby-notifier';
import TextInput from '../text-input';
import { useUserLocale } from '../use-user-locale';
import { calculateNum, calculateTimes, convertToCurrency, currencySymbol } from './calculation';
import type { Milestone, MilestoneLineItemProps, Props } from './interface';

const useStyles = makeStyles({
  titleWrapper: {
    position: 'relative',
    top: '-15px',
  },
  title: {
    opacity: 0.7,
    fontSize: '20px',
  },
  lineItemName: {
    fontSize: '20px',
  },
  rightArrowWrapper: {
    marginLeft: '10px',
    marginRight: '10px',
    marginTop: '5px',
    opacity: 0.7,
  },
  milestoneSumContainer: {
    border: '1px solid #E5E5E5',
    borderRadius: '8px',
    padding: '10px 150px',
    marginBottom: '20px',
    width: '100%',
  },
  sumLabel: {
    fontSize: '1rem',
    color: '#999999',
  },
  sumValue: {
    fontSize: '18px',
    marginRight: '20px',
  },
  priceValue: {
    fontSize: '18px',
    color: '#6239EB',
    marginRight: '20px',
  },
  valueSplit: {
    fontSize: '18px',
    color: '#67d5c9',
  },
  valueSplitError: {
    position: 'absolute',
    left: '100%',
    fontSize: '18px',
    color: '#67d5c9',
  },
  milestoneItemWrapper: {
    position: 'relative',
    border: '1px solid #E5E5E5',
    padding: '5px 25px 15px 25px',
    borderRadius: '8px',
    overflow: 'hidden',
  },
  statusBar: {
    width: '6px',
    position: 'absolute',
    left: '0',
    top: '0',
    height: '100%',
    backgroundColor: '#6239EB',
  },
  milestoneItemContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  milestoneName: {
    fontSize: '1.125rem',
  },
  milestoneAction: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: '1',
  },
  actionIconWrapper: {
    cursor: 'pointer',
    marginLeft: '20px',
  },
  modificationWrapper: {
    marginTop: '10px',
    flexWrap: 'nowrap',
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
  },
  clearIconWrapper: {
    borderRadius: '4px',
    height: '42.63px',
    width: '42.63px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    // @ts-ignore
    flexShrink: '0',
    marginRight: '0',
    cursor: 'pointer',
  },
  relative: {
    position: 'relative',
  },
  maxQuantityText: {
    color: '#6239eb',
    position: 'absolute',
    top: '53px',
    left: '65px',
    fontSize: '0.875rem',
    textDecoration: 'underline',
    cursor: 'pointer',
  },
  errorMessage: {
    marginTop: '5px',
    color: '#ca0035',
    fontSize: '0.875rem',
    cursor: 'pointer',
    fontWeight: 400,
    lineHeight: 1.5,
  },
  newMilestoneContainer: {
    boxShadow: '0 8px 20px 0 rgb(0 0 0 / 4%)',
    paddingBottom: '20px',
    width: '105%',
    marginLeft: '-24px',
    paddingLeft: '24px',
    paddingRight: '24px',
  },
  milestoneContainer: {
    paddingBottom: '20px',
    width: '105%',
    marginLeft: '-24px',
    paddingLeft: '24px',
    paddingRight: '24px',
  },
  scrollContainer: {
    overflowY: 'scroll',
    position: 'relative',
    maxHeight: '450px',
  },
  milestoneFormContainer: {
    display: 'flex',
    alignItems: 'end',
    cursor: 'pointer',
  },
  dateInput: {
    borderRadius: '4px',
    marginTop: '8px',
    border: '1px solid #ced4da',
    '&:focus-within': {
      borderColor: '#6239EB',
    },
    '& input': {
      fontSize: '.875rem',
      padding: '12px 20px',
    },
    '& fieldset': {
      border: '0px',
    },
    '& button': {
      color: '#6239EB',
      opacity: 0.7,
    },
    '& button:hover': {
      color: '#6239EB',
      opacity: 1,
    },
  },
});

const MilestoneItem: React.FC<MilestoneLineItemProps> = (props: MilestoneLineItemProps) => {
  const classes = useStyles();
  const {
    milestoneItem,
    milestoneItems,
    updateAllMilestonesDetail,
    row,
    currencyIsoCode,
    setRefreshFlag,
    refreshFlag,
    handleDeleteMilestone,
    index,
    setMilestoneItems,
    allMilestonesTotalPercentage,
    allMilestonesTotalPrice,
  } = props;
  const { name = '123', id } = milestoneItem;

  const [priceFocused, setPriceFocused] = useState(false);
  const [quantityFocused, setQuantityFocused] = useState(false);
  const [percentageFocused, setPercentageFocused] = useState(false);
  const [showTwoDecimal, setShowTwoDecimal] = useState(true);
  const { getUserLocale } = useUserLocale();
  const [milestonePercentage, setMilestonePercentage] = useState(milestoneItem.percentage);
  const [quantity, setQuantity] = useState<number | string>(milestoneItem.quantity);
  const [price, setPrice] = useState(milestoneItem.price);
  const [isEditing, setIsEditing] = useState(false);

  const updateMilestoneStartDate = (date: string) => {
    milestoneItem.startDate = date;
    setRefreshFlag(refreshFlag + 1);
  };

  const updateLastMilestone = () => {
    // the active milestone is not the last milestone
    if (milestoneItems.length > 1 && index !== milestoneItems.length - 1) {
      const lastMilestone = milestoneItems[milestoneItems.length - 1];
      const percentage = milestoneItems
        .filter((m, i) => i !== milestoneItems.length - 1)
        .reduce((o: number, m: any) => {
          return calculateNum(o, m.percentage || 0);
        }, 0);
      const q = milestoneItems
        .filter((m, i) => i !== milestoneItems.length - 1)
        .reduce((o: number, m: any) => {
          return calculateNum(o, m.quantity || 0);
        }, 0);
      const p = milestoneItems
        .filter((m, i) => i !== milestoneItems.length - 1)
        .reduce((o: number, m: any) => {
          return calculateNum(o, m.price || 0);
        }, 0);
      lastMilestone.percentage = calculateNum(100, percentage, '-');
      lastMilestone.quantity = calculateNum(row.actualQuantity, q, '-');
      lastMilestone.price = calculateNum(row.totalPrice || 0, p, '-');
    }
  };

  const updateMilestoneQuantity = (q: string | number) => {
    const updateQuantity = q;
    if (isNaN(Number(updateQuantity))) {
      return;
    }
    setQuantity(updateQuantity);
    milestoneItem.quantity = parseFloat(updateQuantity as string);
    updateLastMilestone();
    setMilestoneItems([...milestoneItems]);
    updateAllMilestonesDetail();
  };

  const updateMilestonePercentage = (p: string | number) => {
    const percentage = p;
    if (isNaN(Number(percentage)) || percentage === '') {
      return;
    }
    setMilestonePercentage(Number(percentage));
    milestoneItem.percentage = parseFloat(percentage as string);
    if (percentageFocused) {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      updateMilestonePrice(calculateTimes(Number(percentage) / 100, row?.totalPrice || 0));
      updateMilestoneQuantity(calculateTimes(Number(percentage) / 100, row?.quantity || 0));
    }
    updateLastMilestone();
    setMilestoneItems([...milestoneItems]);
    updateAllMilestonesDetail();
  };

  const updateMilestonePrice = (p: string | number) => {
    const updatePrice = p;
    if (isNaN(Number(updatePrice))) {
      return;
    }
    setPrice(Number(updatePrice));
    milestoneItem.price = parseFloat(updatePrice as string);
    if (priceFocused) {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      const percentage =
        calculateTimes(parseFloat(updatePrice as string), 1 / row.totalPrice!) * 100;
      updateMilestonePercentage(percentage.toFixed(2));
      updateMilestoneQuantity(
        calculateTimes(parseFloat(percentage.toFixed(2)) / 100, row?.quantity || 0),
      );
    }
    updateLastMilestone();
    setMilestoneItems([...milestoneItems]);
    updateAllMilestonesDetail();
  };

  const getMilestonePercentageForDisplay = () => {
    if (Object.is(milestonePercentage, 0)) {
      return 0;
    }
    return milestonePercentage;
  };

  const getMilestoneQuantityForDisplay = () => {
    if (Object.is(quantity, 0)) {
      return 0;
    }
    return quantity;
  };

  const getMilestonePriceForDisplay = () => {
    if (Object.is(price, 0)) {
      return 0;
    }
    return price;
  };

  useEffect(() => {
    setMilestonePercentage(milestoneItem.percentage);
  }, [milestoneItem.percentage]);

  useEffect(() => {
    setQuantity(milestoneItem.quantity);
  }, [milestoneItem.quantity]);

  useEffect(() => {
    setPrice(milestoneItem.price);
  }, [milestoneItem.price]);

  const validateMilestoneStartDate = () => {
    const previousMilestoneStartDate =
      index > 0 ? milestoneItems[index - 1].startDate : row?.subscriptionStartDate;
    return +new Date(milestoneItem.startDate) >= +new Date(previousMilestoneStartDate!);
  };

  const validateMilestonePercentage = () => {
    if (allMilestonesTotalPercentage > 100) {
      return false;
    }
    return true;
  };

  const validateMilestonePrice = () => {
    if (milestoneItem.price > row.totalPrice!) {
      return false;
    }
    return true;
  };

  const updateMilestoneName = (mileStoneName: string) => {
    milestoneItem.name = mileStoneName;
    milestoneItem.nameChanged = true;
    setRefreshFlag(refreshFlag + 1);
  };

  return (
    <div className={classes.milestoneItemWrapper}>
      <div className={classes.statusBar} />
      <Grid container className={classes.milestoneItemContainer}>
        {isEditing ? (
          <ClickAwayListener onClickAway={() => setIsEditing(false)}>
            <TextField
              value={milestoneItem.name}
              inputProps={{
                style: {
                  fontSize: '1.125rem',
                  fontWeight: 400,
                  lineHeight: 1.5,
                },
              }}
              autoFocus={true}
              onChange={(e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
                updateMilestoneName(e.target.value);
              }}
            />
          </ClickAwayListener>
        ) : (
          <Typography className={classes.milestoneName}>{milestoneItem.name}</Typography>
        )}
        <Grid item className={classes.milestoneAction}>
          <div
            className={classes.actionIconWrapper}
            role="edit"
            onClick={(event: React.MouseEvent<HTMLDivElement>) => {
              setIsEditing(true);
            }}
          >
            <EditIcon />
          </div>
          <div
            className={classes.clearIconWrapper}
            role="clear"
            onClick={() => handleDeleteMilestone(index)}
          >
            <DeleteIcon />
          </div>
        </Grid>
      </Grid>
      <div className={classes.milestoneFormContainer}>
        <Grid container justifyContent="space-between" spacing={2}>
          <Grid container item xs={12} md={3}>
            <Grid container className={classes.modificationWrapper}>
              <DateInput
                className={classes.dateInput}
                required={false}
                value={milestoneItem.startDate}
                field={{
                  apiName: 'startDate',
                  required: true,
                  name: 'Milestone Date',
                  type: 'date',
                }}
                name="date-input"
                showTooltip={true}
                toolTipText={'The start date of the milestone'}
                handleInputChange={updateMilestoneStartDate}
              />
              <div className={classes.errorMessage}>
                {!validateMilestoneStartDate()
                  ? `The milestone date must be equal to or greater than ${
                      index > 0 ? milestoneItems[index - 1].startDate : row?.subscriptionStartDate
                    }`
                  : ''}
              </div>
            </Grid>
          </Grid>
          <Grid container item xs={12} md={3}>
            <Grid container className={classes.modificationWrapper}>
              <DecimalInput
                value={getMilestonePercentageForDisplay()}
                label="Percent"
                field={{
                  apiName: 'saveAs',
                  name: 'Save As',
                  type: 'text',
                }}
                placeholder="Percentage"
                name="filter-name"
                handleInputChange={updateMilestonePercentage}
                onBlur={() => {
                  setPercentageFocused(false);
                }}
                onFocus={() => {
                  setPercentageFocused(true);
                }}
                required={true}
                showTooltip={true}
                toolTipText={'The percentage of total'}
                endAdornment={
                  <InputAdornment position="end" style={{ marginTop: '8px' }}>
                    {'%'}
                  </InputAdornment>
                }
              />
              <div className={classes.errorMessage}>
                {percentageFocused && !validateMilestonePercentage()
                  ? `The percentage need to be less than ${calculateNum(
                      100,
                      milestoneItems
                        .filter((m, i) => i !== index)
                        .reduce((o: number, m: any) => {
                          return calculateNum(o, m.percentage || 0);
                        }, 0),
                      '-',
                    )}.`
                  : ''}
              </div>
            </Grid>
          </Grid>
          <Grid container item xs={12} md={3}>
            <Grid container item className={classes.modificationWrapper}>
              <DecimalInput
                value={getMilestoneQuantityForDisplay()}
                label="Quantity"
                field={{
                  apiName: 'saveAs',
                  name: 'Save As',
                  type: 'text',
                }}
                placeholder="Milestone Quantity"
                name="filter-name"
                handleInputChange={updateMilestoneQuantity}
                onBlur={() => {
                  setQuantityFocused(false);
                }}
                onFocus={() => {
                  setQuantityFocused(true);
                }}
                required={true}
                showTooltip={true}
                toolTipText={'The quantity included in the milestone'}
              />
            </Grid>
          </Grid>
          <Grid container item xs={12} md={3}>
            <Grid container className={classes.modificationWrapper}>
              <DecimalInput
                value={getMilestonePriceForDisplay()}
                label="Total Price"
                field={{
                  apiName: 'saveAs',
                  name: 'Save As',
                  type: 'text',
                }}
                placeholder="Milestone total price"
                name="filter-name"
                handleInputChange={updateMilestonePrice}
                onBlur={() => {
                  setPriceFocused(false);
                }}
                onFocus={() => {
                  setPriceFocused(true);
                }}
                required={true}
                showTooltip={true}
                toolTipText={'The total price of the milestone'}
                startAdornment={
                  <InputAdornment position="start" style={{ marginTop: '8px' }}>
                    {currencySymbol(currencyIsoCode, getUserLocale())}
                  </InputAdornment>
                }
              />
              <div className={classes.errorMessage}>
                {priceFocused && !validateMilestonePrice()
                  ? `The price must be less than ${row?.totalPrice}.`
                  : ''}
              </div>
            </Grid>
          </Grid>
        </Grid>
      </div>
    </div>
  );
};

const CreateMilestone: React.FC<Props> = (props: Props) => {
  const classes = useStyles();
  const {
    currencyIsoCode,
    openCreateMilestone,
    handleCloseCreateMilestone,
    activeLineItem,
    handleConfirmMilestones,
  } = props;

  const [scrollToTop, setScrollToTop] = useState(true);
  const [scrollToBottom, setScrollToBottom] = useState(false);
  const [scrollItemsRef, setScrollItemsRef] = useState<HTMLDivElement>();
  const [milestoneItems, setMilestoneItems] = useState<any>([]);
  const [refreshFlag, setRefreshFlag] = useState(1);
  const [allMilestonesTotalQuantity, setAllMilestonesTotalQuantity] = useState(0);
  const [allMilestonesTotalPrice, setAllMilestonesTotalPrice] = useState(0);
  const [allMilestonesTotalPercentage, setAllMilestonesTotalPercentage] = useState(0);

  const { showSnackbar, Snackbar, hideSnackbar } = useRubySnackbar();
  const { getUserLocale } = useUserLocale();

  const updateAllMilestonesTotalQuantity = () => {
    const allQuantity = milestoneItems.reduce((pre: number, cur: Milestone) => {
      return calculateNum(pre, cur.quantity || 0);
    }, 0);
    setAllMilestonesTotalQuantity(allQuantity);
  };

  const updateAllMilestonesTotalPrice = () => {
    const allPrice = milestoneItems.reduce((pre: number, cur: Milestone) => {
      return calculateNum(pre, cur.price || 0);
    }, 0);
    setAllMilestonesTotalPrice(allPrice);
  };

  const updateAllMilestonesTotalPercentage = () => {
    const allPercentage = milestoneItems.reduce((pre: number, cur: Milestone) => {
      return calculateNum(pre, cur.percentage || 0);
    }, 0);
    setAllMilestonesTotalPercentage(allPercentage);
  };

  const updateAllMilestonesDetail = () => {
    updateAllMilestonesTotalQuantity();
    updateAllMilestonesTotalPrice();
    updateAllMilestonesTotalPercentage();
  };

  useEffect(() => {
    updateAllMilestonesDetail();
  }, [milestoneItems]);

  const closeDialog = () => {
    handleCloseCreateMilestone();
    setTimeout(() => {
      setMilestoneItems([]);
      hideSnackbar();
    }, 200);
  };

  const isScrollToTop = (element: HTMLDivElement) => {
    const scrollTop = element.scrollTop;
    if (scrollTop === 0) {
      return true;
    }
    return false;
  };

  const isScrollToBottom = (element: HTMLDivElement) => {
    const scrollTop = element.scrollTop;
    const elementheight = element.clientHeight;
    const scrollHeight = element.scrollHeight;
    if (scrollTop + elementheight >= scrollHeight - 10) {
      return true;
    }
    return false;
  };

  const scrollItemsHandler = _.debounce(() => {
    if (scrollItemsRef) {
      if (isScrollToTop(scrollItemsRef)) {
        setScrollToTop(true);
        setScrollToBottom(false);
        return;
      }
      if (isScrollToBottom(scrollItemsRef)) {
        setScrollToBottom(true);
        setScrollToTop(false);
        return;
      }
      setScrollToTop(false);
      setScrollToBottom(false);
    }
  }, 100);

  useEffect(() => {
    if (openCreateMilestone && scrollItemsRef) {
      // scroll up/down callback
      scrollItemsRef.addEventListener('scroll', scrollItemsHandler);
    }
    return () => {
      if (scrollItemsRef) {
        // avoid memory leak
        scrollItemsRef.removeEventListener('scroll', scrollItemsHandler);
      }
    };
  }, [scrollItemsRef, openCreateMilestone]);

  const initMilestoneStartDate = () => {
    if (milestoneItems.length > 0) {
      return dayjs(milestoneItems[milestoneItems.length - 1].startDate)
        .add(1, 'month')
        .format('YYYY-MM-DD');
    } else {
      return activeLineItem.subscriptionStartDate;
    }
  };

  const initMilestonePercent = () => {
    if (milestoneItems.length > 0) {
      const totalMilestonePercent = milestoneItems.reduce((o: number, m: any) => {
        return calculateNum(o, m.percentage || 0);
      }, 0);
      return calculateNum(100, totalMilestonePercent, '-');
    } else {
      return 100;
    }
  };

  const initMilestoneQuantity = () => {
    if (milestoneItems.length > 0) {
      const totalMilestoneQuantity = milestoneItems.reduce((o: number, m: any) => {
        return calculateNum(o, m.quantity || 0);
      }, 0);
      const quantity = calculateNum(activeLineItem.actualQuantity, totalMilestoneQuantity, '-');
      return quantity < 0 ? 0 : quantity;
    } else {
      return activeLineItem.actualQuantity;
    }
  };

  const initMilestonePrice = () => {
    if (milestoneItems.length > 0) {
      const totalMilestonePrice = milestoneItems.reduce((o: number, m: any) => {
        return calculateNum(o, m.price || 0);
      }, 0);
      return calculateNum(activeLineItem.totalPrice || 0, totalMilestonePrice, '-');
    } else {
      return activeLineItem.totalPrice || 0;
    }
  };

  const initMilestoneItem = () => {
    return {
      id: `${activeLineItem.id}_Milestone # ${milestoneItems.length + 1}`,
      name: `Milestone # ${milestoneItems.length + 1}`,
      quantity: initMilestoneQuantity(),
      actualQuantity: initMilestoneQuantity(),
      price: initMilestonePrice(),
      startDate: initMilestoneStartDate(),
      subscriptionStartDate: initMilestoneStartDate(),
      percentage: initMilestonePercent(),
      isMilestoneNameModifiedByUser: false,
      parentId: activeLineItem.id,
      rootId: activeLineItem.id,
      nameChanged: false,
    };
  };

  const handleClickNewMilestone = () => {
    const item = initMilestoneItem();
    item.id = `${activeLineItem.id}_${item.name}}`;
    milestoneItems.push(item);
    setMilestoneItems([...milestoneItems]);
  };

  useEffect(() => {
    if (activeLineItem.milestones) {
      setMilestoneItems(JSON.parse(activeLineItem.milestones));
    }
  }, [activeLineItem]);

  const updateLastMilestone = () => {
    // the active milestone is not the last milestone
    if (milestoneItems.length >= 1) {
      const lastMilestone = milestoneItems[milestoneItems.length - 1];
      const percentage = milestoneItems
        .filter((m: Milestone, i: number) => i !== milestoneItems.length - 1)
        .reduce((o: number, m: any) => {
          return calculateNum(o, m.percentage || 0);
        }, 0);
      const quantity = milestoneItems
        .filter((m: Milestone, i: number) => i !== milestoneItems.length - 1)
        .reduce((o: number, m: any) => {
          return calculateNum(o, m.quantity || 0);
        }, 0);
      const price = milestoneItems
        .filter((m: Milestone, i: number) => i !== milestoneItems.length - 1)
        .reduce((o: number, m: any) => {
          return calculateNum(o, m.price || 0);
        }, 0);
      lastMilestone.percentage = calculateNum(100, percentage, '-');
      lastMilestone.quantity = calculateNum(activeLineItem.actualQuantity, quantity, '-');
      lastMilestone.price = calculateNum(activeLineItem.totalPrice!, price, '-');
    }
  };

  const updateAllMilestoneDefaultName = () => {
    if (milestoneItems.length > 0) {
      milestoneItems.forEach((n: Milestone, i: number) => {
        if (!n.nameChanged) {
          n.name = `Milestone # ${i + 1}`;
        }
        if (n.id?.includes('_')) {
          n.id = `${activeLineItem.id}_${n.name}}`;
        }
      });
    }
  };

  const handleDeleteMilestone = (index: number) => {
    if (milestoneItems.length === 1 && activeLineItem.isChange) {
      return;
    }
    milestoneItems.splice(index, 1);
    updateLastMilestone();
    updateAllMilestoneDefaultName();
    setMilestoneItems([...milestoneItems]);
  };

  const allocateMilestoneEvenly = () => {
    if (milestoneItems.length > 0) {
      milestoneItems.forEach((m: any, i: number) => {
        m.percentage = Math.floor(calculateTimes(100, 1 / milestoneItems.length) * 100) / 100;
        m.quantity =
          Math.floor(calculateTimes(activeLineItem.quantity, m.percentage / 100) * 100) / 100;
        m.price =
          Math.floor(calculateTimes(activeLineItem.totalPrice || 0, m.percentage / 100) * 100) /
          100;

        m.startDate =
          i === 0
            ? activeLineItem.subscriptionStartDate
            : dayjs(milestoneItems[i - 1].startDate)
                .add(1, 'month')
                .format('YYYY-MM-DD');
      });
      updateLastMilestone();
      setMilestoneItems([...milestoneItems]);
    }
  };

  const validateAllMilestonesStartDate = (updatedMilestoneItems: Milestone[]): boolean => {
    let validateRes = true;
    try {
      updatedMilestoneItems.forEach((m: Milestone, i: number) => {
        if (i === 0 && activeLineItem.subscriptionStartDate) {
          if (+new Date(m.startDate) < +new Date(activeLineItem.subscriptionStartDate)) {
            showSnackbar(
              'error',
              "Oops! It's not done yet.",
              `The milestone date of ${m.name}  must be equal to or greater than ${activeLineItem.subscriptionStartDate}`,
            );
            validateRes = false;
            throw new Error('validate error');
          }
        } else if (
          updatedMilestoneItems.length > 1 &&
          +new Date(m.startDate) < +new Date(updatedMilestoneItems[i - 1].startDate)
        ) {
          showSnackbar(
            'error',
            "Oops! It's not done yet.",
            `The milestone date of ${m.name}  must be equal to or greater than ${
              updatedMilestoneItems[i - 1].startDate
            }`,
          );
          validateRes = false;
          throw new Error('validate error');
        }
      });
    } catch (e) {
      throw e;
    }
    return validateRes;
  };

  return (
    <DialogComponent width="md" open={openCreateMilestone} title=" " handleClose={closeDialog}>
      <Grid container alignItems="center" className={classes.titleWrapper}>
        <Typography variant="h2" className={classes.title}>
          Milestones
        </Typography>
        <div className={classes.rightArrowWrapper}>
          <NavigateNextIcon />
        </div>
        <Typography variant="h2" className={classes.lineItemName}>
          {activeLineItem.productName}
        </Typography>
      </Grid>
      <Grid container justifyContent="space-between" className={classes.milestoneSumContainer}>
        <div>
          <Typography className={classes.sumLabel}>START DATE</Typography>
          <Grid container alignItems="center">
            <Typography className={classes.sumValue}>
              {dayjs(activeLineItem.subscriptionStartDate).format('YYYY-MM-DD')}
            </Typography>
          </Grid>
        </div>
        <div className={classes.relative}>
          <Typography className={classes.sumLabel}>QUANTITY</Typography>
          <Grid container alignItems="center">
            <Typography className={classes.sumValue}>{activeLineItem.actualQuantity}</Typography>
            {allMilestonesTotalQuantity !== activeLineItem.actualQuantity ? (
              <Typography className={classes.valueSplit}>
                {'(' + allMilestonesTotalQuantity + ')'}
              </Typography>
            ) : null}
          </Grid>
        </div>
        <div className={classes.relative}>
          <Typography className={classes.sumLabel}>TOTAL PRICE</Typography>
          <Grid container alignItems="center">
            <Typography className={classes.priceValue}>
              {convertToCurrency(activeLineItem.totalPrice || 0, currencyIsoCode, getUserLocale())}
            </Typography>
            {allMilestonesTotalPrice !== activeLineItem.totalPrice ? (
              <Typography className={classes.valueSplitError}>
                {'(' +
                  convertToCurrency(allMilestonesTotalPrice, currencyIsoCode, getUserLocale()) +
                  ')'}
              </Typography>
            ) : null}
          </Grid>
        </div>
      </Grid>
      <Grid
        container
        justifyContent="flex-end"
        className={
          milestoneItems.length >= 2 && !scrollToTop
            ? classes.newMilestoneContainer
            : classes.milestoneContainer
        }
      >
        <RubyButton
          text="New Milestone"
          startIcon={<AddIcon />}
          onClick={handleClickNewMilestone}
        />
      </Grid>
      <Grid
        className={classes.scrollContainer}
        ref={(dom: HTMLDivElement) => setScrollItemsRef(dom)}
        id="scrollItemsRef"
      >
        {milestoneItems.map((milestoneItem: any, index: number) => (
          <div
            style={{
              marginTop: `${index === 0 ? 0 : '16px'}`,
              marginBottom: 0,
            }}
            key={index}
          >
            <MilestoneItem
              currencyIsoCode={currencyIsoCode}
              milestoneItem={milestoneItem}
              milestoneItems={milestoneItems}
              row={activeLineItem}
              updateAllMilestonesDetail={updateAllMilestonesDetail}
              setRefreshFlag={setRefreshFlag}
              refreshFlag={refreshFlag}
              index={index}
              handleDeleteMilestone={handleDeleteMilestone}
              setMilestoneItems={setMilestoneItems}
              allMilestonesTotalPercentage={allMilestonesTotalPercentage}
              allMilestonesTotalPrice={allMilestonesTotalPrice}
            />
          </div>
        ))}
      </Grid>
      <div style={{ marginRight: '-48px' }}>
        <RubyButtonBar
          variant="inPlace"
          style={
            milestoneItems.length >= 2 && !scrollToBottom
              ? {
                  paddingTop: '24px',
                  marginRight: '-24px',
                  marginLeft: '-24px',
                  paddingLeft: '24px',
                  paddingRight: '24px',
                  boxShadow: '0 -12px 20px 0 rgb(0 0 0 / 2%)',
                  width: '100%',
                }
              : {
                  paddingTop: '24px',
                  marginRight: '-24px',
                  marginLeft: '-24px',
                  paddingLeft: '24px',
                  paddingRight: '24px',
                  width: '100%',
                }
          }
          leftButtons={[
            {
              text: 'Cancel',
              onClick: () => {
                closeDialog();
              },
            },
          ]}
          rightButtons={[
            {
              text: 'Confirm',
              onClick: () => {
                if (
                  allMilestonesTotalQuantity !== activeLineItem.actualQuantity &&
                  allMilestonesTotalQuantity !== 0
                ) {
                  showSnackbar(
                    'error',
                    "Oops! It's not done yet.",
                    'Please distribute the entire quantity of the line to the milestones.  ',
                  );
                  return;
                }
                if (
                  allMilestonesTotalPrice !== activeLineItem.totalPrice &&
                  allMilestonesTotalPrice !== 0
                ) {
                  showSnackbar(
                    'error',
                    "Oops! It's not done yet. ",
                    'Please distribute the entire total price of the line to the milestones. ',
                  );
                  return;
                }
                const updatedMilestoneItems =
                  milestoneItems.length > 0
                    ? milestoneItems
                        .map((m: Milestone) => {
                          return {
                            ...m,
                            subscriptionStartDate: m.startDate,
                            totalPrice: m.price,
                            actualQuantity: m.quantity,
                          };
                        })
                        .sort((a: Milestone, b: Milestone) => {
                          return +new Date(a.startDate) > +new Date(b.startDate);
                        })
                    : [];
                let validateRes = true;
                if (updatedMilestoneItems.length > 0) {
                  validateRes = validateAllMilestonesStartDate(updatedMilestoneItems);
                }
                if (validateRes) {
                  handleConfirmMilestones(updatedMilestoneItems);
                  setTimeout(() => {
                    hideSnackbar();
                  }, 200);
                }
              },
            },
            {
              text: 'Allocate Evenly',
              onClick: async () => {
                allocateMilestoneEvenly();
                setTimeout(() => {
                  hideSnackbar();
                }, 200);
              },
            },
          ]}
        />
      </div>
      <Snackbar />
    </DialogComponent>
  );
};

export default CreateMilestone;

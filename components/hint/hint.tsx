import React, { useState } from 'react';
import { usePopper } from 'react-popper';

import { makeStyles } from '@material-ui/core/styles';
import CloseIcon from '@material-ui/icons/Close';
import { FlexDirectionProperty } from 'csstype';

import HintIcon from './icons';
import { HintProps, Placement } from './interface';

const defaultProps: Partial<HintProps> = {
  hintIcon: (
    <HintIcon
      style={{
        width: '32px',
        height: '32px',
      }}
    />
  ),
  placement: 'bottom',
};
const defaultBgColor = '#8748FC';

const useStyles = makeStyles(() => ({
  root: {
    margin: '16px 16px 32px 24px',
    backgroundColor: '#8748FC',
    boxShadow: '0 32px 46px -20px rgba(35,8,86,0.32)',
    borderRadius: '10px',
    display: 'flex',
  },
  content: {
    padding: '16px 16px 32px 24px',
    backgroundColor: '#8748FC',
    boxShadow: '0 32px 46px -20px rgba(35,8,86,0.32)',
    borderRadius: '10px',
    display: 'flex',
    flexDirection: 'column',
    margin: '10px',
  },
  hintIcon: {
    width: '32px',
    height: '32px',
  },
  title: {
    color: '#FFFFFF',
    fontSize: '14px',
    fontWeight: 500,
    letterSpacing: '-0.33px',
    lineHeight: '18px',
    opacity: 0.7,
    paddingTop: '10px',
  },
  closeBtn: {
    display: 'flex',
    flexDirection: 'row-reverse',
    '& > svg': {
      width: '16px',
      height: '16px',
      color: '#FFFFFF',
      opacity: 0.7,
    },
  },
  message: {
    color: '#FFFFFF',
    fontSize: '12px',
    fontWeight: 500,
    letterSpacing: '-0.33px',
    lineHeight: '18px',
    paddingTop: '10px',
    paddingBottom: '10px',
  },
  arrowUp: {},
}));

export const Hint: React.FC<HintProps> = (userProps: HintProps) => {
  const props = { ...defaultProps, ...userProps };
  const { message, title, backgroundColor, open, anchorEl, action, onClose, hintIcon, placement } =
    props;
  const classes = useStyles();
  const [popperElement, setPopperElement] = useState<HTMLDivElement | null>(null);
  const [arrowElement, setArrowElement] = useState<HTMLDivElement | null>(null);

  const { styles, attributes } = usePopper(anchorEl, popperElement, {
    modifiers: [{ name: 'arrow', options: { element: arrowElement } }],
    placement: placement,
  });

  const getArrowStyle = React.useCallback(
    (placement?: Placement) => {
      const arrowUpStyle = {
        borderLeft: '10px solid transparent',
        borderRight: '10px solid transparent',
        borderBottom: `10px solid ${backgroundColor || defaultBgColor}`,
        top: '-4px',
      };

      const arrowLeftStyle = {
        borderTop: '10px solid transparent',
        borderRight: `10px solid ${backgroundColor || defaultBgColor}`,
        borderBottom: `10px solid transparent`,
        left: '-4px',
      };

      const arrowRightStyle = {
        borderTop: '10px solid transparent',
        borderLeft: `10px solid ${backgroundColor || defaultBgColor}`,
        borderBottom: `10px solid transparent`,
        right: '-4px',
      };

      const arrowDownStyle = {
        borderLeft: '10px solid transparent',
        borderRight: '10px solid transparent',
        borderTop: `10px solid ${backgroundColor || defaultBgColor}`,
        bottom: '-4px',
      };

      switch (placement) {
        case 'bottom':
          return arrowUpStyle;
        case 'left':
          return arrowRightStyle;
        case 'right':
          return arrowLeftStyle;
        default:
          return arrowDownStyle;
      }
    },
    [backgroundColor],
  );

  const Arrow = React.useMemo(() => {
    const actualPlacement: Placement | undefined =
      (attributes &&
        attributes.popper &&
        (attributes.popper['data-popper-placement'] as Placement)) ||
      placement;
    if (!actualPlacement) return null;
    return (
      <div
        ref={setArrowElement}
        style={{
          width: 0,
          height: 0,
          ...styles.arrow,
          ...getArrowStyle(actualPlacement),
        }}
      />
    );
  }, [attributes, placement, getArrowStyle, styles.arrow]);

  if (!open) {
    return null;
  }

  // adjust arrow position
  let popperFlexDirection: FlexDirectionProperty = 'column';
  if (placement === 'left') {
    popperFlexDirection = 'row-reverse';
  }

  return (
    <div
      ref={setPopperElement}
      style={{
        display: 'flex',
        flexDirection: popperFlexDirection,
        zIndex: 100,
        ...styles.popper,
      }}
      {...attributes.popper}
    >
      {Arrow}
      <div
        className={classes.content}
        style={{
          width: '271px',
          marginTop: '10px',
          backgroundColor: backgroundColor || defaultBgColor,
        }}
      >
        {onClose && (
          <div className={classes.closeBtn}>
            <CloseIcon onClick={onClose} />
          </div>
        )}
        {hintIcon}
        {title && <div className={classes.title}>{title}</div>}
        <div className={classes.message}>{message}</div>
        {action}
      </div>
    </div>
  );
};

export default Hint;

import type {
  ChangeGroup,
  ChangeItem,
  QueryExecutor,
  RightRevIntegration,
  RubyField,
  RubyObject,
  TabFilter,
  TaxDetail,
  TaxIntegrationResponse,
  change_cart,
  ruby_settings,
} from '@nue-apps/ruby-ui-component';
import type { ChangeItemRequest, ExpressCheckoutRequest } from '../change-cart/interface';
import type {
  AccountHierarchy,
  IntegrationResult,
  SyncRequest,
  SyncResult,
} from '../customer-view-page';
import type {
  GeneralInformationAccount,
  SubscriptionSteamData,
} from '../general-information-summary-view/interface';
import type {
  InvoiceByOrderItem,
  MilestonesChange,
} from '../manage-milestone-dialog/manage-milestone-dialog';
import type { CountQueryExecutor } from '../query-executor';
import type {
  BundleProduct,
  CreditStatistics,
  InvoicePeriods,
  LineItem,
  Subscription,
} from '../revenue-builder-types';
import type { CreditTypes } from '../ruby-settings';
import { ProductRelations } from '../modals/subscription-bulkactions/types';

type NavigateToQuoteBuilderRequest = change_cart.NavigateToQuoteBuilderRequest;
type RubySettings = ruby_settings.RubySettings;

export interface Props {}

export type TabMode = 'Order' | 'Subscription' | 'Entitlement' | 'Asset';

export interface CustomerViewProps {
  executeQuery: QueryExecutor;
  executeGetCountQuery: CountQueryExecutor;
  subscriptionMetadata: RubyObject;
  assetMetadata: RubyObject;
  orderMetadata: RubyObject;
  orderItemMetadata: RubyObject;
  getInvoiceIdsForAsset: (params: { assetNumbers: string[]; assetType: string }) => any;
  getInvoicesByOrderItemIds: (params: {
    orderItemIds: string[];
  }) => Promise<Record<string, InvoiceByOrderItem[]>>;
  getSwapUpgradeDowngradeOptions: (params: {
    subscriptionIds: string[];
  }) => Promise<ProductRelations>;
  UpdateMileStonesStartDate: (params: { orderItems: MilestonesChange[] }) => Promise<void>;
  getOrderProductIdsByAssetId: (params: { assetId: string }) => Promise<string[]>;
  loadInvoicePeriods: () => Promise<InvoicePeriods | null>;
  loadCreditStatistics: (customerId: string) => Promise<CreditStatistics | null>;
  creditCashOut: (
    accountPoolId: string,
    includeExpiredCredits: boolean,
    amount: number,
  ) => Promise<void>;
  getPanelStatistics: (credit: { AccountCreditPoolId: string }) => string;
  changeHistoryMetadata: RubyObject;
  quoteMetadata: RubyObject;
  opportunityMetadata: RubyObject;
  userMetadata: RubyObject;
  openChangeCart: boolean;
  customerMetadata: RubyObject;
  currencyIsoCode: string;
  changeItemRequests: ChangeItemRequest[];
  handleOpenChangeCart: (open: boolean) => void;
  changeGroups: Record<string, ChangeGroup>;
  handleAddToCart: (changeGroup: ChangeGroup, changeItems: ChangeItem[]) => Promise<void>;
  handleAddMultipleGroupsToCart: (
    items: { changeGroup: ChangeGroup; changeItems: ChangeItem[] }[],
  ) => Promise<void>;
  handleRemoveChangesFromCart: (id: string) => Promise<void>;
  handleRemoveChangeItems: (changeGroupId: string, changeItemIds: string[]) => Promise<void>;
  handleRemoveMultipleGroupsFromCart: (ids: string[]) => Promise<void>;
  getFieldSetMetadata: (fieldSetName: string, objectApiName: string) => Promise<RubyField[]>;
  navigateToQuoteBuilder: ({
    mode,
    recordId,
    lineObject,
    changeItemRequests,
  }: NavigateToQuoteBuilderRequest) => Promise<void>;
  navigateToCustomerViewPage: (
    objectApiName: string,
    objectId: string,
    orderId?: string,
  ) => Promise<void>;
  personPlaceholderImage: string;
  tabFilters?: (Partial<TabFilter> | null)[];
  rubySettings: RubySettings;
  getSumOrderProductQuantityAtDate: (
    currentDate: Date,
    subscriptionNumber: string,
    subscriptionVersion: number,
  ) => Promise<number>;
  getSubscriptionsForCoterm: (
    lineEditorStartDate: Date,
    customerId: string | undefined,
  ) => Promise<Subscription[]>;
  entitlementMetadata: RubyObject;
  productOptionMetadata: RubyObject;
  invoiceMetadata: RubyObject;
  creditMemosMetaData: RubyObject;
  paymentMethodsMetaData: RubyObject;
  loadUsageMetadata: () => Promise<RubyObject>;
  loadCreditMetadata: () => Promise<RubyObject>;
  transactionHubMetadata: RubyObject;
  orderId?: string;
  defaultTab?: string;
  getRevenueContracts: (
    search: string | null,
    filter: string | null,
  ) => Promise<RevenueContractsResult>;
  getRevenueIntegration: () => Promise<RightRevIntegration>;
  getCreditTypes: () => Promise<CreditTypes[]>;
  getRelatedBillingAccountsIdOfSalesAccount: ({
    salesAccountId,
  }: {
    salesAccountId: string;
  }) => Promise<string[]>;
  getAccountHierarchy: (id: string) => Promise<AccountHierarchy>;
  loadInvoicesPreviewData?: (option: string, date?: string) => Promise<any>;
  handleSaveInvoicePreview?: (data: any) => Promise<void>;
  loadSubscriptionStreamData?: () => Promise<SubscriptionSteamData>;
  loadChildernAndBillingAccounts?: () => Promise<GeneralInformationAccount>;
  loadStripeCardInfo?: (customerIds: string[]) => Promise<any>;
  getTaxBreakdown?: (id: string) => Promise<TaxDetail | null>;
  taxConfig: TaxIntegrationResponse | null;
  sync?: (request: SyncRequest) => Promise<SyncResult>;
  integrations?: IntegrationResult[];
  checkOut?: (request: ExpressCheckoutRequest) => Promise<any>;
}

export interface Quote {
  id: string;
  name: string;
}

export interface Order {
  id: string;
  name: string;
  status: string;
  ownerId: string;
  owner: {
    id: string;
    name: string;
    imageSignedUrl: string;
  };
  ownerNumber: string;
  orderPlaced?: Date;
  orderTCV?: number;
  orderACV?: number;
  nextInvoiceDate?: Date;
  soldToAddress?: string;
  number: string;
}

export interface Asset {
  assetLevel?: number;
  assetProvidedById?: string;
  assetServicedById?: string;
  availability?: number;
  averageUptimePerDay?: number;
  contactId?: string;
  createdById?: string;
  createdDate?: Date;
  customerId?: string;
  description?: string;
  digitalAssetStatus?: string;
  externalId?: string;
  id: string;
  installDate?: Date;
  internalAsset?: boolean;
  isCompetitorProduct?: boolean;
  lastModifiedById?: string;
  lastModifiedDate?: Date;
  locationId?: string;
  manufactureDate?: Date;
  name: string;
  parentAssetId?: string;
  price?: number;
  productCode?: string;
  productDescription?: string;
  productFamily?: string;
  productId?: string;
  purchaseDate?: Date;
  quantity?: number;
  reliability?: number;
  rootAssetId?: string;
  serialNumber?: string;
  sku?: string;
  status?: string;
  statusReason?: string;
  sumDowntime?: number;
  sumUnplannedDowntime?: number;
  uniqueIdentifier?: string;
  uptimeRecordEnd?: Date;
  uptimeRecordStart?: Date;
  usageEndDate?: Date;
  product: BundleProduct;
}

export interface AssetOrderProduct {
  assetId: string;
  assetNumber?: string;
  name: string;
  assetType: 'Subscription' | 'Asset' | 'Entitlement';
  orderProductId: string;
  orderProduct: LineItem;
  subscriptionNumber?: string;
  serviceEntitlementNumber?: string;
  summaryLineItemId?: string;
}

export interface Opportunity {
  id: string;
  customerId: string;
  stageName: string;
}

export interface RevenueContract {
  allocation_type: string;
  amendment_version: string;
  billed: number;
  ca_cl_position: string;
  created_date: string;
  created_period: string;
  customer: string;
  doc_type: string;
  isonhold: boolean;
  mje_id: string;
  modified_date: string | null;
  modified_period: string | null;
  name: string;
  prior_rc_version: number;
  recognized: number;
  scheduled: number;
  stage: string | null;
  transactional_currency_code: string;
  value: number;
  version: number;
}

export interface RevenueContractsResult {
  count: number;
  message: string;
  total_billed_revenue: number;
  total_recognized_revenue: number;
  total_revenue: number;
  total_scheduled_revenue: number;
  data_items: RevenueContract[];
}

export type SubscriptionTableItem = Subscription & {
  children?: SubscriptionTableItem[];
  disabled?: boolean;
};

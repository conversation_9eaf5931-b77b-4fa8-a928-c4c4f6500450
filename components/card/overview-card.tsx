import React from 'react';

import { IconButton, Typography } from '@material-ui/core';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';

import SettingsIcon from '../settings-icon';

const defaultProps = {};

const useStyles = makeStyles({
  card: {
    backgroundColor: '#fff',
    width: '235px',
    height: '300px',
    display: 'flex',
    flexDirection: 'column',
  },
  container: {
    position: 'relative',
    display: 'flex',
    height: '100%',
    fontFamily: 'inherit',
  },
  contentContainer: {
    display: 'flex',
  },
  content: {
    padding: '20px',
    height: '100%',
    flexDirection: 'column',
    backgroundColor: '#fff',
    display: 'flex',
    borderRadius: 'calc(20px - 10px)',
    borderStyle: 'solid',
    borderColor: '#EEEEEE',
  },
  cardView: {
    display: 'flex',
    flexDirection: 'column',
  },
  headercontainer: {
    height: '32px',
  },
  imgContainer: {
    display: 'flex',
    justifyContent: 'center',
  },
  title: {
    opacity: '0.7',
    fontSize: '20px',
    margin: 0,
    fontWeight: 'bold',
    color: '#000000',
  },
  description: {
    opacity: '0.5',
    color: '#000000',
    fontSize: '12px',
    fontWeight: 500,
    paddingTop: '8px',
    paddingBottom: '12px',
    display: '-webkit-box' /* Fallback for non-webkit */,
    height: '32px' /* Fallback for non-webkit, line-height * 2 */,
    lineHeight: '1.3em',
    '-webkit-line-clamp':
      '3' /* if you change this, make sure to change the fallback line-height and height */,
    '-webkit-box-orient': 'vertical',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  triangle: {
    position: 'absolute',
    width: 0,
    height: 0,
    left: '235px',
    borderStyle: 'solid',
    borderWidth: '0 10px 10px 0',
    borderColor: 'transparent transparent #B095A7 transparent',
    top: '15px',
    zIndex: 90,
  },
  rectangle: {
    position: 'absolute',
    top: '25px',
    left: '145px',
    width: '100px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 90,
    fontSize: '.5rem',
    textTransform: 'uppercase',
    borderRadius: '100px 0 0 100px',
    height: '24px',
    fontWeight: 'bold',
    boxShadow: '0px 4px 10px 0px gainsboro',
  },
  statusContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  titleContainer: {
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
    fontSize: '18px',
    marginTop: '25px',
  },
  subtitle: {
    fontWeight: 500,
    fontSize: '12px',
    opacity: 0.3,
    color: '#000000',
  },
});
export interface Props {
  status: string;
  getIcon?: () => JSX.Element;
  title: string;
  description?: string;
  statusStyle: React.CSSProperties;
  statusShadowStyle: React.CSSProperties;
  activeBorderStyle?: React.CSSProperties;
  onClick?: (navCategory?: string) => void;
  navCategory?: string;
}

export const OverviewCard: React.FC<Props> = (userProps: Props) => {
  const props = { ...defaultProps, ...userProps };

  const {
    status,
    title,
    description,
    statusStyle,
    statusShadowStyle,
    activeBorderStyle,
    getIcon,
    onClick,
    navCategory,
  } = props;

  const classes = useStyles();

  return (
    <div className={classes.container} role="card">
      <div className={classes.contentContainer}>
        <div className={classes.card}>
          <Grid className={classes.content} style={activeBorderStyle}>
            <div className={classes.cardView} role="card-header">
              <div className={classes.headercontainer}>
                <div className={classes.triangle} style={statusShadowStyle} />
                <div className={classes.rectangle} style={statusStyle}>
                  <div className={classes.statusContainer} role="status">
                    <span>{status === 'Connected' ? status : 'Not Connected'}</span>
                  </div>
                </div>
              </div>
              <div className={classes.imgContainer}>
                <div>{getIcon && getIcon()}</div>
              </div>
              <div className={classes.titleContainer}>
                <Typography component="h2" className={classes.title}>
                  {title}
                </Typography>
                <Typography className={classes.description} component="p">
                  {description}
                </Typography>
              </div>
              <div style={{ display: 'flex', justifyContent: 'flex-end', minHeight: 48 }}>
                {navCategory && (
                  <IconButton
                    component="span"
                    onClick={() => {
                      if (onClick) {
                        onClick(navCategory);
                      }
                    }}
                  >
                    <SettingsIcon />
                  </IconButton>
                )}
              </div>
            </div>
          </Grid>
        </div>
      </div>
    </div>
  );
};

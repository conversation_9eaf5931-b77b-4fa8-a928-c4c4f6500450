import React, { CSSProperties } from 'react';
import { Input } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles({
  inlineInput: {
    textAlign: 'center',
  },
});

interface InlineInputProps {
  name: string;
  type: string;
  value: string;
  inputProps: {
    min?: string;
    step?: string;
    type?: string;
  };
  handleInputChange: (newValue: string) => void;
  style?: CSSProperties;
}

const InlineInput: React.FC<InlineInputProps> = ({
  name,
  type,
  value,
  inputProps,
  handleInputChange,
  style,
}) => {
  const classes = useStyles();
  return (
    <Input
      classes={{
        input: classes.inlineInput,
      }}
      name={name}
      type={type}
      value={value}
      inputProps={inputProps}
      onChange={(event) => {
        const value = event.target.value;
        handleInputChange(value);
      }}
      style={{ ...style }}
    />
  );
};

export default InlineInput;

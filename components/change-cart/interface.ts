import type { CardA<PERSON>, PriceBook, RubyObject } from '../metadata/interface';
import { ProductInfoOption } from '../modals/subscription-bulkactions/types';
import type QueryExecutor from '../query-executor';
import type {
  ChangeType,
  LineObject,
  RootChangeOrderPriceCalculationResult,
  Subscription,
} from '../revenue-builder-types';
import type { RubySettings } from '../ruby-settings/interface';

export interface Props {
  changeGroups: Record<string, ChangeGroup>;
  open: boolean;
  quoteMetadata: RubyObject;
  orderMetadata: RubyObject;
  opportunityMetadata: RubyObject;
  executeQuery: QueryExecutor;
  handleOpenChangeCart: (open: boolean) => void;
  handleRemoveChangesFromCart: (id: string) => Promise<void>;
  handleRemoveMultipleGroupsFromCart: (ids: string[]) => Promise<void>;
  handleRemoveChangeItems: (changeGroupId: string, changeItemIds: string[]) => Promise<void>;
  navigateToQuoteBuilder: ({
    mode,
    recordId,
    lineObject,
    changeItemRequests,
  }: NavigateToQuoteBuilderRequest) => Promise<void>;
  rubySettings: RubySettings;
  customerId?: string;
  childrenCustomerIds?: string[];
  checkOut?: (request: ExpressCheckoutRequest) => Promise<any>;
  handleNavigateToOrder: () => void;
}

export interface ChangeGroup {
  id: string;
  name: string;
  priceBook: PriceBook;
  currencyIsoCode: string;
  customer: {
    id: string;
    name: string;
  };
  changeItems?: ChangeItem[];
}

export interface ChangeItem {
  id: string;
  time: string;
  subscriptionMetadata: RubyObject;
  cardAction: CardAction;
  request: ChangeRequest;
  asset: Subscription;
  parentSubscription?: Subscription | null;
  requestWasEdited: boolean;
  newValues?: any;
  objects?: any;
  subscriptions?: any;
  milestoneIds?: string[];
}

export interface CheckoutDialogProps {
  open: boolean;
  executeQuery: QueryExecutor;
  orderMetadata: RubyObject;
  quoteMetadata: RubyObject;
  opportunityMetadata: RubyObject;
  checkedOutChangeGroup?: ChangeGroup;
  handleOpen: (newOpen: boolean) => void;
  handleOpenChangeCart: (open: boolean) => void;
  navigateToQuoteBuilder: ({
    mode,
    recordId,
    lineObject,
    changeItemRequests,
    changeGroupId,
  }: NavigateToQuoteBuilderRequest) => Promise<void>;
  rubySettings: RubySettings;
}

export interface ExpressCheckoutDialogProps {
  open: boolean;
  onClose: () => void;
  handleExpressCheckout: (options: { orderStatusOption: string; includeChildren: boolean }) => void;
}

export interface NavigateToQuoteBuilderRequest {
  mode: string;
  recordId: string | null;
  lineObject: LineObject;
  changeItemRequests: ChangeItemRequest[];
  changeGroupId: string;
}

export interface ChangeItemRequest {
  /**
   * This field is used to denote which ChangeItemText component to render and which action is being applied to the Subscription / Asset / Entitlement
   */
  changeType: ChangeType;
  objectType: 'Asset' | 'Subscription' | 'Entitlement';
  assetName: string;
  assetNumber?: string;
  priceBookId: string;
  label?: string;

  updateToEvergreen?: boolean;
  startDate?: string;
  subscriptionStartDate?: string;
  evergreen?: boolean;
}

export interface RenewalRequest extends ChangeItemRequest {
  renewalTerm: number;
  renewalDate: string | null;
  termDimension?: string;
}

export interface UpdateQuantityRequest extends ChangeItemRequest {
  quantity: number;
  subscriptionStartDate: string;
  subscriptionEndDate: string;
  renewalDate?: string;
}

export interface RenewalUpdateQuantityRequest extends ChangeItemRequest {
  renewalTerm: number;
  renewalDate: string | null;
  termDimension?: string;
  quantity: number;
  subscriptionStartDate: string;
}

export interface UpdateMilestoneRequest extends ChangeItemRequest {
  name: string;
  subscriptionStartDate: string;
  milestoneIds: string[];
  quantity?: number;
}

export interface UpdateTermRequest extends ChangeItemRequest {
  subscriptionTerm: number;
  subscriptionEndDate: string;
  subscriptionStartDate: string;
  changeAction: string;
  shouldProrateCredits?: boolean;

  updateRenewalTerm?: boolean;
  renewalTerm?: number;
  termDimension?: string;
}

export interface CancellationRequest extends ChangeItemRequest {
  cancellationDate: string;
  action: 'today' | 'afterSubscriptionTermEnds' | 'onSpecificDate';
  shouldProrateCredits?: boolean;
}

export interface ReconfigureRequest extends ChangeItemRequest {
  startDate: string;
}

export interface BulkCotermRequest extends ChangeItemRequest {
  subscriptionEndDate: string;
  subscriptionStartDate: string;
  shouldProrateCredits?: boolean;
}

export interface AdjustPriceRequest extends ChangeItemRequest {
  applyToAll?: boolean;
  endDateRadio?: string;
  percent?: number;
  percentAction?: string;
  price?: number;
  priceRadio: string;
  specificDate?: string;
  startDate: string;
  untilAmount?: number;
}

export interface ConvertFreeTrialRequest extends ChangeItemRequest {
  term: number;
  termDimension: string;
  overrideTrialEnd: boolean;
  switchToEvergreen: boolean;
  cotermToBundleSubscription: boolean;
  coTermDate?: string;
  uomDimension: string;
}

export interface UpgradeRequest extends ChangeItemRequest {
  toProduct: ProductInfoOption;
  quantity: number;
  startDate: string;
  withUom: string;
  priceTags: string[];
}

export interface DowngradeRequest extends ChangeItemRequest {
  toProduct: ProductInfoOption;
  quantity: number;
  startDate: string;
  withUom: string;
}

export interface SwapRequest extends ChangeItemRequest {
  toProduct: ProductInfoOption;
  quantity: number;
  startDate: string;
  withUom: string;
  samePriceSwap: boolean;
}

export type ChangeRequest =
  | ChangeItemRequest
  | AdjustPriceRequest
  | RenewalRequest
  | UpdateQuantityRequest
  | RenewalUpdateQuantityRequest
  | UpdateTermRequest
  | CancellationRequest
  | ReconfigureRequest
  | BulkCotermRequest
  | UpdateMilestoneRequest
  | ConvertFreeTrialRequest
  | UpgradeRequest
  | DowngradeRequest
  | SwapRequest;

export interface GenerateLineItemFromSubscriptionProps {
  subscription: Subscription;
  changeItem: ChangeItem;
  order: number;
  lineType: 'RampItem' | 'LineItem' | 'SummaryItem';
  rootId?: string;
  parentId?: string;
  id: string;
}

export interface GenerateLineItemFromChangeItemActionProps {
  changeItem: ChangeItem;
  order: number;
  rootId: string;
  parentId: string;
}

export interface GenerateLineItemsFromChangeOrderPriceCalculation {
  changeOrderCalculation: RootChangeOrderPriceCalculationResult;
  changeItem: ChangeItem;
  order: number;
}

export interface ExpressCheckoutRequest {
  options: ExpressCheckoutOptions;
  bulkChangeOrder?: boolean;
  taxRates?: { productSku: string; taxPercentage: number }[];
  assetChanges: ChangeItemRequest[];
  subscriptionChanges?: ChangeItemRequest[];
}

export interface ExpressCheckoutOptions {
  activateOrder: boolean;
  proceedOption: SubscriptionChangeProceedOption;
  generateInvoice?: boolean;
  disableDetailResponse?: boolean;
  opportunityId?: string;
  nueOrderId?: string;
  containedObjectId?: string;
}

export type SubscriptionChangeProceedOption =
  | 'UseExistingQuote'
  | 'UseExistingOrder'
  | 'CreateOrder'
  | 'CreateQuote';

import React from 'react';

import type { RubyFormControlProps } from '../form-field';
import type {
  AfterFieldChangeListener,
  GetLookupOptionByValue,
  LookupOptionsLoader,
  RubyField,
} from '../metadata/interface';

// todo
export type FieldType = string;
export type FieldTypeMap = Record<FieldType, React.FC<RubyFormControlProps>>;

export interface WithHiddenFields {
  hiddenFields: string[];
  setHiddenFields: React.Dispatch<React.SetStateAction<string[]>>;
}

export interface CustomFormControlProps extends RubyFormControlProps {
  afterFieldChange?: AfterFieldChangeListener;
}

export interface DefaultProps {
  title?: string;
  caption?: string;
  fields?: RubyField[];
  values?: Record<string, any>;
  lookupOptionsLoader?: LookupOptionsLoader;
  getLookupOptionByValue?: GetLookupOptionByValue;
  afterFieldChange?: AfterFieldChangeListener;
  mode?: 'create' | 'edit';
  defaultDateFormat?: string;
  showDivider?: boolean;
  error?: {
    caption?: string;
    message: string;
  };
  titleStyle?: React.CSSProperties;
  locale?: string;
  titleIcon?: React.ReactNode;
}

export const IgnoredFieldsContext: React.Context<string[]> = React.createContext(['']);

export interface Props extends DefaultProps, WithHiddenFields {}

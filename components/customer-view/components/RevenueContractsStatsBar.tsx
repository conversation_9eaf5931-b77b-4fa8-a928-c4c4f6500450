import React, { memo } from 'react';
import { StatsBarData } from '../../ruby-list-view/interface';
import { StatsBarChart, StatsBarChartItem } from '../../stats-bar-chart/stats-bar-chart';
import { formatNumberForDisplay } from '../../list/list';
import { calculateBarItemWidth } from '../utils/utils';
import { useStyles } from '../styles';

interface RevenueContractsStatsBarProps {
  statsBarData: StatsBarData | undefined;
  currencyIsoCode: string;
  locale: string | undefined;
}

const RevenueContractsStatsBar = memo((props: RevenueContractsStatsBarProps) => {
  const { statsBarData, currencyIsoCode, locale } = props;
  const classes = useStyles();

  if (!statsBarData || !statsBarData.items.length) return <></>;

  const defaultWidth = '25%';

  const total: number = statsBarData.items.reduce((sum, item) => sum + item.value, 0);

  const items: StatsBarChartItem[] = statsBarData.items.map(
    (item: { value: number; label: string; color: string }) => ({
      text: {
        value: formatNumberForDisplay(item.value, currencyIsoCode || 'USD', true, false, locale),
      },
      desc: { value: item.label },
      width: calculateBarItemWidth(item.value, total) || defaultWidth,
      backgroundColor: item.color,
    }),
  );

  return (
    <>
      {statsBarData?.total && (
        <div className={classes.statsBarTotal}>
          <span className={classes.statsBarTotalLabel} role="label">
            {statsBarData.total.label}
          </span>
          <span className={classes.statsBarTotalValue} role="label">
            {formatNumberForDisplay(
              statsBarData.total.value,
              currencyIsoCode || 'USD',
              true,
              false,
              locale,
            )}
          </span>
        </div>
      )}
      <StatsBarChart items={items} rootStyle={{ margin: '0px 8px' }} />
    </>
  );
});

export default RevenueContractsStatsBar;

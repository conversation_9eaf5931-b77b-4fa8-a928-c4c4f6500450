import React, { useEffect, useState } from 'react';
import { Grid, Typography, makeStyles, Box } from '@material-ui/core';

import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import HighlightOffOutlinedIcon from '@material-ui/icons/HighlightOffOutlined';
import {
  buildRubyListColumns,
  Cancel2Icon,
  RubyGrid,
  RubyObject,
  ViewIcon,
  ActivateIcon,
} from '@nue-apps/ruby-ui-component';
import { useRubySnackbar } from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import GridActions from '../grid-actions';
import { CellRendererRowProps } from '../grid-actions';
import { EditButton } from '../grid-actions/grid-actions';

import {
  DeleteButton,
  GridButton,
  WebhookButton as InvoiceButton,
} from '../grid-actions/grid-actions';
import { StatusRenderer } from '../index';

const useStyle = makeStyles({
  mappingContainer: {
    marginTop: 10,
  },
});

interface Props {
  getAllPaymentRuleList: (pageSize?: number, currentPage?: number) => Promise<any>;
  handleDeletePaymentRule: (rows: any) => void;
  handleEditPaymentRule: (rows: any) => void;
  handleCopyPaymentRule: (rows: any) => void;
  handleActivePaymentRule: (rows: any) => void;
  handleCancelPaymentRule: (rows: any) => void;
  handleViewPaymentRule: (rows: any) => void;
  paymentRuleMetadata: RubyObject;
  refreshFlag: number;
  canManagePaymentRules: boolean;
  setShowPaymentRuleWarningMessage: (value: boolean) => void;
}
function isCurrentDateInRange(ranges: any[]) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return ranges.some((range) => {
    if (range.status !== 'Active') {
      return false;
    }
    const startDate = new Date(range.startDate);
    startDate.setHours(0, 0, 0, 0);
    const endDate = range.endDate ? new Date(range.endDate) : null;
    if (endDate) {
      endDate.setHours(23, 59, 59, 999);
    }
    return today >= startDate && (endDate === null || today <= endDate);
  });
}

const PaymentRuleList = ({
  getAllPaymentRuleList,
  handleDeletePaymentRule,
  handleEditPaymentRule,
  handleCopyPaymentRule,
  handleActivePaymentRule,
  handleCancelPaymentRule,
  handleViewPaymentRule,
  paymentRuleMetadata,
  refreshFlag,
  canManagePaymentRules,
  setShowPaymentRuleWarningMessage,
}: Props) => {
  const classes = useStyle();

  const [currentPage, setCurrentPage] = React.useState(0);
  const [pageSize, setPageSize] = React.useState(10);
  const [totalCount, setTotalCount] = React.useState(0);
  const [dataSource, setDataSource] = React.useState<any[]>([]);
  //const [originalDataSource, setOriginalDataSource] = React.useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { showSnackbar, Snackbar } = useRubySnackbar();

  const listViewMetadata = [
    {
      apiName: 'name',
      name: 'Name',
      hidden: false,
      type: 'text',
      title: 'Name',
    },
    {
      apiName: 'status',
      name: 'Status',
      hidden: false,
      type: 'text',
      title: 'Status',
      cellRenderer: (props: any) => {
        const { row } = props;
        return <StatusRenderer status={row.status} />;
      },
    },
    {
      apiName: 'startDate',
      name: 'Start Date',
      hidden: false,
      type: 'text',
      title: 'Start Date',
      cellRenderer: (props: any) => {
        const { row } = props;
        return row.startDate ? dayjs(row.startDate).format('MM/DD/YYYY') : '';
      },
    },
    {
      apiName: 'endDate',
      name: 'End Date',
      hidden: false,
      type: 'text',
      title: 'End Date',
      cellRenderer: (props: any) => {
        const { row } = props;
        return row.endDate ? dayjs(row.endDate).format('MM/DD/YYYY') : '';
      },
    },
    {
      apiName: 'createdDate',
      name: 'Created Date',
      hidden: true,
      type: 'text',
      title: 'Created Date',
      cellRenderer: (props: any) => {
        const { row } = props;
        return row.createdDate ? dayjs(row.createdDate).format('MM/DD/YYYY hh:mm a') : '';
      },
    },
    {
      apiName: 'lastModifiedDate',
      name: 'Last Modified Date',
      hidden: true,
      type: 'text',
      title: 'Last Modified Date',
      cellRenderer: (props: any) => {
        const { row } = props;
        return row.lastModifiedDate ? dayjs(row.lastModifiedDate).format('MM/DD/YYYY hh:mm a') : '';
      },
    },
  ];
  const columns = [...buildRubyListColumns(paymentRuleMetadata!, listViewMetadata!)] || [];

  const columnWidths = [
    ...columns.map((_) => {
      return { columnName: _.name, width: _.width || 200 };
    }),
  ];

  const mergedColumnsWidths = [{ columnName: 'actions', width: 200 }, ...columnWidths];

  const [hiddenColumns, setHiddenColumns] = useState<string[]>(
    columns?.filter((_) => _.hidden === true).map((_) => _.name),
  );

  useEffect(() => {
    if (!hiddenColumns || hiddenColumns.length <= 0) {
      const nHiddenColumns = columns.filter((_) => _.hidden === true).map((_) => _.name);
      setHiddenColumns(nHiddenColumns);
    }
  }, [columns, hiddenColumns]);

  useEffect(() => {
    getPaymentRuleList(currentPage, pageSize);
  }, [refreshFlag]);

  const getPaymentRuleList = async (nCurrentPage: number, nPageSize: number) => {
    setLoading(true);
    try {
      const allResult = await getAllPaymentRuleList(0, 1000);
      if (allResult) {
        setTotalCount(allResult.totalCount || allResult.length);
        setDataSource(allResult);
        const isCurrentDateInRangeResult = isCurrentDateInRange(allResult);
        setShowPaymentRuleWarningMessage(!isCurrentDateInRangeResult);
      }
      setLoading(false);
    } catch {
      setLoading(false);
    }
  };

  const combinedColumns = [
    {
      name: 'actions',
      title: 'Actions',
      apiName: 'actions',
      type: 'actions',
      cellRenderer: ({ row }: CellRendererRowProps) => {
        const iconStyles = {
          color: '#6239eb',
          opacity: 1,
          height: '14px',
          width: '14px',
        };
        const viewBox = '0 0 20 20';
        const actions = [];
        if (canManagePaymentRules) {
          if (row.status == 'Draft') {
            actions.push(
              EditButton({
                id: 'edit',
                itemProps: { xs: 2 },
                tooltipText: 'Edit Payment Rule',
                onClickHandler: async () => {
                  handleEditPaymentRule(row);
                },
              }),
            );
            actions.push(
              InvoiceButton({
                id: 'active',
                tooltipText: 'Activate Payment Rule',
                Icon: ActivateIcon,
                style: {
                  width: '26px',
                  height: '26px',
                },
                onClickHandler: async () => {
                  handleActivePaymentRule(row);
                },
              }),
            );
          }
          if (row.status == 'Active') {
            actions.push(
              InvoiceButton({
                id: 'view',
                tooltipText: 'View Payment Rule',
                Icon: ViewIcon,
                style: {
                  width: '20px',
                  height: '20px',
                },
                onClickHandler: async () => {
                  handleViewPaymentRule(row);
                },
              }),
            );
          }
          if (row.status == 'Active') {
            actions.push(
              InvoiceButton({
                id: 'deactivate',
                tooltipText: 'Deactivate Payment Rule',
                Icon: Cancel2Icon,
                onClickHandler: async () => {
                  handleCancelPaymentRule(row);
                },
              }),
            );
          }

          if (row.status == 'Draft') {
            actions.push(
              InvoiceButton({
                id: 'delete',
                tooltipText: 'Delete Payment Rule',
                style: {
                  width: '20px',
                  height: '20px',
                },
                Icon: HighlightOffOutlinedIcon,
                onClickHandler: async () => {
                  handleDeletePaymentRule(row);
                },
              }),
            );
          }

          actions.push(
            DeleteButton({
              id: 'duplicate',
              tooltipText: 'Duplicate Payment Rule',
              Icon: FileCopyOutlinedIcon,

              onClickHandler: async () => {
                handleCopyPaymentRule(row);
              },
            }),
          );
        } else {
          actions.push(
            InvoiceButton({
              id: 'view',
              tooltipText: 'View Payment Rule',
              Icon: ViewIcon,
              style: {
                width: '20px',
                height: '20px',
              },
              onClickHandler: async () => {
                handleViewPaymentRule(row);
              },
            }),
          );
        }

        return <GridActions actionHelpers={{ iconStyles, viewBox }} actions={actions} />;
      },
    },
    ...columns,
  ];

  return (
    <>
      <Grid container className={classes.mappingContainer}>
        <Grid item xs={12}>
          <Snackbar />
          <RubyGrid
            loading={loading}
            columnReordering={true}
            enablePaging={true}
            rows={dataSource}
            totalCount={totalCount}
            //@ts-ignore
            setRows={setDataSource}
            showColumnChooser={true}
            columns={combinedColumns}
            columnWidths={mergedColumnsWidths}
            hiddenColumns={hiddenColumns}
            columnOrder={[...columns.map((_) => _.name)]}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default PaymentRuleList;

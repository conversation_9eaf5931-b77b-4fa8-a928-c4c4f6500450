import React, { useContext, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { Grid, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import produce from 'immer';

import Address from '../address';
import Checkbox from '../checkbox';
import DateInput from '../date-input';
import DateTimeInput from '../date-time-input';
import ErrorIcon from '../error-icon';
import { LookupRecordContext } from '../form-field';
import Lookup from '../lookup';
import type { RubyField } from '../metadata';
import MultiPicklist from '../multi-picklist';
import NumberInput from '../number-input';
import PickList from '../pick-list';
import RadioInput from '../radio-input';
import TextInput from '../text-input';
import CustomFieldControlRegistry from './custom-field-control-registry';
import FieldError from './field-error';
import type { DefaultProps, FieldTypeMap, Props } from './interface';
import { IgnoredFieldsContext } from './interface';

const defaultProps: Partial<Props> = {
  mode: 'create',
  hiddenFields: [],
  defaultDateFormat: 'MM/dd/yyyy',
  showDivider: true,
};

const defaultDateTimeFormat = 'MM/dd/yyyy hh:mm a';

const useStyles = makeStyles({
  root: {
    color: '#000000',
    fontFamily: 'inherit',
  },
  divider: {
    paddingBottom: '40px',
    marginBottom: '40px',
    borderBottom: 'solid 1px #DEDEDE',
  },
  title: {
    opacity: 0.7,
    fontWeight: 500,
    letterSpacing: '-1px',
    lineHeight: '32px',
    margin: '0 0 2px 0',
    paddingBottom: '32px',
  },
  caption: {
    opacity: 0.2,
    fontSize: '12px',
    fontWeight: 'bold',
    letterSpacing: 0,
    lineHeight: '15px',
    margin: '0 0 0 0',
  },
  formErrorWrapper: {
    padding: '24px 8px',
    borderRadius: '8px',
    display: 'flex',
    border: '1px solid #EFEFEF',
    borderTop: '3px solid #E12D38',
    boxSizing: 'border-box',
  },
  formErrorCaption: {
    fontSize: '20px',
    letterSpacing: '-0.77px',
    lineHeight: '25px',
    fontWeight: 500,
    color: '#000000',
    opacity: 0.7,
    width: '100%',
    paddingTop: '5px',
  },
  formErrorMessage: {
    fontSize: '14px',
    fontWeight: 500,
    color: '#000000',
    letterSpacing: '-0.39px',
    lineHeight: '18px',
    opacity: 0.4,
    width: '100%',
    paddingTop: '5px',
  },
  formErrorIcon: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-begin',
    padding: '5px 16px 0 36px',
    '& > svg': {
      fontSize: '32px',
      color: '#ca0035',
    },
  },
  fieldUOM: {
    opacity: 0.5,
    margin: '0px 8px',
    height: '42px',
    lineHeight: 'normal',
    alignContent: 'center',
  },
  uomed: {
    display: 'flex',
    alignItems: 'flex-end',
  },
});

const formFieldControls: FieldTypeMap = {
  text: TextInput,
  String: TextInput,
  Picklist: PickList,
  pickList: PickList,
  MultiPicklist: MultiPicklist,
  multiPickList: MultiPicklist,
  Date: DateInput,
  date: DateInput,
  DateTime: DateTimeInput,
  dateTime: DateTimeInput,
  //todo change field control
  Reference: Lookup,
  bLookup: Lookup,
  bMasterDetail: Lookup,
  Address: Address,
  Boolean: Checkbox,
  boolean: Checkbox,
  checkbox: Checkbox,
  Email: TextInput,
  email: TextInput,
  Phone: TextInput,
  phone: TextInput,
  TextArea: TextInput,
  longText: TextInput,
  Url: TextInput,
  Double: NumberInput,
  Decimal: NumberInput,
  decimal: NumberInput,
  Integer: NumberInput,
  integer: NumberInput,
  Currency: NumberInput,
  currency: NumberInput,
  Percent: NumberInput,
  percent: NumberInput,
  RadioGroup: RadioInput,
  Placeholder: () => null,
};

const FormSection: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const {
    title,
    caption,
    fields,
    lookupOptionsLoader,
    getLookupOptionByValue,
    afterFieldChange,
    mode,
    defaultDateFormat,
    showDivider,
    hiddenFields,
    setHiddenFields,
    error,
    titleStyle,
    locale,
    titleIcon,
  } = props;
  const classes = useStyles();
  const formContext = useFormContext();
  const { setValue, getValues, control, errors } = formContext;

  const [afterFieldChangeThrottleMap, setAfterFieldChangeThrottleMap] = useState<any>({});
  const ignoredFields = useContext(IgnoredFieldsContext);
  const createLookupRecordContext = useContext(LookupRecordContext);

  const handleFieldChange = (changedFieldApiName: string, newValue: any, oldValue: any) => {
    if (afterFieldChange) {
      afterFieldChange(changedFieldApiName, newValue, oldValue, getValues(), {
        ...formContext,
        setFieldVisibility: (fieldApiName, visibility) => {
          if (visibility === false) {
            const nextHiddenFields = produce(hiddenFields, (draft) => {
              draft.push(fieldApiName);
            });
            setHiddenFields(nextHiddenFields);
          } else {
            const nextHiddenFields = hiddenFields.filter((x) => x !== fieldApiName);
            setHiddenFields(nextHiddenFields);
          }
        },
      });
    }
  };

  const getFieldStyles = (field: RubyField, hidden: boolean) => {
    if (hidden) {
      return { display: 'none', ...field.customStyles };
    }
    if (field.type.toLowerCase() === 'boolean') {
      return { display: 'flex', paddingTop: '24px', ...field.customStyles };
    }

    return { display: 'block', ...field.customStyles };
  };

  if (
    fields?.length === 0 ||
    fields?.filter((_) => hiddenFields.indexOf(_.apiName) < 0).length === 0
  ) {
    return null;
  }

  return (
    <Grid container className={classes.root} spacing={2}>
      {title && (
        <Grid item xs={12}>
          {title && (
            <Typography variant="h3" className={classes.title} style={titleStyle}>
              {title}
              {!!titleIcon && titleIcon}
            </Typography>
          )}
          {caption && <h4 className={classes.caption}>{caption}</h4>}
        </Grid>
      )}
      {error && (
        <Grid item xs={12}>
          <div className={classes.formErrorWrapper}>
            <div className={classes.formErrorIcon}>
              <ErrorIcon />
            </div>
            <div>
              <div className={classes.formErrorCaption}>{error.caption}</div>
              <div className={classes.formErrorMessage}>{error.message}</div>
            </div>
          </div>
        </Grid>
      )}
      {fields?.map((field, index) => {
        if (ignoredFields.indexOf(field.apiName) >= 0) {
          console.debug(`${field.apiName} field ignored`);
          return null;
        }

        const { type, afterFieldChangeThrottle } = field;
        const hidden = hiddenFields.indexOf(field.apiName) >= 0;

        const CustomFormField = CustomFieldControlRegistry.get(field.apiName);
        if (CustomFormField) {
          return (
            <Grid
              item
              xs={field.xs || 12}
              key={`${field.apiName}-${index}`}
              style={getFieldStyles(field, hidden)}
            >
              <CustomFormField
                {...{
                  field,
                  mode,
                  lookupOptionsLoader,
                  getLookupOptionByValue,
                  formContext,
                  value: null,
                  hidden,
                }}
                afterFieldChange={afterFieldChange}
              />
            </Grid>
          );
        }

        const FormField = CustomFieldControlRegistry.get(field.apiName) || formFieldControls[type];
        if (FormField) {
          const xs = field.xs || 12;
          const { transformDisplayValue = (val) => val, transformSavedValue = (val) => val } =
            field;
          return (
            <Grid
              item
              xs={xs}
              key={`${field.apiName}-${index}`}
              style={getFieldStyles(field, hidden)}
            >
              <div className={!!field.uom ? classes.uomed : undefined} style={{ width: '100%' }}>
                <Controller
                  name={field.apiName}
                  render={({ value }) => {
                    return (
                      <FormField
                        minDate={field.minDate}
                        readOnly={mode === 'edit' ? !field.updatable : !field.creatable}
                        value={transformDisplayValue(value)}
                        field={field}
                        disableLabel={field.disableLabel}
                        showTooltip={field.showTooltip}
                        toolTipText={field.toolTipText}
                        allowOnlyPositiveNumbers={field.allowOnlyPositiveNumbers}
                        mode={mode}
                        showNone={field.showNone || false}
                        dateFormat={
                          field.type === 'Date' ? defaultDateFormat : defaultDateTimeFormat
                        }
                        lookupOptionsLoader={lookupOptionsLoader}
                        getLookupOptionByValue={getLookupOptionByValue}
                        handleInputChange={(newValue) => {
                          const oldValue = getValues(field.apiName);
                          const transformedNewValue = transformSavedValue(newValue);
                          setValue(field.apiName, transformedNewValue, {
                            shouldValidate: !!field.shouldValidateOnChange,
                          });
                          if (afterFieldChange && afterFieldChangeThrottle) {
                            if (afterFieldChangeThrottleMap[field.apiName] !== null) {
                              clearTimeout(afterFieldChangeThrottleMap[field.apiName]);
                            }
                            let _lastRequestId = setTimeout(async () => {
                              try {
                                handleFieldChange(field.apiName, transformedNewValue, oldValue);
                              } catch (error) {
                                console.error('Error handling input change.', error);
                              }
                            }, afterFieldChangeThrottle);
                            setAfterFieldChangeThrottleMap({
                              ...afterFieldChangeThrottleMap,
                              [field.apiName]: _lastRequestId,
                            });
                          }
                          if (afterFieldChange && !afterFieldChangeThrottle) {
                            handleFieldChange(field.apiName, transformedNewValue, oldValue);
                          }
                        }}
                        formContext={formContext}
                        locale={locale}
                        withCreateLookupRecord={
                          createLookupRecordContext !== null &&
                          createLookupRecordContext.showCreateOnLookupField(field)
                        } // todo should use a contextProvider
                        // if there's no withClearIcon props passed, default set the withClearIcon to be true
                        // or else: user passes the withClearIcon props, just change it to boolean value
                        withClearIcon={
                          typeof field.withClearIcon === 'undefined' ? true : !!field.withClearIcon
                        }
                        onClickCreateLookupRecord={(args) => {
                          if (createLookupRecordContext) {
                            createLookupRecordContext.handleCreateLookupRecord(args);
                          }
                        }}
                        subType={field.subType}
                      />
                    );
                  }}
                  control={control}
                />
                {!!field.uom && (
                  <Typography variant="body1" className={classes.fieldUOM}>
                    {field.uom}
                  </Typography>
                )}
              </div>
              {!field.hideInlineError && <FieldError errors={errors} field={field} />}
            </Grid>
          );
        }

        console.warn(`Undefined form control with type=${type}`, field);
        return null;
      })}
      {showDivider && (
        <Grid item xs={12}>
          <div className={classes.divider} />
        </Grid>
      )}
    </Grid>
  );
};

export const FormSectionWrapper: React.FC<DefaultProps> = (props) => {
  const [hiddenFields, setHiddenFields] = useState<string[]>([]);
  const fullProps: Props = { ...props, hiddenFields, setHiddenFields };
  return <FormSection {...fullProps} />;
};
export default FormSection;

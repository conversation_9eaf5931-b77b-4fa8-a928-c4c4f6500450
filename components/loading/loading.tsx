import React, { useEffect, useState } from 'react';

import { makeStyles } from '@material-ui/core/styles';

import loadingIcon from '../../static/images/loading_icon.gif';
import type { Props } from './interface';

const useStyles = makeStyles({
  container: {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingIcon: {
    height: '40px',
    width: '40px',
  },
});

const Loading: React.FC<Props> = (props) => {
  const classes = useStyles();
  const { loadingText, size, style } = props;
  const [sfLoadingPath, setSfLoadingPath] = useState('');

  const setUp = async () => {
    const loadingResourcePath = window.localStorage.getItem('loadingIconUrl');
    setSfLoadingPath(loadingResourcePath || '');
  };

  useEffect(() => {
    setUp();
  }, []);

  return (
    <div className={classes.container} style={style}>
      <img
        className={classes.loadingIcon}
        style={{ width: size, height: size }}
        src={sfLoadingPath || loadingIcon}
        alt="loading..."
      />
      {loadingText && <p>{loadingText}</p>}
    </div>
  );
};

export default Loading;

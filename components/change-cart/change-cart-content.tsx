import React, { useEffect } from 'react';

import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';

import ChangeCard from '../change-card';
import { RubyAlert } from '../ruby-alert/ruby-alert';
import type { RubySettings } from '../ruby-settings';
import AllCustomersChangeCards from './all-customers-change-cards';
import type { ChangeGroup, ChangeItem } from './interface';
import { getValidateChangeItemsResult } from './change-cart-helper';
import { validateConflictingChanges } from '../subscription-card';

const useStyles = makeStyles({
  changeCardContainer: {
    paddingTop: '24px',
  },
  alertContainer: {
    marginTop: 32,
    width: '100%',
    marginBottom: 16,
  },
  errorContainer: {
    marginTop: 32,
    width: '100%',
  },
});

export interface ExpressCheckoutError {
  id: string;
  messages: string[];
  isExpressCheckOut: boolean;
}

interface ChangeCartContentProps {
  changeGroups: Record<string, ChangeGroup>;
  onCheckOut: (change: ChangeGroup) => void;
  handleRemoveChangeItems: (changeGroupId: string, changeItemIds: string[]) => Promise<void>;
  handleRemoveChangesFromCart: (id: string) => Promise<void>;
  customerId?: string;
  rubySettings: RubySettings;
  expressCheckoutErrors: ExpressCheckoutError[];
}

interface ErrorMessage {
  id: string;
  error: string[];
}

const ChangeCartContent: React.FC<ChangeCartContentProps> = ({
  changeGroups,
  customerId,
  onCheckOut,
  handleRemoveChangeItems,
  handleRemoveChangesFromCart,
  rubySettings,
  expressCheckoutErrors,
}: ChangeCartContentProps) => {
  const classes = useStyles();
  const [errors, setErrors] = React.useState<ErrorMessage[]>([]);
  const changeGroupList = React.useMemo(() => {
    return Object.keys(changeGroups).map((changeGroupId: string) => {
      return changeGroups[changeGroupId];
    });
  }, [changeGroups]);

  useEffect(() => {
    setErrors(
      expressCheckoutErrors.map((x) => {
        return {
          id: x.id,
          error: x.messages,
        };
      }),
    );
  }, [expressCheckoutErrors]);

  const validateChangeItems = React.useCallback(
    (changeItems: ChangeItem[] | undefined, changeGroupId: string) => {
      let errorMessages: string[] = [];
      if (changeItems && changeItems.length) {
        errorMessages = getValidateChangeItemsResult(changeItems, rubySettings);
      }

      if (errors) {
        const newErrors = [...errors];
        const exist = newErrors.find((x: ErrorMessage) => x.id === changeGroupId);
        if (exist) {
          exist.error = errorMessages;
          setErrors(newErrors);
        } else {
          newErrors.push({
            id: changeGroupId,
            error: errorMessages,
          });
          setErrors(newErrors);
        }
      } else {
        setErrors([
          {
            id: changeGroupId,
            error: errorMessages,
          },
        ]);
      }

      return errorMessages && errorMessages.length > 0;
    },
    [errors, rubySettings],
  );

  const onRemoveChangeItems = async (changeGroupId: string, changeItemIds: string[]) => {
    const existing = changeGroupList.find((x: any) => x.id === changeGroupId);
    if (existing) {
      const filteredChangeItems = existing.changeItems?.filter((x: any) =>
        changeItemIds.find((c: string) => c !== x.id),
      );
      validateChangeItems(filteredChangeItems, changeGroupId);
    }

    handleRemoveChangeItems(changeGroupId, changeItemIds);
  };

  const onRemoveChangesFromCart = React.useCallback(
    (id: string) => {
      const newErrors = [...errors].filter((x: ErrorMessage) => x.id !== id);
      setErrors(newErrors);
      handleRemoveChangesFromCart(id);
    },
    [errors, handleRemoveChangesFromCart],
  );

  const onAddToCartComplete = React.useCallback(
    async (changeGroup: ChangeGroup, changeItems: ChangeItem[]) => {
      const existing = changeGroupList.find((x: any) => x.id === changeGroup.id);
      if (existing) {
        const filteredChangeItems = existing.changeItems?.filter((x: any) =>
          changeItems.find((c: any) => c.id !== x.id),
        );
        const conbimed = filteredChangeItems?.concat(changeItems);
        validateChangeItems(conbimed, changeGroup.id);
      }
    },
    [changeGroupList, validateChangeItems],
  );

  const handleCheckOut = React.useCallback(
    async (changeGroup: ChangeGroup) => {
      try {
        validateConflictingChanges([changeGroup]);
      } catch (error) {
        if (error instanceof Error) {
          setErrors([
            {
              id: changeGroup.id,
              error: [error.message],
            },
          ]);
        }
        return;
      }
      if (changeGroup && changeGroup.changeItems && changeGroup.changeItems.length > 0) {
        if (validateChangeItems(changeGroup.changeItems, changeGroup.id)) {
          return;
        }
      }
      onCheckOut(changeGroup);
    },
    [onCheckOut, validateChangeItems],
  );

  const hanldeDeleteMessage = (id: string, index: number) => {
    if (errors && errors.length) {
      const newErrs = [...errors];
      const exist = newErrs.find((x: ErrorMessage) => x.id === id);
      if (exist) {
        exist.error.splice(index, 1);
        setErrors(newErrs);
      }
    }
  };

  if (!customerId) {
    return (
      <>
        {Object.keys(changeGroups).map((changeGroupId: string, index: number) => {
          return (
            <Grid key={changeGroupId} item xs={12} className={classes.changeCardContainer}>
              <ChangeCard
                changeGroupNumber={index + 1}
                changeGroup={changeGroups[changeGroupId]}
                handleCheckout={() => handleCheckOut(changeGroups[changeGroupId])}
                handleRemoveChangeItems={onRemoveChangeItems}
                handleRemoveChangesFromCart={onRemoveChangesFromCart}
                onAddToCartComplete={onAddToCartComplete}
              />
            </Grid>
          );
        })}
      </>
    );
  }

  const currentUserChange = changeGroupList.filter(
    (x: ChangeGroup) => x.customer.id === customerId,
  );
  const otherUsersChange = changeGroupList.filter((x: ChangeGroup) => x.customer.id !== customerId);
  return (
    <>
      {errors &&
        errors.length > 0 &&
        errors.map((err: ErrorMessage) => {
          return err.error?.map((x: string, index: number) => {
            return (
              <div className={classes.errorContainer} key={`key-${x}-${index}`}>
                <RubyAlert
                  type="error"
                  title="Oops! Invalid Change Cart"
                  desc={x}
                  deletable={true}
                  onDelete={() => hanldeDeleteMessage(err.id, index)}
                />
              </div>
            );
          });
        })}
      {currentUserChange && currentUserChange.length > 0 ? (
        <AllCustomersChangeCards
          customerChangeGroups={currentUserChange}
          onCheckOut={handleCheckOut}
          handleRemoveChangeItems={onRemoveChangeItems}
          handleRemoveChangesFromCart={onRemoveChangesFromCart}
          isCurrentUser={true}
          changeGroupStartNumber={1}
          onAddToCartComplete={onAddToCartComplete}
        />
      ) : (
        <div className={classes.alertContainer}>
          <RubyAlert
            type="info"
            title="Information"
            desc="There are no change sets for this customer"
            deletable={true}
          />
        </div>
      )}
      {otherUsersChange && (
        <AllCustomersChangeCards
          customerChangeGroups={otherUsersChange}
          onAddToCartComplete={onAddToCartComplete}
          onCheckOut={handleCheckOut}
          handleRemoveChangeItems={onRemoveChangeItems}
          handleRemoveChangesFromCart={onRemoveChangesFromCart}
          isCurrentUser={false}
          changeGroupStartNumber={
            currentUserChange && currentUserChange.length > 0 ? currentUserChange.length + 1 : 1
          }
        />
      )}
    </>
  );
};

export default ChangeCartContent;

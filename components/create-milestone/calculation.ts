import { formatNumberForDisplay } from '../list';

export const calculateNum = (add: number, reduce: number, s: string = '+', num: number = 10) => {
  const m = Math.pow(10, num); //num是10的次幂
  const res = s == '+' ? (add * m + reduce * m) / m : (add * m - reduce * m) / m;
  return Math.round(res * m) / m;
};

export const convertToCurrency = (n: Number, currencyIsoCode?: string, locale?: string) => {
  const currencyCode = currencyIsoCode || 'USD';
  if (n === null || n === undefined || n === 0) {
    const zero: number = 0;
    return zero.toLocaleString(locale, {
      style: 'currency',
      currency: currencyCode,
    });
  }
  return formatNumberForDisplay(Number(n), currencyCode, false);
};

export const currencySymbol = (currency = 'USD', locale?: string) => {
  const formatter = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  });

  let symbol;
  formatter.formatToParts(0).forEach(({ type, value }) => {
    if (type === 'currency') {
      symbol = value;
    }
  });
  return symbol;
};

export const calculateTimes = (arg1: number, arg2: number) => {
  let m = 0;
  const s1 = arg1.toString();
  const s2 = arg2.toString();
  try {
    m += s1.split('.')[1].length;
  } catch (e) {}
  try {
    m += s2.split('.')[1].length;
  } catch (e) {}
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
};

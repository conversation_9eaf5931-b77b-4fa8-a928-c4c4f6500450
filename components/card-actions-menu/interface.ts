import { CardAction } from '../metadata/interface';

export interface Props {
  actions: Array<CardAction>;
  handleClose: () => void;
  openActionsMenu: boolean;
  handleClickAction: (action: CardAction) => void;
}

export interface ActionListProps {
  actions: Array<CardAction>;
  classes: Record<
    | 'container'
    | 'transition'
    | 'closeIcon'
    | 'title'
    | 'containerPadding'
    | 'actionText'
    | 'actionTextContainer',
    string
  >;
  handleClickAction: (action: CardAction) => void;
}

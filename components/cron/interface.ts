export type PickListOption = {
  name: string;
  value: any;
};

export interface PickListProps {
  showNone?: boolean;
  options?: Array<PickListOption>;
}

export type TimeRunAt = {
  hour: string;
  minute: string;
};

export type MonthWeeklyFrequency = {
  number: string;
  week: string;
  month: string;
};

export interface WeeklyFrequencyProps {
  Monday: string;
  Tuesday: string;
  Wednesday: string;
  Thursday: string;
  Friday: string;
  Saturday: string;
  Sunday: string;
}

export interface DailyObject {
  dailyFrequency: string;
  dailyExpression: string[];
  dailyRunAt: TimeRunAt;
}

export interface WeeklyObject {
  weeklyFrequency: string[];
  weeklyExpression: string[];
  weeklyRunAt: TimeRunAt;
}

export interface MonthlyObject {
  monthlyFrequencyType?: string;
  monthDailyFrequencyDay?: string;
  monthDailyFrequencyMonth?: string;
  monthWeeklyFrequencyNumber?: string;
  monthWeeklyFrequencyWeek?: string;
  monthWeeklyFrequencyMonth?: string;
  monthlyRunAt: TimeRunAt;
  monthlyExpression: string[];
}

export interface CronMonthlyFormProps {
  monthlyObject: MonthlyObject;
  setMonthlyObject: Function;
  disabled?: boolean;
}

export interface CronDailyFormProps {
  dailyObject: DailyObject;
  setDailyObject: Function;
  disabled?: boolean;
}

export interface CronWeeklyFormProps {
  weeklyObject: WeeklyObject;
  setWeeklyObject: Function;
  disabled?: boolean;
}

export interface CronFormControlProps extends PickListProps {
  onDemandFrequency?: string;
  setOnDemandFrequency?: Function;
  cronExpression: string[];
  setCronExpression: Function;
  runAt: TimeRunAt;
  setRunAt: Function;
  disabled?: boolean;
  runImmediately: boolean;
  setRunImmediately: Function;
}

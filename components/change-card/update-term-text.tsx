import React from 'react';

import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';

import type { UpdateTermRequest } from '../change-cart/interface';
import type { ChangeItemText } from './interface';

import {
  formatTerm,
  formatDimension,
} from '../subscription-card/info-section-text-registry/convert-free-trial-text/utils';

const useStyles = makeStyles((theme) => ({
  itemText: {
    color: theme.palette.grey[900],
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
}));

const getChangeAction = (changeAction?: string) => {
  if (!changeAction) {
    return 'set to';
  }
  if (changeAction === 'add') {
    return 'increased by';
  }
  if (changeAction === 'reduce') {
    return 'reduced by';
  }
};

interface UpdateRenewalTextProps {
  request: UpdateTermRequest;
  uomTermDimension: string;
}

const UpdateRenewalText: React.FC<UpdateRenewalTextProps> = ({ request, uomTermDimension }) => {
  const classes = useStyles();

  if (!request.updateRenewalTerm || !request.renewalTerm) {
    return null;
  }

  const formattedTerm = formatTerm(request.renewalTerm);
  const formattedDimension = formatDimension(uomTermDimension, request.renewalTerm);

  return (
    <>
      , and renewal term as <span className={classes.textHighlight}>{formattedTerm}</span>{' '}
      {formattedDimension}
    </>
  );
};

const UpdateTermText: React.FC<ChangeItemText> = (props) => {
  const { changeItem } = props;

  const classes = useStyles();

  const uomTermDimension = React.useMemo(() => changeItem.asset.uom.termDimension, [changeItem]);
  const request = changeItem.request as UpdateTermRequest;
  const isEvergreen = changeItem.asset.evergreen;
  const assetName = <span className={classes.textUnderline}>{changeItem.request.assetName}</span>;

  const highlighted = React.useCallback(
    (text: string) => {
      return <span className={classes.textHighlight}>{text}</span>;
    },
    [classes.textHighlight],
  );

  if (request.updateToEvergreen) {
    return (
      <Typography variant="body2" component="p" display="inline" className={classes.itemText}>
        The subscription {assetName} is changed to {highlighted('evergreen')}
      </Typography>
    );
  }

  const formattedTerm = highlighted(formatTerm(request.subscriptionTerm));
  const formattedDimension = formatDimension(uomTermDimension, request.subscriptionTerm);

  if (isEvergreen) {
    return (
      <Typography variant="body2" component="p" display="inline" className={classes.itemText}>
        The subscription {assetName} is changed from evergreen to termed subscription, with
        subscription term as {formattedTerm} {formattedDimension}
        <UpdateRenewalText request={request} uomTermDimension={uomTermDimension} />
      </Typography>
    );
  }

  return (
    <Typography variant="body2" component="p" display="inline" className={classes.itemText}>
      The subscription term of Subscription {assetName} is {getChangeAction(request.changeAction)}{' '}
      {formattedTerm} {formattedDimension}
      <UpdateRenewalText request={request} uomTermDimension={uomTermDimension} />
    </Typography>
  );
};

export default UpdateTermText;

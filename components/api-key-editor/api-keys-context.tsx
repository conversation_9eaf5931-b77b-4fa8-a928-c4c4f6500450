import React from 'react';

import { User } from '../metadata';

export interface ApiKeysData {
  id: string;
  name: string;
  roleId: string;
  createdDate: string;
  expiredTime?: string;
  lastUsedDate: string;
  masterKey: boolean;
  apiKey?: string;
}

export interface RevealAPIKeyResp {
  id: string;
  apiKey: string;
}

export interface RollAPIKeyResp {
  runImmediately: boolean;
  expirationTime: string;
}

export interface ApiKeysService {
  user: User;
  getApiKeysData: () => Promise<ApiKeysData[]>;
  deleteApiKey: (id: string) => Promise<void>;
  fetchTenantRoles: () => Promise<any[]>;
  authUser: ({ userName, password }: any) => Promise<any>;
  revealApiKey: (id: string, userName: string, password: string) => Promise<RevealAPIKeyResp>;
  rollApiKey: (id: string, isTody: boolean, expireTime: string) => Promise<RollAPIKeyResp>;
  createApiKey: (name: string, roleId: string) => void;
  updateApiKey: (id: string, name: string, roleId: string) => void;
  loadApiKeyLog: (
    apiKeyIds: string[],
    page: number,
    pageSize: number,
    statusCode?: number,
    accessDate?: string,
    endPoint?: string,
    method?: string,
  ) => Promise<any>;
  getEventRequestDetails: (id: string) => any;
}

export const ApiKeysContext = React.createContext<ApiKeysService | null>(null);

export default ApiKeysContext;

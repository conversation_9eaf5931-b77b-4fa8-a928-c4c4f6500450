import React from 'react';

import { makeStyles } from '@material-ui/core';
import Grid from '@material-ui/core/Grid';

import Card from '../card';
import { SummaryItem } from '../list';
import { CardAction } from '../metadata';
import { displayValuesFromFieldSet } from '../metadata/metadata-utils';
import { useUserLocale } from '../use-user-locale';
import { Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  card: {
    // paddingBottom: '20px',
    // marginRight: '24px',
    padding: '24px',
  },
});

export const GridViewItem: React.FC<Props> = (userProps: Props) => {
  const props = { ...defaultProps, ...userProps };

  const classes = useStyles();

  const {
    gridViewMetadata,
    fieldSetMetadata,
    objectMetadata,
    object,
    currencyIsoCode,
    handleCardClick,
    actionEventHandler,
    showSubmitBtn,
    onShowDetails,
    showDetails,
    isPopup,
    locale,
    transactionHub,
  } = props;
  const { getUserLocale } = useUserLocale();

  const items = displayValuesFromFieldSet({
    objectFieldSet: fieldSetMetadata,
    currencyIsoCode: (object.currencyIsoCode as string) || currencyIsoCode,
    object,
    objectMetadata,
    locale: locale || getUserLocale(),
  });

  const cancellationDateIndex = items.findIndex((item) => item.apiName === 'cancellationDate');

  // CancellationDate should be rendered if and only if the order is canceled
  if (
    objectMetadata.apiName === 'Order' &&
    object.status === 'Canceled' &&
    object.cancellationDate
  ) {
    if (cancellationDateIndex < 0) {
      items.push({
        apiName: 'cancellationDate',
        name: 'Cancellation Date',
        type: 'date',
        value: object.cancellationDate,
      });
    }
  } else if (cancellationDateIndex > 0) {
    items.splice(cancellationDateIndex, 1);
  }

  if (!gridViewMetadata || !gridViewMetadata.cardHeaderMetadata) {
    return null;
  }

  const {
    title,
    description,
    status,
    subtitle,
    imageSignedUrl,
    statusStyle,
    statusShadowStyle,
    statusIcon,
  } = gridViewMetadata.cardHeaderMetadata;

  const cardDescription =
    typeof description === 'function' ? description(object) : object[description];
  const cardStatus = typeof status === 'function' ? status(object) : object[status];
  const cardTitle = typeof title === 'function' ? title(object) : object[title];
  const cardSubtitle = typeof subtitle === 'function' ? subtitle(object) : object[subtitle];
  const cardHeaderImage =
    typeof imageSignedUrl === 'function' ? imageSignedUrl(object) : object[imageSignedUrl];
  const actions = gridViewMetadata.cardBodyMetadata?.actions[cardStatus];

  const getMoreActions = () => {
    const actions = isPopup
      ? gridViewMetadata.cardBodyMetadata?.popUpMoreActions
      : gridViewMetadata.cardBodyMetadata?.moreActions;
    const hideActionIds: string[] = [];

    if (
      objectMetadata.name === 'Order' &&
      !props.rubySettings?.showSubscriptionOnCustomerLifecycle
    ) {
      hideActionIds.push('changeOrder');
    }

    if (!props.rubySettings?.showInvoiceOnCustomerLifecycle) {
      hideActionIds.push('viewInvoice');
    }

    if (!props.rubySettings?.showUsageOnCustomerLifecycle) {
      hideActionIds.push('viewUsage');
    }

    if (!props.rubySettings?.showAssetOnCustomerLifecycle) {
      hideActionIds.push('viewAsset');
    }

    if (!props.rubySettings?.showEntitlementOnCustomerLifecycle) {
      hideActionIds.push('viewEntitlement');
    }

    return actions?.filter((x: CardAction) => !hideActionIds.find((y: string) => y === x.id));
  };

  const uom =
    objectMetadata.name === 'Asset' || (objectMetadata.name === 'Entitlement' && object.uom)
      ? object.uom
      : undefined;

  let cardSummaryItem: SummaryItem | undefined;

  const activeBorderStyle = gridViewMetadata.cardBodyMetadata?.activeBorderStyle;
  const summaryItemField = gridViewMetadata.cardBodyMetadata?.summaryItemField;
  const summaryItemPosition = gridViewMetadata.cardBodyMetadata?.summaryItemPosition;
  const numCardActionsPerRow = gridViewMetadata.cardBodyMetadata?.numCardActionsPerRow || 3;

  const cardActiveBorderStyle =
    cardStatus === 'Active' || cardStatus === 'Activated' ? activeBorderStyle : undefined;

  const onTitleClick = () => {
    if (handleCardClick) {
      handleCardClick(object);
    }
  };

  if (!items[0]) {
    return null;
  }

  if (summaryItemField) {
    const summaryItemMetadataField = fieldSetMetadata.find(
      (field) => field.apiName === summaryItemField,
    );
    cardSummaryItem = {
      apiName: summaryItemMetadataField?.apiName || '',
      name: summaryItemMetadataField?.name || '',
      value: object[summaryItemField],
      type: summaryItemMetadataField?.type || '',
      position: summaryItemPosition,
    };
    items.splice(
      items.findIndex((item) => item.name === summaryItemMetadataField?.name),
      1,
    );
  } else {
    cardSummaryItem = {
      apiName: items[0].apiName,
      name: items[0].name,
      value: items[0].value,
      type: items[0].type,
      position: summaryItemPosition,
    };
    items.splice(0, 1);
  }

  const getActions: () => CardAction[] = () => {
    if (typeof actions === 'function') {
      return actions({ object });
    } else {
      return actions;
    }
  };

  return (
    <Grid item className={classes.card}>
      <Card
        moreActions={getMoreActions()}
        showDetails={showDetails}
        numCardActionsPerRow={numCardActionsPerRow}
        onTitleClick={handleCardClick ? onTitleClick : undefined}
        activeBorderStyle={cardActiveBorderStyle}
        status={cardStatus}
        statusStyle={statusStyle[cardStatus]}
        statusShadowStyle={statusShadowStyle[cardStatus]}
        imageSignedUrl={cardHeaderImage}
        title={cardTitle}
        description={cardDescription}
        items={items}
        currencyIsoCode={object.currencyIsoCode || currencyIsoCode}
        subtitle={cardSubtitle}
        StatusIcon={statusIcon[cardStatus]}
        summaryItem={cardSummaryItem}
        actions={getActions()}
        showSubmitBtn={showSubmitBtn === undefined ? true : showSubmitBtn}
        uom={uom}
        onShowDetails={() => {
          if (onShowDetails) {
            onShowDetails(object);
          }
        }}
        actionEventHandler={async (action) => {
          actionEventHandler &&
            (await actionEventHandler({
              object,
              objectMetadata,
              action,
            }));
        }}
        locale={locale}
        transactionHub={transactionHub}
      />
    </Grid>
  );
};

export default GridViewItem;

import React, { useContext, useEffect, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';

import {
  Button,
  Chip,
  Divider,
  FormControl,
  Grid,
  Typography,
  makeStyles,
} from '@material-ui/core';
import FavoriteBorderIcon from '@material-ui/icons/FavoriteBorder';

import DialogComponent from '../dialog-component';
import { FormSectionWrapper as FormSection } from '../form-section/form-section';
import { buildValidationSchema, useYupValidationResolver } from '../form-validation';
import { ActiveIcon, InactiveIcon } from '../icons/icons';
import { RubyButtonBar } from '../ruby-button';
import { useRubySnackbar } from '../ruby-notifier';
import { RubySettingEditorPanel } from '../ruby-settings/ruby-setting-editor-panel';
import WarningIcon from '@material-ui/icons/Warning';
import type {
  AvalaraEInvoicingConnectionRequest,
  CompanyType,
  CountryMandates,
  DocumentType,
} from './avalara-e-invoicing-connection-context';
import AvalaraEInvoicingConnectionContext from './avalara-e-invoicing-connection-context';
import AvalaraEInvoicingCountryMandates from './avalara-e-invocing-countryMandates';
import RubyCheckbox from '../checkbox';
import PickList from '../pick-list';
import clsx from 'clsx';
import { CustomFieldControlRegistry, CustomFormControlProps } from '../form-section';
import UserContext from '../user-context';
import { Tenant } from '../tenant-detail';

const useStyle = makeStyles({
  buttonBarContainer: {
    marginTop: '20px',
  },
  accordionRoot: {
    boxShadow: 'none',
    marginTop: 16,
    width: '100%',
    paddingBottom: 24,
    '&:before': {
      backgroundColor: 'unset',
    },
  },
  accordionSummaryRoot: {
    display: '-webkit-inline-flex',
    padding: '0px',
    width: '100%',
    alignItems: 'center',
  },
  accordionSummaryContainer: {
    display: 'flex',
    justifyContent: 'space-between',
  },
  papper: {
    flexGrow: 2,
    paddingLeft: 32,
    paddingTop: 34,
    paddingBottom: 24,
    paddingRight: 24,
  },
  heading: {
    opacity: '0.7',
    color: '#000000',
    fontWeight: 500,
    lineHeight: '32px',
  },
  header: {
    marginTop: 12,
    display: 'flex',
    alignItems: 'center',
    '& span': {
      textTransform: 'Uppercase',
      minWidth: '61px',
      padding: '6px 12px 6px 12px',
      fontSize: '10px',
      fontWeight: 'bold',
      letterSpacing: 0,
      lineHeight: '12px',
      textAlign: 'center',
      borderRadius: '8px',
    },
  },
  activated: {
    display: 'flex',
    paddingLeft: '4px',
    marginTop: 12,
  },
  activatedText: {
    paddingLeft: '4px',
    fontSize: '.75rem',
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  subHeader: {
    marginBottom: 28,
    marginTop: 8,
    color: '#000',
    opacity: 0.8,
    fontSize: '14px !important',
  },
  sanboxCheckbox: {
    marginTop: '22px',
    marginBottom: '22px',
  },
  flexCenterStart: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
});

interface Props {}

export const AvalarEInvoicingEnvironment: React.FC<CustomFormControlProps> = (props) => {
  const classes = useStyle();

  const { field, formContext, hidden } = props;

  return (
    <Controller
      name={field.apiName}
      render={({ value, onChange }) => {
        return (
          <FormControl style={{ width: '100%', display: 'inline-flex' }}>
            <Grid container>
              <Grid item sm={10}>
                <div>123</div>
              </Grid>
            </Grid>
          </FormControl>
        );
      }}
    />
  );
};

const AvalaraEInvoicingConnection: React.FC<Props> = ({}: Props) => {
  const classes = useStyle();
  const [enabled, setEnabled] = React.useState(false);
  const [inited, seInited] = React.useState(false);
  const initialConnectionValues: Record<string, any> = {
    clientId: '',
    clientSecret: '',
    appCategory: 'EInvoicing',
    integrationType: 'AvalaraELR',
    environment: true,
  };

  const [loading, setLoading] = React.useState(false);
  const [loadingMandates, setLoadingMandates] = React.useState(false);
  const [showDeactivateDialog, setshowDeactivateDialog] = React.useState(false);
  const [showActivateDialog, setshowActivateDialog] = React.useState(false);
  const [lastModifiedDate, setLastModifiedDate] = React.useState('');
  const [avalaraSandbox, setAvalaraSandbox] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [id, setId] = useState('');
  const [countryMandates, setCountryMandates] = React.useState<CountryMandates[]>([]);
  const [companyList, setCompanyList] = useState<CompanyType[]>([]);
  const [company, setCompany] = useState<CompanyType>();
  const [documentType, setDocumentType] = useState<DocumentType>();
  const [documentTypeList, setDocumentTypeList] = React.useState<DocumentType[]>([]);
  const [retriveStatus, setRetriveStatus] = React.useState('');
  const [switchEnvDialog, setSwitchEnvDialog] = React.useState(false);
  const [oldEnvironment, setOldEnvironment] = React.useState('');
  const [tenant, setTenant] = useState<Tenant>();

  const { userContextService } = useContext(UserContext);

  const [objectMetadata, setObjectMetadata] = useState({
    apiName: 'AvalaraEInvoicing',
    name: 'AvalaraEInvoicing',
    objectType: 'Entity',
    customizable: true,
    fields: [
      {
        apiName: 'clientId',
        name: 'Client Id',
        type: 'text',
        required: false,
        updatable: true,
        creatable: true,
        showTooltip: false,
        toolTipText: '',
      },
      {
        apiName: 'clientSecret',
        name: 'Client Secret',
        type: 'text',
        required: false,
        updatable: true,
        creatable: true,
        showTooltip: false,
        toolTipText: '',
      },
      {
        apiName: 'environment',
        name: 'Avalara Sandbox',
        type: 'boolean',
        required: false,
        updatable: true,
        creatable: true,
        showTooltip: false,
        toolTipText: '',
        customStyles: {
          paddingTop: '10px',
          marginBottom: '2px',
        },
      },
    ],
  });

  const avalarEInvoicingConnectionContext = useContext(AvalaraEInvoicingConnectionContext);

  if (!avalarEInvoicingConnectionContext) {
    throw Error(
      'Avalara E-Invoicing Connection requires AvalaraEInvoicingConnectionContext to be declared in context provider',
    );
  }

  const {
    onTestAvalaraEInvoicingIntegration,
    getAvalaraEInvoicingIntegrationDetail,
    getAvalaraEInvoicingCountryMandates,
    getAvalaraEInvoicingCompanyList,
    getAvalaraEInvoicingDocumentTypeList,
    onCreateAvalaraEInvoicingIntegration,
    onUpdateAvalaraEInvoicingIntegration,
    onUpdateAvalaraEInvoicingCountryMandatesStatus,
    onUpdateAvalaraEInvoicingCountryMandatesFilter,
    onGetAvalaraEInvoicingCountryMandatesFilter,
    onCreateNewAvalaraEInvoicingCountryMandates,
    getCountryMandatesMappings,
    getDefaultCountryMandates,
    setCountryMandatesMappings,
    onHandleResotreAvalaraEInvoicingMappings,
    onUpdateAvalaraEInvoicingRetrieveStatus,
    onGetAvalaraEInvoicingRetrieveStatus,
    onGetAvalaraEInvoicingAllowedXmlPath,
    downloadEInvoicingDocument,
    getTenantDetails,
  } = avalarEInvoicingConnectionContext;

  const validationSchema = buildValidationSchema(objectMetadata);
  const resolver = useYupValidationResolver(validationSchema);
  const { showSnackbar, Snackbar } = useRubySnackbar();

  const createIntegrationFormMethods = useForm({
    defaultValues: initialConnectionValues,
    //@ts-ignore
    resolver,
  });

  const { reset, setValue } = createIntegrationFormMethods;

  const handleTestConnection = React.useCallback(
    async (
      clientId?: string,
      clientSecret?: string,
      environment?: string,
      testId?: string,
      successCallback?: () => void,
    ) => {
      const request: AvalaraEInvoicingConnectionRequest = {
        clientId: clientId ? clientId : null,
        clientSecret: clientSecret ? clientSecret : null,
        environment: environment ? environment : 'Sandbox',
      };
      try {
        const res = await onTestAvalaraEInvoicingIntegration(request, testId || '');

        if (res.data.success) {
          if (successCallback) {
            successCallback();
          }
        } else {
          if (res.data.error) {
            showSnackbar('error', 'Oops! An error occurred', res.data.error);
          }
        }
      } catch (err) {
        console.log('err', err);
        showSnackbar(
          'error',
          'Oops! An error occurred',
          'The Avalara ELR connection test has failed. Please check your credentials and configuration settings, and try again. ',
        );
      }
    },
    [onTestAvalaraEInvoicingIntegration],
  );

  const loadAvalarEInvoicingCountryMandates = React.useCallback(async () => {
    try {
      setLoadingMandates(true);
      const result = await getAvalaraEInvoicingCountryMandates();
      if (result) {
        setCountryMandates(result);
      }
    } catch (err) {
      console.log('err', err);
    }
    setLoadingMandates(false);
  }, [getAvalaraEInvoicingCountryMandates]);

  const updateCountryMandates = React.useCallback(
    async (c?: CompanyType, d?: DocumentType) => {
      try {
        setLoadingMandates(true);
        const result = await getAvalaraEInvoicingCountryMandates(c, d);
        if (result) {
          setCountryMandates(result.sort((a, b) => (a.countryName > b.countryName ? 1 : -1)));
        }
      } catch (err) {
        console.log('err', err);
      }
      setLoadingMandates(false);
    },
    [getAvalaraEInvoicingCountryMandates],
  );

  useEffect(() => {
    if (company && documentType) {
      updateCountryMandates(company, documentType);
    }
  }, [company, documentType]);

  const onGetAvalaraEInvoicingCompanyList = React.useCallback(async () => {
    const companys = await getAvalaraEInvoicingCompanyList();
    if (companys) {
      setCompanyList(companys.value);
      setCompany(companys.value[0]);
      setDocumentTypeList([
        {
          name: 'Invoice',
          id: '1',
          apiName: 'Invoice',
        },
        {
          name: 'CreditMemo',
          id: '2',
          apiName: 'CreditMemo',
        },
      ]);
      setDocumentType({
        name: 'Invoice',
        id: '1',
        apiName: 'Invoice',
      });
    }
  }, [getAvalaraEInvoicingCompanyList]);

  const loadData = React.useCallback(
    async (isReload?: boolean) => {
      setLoading(true);
      const result = await getAvalaraEInvoicingIntegrationDetail();
      if (!result) {
        setId('');
      } else {
        setId(result.id);
        const { config } = result;
        const status = result.status == 'Activated' ? true : false;
        const active = result.active;
        const sandBoxMetadata = objectMetadata.fields.find((f) => f.apiName === 'environment');
        if (result.status == 'Activated') {
          sandBoxMetadata!.creatable = false;
          await onGetAvalaraEInvoicingCompanyList();
          const res = await onGetAvalaraEInvoicingRetrieveStatus();
          if (res.data) {
            setRetriveStatus(res.data.elrSchedule);
          }
          if (getTenantDetails) {
            const tenantDetails = await getTenantDetails();
            setTenant(tenantDetails);
          }
        } else {
          sandBoxMetadata!.creatable = true;
        }
        reset({
          clientId: config.clientId,
          clientSecret: status ? config.clientSecret : '',
          environment: config.environment === 'Sandbox' ? true : false,
        });
        setLastModifiedDate(new Date(config.lastModifiedDate).toString());
        setEnabled(status);
      }
      seInited(true);
      setLoading(false);
    },
    [getAvalaraEInvoicingIntegrationDetail, onGetAvalaraEInvoicingCompanyList, reset],
  );

  const getUserInputs = React.useCallback(() => {
    const clientId = createIntegrationFormMethods.getValues('clientId');
    if (!clientId) {
      showSnackbar('error', 'Error', 'Please provide a clientId');
      return;
    }
    const clientSecret = createIntegrationFormMethods.getValues('clientSecret');
    if (!clientSecret) {
      showSnackbar('error', 'Error', 'Please provide a clientSecret');
      return;
    }
    const environment = createIntegrationFormMethods.getValues('environment')
      ? 'Sandbox'
      : 'Production';

    return {
      clientId,
      clientSecret,
      environment,
    };
  }, [createIntegrationFormMethods, showSnackbar]);

  const handleCreateAvalarEInvoicingIntegration = React.useCallback(async () => {
    const userInputs = getUserInputs();
    if (!userInputs) {
      return;
    }
    const { clientId, clientSecret, environment } = userInputs;
    try {
      await onCreateAvalaraEInvoicingIntegration(
        {
          clientId: clientId,
          clientSecret: clientSecret,
          environment: environment,
          appCategory: 'EInvoicing',
          integrationType: 'AvalaraELR',
        },
        'POST',
      );

      await loadData(true);

      showSnackbar(
        'confirm',
        'Success',
        'The Avalara ELR connection has been successfully activated. ',
      );
    } catch (err) {
      console.log('err', err);
    }
  }, [getUserInputs, onCreateAvalaraEInvoicingIntegration, showSnackbar, loadData]);

  const handleUpdateAvalaraEInvoicingIntegration = React.useCallback(
    async (status: boolean) => {
      const userInputs = getUserInputs();
      if (!userInputs) {
        return;
      }
      let detailId;
      setIsSubmitting(true);
      if (!id) {
        const { clientId, clientSecret, environment } = userInputs;
        const res = await onCreateAvalaraEInvoicingIntegration(
          {
            clientId: clientId,
            clientSecret: clientSecret,
            environment: environment,
            appCategory: 'EInvoicing',
            integrationType: 'AvalaraELR',
          },
          'POST',
        );
        if (res) {
          detailId = res.data.id;
        }
      } else if (id && status) {
        const { clientId, clientSecret, environment } = userInputs;
        const res = await onCreateAvalaraEInvoicingIntegration(
          {
            clientId: clientId,
            clientSecret: clientSecret,
            environment: environment,
            appCategory: 'EInvoicing',
            integrationType: 'AvalaraELR',
            id: id,
          },
          'PATCH',
        );
        if (res) {
          detailId = res.data.id;
        }
      }
      try {
        await onUpdateAvalaraEInvoicingIntegration(
          status ? 'activate' : 'deactivate',
          id || detailId || '',
        );
        await loadData(true);
        if (status) {
          showSnackbar(
            'confirm',
            'Success',
            'The Avalara ELR connection has been successfully activated. ',
          );
        } else {
          setValue('secretKey', '');
          showSnackbar(
            'confirm',
            'Success',
            'The Avalara ELR connection has been successfully deactivated. ',
          );
        }
      } catch (err) {
        console.log('err', err);
      }
      setIsSubmitting(false);
    },
    [
      getUserInputs,
      onUpdateAvalaraEInvoicingIntegration,
      showSnackbar,
      loadData,
      setValue,
      handleCreateAvalarEInvoicingIntegration,
    ],
  );

  const handleUpdateCountryMandatesStatus = async (status: string, mandateId: string) => {
    await onUpdateAvalaraEInvoicingCountryMandatesStatus(status, mandateId);
    await updateCountryMandates(company, documentType);
    showSnackbar('confirm', 'Success', `Countrymandate has been ${status}d successfully.`);
  };

  const handleUpdateRetriveStatus = async (status: string) => {
    setRetriveStatus(status);
    const res = await onUpdateAvalaraEInvoicingRetrieveStatus(status);
    showSnackbar('confirm', 'Success', 'Your Retrieve Status has been updated.');
  };

  const handleResotreAvalaraEInvoicingMappings = async (mandateId: string) => {};

  const afterFieldChange = (changedFieldApiName: string, newValue: string, oldValue: string) => {
    if (changedFieldApiName === 'environment') {
      if (
        id &&
        (createIntegrationFormMethods.getValues('clientId') ||
          createIntegrationFormMethods.getValues('clientSecret'))
      ) {
        setOldEnvironment(oldValue);
        setSwitchEnvDialog(true);
      }
    }
  };

  CustomFieldControlRegistry.register('avalarEInvoicingEnvironment', AvalarEInvoicingEnvironment);

  React.useEffect(() => {
    if (!loading && !inited) {
      loadData();
    }
  }, [loadData, loading, inited]);

  if (!userContextService) {
    return null;
  }

  return (
    <>
      <DialogComponent
        title={`Switching between Sandbox and Production`}
        open={switchEnvDialog}
        submitButtonText="Yes"
        cancelButtonText="No"
        width={'sm'}
        handleSubmit={async () => {
          reset({
            clientId: '',
            clientSecret: '',
          });
          if (id) {
            const res = await onCreateAvalaraEInvoicingIntegration(
              {
                clientId: '',
                clientSecret: '',
                environment: createIntegrationFormMethods.getValues('environment')
                  ? 'Sandbox'
                  : 'Production',
                appCategory: 'EInvoicing',
                integrationType: 'AvalaraELR',
                id: id,
              },
              'PATCH',
            );
          }
          setSwitchEnvDialog(false);
        }}
        handleClose={() => {
          setSwitchEnvDialog(false);
          reset({
            environment: oldEnvironment,
          });
        }}
      >
        <p>
          Switching between Sandbox and Production will clear the Client ID and Client Secret. Do
          you wish to continue?
        </p>
      </DialogComponent>
      <DialogComponent
        title={`Deactivate Avalara E-Invoicing`}
        open={showDeactivateDialog}
        submitButtonText="Yes"
        cancelButtonText="No"
        width={'sm'}
        handleSubmit={async () => {
          await handleUpdateAvalaraEInvoicingIntegration(false);
          setshowDeactivateDialog(false);
        }}
        handleClose={() => {
          setshowDeactivateDialog(false);
        }}
        processing={isSubmitting}
      >
        <p>
          Deactivating the Avalara ELR connection will clear the secret key and pause all ongoing
          data transactions between Nue and Avalara ELR. Are you sure you want to continue with
          deactivation?
        </p>
      </DialogComponent>
      <DialogComponent
        title={`Activate Avalara E-Invoicing`}
        open={showActivateDialog}
        submitButtonText="Yes"
        cancelButtonText="No"
        width={'sm'}
        handleSubmit={async () => {
          try {
            await handleUpdateAvalaraEInvoicingIntegration(true);
          } catch (e) {
            console.log('error', e);
          }
          setshowActivateDialog(false);
        }}
        handleClose={() => {
          setshowActivateDialog(false);
        }}
        processing={isSubmitting}
      >
        <p>The Aalara E-Invoicing will be activated. Do you wish to proceed?</p>
      </DialogComponent>
      <FormProvider {...createIntegrationFormMethods}>
        <Snackbar />
        <RubySettingEditorPanel title="E-Invoicing">
          <Typography variant="h6" className={classes.heading} gutterBottom>
            Connect to Avalara ELR
          </Typography>
          <div className={classes.subHeader}>
            <span>
              To generate the client credentials for Avalara ELR, log on to the ELR Monitor UI for
              either a Sandbox or a Production environment. Click on Integrations, and then click on
              Generate Token. Copy the resulting Key and Secret.
            </span>
          </div>
          {!loading && (
            <>
              <Grid item xs={12}>
                <div>
                  <FormSection
                    showDivider={false}
                    fields={objectMetadata.fields}
                    values={initialConnectionValues}
                    afterFieldChange={afterFieldChange}
                  />
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className={classes.activated}>
                  {enabled ? (
                    <ActiveIcon style={{ color: '#17981D' }} />
                  ) : (
                    <InactiveIcon style={{ height: '16px', width: '16px', color: '#999999' }} />
                  )}
                  <div style={{ display: 'flex', alignItems: 'baseline' }}>
                    <Typography
                      style={enabled ? { color: '#17981d' } : { color: '#999999' }}
                      className={classes.activatedText}
                      variant="body2"
                      component="p"
                    >
                      {enabled ? 'Active' : 'Inactive'}
                      {tenant && (!tenant.legalName || !tenant.country) && enabled && (
                        <div
                          style={{
                            fontWeight: 'normal',
                            marginLeft: '-27px',
                            textTransform: 'none',
                            marginTop: '5px',
                          }}
                        >
                          <WarningIcon
                            style={{
                              fontSize: '14px',
                              verticalAlign: 'middle',
                              color: 'darkorange',
                            }}
                          />
                          <span
                            style={{
                              marginLeft: '13px',
                              color: '#000',
                              opacity: '0.8',
                              fontSize: '14px',
                            }}
                          >
                            <strong>Warning: </strong>E-Invoicing cannot be activated without
                            completing your company and country setup. Please navigate to the
                            Company Settings page to ensure all required information is configured
                            before enabling this feature.
                          </span>
                        </div>
                      )}
                    </Typography>
                  </div>
                </div>
              </Grid>
              <Grid item xs={12} className={classes.buttonBarContainer}>
                <RubyButtonBar
                  variant="inPlace"
                  processing={false}
                  buttonSize="large"
                  leftButtons={[]}
                  rightButtons={
                    enabled
                      ? [
                          {
                            text: 'Deactivate',
                            onClick: async () => {
                              setshowDeactivateDialog(true);
                            },
                          },
                        ]
                      : [
                          {
                            disabled: false,
                            text: 'Activate',
                            onClick: async () => {
                              setshowActivateDialog(true);
                            },
                          },
                          {
                            text: 'Test',
                            disabled: false,
                            onClick: async () => {
                              const userInputs = getUserInputs();
                              if (!userInputs) {
                                return;
                              }
                              let detailId;
                              const { clientId, clientSecret, environment } = userInputs;
                              try {
                                const res = await onCreateAvalaraEInvoicingIntegration(
                                  {
                                    clientId: clientId,
                                    clientSecret: clientSecret,
                                    environment: environment,
                                    appCategory: 'EInvoicing',
                                    integrationType: 'AvalaraELR',
                                    id: id,
                                  },
                                  id ? 'PATCH' : 'POST',
                                );
                                if (res && res.data.id) {
                                  detailId = res.data.id;
                                  setId(detailId);
                                }
                                await handleTestConnection(
                                  clientId,
                                  clientSecret,
                                  environment,
                                  id || detailId,
                                  () => {
                                    showSnackbar(
                                      'confirm',
                                      'Success!',
                                      'The Avalara ELR connection has been successfully tested.',
                                    );
                                  },
                                );
                              } catch (e) {}
                            },
                          },
                        ]
                  }
                  customButtonBarStyles={{
                    paddingLeft: '12px',
                    paddingRight: 0,
                    width: 'calc(100% + 32px)',
                  }}
                />
              </Grid>
              {enabled && documentTypeList.length > 0 && documentType && (
                <>
                  <Divider
                    style={{
                      margin: '40px 0',
                    }}
                  />

                  <AvalaraEInvoicingCountryMandates
                    countryMandates={countryMandates}
                    setCountryMandates={setCountryMandates}
                    handleUpdateCountryMandatesStatus={handleUpdateCountryMandatesStatus}
                    companyList={companyList}
                    documentTypeList={documentTypeList}
                    loading={loadingMandates}
                    company={company}
                    setCompany={setCompany}
                    documentType={documentType}
                    setDocumentType={setDocumentType}
                    onUpdateAvalaraEInvoicingCountryMandatesFilter={
                      onUpdateAvalaraEInvoicingCountryMandatesFilter
                    }
                    onGetAvalaraEInvoicingCountryMandatesFilter={
                      onGetAvalaraEInvoicingCountryMandatesFilter
                    }
                    onCreateNewAvalaraEInvoicingCountryMandates={
                      onCreateNewAvalaraEInvoicingCountryMandates
                    }
                    updateCountryMandates={updateCountryMandates}
                    getCountryMandatesMappings={getCountryMandatesMappings}
                    getDefaultCountryMandates={getDefaultCountryMandates}
                    setCountryMandatesMappings={setCountryMandatesMappings}
                    onResotreFiledMappings={onHandleResotreAvalaraEInvoicingMappings}
                    onGetAvalaraEInvoicingAllowedXmlPath={onGetAvalaraEInvoicingAllowedXmlPath}
                    reloadCountryMandates={() => {}}
                  />

                  <Divider
                    style={{
                      margin: '40px 0',
                    }}
                  />

                  <Grid>
                    <Grid item xs={12} spacing={3}>
                      <Typography variant="h6" className={classes.heading} gutterBottom>
                        Retrieve Document Status
                      </Typography>
                      <Grid
                        item
                        xs={12}
                        className={clsx(classes.subHeader, classes.flexCenterStart)}
                      >
                        <Typography className={classes.heading} gutterBottom>
                          Retrieve Document Status
                        </Typography>
                        <Grid
                          item
                          xs={3}
                          style={{
                            marginLeft: '20px',
                          }}
                        >
                          <PickList
                            value={retriveStatus}
                            label=""
                            disableLabel={true}
                            field={{
                              apiName: 'sattus',
                              name: 'Status Filter',
                              type: 'text',
                            }}
                            name="params"
                            options={[
                              {
                                name: 'EveryHour',
                                id: 'EveryHour',
                                label: 'Every Hour',
                                value: 'EveryHour',
                              },
                              {
                                name: 'EveryTenMinutes',
                                id: 'EveryTenMinutes',
                                label: 'Every 10 minutes',
                                value: 'EveryTenMinutes',
                              },
                              {
                                name: 'EveryThirtyMinutes',
                                id: 'EveryThirtyMinutes',
                                label: 'Every 30 minutes',
                                value: 'EveryThirtyMinutes',
                              },
                            ].map((option: any) => ({
                              id: option.id,
                              name: option.label,
                              value: option.value,
                              label: option.label,
                            }))}
                            handleInputChange={(status) => {
                              handleUpdateRetriveStatus(status);
                            }}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </>
              )}
            </>
          )}
        </RubySettingEditorPanel>
      </FormProvider>
    </>
  );
};

export default AvalaraEInvoicingConnection;

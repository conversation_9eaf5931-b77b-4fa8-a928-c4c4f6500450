import React from 'react';

import InfoCard from '../info-card';
import { formatNumberForDisplay } from '../list/list';
import { MetricCardProps } from './interface';

export const MetricCard: React.FC<MetricCardProps> = (props) => {
  const {
    title,
    currencyIsoCode,
    value,
    change,
    basis,
    noSubInfo,
    subInfoPrefix = 'YoY',
    tipTitle,
    isCurrency,
    locale,
  } = props;

  const browserLocale = navigator.language ? navigator.language : 'en-US';
  const shortFormatter = new Intl.NumberFormat(browserLocale, {
    // @ts-ignore
    notation: 'compact',
    compactDisplay: 'short',
  });

  function getSubInfo() {
    if (noSubInfo === true) {
      return undefined;
    }
    if (change || change === 0) {
      return (basis ? basis : subInfoPrefix) + ' ' + (change * 100).toFixed(1) + '%';
    } else {
      return `${subInfoPrefix} -`;
    }
  }

  let subInfo = getSubInfo();
  const subInfoColor = change ? (change > 0 ? 'green' : 'red') : 'black';
  const infoColor = value < 0 ? 'red' : 'black';
  let info: string;
  if (isCurrency) {
    info = formatNumberForDisplay(value, currencyIsoCode, true, false, locale);
  } else {
    info = value < 0 ? `(${shortFormatter.format(-value)})` : shortFormatter.format(value);
  }

  return (
    <InfoCard
      title={title}
      info={info}
      infoSymbol={isCurrency ? '' : currencyIsoCode}
      infoColor={infoColor}
      subInfo={subInfo}
      subInfoColor={subInfoColor}
      tipTitle={tipTitle}
    />
  );
};

export default MetricCard;

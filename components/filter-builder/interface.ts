import { Reference } from 'react-popper';
import type { Condition, OrderByField, RubyFilter } from '../graph-ql-query-constructor';
import type { RubyObject } from '../metadata/interface';
import type { ListViewLocalState } from '../ruby-list-view-local-state-context';
import { RubyButtonProps } from '../ruby-button';

export interface DeleteFilterResponse {
  error?: string;
}

export type SaveFilterHandler = (
  objectMetadataApiName: string,
  filterName: string,
  conditions: Condition[],
) => Promise<RubyFilter>;

export type UpdateFiltersHandler = (
  objectMetadataApiName: string,
  referenceFilterId: string,
  filterName: string,
  conditions: Condition[],
) => Promise<RubyFilter>;

export type DeleteFilterHandler = (
  objectApiName: string,
  existingFilterId: string,
) => Promise<DeleteFilterResponse>;

export interface WarningBeforeUpdateProps {
  warningBeforeUpdate?: {
    show: boolean;
    title: string;
    message: string;
    dontAskAgain?: boolean;
    width?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
    getDontAskAgain: () => Promise<boolean>;
    storeDontAskAgain: (val: boolean) => Promise<void>;
  };
}
export interface Props extends WarningBeforeUpdateProps {
  filterName: string;
  searchResult: string;
  handleSearch?: (
    searchResult: string,
    filters: RubyFilter[],
    orderByFields?: OrderByField[],
  ) => Promise<void>;
  handleClose?: () => void;
  objectMetadata: RubyObject;
  allMetadatas?: RubyObject[];
  classes: any;
  referencedFilter: RubyFilter | null;
  saveFilter?: SaveFilterHandler;
  handleUpdateFilterNameWithValue?: (newName: string) => void;
  handleUpdateReferencedFilter?: (newFilter: RubyFilter) => void;
  savedFilters: RubyFilter[];
  updateListOfSavedFilters?: (newFilters: RubyFilter[]) => void;
  preLoadConditions?: Condition[];
  updateFilters?: UpdateFiltersHandler;
  deleteFilter?: DeleteFilterHandler;
  updateListViewLocalState?: (payload: Partial<ListViewLocalState>) => Promise<void>;
  hiddenButton?: boolean;
  graphqlGenerator?: boolean;
  setGraphqlGeneratorResult?: Function;
  invokeObject?: InvokeObject;
  disabled?: boolean;
  temporaryFilter?: RubyFilter[];
  setTemporaryFilter?: Function;
  fitlerForDeletion?: string;
  hideNestedCondition?: boolean;
  disableDeleteButton?: boolean;
  disableSaveButton?: boolean;
  currentUserId?: string;
  disableApplyButton?: boolean;
  disableSave?: boolean;
  specifyRelatedObjects?: Record<string, string[]>;
  requireAtLeastOneCondition?: boolean;
}

export interface States {
  openWarningBeforeUpdate: boolean;
  warningDontAskAgain: boolean;
}

export interface InvokeObject {
  validateAllConditions: Function;
  graphqlGenerator: Function;
}

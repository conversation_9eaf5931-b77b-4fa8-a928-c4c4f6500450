export interface TimeZone {
  zoneId: string;
  name: string;
}

export const TimeZoneList: TimeZone[] = [
  { zoneId: 'Pacific/Kiritimati', name: ' GMT+14:00 Line Islands Time (Pacific/Kiritimati) ' },
  { zoneId: 'Pacific/Apia', name: ' GMT+13:00 Apia Standard Time (Pacific/Apia) ' },
  { zoneId: 'Pacific/Enderbury', name: ' GMT+13:00 Phoenix Islands Time (Pacific/Enderbury) ' },
  { zoneId: 'Pacific/Fakaofo', name: ' GMT+13:00 Tokelau Time (Pacific/Fakaofo) ' },
  { zoneId: 'Pacific/Tongatapu', name: ' GMT+13:00 Tonga Standard Time (Pacific/Tongatapu) ' },
  { zoneId: 'Pacific/Chatham', name: ' GMT+12:45 Chatham Standard Time (Pacific/Chatham) ' },
  {
    zoneId: 'Antarctica/McMurdo',
    name: ' GMT+12:00 New Zealand Standard Time (Antarctica/McMurdo) ',
  },
  { zoneId: 'Asia/Anadyr', name: ' GMT+12:00 Anadyr Standard Time (Asia/Anadyr) ' },
  {
    zoneId: 'Asia/Kamchatka',
    name: ' GMT+12:00 Petropavlovsk-Kamchatski Standard Time (Asia/Kamchatka) ',
  },
  { zoneId: 'Pacific/Auckland', name: ' GMT+12:00 New Zealand Standard Time (Pacific/Auckland) ' },
  { zoneId: 'Pacific/Fiji', name: ' GMT+12:00 Fiji Standard Time (Pacific/Fiji) ' },
  { zoneId: 'Pacific/Funafuti', name: ' GMT+12:00 Tuvalu Time (Pacific/Funafuti) ' },
  { zoneId: 'Pacific/Kwajalein', name: ' GMT+12:00 Marshall Islands Time (Pacific/Kwajalein) ' },
  { zoneId: 'Pacific/Majuro', name: ' GMT+12:00 Marshall Islands Time (Pacific/Majuro) ' },
  { zoneId: 'Pacific/Nauru', name: ' GMT+12:00 Nauru Time (Pacific/Nauru) ' },
  { zoneId: 'Pacific/Tarawa', name: ' GMT+12:00 Gilbert Islands Time (Pacific/Tarawa) ' },
  { zoneId: 'Pacific/Wake', name: ' GMT+12:00 Wake Island Time (Pacific/Wake) ' },
  { zoneId: 'Pacific/Wallis', name: ' GMT+12:00 Wallis & Futuna Time (Pacific/Wallis) ' },
  { zoneId: 'Antarctica/Casey', name: ' GMT+11:00 Casey Time (Antarctica/Casey) ' },
  { zoneId: 'Asia/Magadan', name: ' GMT+11:00 Magadan Standard Time (Asia/Magadan) ' },
  { zoneId: 'Asia/Sakhalin', name: ' GMT+11:00 Sakhalin Standard Time (Asia/Sakhalin) ' },
  { zoneId: 'Asia/Srednekolymsk', name: ' GMT+11:00 Magadan Standard Time (Asia/Srednekolymsk) ' },
  {
    zoneId: 'Pacific/Bougainville',
    name: ' GMT+11:00 Bougainville Standard Time (Pacific/Bougainville) ',
  },
  { zoneId: 'Pacific/Efate', name: ' GMT+11:00 Vanuatu Standard Time (Pacific/Efate) ' },
  { zoneId: 'Pacific/Guadalcanal', name: ' GMT+11:00 Solomon Islands Time (Pacific/Guadalcanal) ' },
  { zoneId: 'Pacific/Kosrae', name: ' GMT+11:00 Kosrae Time (Pacific/Kosrae) ' },
  { zoneId: 'Pacific/Norfolk', name: ' GMT+11:00 Norfolk Island Standard Time (Pacific/Norfolk) ' },
  { zoneId: 'Pacific/Noumea', name: ' GMT+11:00 New Caledonia Standard Time (Pacific/Noumea) ' },
  { zoneId: 'Pacific/Ponape', name: ' GMT+11:00 Ponape Time (Pacific/Ponape) ' },
  {
    zoneId: 'Australia/Lord_Howe',
    name: 'GMT+10:30 Lord Howe Standard Time (Australia/Lord_Howe)',
  },
  {
    zoneId: 'Antarctica/DumontDUrville',
    name: ' GMT+10:00 Dumont-d’Urville Time (Antarctica/DumontDUrville) ',
  },
  {
    zoneId: 'Antarctica/Macquarie',
    name: ' GMT+10:00 Australian Eastern Standard Time (Antarctica/Macquarie) ',
  },
  { zoneId: 'Asia/Ust-Nera', name: ' GMT+10:00 Vladivostok Standard Time (Asia/Ust-Nera) ' },
  { zoneId: 'Asia/Vladivostok', name: ' GMT+10:00 Vladivostok Standard Time (Asia/Vladivostok) ' },
  {
    zoneId: 'Australia/Brisbane',
    name: ' GMT+10:00 Australian Eastern Standard Time (Australia/Brisbane) ',
  },
  {
    zoneId: 'Australia/Currie',
    name: ' GMT+10:00 Australian Eastern Standard Time (Australia/Currie) ',
  },
  {
    zoneId: 'Australia/Hobart',
    name: ' GMT+10:00 Australian Eastern Standard Time (Australia/Hobart) ',
  },
  {
    zoneId: 'Australia/Lindeman',
    name: ' GMT+10:00 Australian Eastern Standard Time (Australia/Lindeman) ',
  },
  {
    zoneId: 'Australia/Melbourne',
    name: ' GMT+10:00 Australian Eastern Standard Time (Australia/Melbourne) ',
  },
  {
    zoneId: 'Australia/Sydney',
    name: ' GMT+10:00 Australian Eastern Standard Time (Australia/Sydney) ',
  },
  { zoneId: 'Pacific/Guam', name: ' GMT+10:00 Chamorro Standard Time (Pacific/Guam) ' },
  {
    zoneId: 'Pacific/Port_Moresby',
    name: ' GMT+10:00 Papua New Guinea Time (Pacific/Port_Moresby) ',
  },
  { zoneId: 'Pacific/Saipan', name: ' GMT+10:00 Chamorro Standard Time (Pacific/Saipan) ' },
  { zoneId: 'Pacific/Truk', name: ' GMT+10:00 Chuuk Time (Pacific/Truk) ' },
  {
    zoneId: 'Australia/Adelaide',
    name: ' GMT+09:30 Australian Central Standard Time (Australia/Adelaide) ',
  },
  {
    zoneId: 'Australia/Broken_Hill',
    name: ' GMT+09:30 Australian Central Standard Time (Australia/Broken_Hill) ',
  },
  {
    zoneId: 'Australia/Darwin',
    name: ' GMT+09:30 Australian Central Standard Time (Australia/Darwin) ',
  },
  { zoneId: 'Asia/Chita', name: ' GMT+09:00 Yakutsk Standard Time (Asia/Chita) ' },
  { zoneId: 'Asia/Dili', name: ' GMT+09:00 East Timor Time (Asia/Dili) ' },
  { zoneId: 'Asia/Jayapura', name: ' GMT+09:00 Eastern Indonesia Time (Asia/Jayapura) ' },
  { zoneId: 'Asia/Khandyga', name: ' GMT+09:00 Yakutsk Standard Time (Asia/Khandyga) ' },
  { zoneId: 'Asia/Pyongyang', name: ' GMT+09:00 Korean Standard Time (Asia/Pyongyang) ' },
  { zoneId: 'Asia/Seoul', name: ' GMT+09:00 Korean Standard Time (Asia/Seoul) ' },
  { zoneId: 'Asia/Tokyo', name: ' GMT+09:00 Japan Standard Time (Asia/Tokyo) ' },
  { zoneId: 'Asia/Yakutsk', name: ' GMT+09:00 Yakutsk Standard Time (Asia/Yakutsk) ' },
  { zoneId: 'Pacific/Palau', name: ' GMT+09:00 Palau Time (Pacific/Palau) ' },
  {
    zoneId: 'Australia/Eucla',
    name: ' GMT+08:45 Australian Central Western Standard Time (Australia/Eucla) ',
  },
  { zoneId: 'Asia/Brunei', name: ' GMT+08:00 Brunei Darussalam Time (Asia/Brunei) ' },
  { zoneId: 'Asia/Choibalsan', name: ' GMT+08:00 Ulaanbaatar Standard Time (Asia/Choibalsan) ' },
  { zoneId: 'Asia/Hong_Kong', name: ' GMT+08:00 Hong Kong Standard Time (Asia/Hong_Kong) ' },
  { zoneId: 'Asia/Irkutsk', name: ' GMT+08:00 Irkutsk Standard Time (Asia/Irkutsk) ' },
  { zoneId: 'Asia/Kuala_Lumpur', name: ' GMT+08:00 Malaysia Time (Asia/Kuala_Lumpur) ' },
  { zoneId: 'Asia/Kuching', name: ' GMT+08:00 Malaysia Time (Asia/Kuching) ' },
  { zoneId: 'Asia/Macau', name: ' GMT+08:00 China Standard Time (Asia/Macau) ' },
  { zoneId: 'Asia/Makassar', name: ' GMT+08:00 Central Indonesia Time (Asia/Makassar) ' },
  { zoneId: 'Asia/Manila', name: ' GMT+08:00 Philippine Standard Time (Asia/Manila) ' },
  { zoneId: 'Asia/Shanghai', name: ' GMT+08:00 China Standard Time (Asia/Shanghai) ' },
  { zoneId: 'Asia/Singapore', name: ' GMT+08:00 Singapore Standard Time (Asia/Singapore) ' },
  { zoneId: 'Asia/Taipei', name: ' GMT+08:00 Taipei Standard Time (Asia/Taipei) ' },
  { zoneId: 'Asia/Ulaanbaatar', name: ' GMT+08:00 Ulaanbaatar Standard Time (Asia/Ulaanbaatar) ' },
  {
    zoneId: 'Australia/Perth',
    name: ' GMT+08:00 Australian Western Standard Time (Australia/Perth) ',
  },
  { zoneId: 'Antarctica/Davis', name: ' GMT+07:00 Davis Time (Antarctica/Davis) ' },
  { zoneId: 'Asia/Bangkok', name: ' GMT+07:00 Indochina Time (Asia/Bangkok) ' },
  { zoneId: 'Asia/Barnaul', name: ' GMT+07:00 Moscow Standard Time + 4 (Asia/Barnaul) ' },
  { zoneId: 'Asia/Ho_Chi_Minh', name: ' GMT+07:00 Indochina Time (Asia/Ho_Chi_Minh) ' },
  { zoneId: 'Asia/Hovd', name: ' GMT+07:00 Hovd Standard Time (Asia/Hovd) ' },
  { zoneId: 'Asia/Jakarta', name: ' GMT+07:00 Western Indonesia Time (Asia/Jakarta) ' },
  { zoneId: 'Asia/Krasnoyarsk', name: ' GMT+07:00 Krasnoyarsk Standard Time (Asia/Krasnoyarsk) ' },
  {
    zoneId: 'Asia/Novokuznetsk',
    name: ' GMT+07:00 Krasnoyarsk Standard Time (Asia/Novokuznetsk) ',
  },
  { zoneId: 'Asia/Novosibirsk', name: ' GMT+07:00 Novosibirsk Standard Time (Asia/Novosibirsk) ' },
  { zoneId: 'Asia/Phnom_Penh', name: ' GMT+07:00 Indochina Time (Asia/Phnom_Penh) ' },
  { zoneId: 'Asia/Pontianak', name: ' GMT+07:00 Western Indonesia Time (Asia/Pontianak) ' },
  { zoneId: 'Asia/Tomsk', name: ' GMT+07:00 Moscow Standard Time + 4 (Asia/Tomsk) ' },
  { zoneId: 'Asia/Vientiane', name: ' GMT+07:00 Indochina Time (Asia/Vientiane) ' },
  { zoneId: 'Indian/Christmas', name: ' GMT+07:00 Christmas Island Time (Indian/Christmas) ' },
  { zoneId: 'Asia/Rangoon', name: ' GMT+06:30 Myanmar Time (Asia/Rangoon) ' },
  { zoneId: 'Indian/Cocos', name: ' GMT+06:30 Cocos Islands Time (Indian/Cocos) ' },
  { zoneId: 'Antarctica/Vostok', name: ' GMT+06:00 Vostok Time (Antarctica/Vostok) ' },
  { zoneId: 'Asia/Almaty', name: ' GMT+06:00 East Kazakhstan Time (Asia/Almaty) ' },
  { zoneId: 'Asia/Bishkek', name: ' GMT+06:00 Kyrgyzstan Time (Asia/Bishkek) ' },
  { zoneId: 'Asia/Dhaka', name: ' GMT+06:00 Bangladesh Standard Time (Asia/Dhaka) ' },
  { zoneId: 'Asia/Omsk', name: ' GMT+06:00 Omsk Standard Time (Asia/Omsk) ' },
  { zoneId: 'Asia/Qostanay', name: ' GMT+06:00 East Kazakhstan Time (Asia/Qostanay) ' },
  { zoneId: 'Asia/Thimphu', name: ' GMT+06:00 Bhutan Time (Asia/Thimphu) ' },
  { zoneId: 'Asia/Urumqi', name: ' GMT+06:00 China Standard Time (Asia/Urumqi) ' },
  { zoneId: 'Indian/Chagos', name: ' GMT+06:00 Indian Ocean Time (Indian/Chagos) ' },
  { zoneId: 'Asia/Kathmandu', name: ' GMT+05:45 Nepal Time (Asia/Kathmandu) ' },
  { zoneId: 'Asia/Colombo', name: ' GMT+05:30 India Standard Time (Asia/Colombo) ' },
  { zoneId: 'Asia/Kolkata', name: ' GMT+05:30 India Standard Time (Asia/Kolkata) ' },
  { zoneId: 'Antarctica/Mawson', name: ' GMT+05:00 Mawson Time (Antarctica/Mawson) ' },
  { zoneId: 'Asia/Aqtau', name: ' GMT+05:00 West Kazakhstan Time (Asia/Aqtau) ' },
  { zoneId: 'Asia/Aqtobe', name: ' GMT+05:00 West Kazakhstan Time (Asia/Aqtobe) ' },
  { zoneId: 'Asia/Ashgabat', name: ' GMT+05:00 Turkmenistan Standard Time (Asia/Ashgabat) ' },
  { zoneId: 'Asia/Atyrau', name: ' GMT+05:00 West Kazakhstan Time (Asia/Atyrau) ' },
  { zoneId: 'Asia/Dushanbe', name: ' GMT+05:00 Tajikistan Time (Asia/Dushanbe) ' },
  { zoneId: 'Asia/Karachi', name: ' GMT+05:00 Pakistan Standard Time (Asia/Karachi) ' },
  { zoneId: 'Asia/Oral', name: ' GMT+05:00 West Kazakhstan Time (Asia/Oral) ' },
  { zoneId: 'Asia/Qyzylorda', name: ' GMT+05:00 West Kazakhstan Time (Asia/Qyzylorda) ' },
  { zoneId: 'Asia/Samarkand', name: ' GMT+05:00 Uzbekistan Standard Time (Asia/Samarkand) ' },
  { zoneId: 'Asia/Tashkent', name: ' GMT+05:00 Uzbekistan Standard Time (Asia/Tashkent) ' },
  {
    zoneId: 'Asia/Yekaterinburg',
    name: ' GMT+05:00 Yekaterinburg Standard Time (Asia/Yekaterinburg) ',
  },
  {
    zoneId: 'Indian/Kerguelen',
    name: ' GMT+05:00 French Southern & Antarctic Time (Indian/Kerguelen) ',
  },
  { zoneId: 'Indian/Maldives', name: ' GMT+05:00 Maldives Time (Indian/Maldives) ' },
  { zoneId: 'Asia/Kabul', name: ' GMT+04:30 Afghanistan Time (Asia/Kabul) ' },
  { zoneId: 'Asia/Baku', name: ' GMT+04:00 Azerbaijan Standard Time (Asia/Baku) ' },
  { zoneId: 'Asia/Dubai', name: ' GMT+04:00 Gulf Standard Time (Asia/Dubai) ' },
  { zoneId: 'Asia/Muscat', name: ' GMT+04:00 Gulf Standard Time (Asia/Muscat) ' },
  { zoneId: 'Asia/Tbilisi', name: ' GMT+04:00 Georgia Standard Time (Asia/Tbilisi) ' },
  { zoneId: 'Asia/Yerevan', name: ' GMT+04:00 Armenia Standard Time (Asia/Yerevan) ' },
  { zoneId: 'Europe/Astrakhan', name: ' GMT+04:00 Samara Standard Time (Europe/Astrakhan) ' },
  { zoneId: 'Europe/Samara', name: ' GMT+04:00 Samara Standard Time (Europe/Samara) ' },
  { zoneId: 'Europe/Saratov', name: ' GMT+04:00 Moscow Standard Time + 1 (Europe/Saratov) ' },
  { zoneId: 'Europe/Ulyanovsk', name: ' GMT+04:00 Moscow Standard Time + 1 (Europe/Ulyanovsk) ' },
  { zoneId: 'Indian/Mahe', name: ' GMT+04:00 Seychelles Time (Indian/Mahe) ' },
  { zoneId: 'Indian/Mauritius', name: ' GMT+04:00 Mauritius Standard Time (Indian/Mauritius) ' },
  { zoneId: 'Indian/Reunion', name: ' GMT+04:00 Réunion Time (Indian/Reunion) ' },
  { zoneId: 'Asia/Tehran', name: ' GMT+03:30 Iran Standard Time (Asia/Tehran) ' },
  { zoneId: 'Africa/Addis_Ababa', name: ' GMT+03:00 East Africa Time (Africa/Addis_Ababa) ' },
  { zoneId: 'Africa/Asmera', name: ' GMT+03:00 East Africa Time (Africa/Asmera) ' },
  { zoneId: 'Africa/Dar_es_Salaam', name: ' GMT+03:00 East Africa Time (Africa/Dar_es_Salaam) ' },
  { zoneId: 'Africa/Djibouti', name: ' GMT+03:00 East Africa Time (Africa/Djibouti) ' },
  { zoneId: 'Africa/Kampala', name: ' GMT+03:00 East Africa Time (Africa/Kampala) ' },
  { zoneId: 'Africa/Mogadishu', name: ' GMT+03:00 East Africa Time (Africa/Mogadishu) ' },
  { zoneId: 'Africa/Nairobi', name: ' GMT+03:00 East Africa Time (Africa/Nairobi) ' },
  { zoneId: 'Antarctica/Syowa', name: ' GMT+03:00 Syowa Time (Antarctica/Syowa) ' },
  { zoneId: 'Asia/Aden', name: ' GMT+03:00 Arabian Standard Time (Asia/Aden) ' },
  { zoneId: 'Asia/Baghdad', name: ' GMT+03:00 Arabian Standard Time (Asia/Baghdad) ' },
  { zoneId: 'Asia/Bahrain', name: ' GMT+03:00 Arabian Standard Time (Asia/Bahrain) ' },
  { zoneId: 'Asia/Kuwait', name: ' GMT+03:00 Arabian Standard Time (Asia/Kuwait) ' },
  { zoneId: 'Asia/Qatar', name: ' GMT+03:00 Arabian Standard Time (Asia/Qatar) ' },
  { zoneId: 'Asia/Riyadh', name: ' GMT+03:00 Arabian Standard Time (Asia/Riyadh) ' },
  {
    zoneId: 'Europe/Istanbul',
    name: ' GMT+03:00 Eastern European Standard Time (Europe/Istanbul) ',
  },
  { zoneId: 'Europe/Kirov', name: ' GMT+03:00 Moscow Standard Time (Europe/Kirov) ' },
  { zoneId: 'Europe/Minsk', name: ' GMT+03:00 Moscow Standard Time (Europe/Minsk) ' },
  { zoneId: 'Europe/Moscow', name: ' GMT+03:00 Moscow Standard Time (Europe/Moscow) ' },
  { zoneId: 'Europe/Simferopol', name: ' GMT+03:00 Moscow Standard Time (Europe/Simferopol) ' },
  { zoneId: 'Europe/Volgograd', name: ' GMT+03:00 Volgograd Standard Time (Europe/Volgograd) ' },
  { zoneId: 'Indian/Antananarivo', name: ' GMT+03:00 East Africa Time (Indian/Antananarivo) ' },
  { zoneId: 'Indian/Comoro', name: ' GMT+03:00 East Africa Time (Indian/Comoro) ' },
  { zoneId: 'Indian/Mayotte', name: ' GMT+03:00 East Africa Time (Indian/Mayotte) ' },
  { zoneId: 'Africa/Blantyre', name: ' GMT+02:00 Central Africa Time (Africa/Blantyre) ' },
  { zoneId: 'Africa/Bujumbura', name: ' GMT+02:00 Central Africa Time (Africa/Bujumbura) ' },
  { zoneId: 'Africa/Cairo', name: ' GMT+02:00 Eastern European Standard Time (Africa/Cairo) ' },
  { zoneId: 'Africa/Gaborone', name: ' GMT+02:00 Central Africa Time (Africa/Gaborone) ' },
  { zoneId: 'Africa/Harare', name: ' GMT+02:00 Central Africa Time (Africa/Harare) ' },
  {
    zoneId: 'Africa/Johannesburg',
    name: ' GMT+02:00 South Africa Standard Time (Africa/Johannesburg) ',
  },
  { zoneId: 'Africa/Juba', name: ' GMT+02:00 Central Africa Time (Africa/Juba) ' },
  { zoneId: 'Africa/Khartoum', name: ' GMT+02:00 Central Africa Time (Africa/Khartoum) ' },
  { zoneId: 'Africa/Kigali', name: ' GMT+02:00 Central Africa Time (Africa/Kigali) ' },
  { zoneId: 'Africa/Lubumbashi', name: ' GMT+02:00 Central Africa Time (Africa/Lubumbashi) ' },
  { zoneId: 'Africa/Lusaka', name: ' GMT+02:00 Central Africa Time (Africa/Lusaka) ' },
  { zoneId: 'Africa/Maputo', name: ' GMT+02:00 Central Africa Time (Africa/Maputo) ' },
  { zoneId: 'Africa/Maseru', name: ' GMT+02:00 South Africa Standard Time (Africa/Maseru) ' },
  { zoneId: 'Africa/Mbabane', name: ' GMT+02:00 South Africa Standard Time (Africa/Mbabane) ' },
  { zoneId: 'Africa/Tripoli', name: ' GMT+02:00 Eastern European Standard Time (Africa/Tripoli) ' },
  { zoneId: 'Africa/Windhoek', name: ' GMT+02:00 Central Africa Time (Africa/Windhoek) ' },
  { zoneId: 'Asia/Amman', name: ' GMT+02:00 Eastern European Standard Time (Asia/Amman) ' },
  { zoneId: 'Asia/Beirut', name: ' GMT+02:00 Eastern European Standard Time (Asia/Beirut) ' },
  { zoneId: 'Asia/Damascus', name: ' GMT+02:00 Eastern European Standard Time (Asia/Damascus) ' },
  { zoneId: 'Asia/Famagusta', name: ' GMT+02:00 Eastern European Standard Time (Asia/Famagusta) ' },
  { zoneId: 'Asia/Gaza', name: ' GMT+02:00 Eastern European Standard Time (Asia/Gaza) ' },
  { zoneId: 'Asia/Hebron', name: ' GMT+02:00 Eastern European Standard Time (Asia/Hebron) ' },
  { zoneId: 'Asia/Jerusalem', name: ' GMT+02:00 Israel Standard Time (Asia/Jerusalem) ' },
  { zoneId: 'Asia/Nicosia', name: ' GMT+02:00 Eastern European Standard Time (Asia/Nicosia) ' },
  { zoneId: 'Europe/Athens', name: ' GMT+02:00 Eastern European Standard Time (Europe/Athens) ' },
  {
    zoneId: 'Europe/Bucharest',
    name: ' GMT+02:00 Eastern European Standard Time (Europe/Bucharest) ',
  },
  {
    zoneId: 'Europe/Chisinau',
    name: ' GMT+02:00 Eastern European Standard Time (Europe/Chisinau) ',
  },
  {
    zoneId: 'Europe/Helsinki',
    name: ' GMT+02:00 Eastern European Standard Time (Europe/Helsinki) ',
  },
  {
    zoneId: 'Europe/Kaliningrad',
    name: ' GMT+02:00 Eastern European Standard Time (Europe/Kaliningrad) ',
  },
  { zoneId: 'Europe/Kiev', name: ' GMT+02:00 Eastern European Standard Time (Europe/Kiev) ' },
  {
    zoneId: 'Europe/Mariehamn',
    name: ' GMT+02:00 Eastern European Standard Time (Europe/Mariehamn) ',
  },
  { zoneId: 'Europe/Riga', name: ' GMT+02:00 Eastern European Standard Time (Europe/Riga) ' },
  { zoneId: 'Europe/Sofia', name: ' GMT+02:00 Eastern European Standard Time (Europe/Sofia) ' },
  { zoneId: 'Europe/Tallinn', name: ' GMT+02:00 Eastern European Standard Time (Europe/Tallinn) ' },
  {
    zoneId: 'Europe/Uzhgorod',
    name: ' GMT+02:00 Eastern European Standard Time (Europe/Uzhgorod) ',
  },
  { zoneId: 'Europe/Vilnius', name: ' GMT+02:00 Eastern European Standard Time (Europe/Vilnius) ' },
  {
    zoneId: 'Europe/Zaporozhye',
    name: ' GMT+02:00 Eastern European Standard Time (Europe/Zaporozhye) ',
  },
  { zoneId: 'Africa/Algiers', name: ' GMT+01:00 Central European Standard Time (Africa/Algiers) ' },
  { zoneId: 'Africa/Bangui', name: ' GMT+01:00 West Africa Standard Time (Africa/Bangui) ' },
  {
    zoneId: 'Africa/Brazzaville',
    name: ' GMT+01:00 West Africa Standard Time (Africa/Brazzaville) ',
  },
  { zoneId: 'Africa/Ceuta', name: ' GMT+01:00 Central European Standard Time (Africa/Ceuta) ' },
  { zoneId: 'Africa/Douala', name: ' GMT+01:00 West Africa Standard Time (Africa/Douala) ' },
  { zoneId: 'Africa/Kinshasa', name: ' GMT+01:00 West Africa Standard Time (Africa/Kinshasa) ' },
  { zoneId: 'Africa/Lagos', name: ' GMT+01:00 West Africa Standard Time (Africa/Lagos) ' },
  {
    zoneId: 'Africa/Libreville',
    name: ' GMT+01:00 West Africa Standard Time (Africa/Libreville) ',
  },
  { zoneId: 'Africa/Luanda', name: ' GMT+01:00 West Africa Standard Time (Africa/Luanda) ' },
  { zoneId: 'Africa/Malabo', name: ' GMT+01:00 West Africa Standard Time (Africa/Malabo) ' },
  { zoneId: 'Africa/Ndjamena', name: ' GMT+01:00 West Africa Standard Time (Africa/Ndjamena) ' },
  { zoneId: 'Africa/Niamey', name: ' GMT+01:00 West Africa Standard Time (Africa/Niamey) ' },
  {
    zoneId: 'Africa/Porto-Novo',
    name: ' GMT+01:00 West Africa Standard Time (Africa/Porto-Novo) ',
  },
  { zoneId: 'Africa/Tunis', name: ' GMT+01:00 Central European Standard Time (Africa/Tunis) ' },
  {
    zoneId: 'Arctic/Longyearbyen',
    name: ' GMT+01:00 Central European Standard Time (Arctic/Longyearbyen) ',
  },
  {
    zoneId: 'Europe/Amsterdam',
    name: ' GMT+01:00 Central European Standard Time (Europe/Amsterdam) ',
  },
  { zoneId: 'Europe/Andorra', name: ' GMT+01:00 Central European Standard Time (Europe/Andorra) ' },
  {
    zoneId: 'Europe/Belgrade',
    name: ' GMT+01:00 Central European Standard Time (Europe/Belgrade) ',
  },
  { zoneId: 'Europe/Berlin', name: ' GMT+01:00 Central European Standard Time (Europe/Berlin) ' },
  {
    zoneId: 'Europe/Bratislava',
    name: ' GMT+01:00 Central European Standard Time (Europe/Bratislava) ',
  },
  {
    zoneId: 'Europe/Brussels',
    name: ' GMT+01:00 Central European Standard Time (Europe/Brussels) ',
  },
  {
    zoneId: 'Europe/Budapest',
    name: ' GMT+01:00 Central European Standard Time (Europe/Budapest) ',
  },
  {
    zoneId: 'Europe/Busingen',
    name: ' GMT+01:00 Central European Standard Time (Europe/Busingen) ',
  },
  {
    zoneId: 'Europe/Copenhagen',
    name: ' GMT+01:00 Central European Standard Time (Europe/Copenhagen) ',
  },
  {
    zoneId: 'Europe/Gibraltar',
    name: ' GMT+01:00 Central European Standard Time (Europe/Gibraltar) ',
  },
  {
    zoneId: 'Europe/Ljubljana',
    name: ' GMT+01:00 Central European Standard Time (Europe/Ljubljana) ',
  },
  {
    zoneId: 'Europe/Luxembourg',
    name: ' GMT+01:00 Central European Standard Time (Europe/Luxembourg) ',
  },
  { zoneId: 'Europe/Madrid', name: ' GMT+01:00 Central European Standard Time (Europe/Madrid) ' },
  { zoneId: 'Europe/Malta', name: ' GMT+01:00 Central European Standard Time (Europe/Malta) ' },
  { zoneId: 'Europe/Monaco', name: ' GMT+01:00 Central European Standard Time (Europe/Monaco) ' },
  { zoneId: 'Europe/Oslo', name: ' GMT+01:00 Central European Standard Time (Europe/Oslo) ' },
  { zoneId: 'Europe/Paris', name: ' GMT+01:00 Central European Standard Time (Europe/Paris) ' },
  {
    zoneId: 'Europe/Podgorica',
    name: ' GMT+01:00 Central European Standard Time (Europe/Podgorica) ',
  },
  { zoneId: 'Europe/Prague', name: ' GMT+01:00 Central European Standard Time (Europe/Prague) ' },
  { zoneId: 'Europe/Rome', name: ' GMT+01:00 Central European Standard Time (Europe/Rome) ' },
  {
    zoneId: 'Europe/San_Marino',
    name: ' GMT+01:00 Central European Standard Time (Europe/San_Marino) ',
  },
  {
    zoneId: 'Europe/Sarajevo',
    name: ' GMT+01:00 Central European Standard Time (Europe/Sarajevo) ',
  },
  { zoneId: 'Europe/Skopje', name: ' GMT+01:00 Central European Standard Time (Europe/Skopje) ' },
  {
    zoneId: 'Europe/Stockholm',
    name: ' GMT+01:00 Central European Standard Time (Europe/Stockholm) ',
  },
  { zoneId: 'Europe/Tirane', name: ' GMT+01:00 Central European Standard Time (Europe/Tirane) ' },
  { zoneId: 'Europe/Vaduz', name: ' GMT+01:00 Central European Standard Time (Europe/Vaduz) ' },
  { zoneId: 'Europe/Vatican', name: ' GMT+01:00 Central European Standard Time (Europe/Vatican) ' },
  { zoneId: 'Europe/Vienna', name: ' GMT+01:00 Central European Standard Time (Europe/Vienna) ' },
  { zoneId: 'Europe/Warsaw', name: ' GMT+01:00 Central European Standard Time (Europe/Warsaw) ' },
  { zoneId: 'Europe/Zagreb', name: ' GMT+01:00 Central European Standard Time (Europe/Zagreb) ' },
  { zoneId: 'Europe/Zurich', name: ' GMT+01:00 Central European Standard Time (Europe/Zurich) ' },
  { zoneId: 'Africa/Abidjan', name: ' GMT+00:00 Greenwich Mean Time (Africa/Abidjan) ' },
  { zoneId: 'Africa/Accra', name: ' GMT+00:00 Greenwich Mean Time (Africa/Accra) ' },
  { zoneId: 'Africa/Bamako', name: ' GMT+00:00 Greenwich Mean Time (Africa/Bamako) ' },
  { zoneId: 'Africa/Banjul', name: ' GMT+00:00 Greenwich Mean Time (Africa/Banjul) ' },
  { zoneId: 'Africa/Bissau', name: ' GMT+00:00 Greenwich Mean Time (Africa/Bissau) ' },
  {
    zoneId: 'Africa/Casablanca',
    name: ' GMT+00:00 Western European Standard Time (Africa/Casablanca) ',
  },
  { zoneId: 'Africa/Conakry', name: ' GMT+00:00 Greenwich Mean Time (Africa/Conakry) ' },
  { zoneId: 'Africa/Dakar', name: ' GMT+00:00 Greenwich Mean Time (Africa/Dakar) ' },
  {
    zoneId: 'Africa/El_Aaiun',
    name: ' GMT+00:00 Western European Standard Time (Africa/El_Aaiun) ',
  },
  { zoneId: 'Africa/Freetown', name: ' GMT+00:00 Greenwich Mean Time (Africa/Freetown) ' },
  { zoneId: 'Africa/Lome', name: ' GMT+00:00 Greenwich Mean Time (Africa/Lome) ' },
  { zoneId: 'Africa/Monrovia', name: ' GMT+00:00 Greenwich Mean Time (Africa/Monrovia) ' },
  { zoneId: 'Africa/Nouakchott', name: ' GMT+00:00 Greenwich Mean Time (Africa/Nouakchott) ' },
  { zoneId: 'Africa/Ouagadougou', name: ' GMT+00:00 Greenwich Mean Time (Africa/Ouagadougou) ' },
  { zoneId: 'Africa/Sao_Tome', name: ' GMT+00:00 Greenwich Mean Time (Africa/Sao_Tome) ' },
  {
    zoneId: 'America/Danmarkshavn',
    name: ' GMT+00:00 Greenwich Mean Time (America/Danmarkshavn) ',
  },
  { zoneId: 'Antarctica/Troll', name: ' GMT+00:00 Greenwich Mean Time (Antarctica/Troll) ' },
  {
    zoneId: 'Atlantic/Canary',
    name: ' GMT+00:00 Western European Standard Time (Atlantic/Canary) ',
  },
  {
    zoneId: 'Atlantic/Faeroe',
    name: ' GMT+00:00 Western European Standard Time (Atlantic/Faeroe) ',
  },
  {
    zoneId: 'Atlantic/Madeira',
    name: ' GMT+00:00 Western European Standard Time (Atlantic/Madeira) ',
  },
  { zoneId: 'Atlantic/Reykjavik', name: ' GMT+00:00 Greenwich Mean Time (Atlantic/Reykjavik) ' },
  { zoneId: 'Atlantic/St_Helena', name: ' GMT+00:00 Greenwich Mean Time (Atlantic/St_Helena) ' },
  { zoneId: 'Europe/Dublin', name: ' GMT+00:00 Greenwich Mean Time (Europe/Dublin) ' },
  { zoneId: 'Europe/Guernsey', name: ' GMT+00:00 Greenwich Mean Time (Europe/Guernsey) ' },
  { zoneId: 'Europe/Isle_of_Man', name: ' GMT+00:00 Greenwich Mean Time (Europe/Isle_of_Man) ' },
  { zoneId: 'Europe/Jersey', name: ' GMT+00:00 Greenwich Mean Time (Europe/Jersey) ' },
  { zoneId: 'Europe/Lisbon', name: ' GMT+00:00 Western European Standard Time (Europe/Lisbon) ' },
  { zoneId: 'Europe/London', name: ' GMT+00:00 Greenwich Mean Time (Europe/London) ' },
  { zoneId: 'GMT', name: ' GMT+00:00 Greenwich Mean Time (GMT) ' },
  {
    zoneId: 'America/Scoresbysund',
    name: ' GMT-01:00 East Greenland Standard Time (America/Scoresbysund) ',
  },
  { zoneId: 'Atlantic/Azores', name: ' GMT-01:00 Azores Standard Time (Atlantic/Azores) ' },
  {
    zoneId: 'Atlantic/Cape_Verde',
    name: ' GMT-01:00 Cape Verde Standard Time (Atlantic/Cape_Verde) ',
  },
  {
    zoneId: 'America/Noronha',
    name: ' GMT-02:00 Fernando de Noronha Standard Time (America/Noronha) ',
  },
  {
    zoneId: 'Atlantic/South_Georgia',
    name: ' GMT-02:00 South Georgia Time (Atlantic/South_Georgia) ',
  },
  { zoneId: 'America/Araguaina', name: ' GMT-03:00 Brasilia Standard Time (America/Araguaina) ' },
  {
    zoneId: 'America/Argentina/Buenos_Aires',
    name: ' GMT-03:00 Argentina Standard Time (America/Argentina/Buenos_Aires) ',
  },
  {
    zoneId: 'America/Argentina/La_Rioja',
    name: ' GMT-03:00 Argentina Standard Time (America/Argentina/La_Rioja) ',
  },
  {
    zoneId: 'America/Argentina/Rio_Gallegos',
    name: ' GMT-03:00 Argentina Standard Time (America/Argentina/Rio_Gallegos) ',
  },
  {
    zoneId: 'America/Argentina/Salta',
    name: ' GMT-03:00 Argentina Standard Time (America/Argentina/Salta) ',
  },
  {
    zoneId: 'America/Argentina/San_Juan',
    name: ' GMT-03:00 Argentina Standard Time (America/Argentina/San_Juan) ',
  },
  {
    zoneId: 'America/Argentina/San_Luis',
    name: ' GMT-03:00 Argentina Standard Time (America/Argentina/San_Luis) ',
  },
  {
    zoneId: 'America/Argentina/Tucuman',
    name: ' GMT-03:00 Argentina Standard Time (America/Argentina/Tucuman) ',
  },
  {
    zoneId: 'America/Argentina/Ushuaia',
    name: ' GMT-03:00 Argentina Standard Time (America/Argentina/Ushuaia) ',
  },
  { zoneId: 'America/Bahia', name: ' GMT-03:00 Brasilia Standard Time (America/Bahia) ' },
  { zoneId: 'America/Belem', name: ' GMT-03:00 Brasilia Standard Time (America/Belem) ' },
  { zoneId: 'America/Catamarca', name: ' GMT-03:00 Argentina Standard Time (America/Catamarca) ' },
  { zoneId: 'America/Cayenne', name: ' GMT-03:00 French Guiana Time (America/Cayenne) ' },
  { zoneId: 'America/Cordoba', name: ' GMT-03:00 Argentina Standard Time (America/Cordoba) ' },
  { zoneId: 'America/Fortaleza', name: ' GMT-03:00 Brasilia Standard Time (America/Fortaleza) ' },
  { zoneId: 'America/Godthab', name: ' GMT-03:00 West Greenland Standard Time (America/Godthab) ' },
  { zoneId: 'America/Jujuy', name: ' GMT-03:00 Argentina Standard Time (America/Jujuy) ' },
  { zoneId: 'America/Maceio', name: ' GMT-03:00 Brasilia Standard Time (America/Maceio) ' },
  { zoneId: 'America/Mendoza', name: ' GMT-03:00 Argentina Standard Time (America/Mendoza) ' },
  {
    zoneId: 'America/Miquelon',
    name: ' GMT-03:00 St. Pierre & Miquelon Standard Time (America/Miquelon) ',
  },
  { zoneId: 'America/Montevideo', name: ' GMT-03:00 Uruguay Standard Time (America/Montevideo) ' },
  { zoneId: 'America/Paramaribo', name: ' GMT-03:00 Suriname Time (America/Paramaribo) ' },
  {
    zoneId: 'America/Punta_Arenas',
    name: ' GMT-03:00 Chile Standard Time (America/Punta_Arenas) ',
  },
  { zoneId: 'America/Recife', name: ' GMT-03:00 Brasilia Standard Time (America/Recife) ' },
  { zoneId: 'America/Santarem', name: ' GMT-03:00 Brasilia Standard Time (America/Santarem) ' },
  { zoneId: 'America/Sao_Paulo', name: ' GMT-03:00 Brasilia Standard Time (America/Sao_Paulo) ' },
  { zoneId: 'Antarctica/Palmer', name: ' GMT-03:00 Chile Standard Time (Antarctica/Palmer) ' },
  { zoneId: 'Antarctica/Rothera', name: ' GMT-03:00 Rothera Time (Antarctica/Rothera) ' },
  {
    zoneId: 'Atlantic/Stanley',
    name: ' GMT-03:00 Falkland Islands Standard Time (Atlantic/Stanley) ',
  },
  { zoneId: 'America/St_Johns', name: ' GMT-03:30 Newfoundland Standard Time (America/St_Johns) ' },
  { zoneId: 'America/Anguilla', name: ' GMT-04:00 Atlantic Standard Time (America/Anguilla) ' },
  { zoneId: 'America/Antigua', name: ' GMT-04:00 Atlantic Standard Time (America/Antigua) ' },
  { zoneId: 'America/Aruba', name: ' GMT-04:00 Atlantic Standard Time (America/Aruba) ' },
  { zoneId: 'America/Asuncion', name: ' GMT-04:00 Paraguay Standard Time (America/Asuncion) ' },
  { zoneId: 'America/Barbados', name: ' GMT-04:00 Atlantic Standard Time (America/Barbados) ' },
  {
    zoneId: 'America/Blanc-Sablon',
    name: ' GMT-04:00 Atlantic Standard Time (America/Blanc-Sablon) ',
  },
  { zoneId: 'America/Boa_Vista', name: ' GMT-04:00 Amazon Standard Time (America/Boa_Vista) ' },
  {
    zoneId: 'America/Campo_Grande',
    name: ' GMT-04:00 Amazon Standard Time (America/Campo_Grande) ',
  },
  { zoneId: 'America/Caracas', name: ' GMT-04:00 Venezuela Time (America/Caracas) ' },
  { zoneId: 'America/Cuiaba', name: ' GMT-04:00 Amazon Standard Time (America/Cuiaba) ' },
  { zoneId: 'America/Curacao', name: ' GMT-04:00 Atlantic Standard Time (America/Curacao) ' },
  { zoneId: 'America/Dominica', name: ' GMT-04:00 Atlantic Standard Time (America/Dominica) ' },
  { zoneId: 'America/Glace_Bay', name: ' GMT-04:00 Atlantic Standard Time (America/Glace_Bay) ' },
  { zoneId: 'America/Goose_Bay', name: ' GMT-04:00 Atlantic Standard Time (America/Goose_Bay) ' },
  { zoneId: 'America/Grenada', name: ' GMT-04:00 Atlantic Standard Time (America/Grenada) ' },
  { zoneId: 'America/Guadeloupe', name: ' GMT-04:00 Atlantic Standard Time (America/Guadeloupe) ' },
  { zoneId: 'America/Guyana', name: ' GMT-04:00 Guyana Time (America/Guyana) ' },
  { zoneId: 'America/Halifax', name: ' GMT-04:00 Atlantic Standard Time (America/Halifax) ' },
  { zoneId: 'America/Kralendijk', name: ' GMT-04:00 Atlantic Standard Time (America/Kralendijk) ' },
  { zoneId: 'America/La_Paz', name: ' GMT-04:00 Bolivia Time (America/La_Paz) ' },
  {
    zoneId: 'America/Lower_Princes',
    name: ' GMT-04:00 Atlantic Standard Time (America/Lower_Princes) ',
  },
  { zoneId: 'America/Manaus', name: ' GMT-04:00 Amazon Standard Time (America/Manaus) ' },
  { zoneId: 'America/Marigot', name: ' GMT-04:00 Atlantic Standard Time (America/Marigot) ' },
  { zoneId: 'America/Martinique', name: ' GMT-04:00 Atlantic Standard Time (America/Martinique) ' },
  { zoneId: 'America/Moncton', name: ' GMT-04:00 Atlantic Standard Time (America/Moncton) ' },
  { zoneId: 'America/Montserrat', name: ' GMT-04:00 Atlantic Standard Time (America/Montserrat) ' },
  {
    zoneId: 'America/Port_of_Spain',
    name: ' GMT-04:00 Atlantic Standard Time (America/Port_of_Spain) ',
  },
  { zoneId: 'America/Porto_Velho', name: ' GMT-04:00 Amazon Standard Time (America/Porto_Velho) ' },
  {
    zoneId: 'America/Puerto_Rico',
    name: ' GMT-04:00 Atlantic Standard Time (America/Puerto_Rico) ',
  },
  { zoneId: 'America/Santiago', name: ' GMT-04:00 Chile Standard Time (America/Santiago) ' },
  {
    zoneId: 'America/Santo_Domingo',
    name: ' GMT-04:00 Atlantic Standard Time (America/Santo_Domingo) ',
  },
  {
    zoneId: 'America/St_Barthelemy',
    name: ' GMT-04:00 Atlantic Standard Time (America/St_Barthelemy) ',
  },
  { zoneId: 'America/St_Kitts', name: ' GMT-04:00 Atlantic Standard Time (America/St_Kitts) ' },
  { zoneId: 'America/St_Lucia', name: ' GMT-04:00 Atlantic Standard Time (America/St_Lucia) ' },
  { zoneId: 'America/St_Thomas', name: ' GMT-04:00 Atlantic Standard Time (America/St_Thomas) ' },
  { zoneId: 'America/St_Vincent', name: ' GMT-04:00 Atlantic Standard Time (America/St_Vincent) ' },
  { zoneId: 'America/Thule', name: ' GMT-04:00 Atlantic Standard Time (America/Thule) ' },
  { zoneId: 'America/Tortola', name: ' GMT-04:00 Atlantic Standard Time (America/Tortola) ' },
  { zoneId: 'Atlantic/Bermuda', name: ' GMT-04:00 Atlantic Standard Time (Atlantic/Bermuda) ' },
  { zoneId: 'America/Bogota', name: ' GMT-05:00 Colombia Standard Time (America/Bogota) ' },
  { zoneId: 'America/Cancun', name: ' GMT-05:00 Eastern Standard Time (America/Cancun) ' },
  { zoneId: 'America/Cayman', name: ' GMT-05:00 Eastern Standard Time (America/Cayman) ' },
  {
    zoneId: 'America/Coral_Harbour',
    name: ' GMT-05:00 Eastern Standard Time (America/Coral_Harbour) ',
  },
  { zoneId: 'America/Detroit', name: ' GMT-05:00 Eastern Standard Time (America/Detroit) ' },
  { zoneId: 'America/Eirunepe', name: ' GMT-05:00 Acre Standard Time (America/Eirunepe) ' },
  { zoneId: 'America/Grand_Turk', name: ' GMT-05:00 Eastern Standard Time (America/Grand_Turk) ' },
  { zoneId: 'America/Guayaquil', name: ' GMT-05:00 Ecuador Time (America/Guayaquil) ' },
  { zoneId: 'America/Havana', name: ' GMT-05:00 Cuba Standard Time (America/Havana) ' },
  {
    zoneId: 'America/Indiana/Indianapolis',
    name: ' GMT-05:00 Eastern Standard Time (America/Indiana/Indianapolis) ',
  },
  {
    zoneId: 'America/Indiana/Marengo',
    name: ' GMT-05:00 Eastern Standard Time (America/Indiana/Marengo) ',
  },
  {
    zoneId: 'America/Indiana/Petersburg',
    name: ' GMT-05:00 Eastern Standard Time (America/Indiana/Petersburg) ',
  },
  {
    zoneId: 'America/Indiana/Vevay',
    name: ' GMT-05:00 Eastern Standard Time (America/Indiana/Vevay) ',
  },
  {
    zoneId: 'America/Indiana/Vincennes',
    name: ' GMT-05:00 Eastern Standard Time (America/Indiana/Vincennes) ',
  },
  {
    zoneId: 'America/Indiana/Winamac',
    name: ' GMT-05:00 Eastern Standard Time (America/Indiana/Winamac) ',
  },
  { zoneId: 'America/Iqaluit', name: ' GMT-05:00 Eastern Standard Time (America/Iqaluit) ' },
  { zoneId: 'America/Jamaica', name: ' GMT-05:00 Eastern Standard Time (America/Jamaica) ' },
  {
    zoneId: 'America/Kentucky/Monticello',
    name: ' GMT-05:00 Eastern Standard Time (America/Kentucky/Monticello) ',
  },
  { zoneId: 'America/Lima', name: ' GMT-05:00 Peru Standard Time (America/Lima) ' },
  { zoneId: 'America/Louisville', name: ' GMT-05:00 Eastern Standard Time (America/Louisville) ' },
  { zoneId: 'America/Montreal', name: ' GMT-05:00 Eastern Standard Time (America/Montreal) ' },
  { zoneId: 'America/Nassau', name: ' GMT-05:00 Eastern Standard Time (America/Nassau) ' },
  { zoneId: 'America/New_York', name: ' GMT-05:00 Eastern Standard Time (America/New_York) ' },
  { zoneId: 'America/Nipigon', name: ' GMT-05:00 Eastern Standard Time (America/Nipigon) ' },
  { zoneId: 'America/Panama', name: ' GMT-05:00 Eastern Standard Time (America/Panama) ' },
  {
    zoneId: 'America/Pangnirtung',
    name: ' GMT-05:00 Eastern Standard Time (America/Pangnirtung) ',
  },
  {
    zoneId: 'America/Port-au-Prince',
    name: ' GMT-05:00 Eastern Standard Time (America/Port-au-Prince) ',
  },
  { zoneId: 'America/Rio_Branco', name: ' GMT-05:00 Acre Standard Time (America/Rio_Branco) ' },
  {
    zoneId: 'America/Thunder_Bay',
    name: ' GMT-05:00 Eastern Standard Time (America/Thunder_Bay) ',
  },
  { zoneId: 'America/Toronto', name: ' GMT-05:00 Eastern Standard Time (America/Toronto) ' },
  {
    zoneId: 'America/Bahia_Banderas',
    name: ' GMT-06:00 Central Standard Time (America/Bahia_Banderas) ',
  },
  { zoneId: 'America/Belize', name: ' GMT-06:00 Central Standard Time (America/Belize) ' },
  { zoneId: 'America/Chicago', name: ' GMT-06:00 Central Standard Time (America/Chicago) ' },
  { zoneId: 'America/Costa_Rica', name: ' GMT-06:00 Central Standard Time (America/Costa_Rica) ' },
  {
    zoneId: 'America/El_Salvador',
    name: ' GMT-06:00 Central Standard Time (America/El_Salvador) ',
  },
  { zoneId: 'America/Guatemala', name: ' GMT-06:00 Central Standard Time (America/Guatemala) ' },
  {
    zoneId: 'America/Indiana/Knox',
    name: ' GMT-06:00 Central Standard Time (America/Indiana/Knox) ',
  },
  {
    zoneId: 'America/Indiana/Tell_City',
    name: ' GMT-06:00 Central Standard Time (America/Indiana/Tell_City) ',
  },
  { zoneId: 'America/Managua', name: ' GMT-06:00 Central Standard Time (America/Managua) ' },
  { zoneId: 'America/Matamoros', name: ' GMT-06:00 Central Standard Time (America/Matamoros) ' },
  { zoneId: 'America/Menominee', name: ' GMT-06:00 Central Standard Time (America/Menominee) ' },
  { zoneId: 'America/Merida', name: ' GMT-06:00 Central Standard Time (America/Merida) ' },
  {
    zoneId: 'America/Mexico_City',
    name: ' GMT-06:00 Central Standard Time (America/Mexico_City) ',
  },
  { zoneId: 'America/Monterrey', name: ' GMT-06:00 Central Standard Time (America/Monterrey) ' },
  {
    zoneId: 'America/North_Dakota/Beulah',
    name: ' GMT-06:00 Central Standard Time (America/North_Dakota/Beulah) ',
  },
  {
    zoneId: 'America/North_Dakota/Center',
    name: ' GMT-06:00 Central Standard Time (America/North_Dakota/Center) ',
  },
  {
    zoneId: 'America/North_Dakota/New_Salem',
    name: ' GMT-06:00 Central Standard Time (America/North_Dakota/New_Salem) ',
  },
  {
    zoneId: 'America/Rainy_River',
    name: ' GMT-06:00 Central Standard Time (America/Rainy_River) ',
  },
  {
    zoneId: 'America/Rankin_Inlet',
    name: ' GMT-06:00 Central Standard Time (America/Rankin_Inlet) ',
  },
  { zoneId: 'America/Regina', name: ' GMT-06:00 Central Standard Time (America/Regina) ' },
  { zoneId: 'America/Resolute', name: ' GMT-06:00 Central Standard Time (America/Resolute) ' },
  {
    zoneId: 'America/Swift_Current',
    name: ' GMT-06:00 Central Standard Time (America/Swift_Current) ',
  },
  {
    zoneId: 'America/Tegucigalpa',
    name: ' GMT-06:00 Central Standard Time (America/Tegucigalpa) ',
  },
  { zoneId: 'America/Winnipeg', name: ' GMT-06:00 Central Standard Time (America/Winnipeg) ' },
  { zoneId: 'Pacific/Easter', name: ' GMT-06:00 Easter Island Standard Time (Pacific/Easter) ' },
  { zoneId: 'Pacific/Galapagos', name: ' GMT-06:00 Galapagos Time (Pacific/Galapagos) ' },
  { zoneId: 'America/Boise', name: ' GMT-07:00 Mountain Standard Time (America/Boise) ' },
  {
    zoneId: 'America/Cambridge_Bay',
    name: ' GMT-07:00 Mountain Standard Time (America/Cambridge_Bay) ',
  },
  {
    zoneId: 'America/Chihuahua',
    name: ' GMT-07:00 Mexican Pacific Standard Time (America/Chihuahua) ',
  },
  { zoneId: 'America/Creston', name: ' GMT-07:00 Mountain Standard Time (America/Creston) ' },
  { zoneId: 'America/Dawson', name: ' GMT-07:00 Mountain Standard Time (America/Dawson) ' },
  {
    zoneId: 'America/Dawson_Creek',
    name: ' GMT-07:00 Mountain Standard Time (America/Dawson_Creek) ',
  },
  { zoneId: 'America/Denver', name: ' GMT-07:00 Mountain Standard Time (America/Denver) ' },
  { zoneId: 'America/Edmonton', name: ' GMT-07:00 Mountain Standard Time (America/Edmonton) ' },
  {
    zoneId: 'America/Fort_Nelson',
    name: ' GMT-07:00 Mountain Standard Time (America/Fort_Nelson) ',
  },
  {
    zoneId: 'America/Hermosillo',
    name: ' GMT-07:00 Mexican Pacific Standard Time (America/Hermosillo) ',
  },
  { zoneId: 'America/Inuvik', name: ' GMT-07:00 Mountain Standard Time (America/Inuvik) ' },
  {
    zoneId: 'America/Mazatlan',
    name: ' GMT-07:00 Mexican Pacific Standard Time (America/Mazatlan) ',
  },
  { zoneId: 'America/Ojinaga', name: ' GMT-07:00 Mountain Standard Time (America/Ojinaga) ' },
  { zoneId: 'America/Phoenix', name: ' GMT-07:00 Mountain Standard Time (America/Phoenix) ' },
  { zoneId: 'America/Whitehorse', name: ' GMT-07:00 Mountain Standard Time (America/Whitehorse) ' },
  {
    zoneId: 'America/Yellowknife',
    name: ' GMT-07:00 Mountain Standard Time (America/Yellowknife) ',
  },
  {
    zoneId: 'America/Los_Angeles',
    name: ' GMT-08:00 Pacific Standard Time (America/Los_Angeles) ',
  },
  {
    zoneId: 'America/Santa_Isabel',
    name: ' GMT-08:00 Northwest Mexico Standard Time (America/Santa_Isabel) ',
  },
  { zoneId: 'America/Tijuana', name: ' GMT-08:00 Pacific Standard Time (America/Tijuana) ' },
  { zoneId: 'America/Vancouver', name: ' GMT-08:00 Pacific Standard Time (America/Vancouver) ' },
  { zoneId: 'Pacific/Pitcairn', name: ' GMT-08:00 Pitcairn Time (Pacific/Pitcairn) ' },
  { zoneId: 'America/Anchorage', name: ' GMT-09:00 Alaska Standard Time (America/Anchorage) ' },
  { zoneId: 'America/Juneau', name: ' GMT-09:00 Alaska Standard Time (America/Juneau) ' },
  { zoneId: 'America/Metlakatla', name: ' GMT-09:00 Alaska Standard Time (America/Metlakatla) ' },
  { zoneId: 'America/Nome', name: ' GMT-09:00 Alaska Standard Time (America/Nome) ' },
  { zoneId: 'America/Sitka', name: ' GMT-09:00 Alaska Standard Time (America/Sitka) ' },
  { zoneId: 'America/Yakutat', name: ' GMT-09:00 Alaska Standard Time (America/Yakutat) ' },
  { zoneId: 'Pacific/Gambier', name: ' GMT-09:00 Gambier Time (Pacific/Gambier) ' },
  { zoneId: 'Pacific/Marquesas', name: ' GMT-09:30 Marquesas Time (Pacific/Marquesas) ' },
  { zoneId: 'America/Adak', name: ' GMT-10:00 Hawaii-Aleutian Standard Time (America/Adak) ' },
  {
    zoneId: 'Pacific/Honolulu',
    name: ' GMT-10:00 Hawaii-Aleutian Standard Time (Pacific/Honolulu) ',
  },
  {
    zoneId: 'Pacific/Johnston',
    name: ' GMT-10:00 Hawaii-Aleutian Standard Time (Pacific/Johnston) ',
  },
  {
    zoneId: 'Pacific/Rarotonga',
    name: ' GMT-10:00 Cook Islands Standard Time (Pacific/Rarotonga) ',
  },
  { zoneId: 'Pacific/Tahiti', name: ' GMT-10:00 Tahiti Time (Pacific/Tahiti) ' },
  { zoneId: 'Pacific/Midway', name: ' GMT-11:00 Samoa Standard Time (Pacific/Midway) ' },
  { zoneId: 'Pacific/Niue', name: ' GMT-11:00 Niue Time (Pacific/Niue) ' },
  { zoneId: 'Pacific/Pago_Pago', name: ' GMT-11:00 Samoa Standard Time (Pacific/Pago_Pago) ' },
];

export default TimeZoneList;

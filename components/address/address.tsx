import React, { useContext, useEffect, useState } from 'react';

import { FormControl, Grid, makeStyles } from '@material-ui/core';
import TextField from '@material-ui/core/TextField';
import { withStyles } from '@material-ui/core/styles';
import Autocomplete from '@material-ui/lab/Autocomplete';
import produce from 'immer';

import InputBaseComponent from '../input-base-component';
import InputLabelComponent from '../input-label-component';
import { Country, CountryStateListContext, Props, State } from './interface';

const defaultProps = {};

const useStyle = makeStyles({});

declare type AddressType = Record<string, string | number | undefined>;

declare type AddressPartProp = {
  label: string;
  partKey: string;
  address: AddressType;
  setAddress: React.Dispatch<React.SetStateAction<AddressType>>;
  required: boolean;
  disabled: boolean;
  readOnly: boolean;
  name: string;
  handleInputChange?: (newValue: any) => void;
  format?: string;
};

const StyledAutocomplete = withStyles((theme: any) => ({
  root: {
    'label + &': {
      marginTop: theme.spacing(1),
    },
    test: {
      borderColor: 'green',
    },
    '& fieldset': {
      border: 'none',
    },
  },
  inputRoot: {
    backgroundColor: '#ffffff !important',
    border: '1px solid #ced4da',
    fontSize: '.875rem',
    paddingTop: '1px !important',
    paddingBottom: '1px !important',
  },
}))(Autocomplete);

const CountrySelector: React.FC<AddressPartProp> = ({
  label,
  partKey,
  address,
  setAddress,
  required,
  disabled,
  readOnly,
  name,
  handleInputChange,
  format,
}) => {
  const countryStateListService = useContext(CountryStateListContext);
  const countryList = countryStateListService.countryList;

  return (
    <>
      <FormControl style={{ width: '100%' }}>
        <InputLabelComponent required={required} shrink htmlFor={name}>
          {label}
        </InputLabelComponent>
        <StyledAutocomplete
          autoComplete={true}
          disableClearable={true}
          options={countryList.map((country, index) => ({
            name: country.name,
            apiName: country.code,
          }))}
          //@ts-ignore
          getOptionLabel={(option: Country) => {
            return option ? option.name : '';
          }}
          value={
            address[partKey]
              ? countryList.find(
                  (_) =>
                    _.name.toLowerCase() === address[partKey]?.toString().toLowerCase() ||
                    _.code.toLowerCase() === address[partKey]?.toString().toLowerCase(),
                )
              : ''
          }
          onChange={(event, newValue) => {
            const newAddress = produce(address, (draft) => {
              //@ts-ignore
              draft[partKey] = format === 'short' ? newValue.apiName : newValue.name;
              delete draft['state'];
            });
            setAddress(newAddress);
            if (handleInputChange) {
              handleInputChange(newAddress);
            }
          }}
          renderInput={(params: any) => (
            <TextField
              {...params}
              variant="outlined"
              placeholder={label}
              InputProps={{
                ...params.InputProps,
                autoComplete: 'new-password',
              }}
              onChange={(event) => {
                const newValue = event.target.value;
                if (newValue === '') {
                  const newAddress = produce(address, (draft) => {
                    //@ts-ignore
                    delete draft[partKey];
                    delete draft['state'];
                  });
                  setAddress(newAddress);
                  if (handleInputChange) {
                    handleInputChange(newAddress);
                  }
                }
              }}
              inputProps={{
                ...params.inputProps,
                autoComplete: 'new-password',
              }}
            />
          )}
        />
      </FormControl>
    </>
  );
};

const StateSelector: React.FC<AddressPartProp> = ({
  label,
  partKey,
  address,
  setAddress,
  required,
  disabled,
  readOnly,
  name,
  handleInputChange,
}) => {
  const countryStateListService = useContext(CountryStateListContext);
  const [stateList, setStateList] = useState<State[]>([]);

  useEffect(() => {
    setUp();
  }, [address['country']]);

  const setUp = async () => {
    const countryName: string = (address['country'] as string) || '';
    if (!countryName) {
      return;
    }
    const country = countryStateListService.countryList.find(
      (_) =>
        _.name.toLowerCase() === countryName.toLowerCase() ||
        _.name.toLowerCase() ==
          countryStateListService.countryList
            .find(
              (_country) =>
                _country.code.toLowerCase() === countryName.toLowerCase() ||
                _country.name.toLowerCase() === countryName.toLowerCase(),
            )
            ?.name?.toLowerCase(),
    );
    if (!country) {
      return;
    }
    const stateList = await countryStateListService.getStateList(country);
    setStateList(stateList);
    return;
  };

  if (stateList.length === 0) {
    return null;
  }

  return (
    <>
      <FormControl style={{ width: '100%' }}>
        <InputLabelComponent required={required} shrink htmlFor={name}>
          {label}
        </InputLabelComponent>
        <StyledAutocomplete
          autoComplete={true}
          disableClearable={true}
          options={stateList.map((country, index) => ({
            name: country.name,
            apiName: country.code,
          }))}
          //@ts-ignore
          getOptionLabel={(option: Country) => {
            return option ? option.name : '';
          }}
          value={
            address[partKey]
              ? stateList.find(
                  (_) =>
                    _.name.toLowerCase() === address[partKey]?.toString().toLowerCase() ||
                    _.code.toLowerCase() === address[partKey]?.toString().toLowerCase(),
                )
              : ''
          }
          onChange={(event, newValue) => {
            const newAddress = produce(address, (draft) => {
              //@ts-ignore
              draft[partKey] = newValue.name;
            });
            setAddress(newAddress);
            if (handleInputChange) {
              handleInputChange(newAddress);
            }
          }}
          renderInput={(params: any) => (
            <TextField
              {...params}
              variant="outlined"
              placeholder={label}
              InputProps={{
                ...params.InputProps,
                autoComplete: 'new-password',
              }}
              onChange={(event) => {
                const newValue = event.target.value;
                if (newValue === '') {
                  const newAddress = produce(address, (draft) => {
                    //@ts-ignore
                    delete draft[partKey];
                  });
                  setAddress(newAddress);
                  if (handleInputChange) {
                    handleInputChange(newAddress);
                  }
                }
              }}
              inputProps={{
                ...params.inputProps,
                autoComplete: 'new-password',
              }}
            />
          )}
        />
      </FormControl>
    </>
  );
};

const AddressPart: React.FC<AddressPartProp> = (props) => {
  const {
    label,
    partKey,
    address,
    setAddress,
    required,
    disabled,
    readOnly,
    name,
    handleInputChange,
  } = props;
  return (
    <FormControl style={{ width: '100%' }}>
      <InputLabelComponent required={required} shrink htmlFor={name}>
        {label}
      </InputLabelComponent>
      <InputBaseComponent
        value={address[partKey] || ''}
        placeholder={label}
        name={name}
        type="text"
        required={required}
        onChange={(event) => {
          const newAddress = produce(address, (draft) => {
            draft[partKey] = event.target.value;
          });
          setAddress(newAddress);
          if (handleInputChange) {
            handleInputChange(newAddress);
          }
        }}
        fullWidth
        disabled={disabled || readOnly}
        readOnly={readOnly}
      />
    </FormControl>
  );
};

export const Address: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const classes = useStyle();
  const {
    field,
    label,
    value,
    handleInputChange,
    placeholder,
    required,
    disabled,
    readOnly,
    mode,
  } = props;

  const [address, setAddress] = useState<AddressType>(value || {});
  const addressPrefix = field.apiName.replace('Address', '');
  const [Street, City, State, PostalCode, Country] = [
    'Street',
    'City',
    'State',
    'PostalCode',
    'Country',
  ]
    .map((x) => field.apiName.replace('Address', x))
    .map((apiName) => field.compoundedFields?.find((x) => x.apiName === apiName))
    .map((compoundField) => {
      if (!compoundField) {
        return null;
      }

      let disabled = false,
        readOnly = false;
      if (
        (mode === 'create' && compoundField.creatable === false) ||
        (mode === 'edit' && compoundField.updatable === false)
      ) {
        readOnly = true;
        disabled = true;
      }

      const [first, ...rest] = compoundField.apiName.replace(addressPrefix, '');
      const partKey = [first.toLowerCase(), ...rest].join('');

      if (partKey === 'country') {
        return (
          <CountrySelector
            address={address}
            disabled={disabled}
            setAddress={setAddress}
            name={compoundField.name || ''}
            required={compoundField.required || false}
            readOnly={readOnly}
            partKey={partKey}
            label={compoundField.name || ''}
            handleInputChange={handleInputChange}
            format={compoundField.format}
          />
        );
      }

      if (partKey === 'state') {
        return (
          <StateSelector
            address={address}
            disabled={disabled}
            setAddress={setAddress}
            name={compoundField.name || ''}
            required={compoundField.required || false}
            readOnly={readOnly}
            partKey={partKey}
            label={compoundField.name || ''}
            handleInputChange={handleInputChange}
          />
        );
      }

      return (
        <AddressPart
          address={address}
          disabled={disabled}
          setAddress={setAddress}
          name={compoundField.name || ''}
          required={compoundField.required || false}
          readOnly={readOnly}
          partKey={partKey}
          label={compoundField.name || ''}
          handleInputChange={handleInputChange}
        />
      );
    });

  useEffect(() => {
    setAddress(value || {});
  }, [value]);

  return (
    <>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          {Street}
        </Grid>
      </Grid>
      <Grid container spacing={2}>
        <Grid item xs={6}>
          {City}
        </Grid>
        <Grid item xs={6}>
          {PostalCode}
        </Grid>
      </Grid>
      <Grid container spacing={2}>
        <Grid item xs={6}>
          {Country}
        </Grid>
        <Grid item xs={6}>
          {State}
        </Grid>
      </Grid>
    </>
  );
};

export default Address;

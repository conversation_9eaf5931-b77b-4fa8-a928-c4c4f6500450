import makeStyles from '@material-ui/core/styles/makeStyles';
import React from 'react';

export interface TextHighlightProps {
  children: React.ReactNode;
}

const useStyles = makeStyles((theme) => ({
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
  },
}));

const TextHighlight: React.FC<TextHighlightProps> = ({ children }) => {
  const classes = useStyles();

  return <span className={classes.textHighlight}>{children}</span>;
};

export default TextHighlight;

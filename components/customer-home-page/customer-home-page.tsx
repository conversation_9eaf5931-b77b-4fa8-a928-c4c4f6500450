import React, { useState, forwardRef } from 'react';

import BreadcrumbContext from '../breadcrumb-context';
import { Breadcrumb } from '../breadcrumb-context/interface';
import PageHeader from '../page-header';
import { Props } from './interface';
import CustomerContext from '../customer-context';
import CustomerHome from '../customer-home';
import { CustomerFacade } from '../customer-context/customer-context';

const defaultProps = {};

const CustomerHomePage = forwardRef<{ refreshCustomerData: () => void }, Props>(
  (userProps, ref) => {
    const props = { ...defaultProps, ...userProps };

    const {
      navigateToNewCustomerViewPage,
      customerMetadata,
      fieldSetMetadata,
      currencyIsoCode,
      executeQuery,
      updateFilters,
      listFilters,
      saveFilter,
      getFieldSetMetadata,
      activeTabIndex,
      setActiveTabIndex,
      idsCondition,
      jumpFrom,
      getTransactionHubStatus,
      transactionHubColumn,
      executeGetCountQuery,
    } = props;

    const [breadcrumbs, setBreadcrumbs] = useState<Array<Breadcrumb>>([]);

    const breadcrumbFacade = {
      breadcrumbService: {
        getBreadcrumbConfig: () => {
          return {
            breadcrumbs: breadcrumbs,
            updateBreadcrumbs: (newBreadcrumbs: Array<Breadcrumb>) => {
              setBreadcrumbs(newBreadcrumbs);
            },
          };
        },
      },
    };

    const customerHomeFacade: CustomerFacade = {
      customerHomeService: {
        getCustomerHomeConfig: () => {
          return {
            customerHomeConfigs: {
              getFieldSetMetadata: getFieldSetMetadata,
              navigateToNewCustomerViewPage: navigateToNewCustomerViewPage,
              customerMetadata: customerMetadata,
              executeQuery: executeQuery,
              executeGetCountQuery: executeGetCountQuery,
              currencyIsoCode: currencyIsoCode,
              fieldSetMetadata,
              getTransactionHubStatus: getTransactionHubStatus,
              transactionHubColumn: transactionHubColumn,
            },
            searchConfigs: {
              updateFilters: updateFilters,
              saveFilter: saveFilter,
              listFilters: listFilters,
            },
          };
        },
        actionEventHandler: props.actionEventHandler,
      },
    };

    const title = jumpFrom ? jumpFrom + ' > Customers' : 'Customers';

    return (
      <BreadcrumbContext.Provider value={breadcrumbFacade}>
        <CustomerContext.Provider value={customerHomeFacade}>
          <PageHeader title={title} breadcrumbs={breadcrumbs} />
          <CustomerHome
            ref={ref}
            activeTabIndex={activeTabIndex}
            setActiveTabIndex={setActiveTabIndex}
            idsCondition={idsCondition}
            jumpFrom={!!jumpFrom}
          />
        </CustomerContext.Provider>
      </BreadcrumbContext.Provider>
    );
  },
);

export default CustomerHomePage;

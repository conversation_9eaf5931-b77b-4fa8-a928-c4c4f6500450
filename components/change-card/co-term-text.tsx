import React from 'react';

import Link from '@material-ui/core/Link';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import dayjs from 'dayjs';

import { UpdateTermRequest } from '../change-cart/interface';
import { ChangeItemText } from './interface';

const useStyles = makeStyles({
  itemText: {
    opacity: 0.7,
    color: '#000000',
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
    opacity: '1 !important',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
});
const CoTermText: React.FC<ChangeItemText> = (props) => {
  const { changeItem } = props;
  const classes = useStyles();

  const request = changeItem.request as UpdateTermRequest;
  return (
    <Typography variant="body2" component="p" display="inline">
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        The subscription end date of Subscription{' '}
      </Typography>
      <Typography variant="body2" className={classes.textUnderline} component="p" display="inline">
        {request.assetName}{' '}
      </Typography>
      <Typography className={classes.itemText} variant="body2" component="p" display="inline">
        is co-termed to
      </Typography>
      <Link
        variant="body2"
        href="#"
        onClick={() => {}}
        className={classes.textHighlight}
        component="p"
        display="inline"
      >
        {' '}
        {
          //@ts-ignore
          dayjs(request.subscriptionEndDate).format('MM/DD/YYYY')
        }{' '}
      </Link>
      {request.updateRenewalTerm && request.renewalTerm && (
        <>
          <Typography className={classes.itemText} variant="body2" component="p" display="inline">
            , and renewal term as{' '}
          </Typography>
          <Link
            variant="body2"
            href="#"
            onClick={() => {}}
            className={classes.textHighlight}
            component="p"
            display="inline"
          >
            {parseFloat(Math.abs(request.renewalTerm).toFixed(2))}{' '}
          </Link>
          <Typography className={classes.itemText} variant="body2" component="p" display="inline">
            {request.termDimension}
          </Typography>
        </>
      )}
    </Typography>
  );
};

export default CoTermText;

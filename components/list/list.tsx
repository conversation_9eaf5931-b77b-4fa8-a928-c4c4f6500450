import React from 'react';

import Grid from '@material-ui/core/Grid';
import Tooltip from '@material-ui/core/Tooltip';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import InfoIcon from '@material-ui/icons/Info';
import dayjs from 'dayjs';

import { RubyCheckbox } from '../checkbox/checkbox';
import Loading from '../loading';
import { useUserLocale } from '../use-user-locale';
import type {
  Item,
  ItemProps,
  Props,
  SummaryItem,
  SummaryItemProps,
  SummaryTitleProps,
} from './interface';
import { formatCurrency } from '../util';

const defaultProps = {};

const useStyles = makeStyles({
  summaryItemTitle: {
    display: 'flex',
    alignItems: 'center',
    color: '#959595',
    fontWeight: 'bold',
    letterSpacing: 0,
    lineHeight: '15px',
    fontSize: '.75rem',
    paddingTop: '8px',
  },
  itemTitle: {
    display: 'flex',
    alignItems: 'center',
    opacity: 0.5,
    lineHeight: '18px',
    fontSize: '.875rem',
  },
  itemValue: {
    textAlign: 'right',
    opacity: 0.8,
    fontSize: '.875rem',
    fontWeight: 500,
  },
  negativeItemValue: {
    color: '#ff0000',
  },
  title: {
    paddingBottom: '8px',
    fontSize: '1.125rem',
  },
  infoIcon: {
    fontSize: '.875rem',
    color: '#9c9c9c',
    cursor: 'pointer',
    marginLeft: '4px',
  },
  topTotal: {
    fontWeight: 500,
    fontSize: '1.5rem',
    wordBreak: 'break-word',
  },
  bottomTotal: {
    fontWeight: 500,
    wordBreak: 'break-word',
  },
  summaryItemValue: {
    color: '#6239eb',
  },
  list: {
    padding: 0,
    marginTop: 0,
    marginBottom: 0,
    width: '100%',
  },
  linkWithTitleContainer: {
    display: 'flex',
    paddingBottom: '16px',
  },
  linkContainer: {
    alignSelf: 'flex-end',
    paddingBottom: '6px',
    position: 'relative',
  },
  link: {
    color: '#6239eb',
    fontSize: '.875rem',
    fontWeight: 500,
    cursor: 'pointer',
  },
  uomText: {
    paddingLeft: '8px',
  },
});

const shouldFormatNegativeNumbers = (
  item: Item,
  shouldDisplayNegativeNumberStyling?: (type: string, itemApiName: string) => boolean,
) => {
  return shouldDisplayNegativeNumberStyling
    ? shouldDisplayNegativeNumberStyling(item.type, item.apiName)
    : false;
};

export const formatNumberForDisplay = (
  value: number,
  currencyIsoCode: string,
  shouldDisplayNegativeNumberStyling: boolean,
  hidePrefix?: boolean,
  locale?: string,
  maximumFractionDigits?: number,
) => {
  const shouldDisplayNegativeNumberBasedOnTypeAndNegativeValue =
    shouldDisplayNegativeNumberStyling && value < 0;
  const absoluteValue = Math.abs(value);
  const _locale = locale;
  const formattedValue = shouldDisplayNegativeNumberBasedOnTypeAndNegativeValue
    ? hidePrefix
      ? absoluteValue.toString()
      : formatCurrency(absoluteValue, { locale: _locale, currencyIsoCode, maximumFractionDigits })
    : hidePrefix
      ? value.toString()
      : formatCurrency(value, { locale: _locale, currencyIsoCode, maximumFractionDigits });
  return shouldDisplayNegativeNumberBasedOnTypeAndNegativeValue
    ? '(' + formattedValue + ')'
    : formattedValue;
};

const formatNegativeZero = (value: number) => {
  if (value === -0) {
    return Math.abs(value);
  }
  return value;
};

export const formatPercentForDisplay = (
  value: number,
  currencyIsoCode: string,
  shouldDisplayNegativeNumberStyling: boolean,
  locale?: string,
) => {
  const shouldDisplayNegativeNumberBasedOnTypeAndNegativeValue =
    shouldDisplayNegativeNumberStyling && value < 0;
  const absoluteValue = Math.abs(value);

  const _locale = locale;

  const formattedValue = shouldDisplayNegativeNumberBasedOnTypeAndNegativeValue
    ? (absoluteValue / 100).toLocaleString(_locale, {
        style: 'percent',
        maximumFractionDigits: 2,
      })
    : (formatNegativeZero(value) / 100).toLocaleString(_locale, {
        style: 'percent',
        maximumFractionDigits: 2,
      });
  return shouldDisplayNegativeNumberBasedOnTypeAndNegativeValue
    ? '(' + formattedValue + ')'
    : formattedValue;
};

const convertToCurrency = (
  item: Item,
  currencyIsoCode?: string,
  shouldDisplayNegativeNumberStyling?: (type: string, itemApiName: string) => boolean,
  locale?: string,
) => {
  const currencyCode = currencyIsoCode || 'USD';
  let _locale = locale;
  if (item.value === null || item.value === undefined) {
    return '-';
  }
  if (item.value === 0) {
    const zero: number = 0;
    return formatCurrency(zero, { locale: _locale, currencyIsoCode: currencyCode });
  }
  const shouldFormatNegativeNum = shouldFormatNegativeNumbers(
    item,
    shouldDisplayNegativeNumberStyling,
  );
  return formatNumberForDisplay(
    Number(item.value),
    currencyCode,
    shouldFormatNegativeNum,
    false,
    _locale,
  );
};

const convertToPercent = (
  item: Item,
  currencyIsoCode?: string,
  shouldDisplayNegativeNumberStyling?: (type: string, itemApiName: string) => boolean,
  locale?: string,
) => {
  const currencyCode = currencyIsoCode || 'USD';
  const shouldFormatNegativeNum = shouldFormatNegativeNumbers(
    item,
    shouldDisplayNegativeNumberStyling,
  );
  return formatPercentForDisplay(Number(item.value), currencyCode, shouldFormatNegativeNum, locale);
};

export const List: React.FC<Props> = (userProps) => {
  const classes = useStyles();
  const { getUserLocale } = useUserLocale();

  const props = { ...defaultProps, ...userProps };

  const {
    items,
    summaryItem,
    currencyIsoCode,
    link,
    handleOnClickLink,
    mouseOverPopupAria,
    PopoverComponent,
    shouldDisplayNegativeNumberStyling,
    uom,
  } = props;

  const getItemValue = (item: Item) => {
    const locale = props.locale || getUserLocale();
    if (item.type.toLowerCase() === 'currency') {
      return convertToCurrency(item, currencyIsoCode, shouldDisplayNegativeNumberStyling, locale);
    }
    if (item.value && item.type.toLowerCase() === 'date' && item.value) {
      return dayjs(item.value).format('MM/DD/YYYY');
    }
    if (item.type.toLowerCase() === 'datetime' && item.value) {
      return dayjs(item.value).format('MM/DD/YYYY hh:mm a');
    }
    if (item.type.toLowerCase() === 'percent') {
      return convertToPercent(item, currencyIsoCode, shouldDisplayNegativeNumberStyling, locale);
    }
    if (item.type.toLowerCase() === 'picklist') {
      // one-off usecase for supporting autoRenew picklist on Account
      if (item.value?.toLowerCase() === 'yes' || item.value?.toLowerCase() === 'no') {
        return (
          <RubyCheckbox
            style={{
              width: '24px',
            }}
            field={{
              name: '',
              apiName: item.apiName,
              type: item.type,
            }}
            value={item.value?.toLowerCase() === 'yes' ? true : false}
            handleInputChange={() => {}}
          />
        );
      }
      return item.value;
    }
    if (item.type.toLowerCase() === 'boolean') {
      return item.value ? 'True' : 'False';
    }
    if (item.value !== null && item.value !== undefined) {
      return item.value;
    }
    return '-';
  };

  const shouldAddNegativeNumberTextColor = (
    item: Item,
    shouldDisplayNegativeNumberStyling?: (type: string, itemApiName: string) => boolean,
  ) => {
    return (item.type.toLowerCase() === 'currency' || item.type.toLowerCase() === 'percent') &&
      shouldDisplayNegativeNumberStyling &&
      Number(item.value) < 0
      ? shouldDisplayNegativeNumberStyling(item.type, item.apiName)
      : false;
  };

  const getTotalStyles = (summaryItem: SummaryItem) => {
    let style = '';
    if (summaryItem.position === 'top-left') {
      style = `${classes.topTotal}`;
    } else {
      style = `${classes.bottomTotal}`;
    }
    if (shouldAddNegativeNumberTextColor(summaryItem, shouldDisplayNegativeNumberStyling)) {
      style += ` ${classes.negativeItemValue}`;
    } else {
      style += ` ${classes.summaryItemValue}`;
    }
    return style;
  };

  const SummaryTitle = ({ summaryItem }: SummaryTitleProps) => (
    <Typography variant="caption" className={classes.summaryItemTitle} noWrap>
      {summaryItem.toolTipText && (
        <Tooltip title={summaryItem.toolTipText} arrow placement="top">
          {summaryItem.toolTipIcon ? (
            summaryItem.toolTipIcon()
          ) : (
            <InfoIcon className={classes.infoIcon} />
          )}
        </Tooltip>
      )}
      {summaryItem.name}
    </Typography>
  );

  const Title = ({ item }: ItemProps) => (
    <Typography variant="caption" className={classes.itemTitle} noWrap>
      {item.name}
      {item.toolTipText && (
        <Tooltip title={item.toolTipText} arrow placement="top" style={{ marginLeft: '4px' }}>
          {item.toolTipIcon ? item.toolTipIcon() : <InfoIcon className={classes.infoIcon} />}
        </Tooltip>
      )}
    </Typography>
  );

  const Item = ({ item }: ItemProps) => (
    <Grid container justifyContent="space-between" alignItems="center">
      <Title item={item} />
      {!item.isLoading ? (
        <Typography
          variant="subtitle1"
          className={
            shouldAddNegativeNumberTextColor(item, shouldDisplayNegativeNumberStyling)
              ? `${classes.negativeItemValue} ${classes.itemValue}`
              : `${classes.itemValue}`
          }
          noWrap
        >
          {getItemValue(item)}
        </Typography>
      ) : (
        <div className={classes.itemValue}>
          <Loading size="20px" />
        </div>
      )}
    </Grid>
  );

  const Total = ({ summaryItem, uom }: SummaryItemProps) => (
    <Grid
      container
      direction="column"
      alignItems={summaryItem.position === 'top-left' ? 'flex-start' : 'flex-end'}
    >
      <SummaryTitle summaryItem={{ ...summaryItem, name: summaryItem.name.toUpperCase() }} />
      {!summaryItem.isLoading ? (
        uom ? (
          <Typography
            variant="body2"
            component="p"
            className={getTotalStyles(summaryItem)}
            display="inline"
          >
            {getItemValue(summaryItem)}
            <Typography className={classes.uomText} variant="body2" component="p" display="inline">
              {uom.name}
            </Typography>
          </Typography>
        ) : (
          <Typography variant="h6" className={getTotalStyles(summaryItem)}>
            {getItemValue(summaryItem)}
          </Typography>
        )
      ) : (
        <div className={getTotalStyles(summaryItem)}>
          <Loading size="40px" />
        </div>
      )}
    </Grid>
  );

  return (
    <ul className={classes.list}>
      {summaryItem && summaryItem.position === 'top-left' && (
        <li key={summaryItem.name} style={{ listStyle: 'none' }}>
          <Grid container justifyContent="space-between" className={classes.linkWithTitleContainer}>
            <Grid item>
              <Total summaryItem={summaryItem} uom={uom} />
            </Grid>
            {link && (
              <Grid item className={classes.linkContainer}>
                <Typography
                  aria-owns={mouseOverPopupAria}
                  aria-haspopup="true"
                  onClick={async (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
                    if (handleOnClickLink) {
                      await handleOnClickLink(e);
                    }
                  }}
                  variant="subtitle1"
                  className={classes.link}
                  noWrap
                >
                  {link}
                </Typography>
                {PopoverComponent && <PopoverComponent />}
              </Grid>
            )}
          </Grid>
        </li>
      )}
      {items.map((item: Item, index: number) => {
        return (
          <li key={item.name} style={{ listStyle: 'none', padding: '3px 0' }}>
            <Item item={item} />
          </li>
        );
      })}
      {summaryItem && summaryItem.position !== 'top-left' && (
        <li key={summaryItem.name} style={{ listStyle: 'none' }}>
          <Total summaryItem={summaryItem} uom={uom} />
        </li>
      )}
    </ul>
  );
};

export default List;

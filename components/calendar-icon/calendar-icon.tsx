import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { Props } from './interface';

const CalendarIcon: React.FC<Props> = ({ viewBox, width, height }) => {
  return (
    <SvgIcon viewBox={viewBox}>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>D55ACB9A-7CE5-46C1-8996-C09CB1710935</title>
        <g
          id="06-Quote-Builder"
          stroke="none"
          strokeWidth="1"
          fill="none"
          fillRule="evenodd"
          opacity="0.400000006"
        >
          <g id="6-1-6-3-Edit-Lines-(Price-Tags)" transform="translate(-1211.000000, -332.000000)">
            <g id="Group-15" transform="translate(531.000000, 77.000000)">
              <g id="Group-11" transform="translate(40.000000, 211.000000)">
                <g id="calendar-2-line" transform="translate(640.000000, 44.000000)">
                  <polygon id="Path" points="0 0 24 0 24 24 0 24" />
                  <path
                    d="M17,3 L21,3 C21.5522847,3 22,3.44771525 22,4 L22,20 C22,20.5522847 21.5522847,21 21,21 L3,21 C2.44771525,21 2,20.5522847 2,20 L2,4 C2,3.44771525 2.44771525,3 3,3 L7,3 L7,1 L9,1 L9,3 L15,3 L15,1 L17,1 L17,3 Z M20,11 L4,11 L4,19 L20,19 L20,11 Z M15,5 L9,5 L9,7 L7,7 L7,5 L4,5 L4,9 L20,9 L20,5 L17,5 L17,7 L15,7 L15,5 Z M6,13 L8,13 L8,15 L6,15 L6,13 Z M11,13 L13,13 L13,15 L11,15 L11,13 Z M16,13 L18,13 L18,15 L16,15 L16,13 Z"
                    id="Shape"
                    fill="#000000"
                    fillRule="nonzero"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default CalendarIcon;

import React, { useEffect, useMemo, useState } from 'react';
import type { ChangeItem, CustomerViewService } from '@nue-apps/ruby-ui-component';
import DialogComponent from '../dialog-component';
import { useEventBus } from '../event-bus';
import type { RubySettings } from '../ruby-settings';
import { SubscriptionCancelForm } from '../subscription-card/subscription-cancel-form';
import { SubscriptionUpdateTermForm } from '../subscription-card/subscription-update-term-form';
import { UpdateQuantityForm } from '../subscription-card/update-quantity-form';
import { UpgradeSubscriptionForm } from '../subscription-card/upgrade-subscription-form';
import { DowngradeSubscriptionForm } from '../subscription-card/downgrade-subscription-form';
import { SwapSubscriptionForm } from '../subscription-card/swap-subscription-form';
import { useProductRelations } from './hooks';

export interface OpenActionDialogEvent {
  changeItem: ChangeItem;
  submitButtonText?: string;
}

export interface ActionDialogProps {
  customerViewService: CustomerViewService;
  rubySettings: RubySettings;
}

export const ActionDialog: React.FC<ActionDialogProps> = ({
  customerViewService,
  rubySettings,
}) => {
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState('');
  const { subscribeEvent, unsubscribeEvent } = useEventBus();
  const [event, setEvent] = useState<OpenActionDialogEvent>();
  const [confirmButtonText, setConfirmButtonText] = useState('Confirm');
  const customerViewConfigs = customerViewService.getCustomerViewConfig().customerViewConfigs;
  const { getSwapUpgradeDowngradeOptions } = customerViewConfigs;

  const openActionDialogHandler = (newEvent: OpenActionDialogEvent) => {
    const { changeItem, submitButtonText } = newEvent;
    setTitle(changeItem.cardAction.name);
    setEvent(newEvent);
    setConfirmButtonText(submitButtonText || 'Confirm');
    setOpen(true);
  };
  const closeActionDialogHandler = () => setOpen(false);

  const subscriptions = useMemo(() => {
    return event ? [event.changeItem.asset] : [];
  }, [event?.changeItem.asset]);

  const { productRelationsByActionType } = useProductRelations(
    subscriptions,
    getSwapUpgradeDowngradeOptions,
  );

  useEffect(() => {
    subscribeEvent('openActionDialog', 'actionDialog', openActionDialogHandler);
    return () => {
      unsubscribeEvent('openActionDialog', 'actionDialog', openActionDialogHandler);
    };
  }, []);

  if (!event) return null;

  const getFormContent = () => {
    if (event?.changeItem.cardAction.id === 'updateTerm') {
      return (
        <SubscriptionUpdateTermForm
          changeItem={event.changeItem}
          getSubscriptionsForCoterm={customerViewConfigs.getSubscriptionsForCoterm}
          handleAddToCart={customerViewConfigs.handleAddToCart}
          rubySettings={rubySettings}
          closeDialogHandler={closeActionDialogHandler}
          submitButtonText={confirmButtonText}
        />
      );
    }
    if (event?.changeItem.cardAction.id === 'updateQuantity') {
      return (
        <UpdateQuantityForm
          changeItem={event.changeItem}
          handleAddToCart={customerViewConfigs.handleAddToCart}
          rubySettings={rubySettings}
          closeDialogHandler={closeActionDialogHandler}
          executeQuery={customerViewConfigs.executeQuery}
          submitButtonText={confirmButtonText}
        />
      );
    }
    if (event?.changeItem.cardAction.id === 'cancel') {
      return (
        <SubscriptionCancelForm
          changeItem={event.changeItem}
          handleAddToCart={customerViewConfigs.handleAddToCart}
          rubySettings={rubySettings}
          closeDialogHandler={closeActionDialogHandler}
          executeQuery={customerViewConfigs.executeQuery}
          submitButtonText={confirmButtonText}
        />
      );
    }
    if (event?.changeItem.cardAction.id === 'upgrade') {
      return (
        <UpgradeSubscriptionForm
          changeItem={event.changeItem}
          handleAddToCart={customerViewConfigs.handleAddToCart}
          rubySettings={rubySettings}
          closeDialogHandler={closeActionDialogHandler}
          executeQuery={customerViewConfigs.executeQuery}
          submitButtonText={confirmButtonText}
          productRelationsByActionType={productRelationsByActionType['upgrade']}
        />
      );
    }
    if (event?.changeItem.cardAction.id === 'downgrade') {
      return (
        <DowngradeSubscriptionForm
          changeItem={event.changeItem}
          handleAddToCart={customerViewConfigs.handleAddToCart}
          rubySettings={rubySettings}
          closeDialogHandler={closeActionDialogHandler}
          executeQuery={customerViewConfigs.executeQuery}
          submitButtonText={confirmButtonText}
          productRelationsByActionType={productRelationsByActionType['downgrade']}
        />
      );
    }
    if (event?.changeItem.cardAction.id === 'swap') {
      return (
        <SwapSubscriptionForm
          changeItem={event.changeItem}
          handleAddToCart={customerViewConfigs.handleAddToCart}
          rubySettings={rubySettings}
          closeDialogHandler={closeActionDialogHandler}
          executeQuery={customerViewConfigs.executeQuery}
          submitButtonText={confirmButtonText}
          productRelationsByActionType={productRelationsByActionType['swap']}
        />
      );
    }
    console.error('Undefined action dialog type');
  };

  return (
    <DialogComponent
      width="sm"
      open={open}
      title={title}
      dontShowCloseIcon={true}
      submitButtonText={confirmButtonText}
    >
      {getFormContent()}
    </DialogComponent>
  );
};

export default [
  {
    apiName: 'productName',
    name: 'Product Name',
    type: 'text',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
  },
  {
    apiName: 'subscriptionNumber',
    name: 'Subscription Number',
    type: 'text',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
  },
  {
    apiName: 'totalAmount',
    name: 'Total Amount',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    precision: 15,
    scale: 3,
  },
  {
    apiName: 'subscriptionStartDate',
    name: 'Subscription Start Date',
    type: 'date',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
  },
  {
    apiName: 'subscriptionEndDate',
    name: 'Subscription End Date',
    type: 'date',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
  },
  {
    apiName: 'quantity',
    name: 'Quantity',
    type: 'decimal',
    creatable: true,
    updatable: true,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    precision: 15,
    scale: 3,
  },
  {
    apiName: 'status',
    name: 'Status',
    type: 'text',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
];

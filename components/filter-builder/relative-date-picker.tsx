import React, { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FormControl, Grid, Typography, makeStyles } from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import {
  DialogComponent,
  HelpIcon,
  PickList,
  RadioGroupComponent,
  RadioSelection,
  useYupValidationResolver,
} from '@nue-apps/ruby-ui-component';
import dayjs, { Dayjs } from 'dayjs';
import * as yup from 'yup';
import NumberInput from '../number-input';
import ToolTipContainer from '../subscription-card/info-section-text-registry/tool-tip-container';

interface Props {
  open: boolean;
  onClose: () => void;
  defaultValues?: Record<string, any>;
  onSubmit: (values: any) => void;
  type: string;
}

const useStyles = makeStyles((theme) => ({
  textHighlight: {
    color: theme.palette.secondary.main,
    fontWeight: 'bold',
  },
}));

export const RelavtiveDatePicker = ({ open, onClose, defaultValues, onSubmit, type }: Props) => {
  const [selectedOption, setSelectOption] = useState('day');
  const classes = useStyles();
  //@ts-ignore
  const validationSchema = yup.object({
    amount: yup.number(),
  });

  const options: any[] = [
    {
      label: 'Plus',
      radioSelection: { value: 'plus' },
      type: 'radio' as const,
    },
    {
      label: 'Minus',
      radioSelection: { value: 'minus' },
      type: 'radio' as const,
    },
  ];

  React.useEffect(() => {
    if (defaultValues && defaultValues.option) {
      setSelectOption(defaultValues.option);
    }
  }, [defaultValues]);

  const resolver = useYupValidationResolver(validationSchema);
  const methods = useForm({
    defaultValues: defaultValues || {
      datePickerRadio: 'plus',
      amount: 0,
    },
    //@ts-ignore
    resolver,
  });

  const { getValues, watch, handleSubmit, setValue, register, errors } = methods;
  const [examplePrefixDesc, setExamplePrefixDesc] = useState('');
  const [exampleValue, setExampleValue] = useState('');

  useEffect(() => {
    if (defaultValues) {
      setValue('amount', defaultValues.amount);
      setValue('datePickerRadio', defaultValues.datePickerRadio);
      updateExampleValue();
    }
  }, [defaultValues, setValue]);

  const option = watch('datePickerRadio');
  const amount = watch('amount');

  useEffect(() => {
    updateExampleValue();
  }, [option, amount, selectedOption]);

  const getQuarterStart = (date: Dayjs): Dayjs => {
    const month = date.month();
    const quarterStartMonth = Math.floor(month / 3) * 3;
    return date.month(quarterStartMonth).startOf('month');
  };
  const getQuarterEnd = (date: Dayjs): Dayjs => {
    const month = date.month();
    const quarterEndMonth = Math.floor(month / 3) * 3 + 2;
    return date.month(quarterEndMonth).endOf('month');
  };

  const updateExampleValue = () => {
    const isDateType = type.toLowerCase() === 'date';
    const dateFormat = isDateType ? 'MM/DD/YYYY' : 'hh:mma MM/DD/YYYY';
    const currentDate = dayjs();

    const getPrefixDesc = (option: string, unit: string, amount: number) => {
      const operation = isDateType
        ? option === 'plus'
          ? 'End of'
          : 'Start of'
        : option === 'plus'
          ? 'End time of'
          : 'Start time of';
      let desc = `${operation} `;

      if (amount === 0) {
        desc += unit === 'day' ? 'today' : `current ${unit}`;
      } else {
        const unitString = amount > 1 ? `${amount} ${unit}s` : `${amount} ${unit}`;
        const timeDescriptor = option === 'plus' ? 'later' : 'earlier';
        desc += `${unitString} ${timeDescriptor}`;
      }

      return desc;
    };

    const getFormattedDate = (option: string, unit: string, amount: number): string => {
      let date = currentDate;
      const multiplier = option === 'plus' ? 1 : -1;
      const addAmount = multiplier * amount;

      if (amount === 0) {
        switch (unit) {
          case 'day':
            date = option === 'plus' ? date.endOf('day') : date.startOf('day');
            break;
          case 'month':
            date = option === 'plus' ? date.endOf('month') : date.startOf('month');
            break;
          case 'quarter':
            date = option === 'plus' ? getQuarterEnd(date) : getQuarterStart(date);
            break;
          case 'year':
            date = option === 'plus' ? date.endOf('year') : date.startOf('year');
            break;
        }
      } else {
        switch (unit) {
          case 'day':
            date = date.add(addAmount, 'day');
            break;
          case 'month':
            date = date.add(addAmount, 'month');
            break;
          case 'quarter':
            date = date.add(addAmount * 3, 'month');
            break;
          case 'year':
            date = date.add(addAmount, 'year');
            break;
        }
        date = option === 'plus' ? date.endOf('day') : date.startOf('day');
      }

      return date.format(dateFormat);
    };

    const prefixDesc = getPrefixDesc(option, selectedOption, amount);
    const formattedDate = getFormattedDate(option, selectedOption, amount);

    setExamplePrefixDesc(prefixDesc);
    setExampleValue(formattedDate);
  };

  return (
    <FormProvider {...methods}>
      <DialogComponent
        width="sm"
        open={open}
        title={''}
        handleClose={onClose}
        submitButtonText="Apply"
        handleSubmit={() => {
          const result = getValues();
          handleSubmit(() => {
            onSubmit({ ...result, option: selectedOption });
          })();
        }}
        dontShowCloseIcon={true}
      >
        {(amount === null || amount === undefined || amount === '') && (
          <Alert severity="error">Please enter valid amount</Alert>
        )}

        <Grid container xs={12} spacing={2}>
          <Grid item xs={1} style={{ marginTop: 18, marginRight: 16 }}>
            {type.toLowerCase() === 'date' ? 'Today' : 'Now'}
          </Grid>
          <Grid item xs={2} style={{ marginTop: 8 }}>
            <RadioGroupComponent
              //@ts-ignore
              {...register('datePickerRadio', { required: true })}
              options={options}
              radioSelection={{ value: option || '' }}
              handleSetRadioSelection={async (newRadioSelection: RadioSelection) => {
                await setValue('datePickerRadio', newRadioSelection.value);
              }}
              handleUpdateOptions={() => {}}
            />
          </Grid>
          <Grid item xs={2}>
            <FormControl>
              <NumberInput
                //@ts-ignore
                {...register('amount', { required: true })}
                // @ts-ignore
                field={{
                  disableLabel: true,
                  name: '',
                  apiName: 'amount',
                  type: 'integer',
                  updatable: true,
                  creatable: true,
                  required: true,
                  shouldValidateOnChange: true,
                  showNone: false,
                  hideInlineError: true,
                }}
                required={false}
                value={amount}
                disableLabel
                name="number-input"
                handleInputChange={async (newValue: number) => {
                  await setValue('amount', newValue);
                }}
              />
            </FormControl>
          </Grid>
          <Grid item xs={4} style={{ marginTop: 8 }}>
            <PickList
              value={selectedOption}
              label=" "
              field={{
                apiName: 'savedFilter',
                name: 'Saved Filter',
                type: 'text',
              }}
              disableLabel
              name="filter"
              options={[
                {
                  name: 'Days',
                  value: 'day',
                },
                {
                  name: 'Months',
                  value: 'month',
                },
                {
                  name: 'Quarters',
                  value: 'quarter',
                },
                {
                  name: 'Years',
                  value: 'year',
                },
              ]}
              handleInputChange={(sValue: string) => {
                setSelectOption(sValue);
              }}
            />
          </Grid>
          <ToolTipContainer toolTipText={''} ToolTipIcon={HelpIcon} style={{ marginLeft: '5px' }}>
            <Typography variant="body2" component="p" display="inline">
              {examplePrefixDesc}
              {`, i.e., `}
              <Typography
                variant="body2"
                className={classes.textHighlight}
                component="p"
                display="inline"
              >
                {exampleValue}
              </Typography>
            </Typography>
          </ToolTipContainer>
        </Grid>
      </DialogComponent>
    </FormProvider>
  );
};

import React from 'react';

export interface AvalaraEInvoicingConnectionRequest {
  clientId: string | null;
  clientSecret?: string | null;
  environment?: string;
  appCategory?: string;
  integrationType?: string;
  id?: string;
}

export interface AvalaraEInvoicingFilterRequest {
  templateId: string;
  name: string;
  graphqlFilter: string;
  condition: string;
}

export interface AvalaraEInvoicingCountryMandatesRequest {
  companyId: string;
  countryName: string;
  countryCode: string;
  countryMandateCode: string;
  documentType: string;
}

export interface IntegrationResponse {
  config: IntegrationDetail;
  active: boolean;
  id: string;
  integrationType: string;
  status: string;
}

export interface IntegrationDetail {
  clientId: string;
  clientSecret: string;
  integrationStatus: string;
  lastModifiedDate: string;
  environment: string;
}

export interface CountryMandates {
  active: boolean;
  companyId: string;
  countryCode: string;
  countryMandateCode: string;
  countryName: string;
  dataFormat: string;
  id: string;
  sequence: number;
}

export interface MappingSchema {
  id?: string | null;
  templateId?: string;
  elrXpath?: string;
  nueObject?: string;
  nueField?: string;
  optionality?: string;
  staticValue?: string;
  mappingType: string;
  dataType: string;
  attributeName?: string;
  valueSet?: any;
  disableStaticValue?: boolean;
  disableNueMappings?: boolean;
}

export interface CompanyType {
  companyName: string;
  id: string;
  active: boolean;
  companyCode: string;
  defaultCountry: string;
}

export interface DocumentType {
  name: string;
  id: string;
  apiName: string;
}

export interface TestResponseBody {
  success: boolean;
  error?: string;
}

export interface TestResponse {
  data: TestResponseBody;
}

export interface AvalaraEInvoicingEditingMappingsDialogProps {
  open: boolean;
  onClose: any;
  mappingSchema: MappingSchema[];
  documentType?: DocumentType;
  setMappingSchema: any;
  countryMandate?: CountryMandates;
  handleClose: any;
  doSave: any;
  doRestore: any;
  deletedRowIds: any;
  setDeletedRowIds: any;
  rowChanges: any;
  setRowChanges: any;
  editingRowIds: any;
  setEditingRowIds: any;
  mappingField: any;
  setMappingField: any;
  activeFilterRow: any;
  allowedXmlPath: any;
}

export interface AvalaraEInvoicingConnectionService {
  onTestAvalaraEInvoicingIntegration: (
    request: AvalaraEInvoicingConnectionRequest,
    id: string,
  ) => Promise<TestResponse>;
  getAvalaraEInvoicingIntegrationDetail: () => Promise<IntegrationResponse>;
  getAvalaraEInvoicingCountryMandates: (
    company?: CompanyType,
    documentType?: DocumentType,
  ) => Promise<CountryMandates[]>;
  getAvalaraEInvoicingCompanyList: () => Promise<{ value: CompanyType[] }>;
  getAvalaraEInvoicingDocumentTypeList: () => Promise<DocumentType[]>;
  onCreateAvalaraEInvoicingIntegration: (
    request: AvalaraEInvoicingConnectionRequest,
    method: string,
  ) => Promise<{ data: { id: string } }>;
  onUpdateAvalaraEInvoicingIntegration: (enabled: string, id: string) => Promise<void>;
  onUpdateAvalaraEInvoicingCountryMandatesStatus: (enabled: string, id: string) => Promise<void>;
  onUpdateAvalaraEInvoicingCountryMandatesFilter: (
    request: AvalaraEInvoicingFilterRequest,
  ) => Promise<void>;
  onGetAvalaraEInvoicingCountryMandatesFilter: (countryMandateId: string) => Promise<void>;
  onCreateNewAvalaraEInvoicingCountryMandates: (
    request: AvalaraEInvoicingCountryMandatesRequest,
  ) => Promise<void>;
  getCountryMandatesMappings: (
    mandateCode: string,
    documentType: string,
    templateId: string,
  ) => Promise<void>;
  getDefaultCountryMandates: () => Promise<any>;
  setCountryMandatesMappings: (request: any) => Promise<void>;
  onHandleResotreAvalaraEInvoicingMappings: (mandateId: string) => Promise<void>;
  onUpdateAvalaraEInvoicingRetrieveStatus: (status: string) => Promise<any>;
  onGetAvalaraEInvoicingRetrieveStatus: () => Promise<any>;
  onGetAvalaraEInvoicingAllowedXmlPath: (
    mandateCode: string,
    documentType: string,
  ) => Promise<void>;
  downloadEInvoicingDocument: (id: string, type: string) => Promise<any>;
  getTenantDetails: () => Promise<any>;
}

export const AvalaraEInvoicingConnectionContext =
  React.createContext<AvalaraEInvoicingConnectionService | null>(null);

export default AvalaraEInvoicingConnectionContext;

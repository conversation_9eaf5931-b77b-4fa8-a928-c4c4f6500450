/*eslint @typescript-eslint/no-shadow:0 */

import {
  Dialog,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Grid,
  makeStyles,
  Radio,
  RadioGroup,
  Snackbar,
} from '@material-ui/core';
import React, { useContext, useEffect, useState } from 'react';
import ModalAppBar from '../modal-app-bar';
import Loading from '../loading';
import type {
  AvalaraEInvoicingEditingMappingsDialogProps,
  MappingSchema,
} from './avalara-e-invoicing-connection-context';
import AvalaraEInvoicingConnectionMappingsEditor from './avalara-e-invoicing-connection-mappings-editor';
import * as Yup from 'yup';
import _ from 'lodash';
import type { RubyField, ValuePair } from '../metadata';
import { BillingSettingsContext } from '../ruby-settings';
import RubySwitch from '../ruby-switch';
import { RubyButtonBar } from '../ruby-button';

// import { useSelector } from 'react-redux'
// import GraphQLQueryApi from '../../../api/GraphQLQueryApi'
// import Button from '@material-ui/core/Button'
// import '../../../components/CreatePriceBookEntry/CreatePriceBookEntry.css'

// import { deleteObjects, saveOrUpdateObjects } from '../../../util/FormUtil'
// import PropTypes from 'prop-types'
// import DialogTitle from '@material-ui/core/DialogTitle'
// import DialogContent from '@material-ui/core/DialogContent'
// import Dialog from '@material-ui/core/Dialog'
// import HighlightOffIcon from '@material-ui/icons/HighlightOff'
// import RubyButtonBar from '../../../components/RubyButton/RubyButtonBar'
// import DialogComponent from '../../../components/DialogComponent/DialogComponent'
// import {
//   GraphqlQueryConstructor,
//   PriceBookEntryGrid,
//   ToolbarFilter,
//   useRubySnackbar
// } from '@nue-apps/ruby-ui-component'
// import Grid from '@material-ui/core/Grid'
// import MetadataSelectInput from '../../../components/MetadataSelectInput/MetadataSelectInput'
// import { lowercaseFirstChar } from '../../../util/StringUtils'
// import useMultiCurrencyInfo from '../../../hooks/useMultiCurrencyInfo'
// import shortUUID from 'short-uuid'
// import _ from 'lodash'
// import {
//   buildValidator,
//   getFilterableColumns,
//   getPricebookEntryCustomFields
// } from '../../components/price'
// import { Alert } from '@material-ui/lab'
// import { useRubySettings } from '../../../hooks/useRubySettings'

const ROW_STATE_SYMBOL = Symbol.for('RowState');

// Yup.addMethod(Yup.array, 'unique', function (message, mapper = (a) => a) {
//   return this.test('unique', message, function (list) {
//     return list.length === new Set(list.map(mapper)).size
//   })
// })

const useStyles = makeStyles({
  dialogTitle: {
    padding: 0,
  },
  balanceIcon: {
    color: '#FF7D00',
    marginRight: 8,
  },
  gridContainer: {
    display: 'flex',
    flexDirection: 'column',
    marginBottom: 60,
  },
  headerContainer: {
    marginBottom: 48,
  },
});

const AvalaraEInvoicingEditingMappingsDialog = (
  props: AvalaraEInvoicingEditingMappingsDialogProps,
) => {
  const classes = useStyles();
  const {
    open,
    onClose,
    mappingSchema,
    documentType,
    setMappingSchema,
    countryMandate,
    handleClose,
    doSave,
    doRestore,
    deletedRowIds,
    setDeletedRowIds,
    rowChanges,
    setRowChanges,
    editingRowIds,
    setEditingRowIds,
    mappingField,
    setMappingField,
    activeFilterRow,
    allowedXmlPath,
  } = props;
  const billingSettingsCtx = useContext(BillingSettingsContext);
  const [mappedFieldValuePairs, setMappedFieldValuePairs] = useState<ValuePair[]>([]);
  const [xpathValuePairs, setXpathValuePairs] = useState<ValuePair[]>([]);
  const [originalRowChanges, setOriginalRowChanges] = useState(rowChanges);

  const [loading, setLoading] = useState(false);
  const [columns, setColumns] = useState([
    {
      apiName: 'elrXpath',
      name: 'elrXpath',
      title: 'DOCUMENT FIELD',
      type: 'pickList',
      required: false,
      updatable: false,
      creatable: false,
      showTooltip: false,
      toolTipText: '',
      valueSet: {
        valuePairs: allowedXmlPath.map((path: any) => {
          return {
            name: path.path,
            apiName: path.path,
            active: true,
            readOnly: true,
          };
        }),
      },
    },
    {
      apiName: 'dataType',
      name: 'dataType',
      title: 'FIELD TYPE',
      type: 'text',
      required: false,
      updatable: false,
      creatable: false,
      showTooltip: false,
      toolTipText: '',
    },
    {
      apiName: 'staticValue',
      name: 'staticValue',
      title: 'CONSTANT VALUE',
      type: 'text',
      required: false,
      updatable: true,
      creatable: true,
      showTooltip: false,
      toolTipText: '',
    },
    {
      apiName: 'nueObject',
      name: 'nueObject',
      title: 'MAPPED OBJECT',
      type: 'pickList',
      required: false,
      updatable: true,
      creatable: true,
      showTooltip: false,
      toolTipText: '',
      valueSet: {
        valuePairs: [
          {
            name: 'Organization',
            apiName: 'organization',
            active: true,
            readOnly: true,
          },
          {
            name: 'Sales Account',
            apiName: 'UserProfile',
            active: true,
            readOnly: false,
          },
          {
            name: 'Billing Account',
            apiName: 'Customer',
            active: true,
            readOnly: false,
          },
          {
            name: documentType?.name || '',
            apiName: documentType?.apiName || '',
            active: true,
            readOnly: false,
          },
          {
            name:
              documentType?.name === 'Invoice'
                ? 'Invoice Item'
                : documentType?.name === 'CreditMemo'
                  ? 'Credit Memo Item'
                  : '',
            apiName:
              documentType?.name === 'Invoice'
                ? 'InvoiceItem'
                : documentType?.name === 'CreditMemo'
                  ? 'CreditMemoItem'
                  : '',
            active: true,
            readOnly: false,
          },
          {
            name: 'Product',
            apiName: 'Product',
            active: true,
            readOnly: false,
          },
          {
            name: 'Uom',
            apiName: 'UOM',
            active: true,
          },
        ],
      },
    },
    {
      apiName: 'nueField',
      name: 'nueField',
      title: 'MAPPED FIELD',
      type: 'pickList',
      required: false,
      updatable: true,
      creatable: true,
      showTooltip: false,
      toolTipText: '',
      valueSet: {
        valuePairs: mappedFieldValuePairs,
      },
    },
    // {
    //   apiName: 'elrXpath',
    //   name: 'elrXpath',
    //   title: 'Elr Xpath',
    //   type: 'pickList',
    //   required: false,
    //   updatable: true,
    //   creatable: true,
    //   showTooltip: false,
    //   toolTipText: '',
    //   valueSet: {
    //     valuePairs: allowedXmlPath.map((path: any) => {
    //       return {
    //         name: path.path,
    //         apiName: path.path,
    //         active: true,
    //         readOnly: true,
    //       };
    //     }),
    //   },
    // },
  ]);
  const [columnWidths, setColumnWidths] = useState([
    { columnName: 'elrXpath', width: 800 },
    { columnName: 'dataType', width: 150 },
    // { columnName: 'elrXpath', width: 800 },
    { columnName: 'staticValue', width: 150 },
    { columnName: 'nueObject', width: 150 },
    { columnName: 'nueField', width: 150 },
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validate = (columns: RubyField[]) => {
    const rowShape = {
      productId: Yup.string().required('Product is required'),
    };

    const objValidateSchema = Yup.object().shape(rowShape);
    const schema = Yup.array().of(objValidateSchema);

    const validate = async (rows: MappingSchema[]) => {
      const validationStatus = new Map();
      try {
        await schema.validate(rows);
      } catch (yupValidateResult) {
        if (yupValidateResult.errors.length > 0) {
          if (yupValidateResult.path && yupValidateResult.path.indexOf('.') >= 0) {
            const [idxPath, columnName] = yupValidateResult.path.split('.');
            const row = _.get(rows, idxPath);
            const errorMessage = {};
            //@ts-ignore
            errorMessage[columnName] = {
              isValid: false,
              error: yupValidateResult.errors[0],
            };
            validationStatus.set(row.id, errorMessage);
          }
        }
      }
      return validationStatus;
    };
    return validate;
  };

  // filter the metadata field based on the field type
  const filterMetadataFieldFromFieldType = (field: RubyField, mapping: MappingSchema) => {
    switch (mapping.dataType) {
      case 'currency':
        return field.type === 'currency';
      case 'percentage':
        return field.type === 'percent';
      case 'percent':
        return field.type === 'percent';
      case 'decimal':
        return field.type === 'decimal';
      case 'number':
        return field.type === 'pickList'
          ? _.isNumber(field.valueSet?.valuePairs[0].name)
          : ['decimal', 'percent', 'integer', 'number', 'currency'].includes(field.type);
      case 'numeric':
        return field.type === 'pickList'
          ? _.isNumber(field.valueSet?.valuePairs[0].name)
          : ['decimal', 'percent', 'integer', 'number', 'currency'].includes(field.type);
      case 'string':
        return field.type === 'pickList'
          ? _.isString(field.valueSet?.valuePairs[0].name)
          : ['autoNumber', 'text', 'bId', 'longText', 'id', 'url', 'string'].includes(field.type);
      case 'text':
        return field.type === 'pickList'
          ? _.isString(field.valueSet?.valuePairs[0].name)
          : ['autoNumber', 'text', 'bId', 'longText', 'id', 'url', 'string'].includes(field.type);
      case 'checkbox':
        return field.type === 'boolean';
      case 'boolean':
        return field.type === 'boolean';
      case 'date':
        return field.type === 'date';
      case 'dateTime':
        return field.type === 'dateTime';
      default:
        return false;
    }
  };

  const handleUpdateColumnConfiguration = _.debounce(() => {
    if (rowChanges && Object.keys(rowChanges).length > 0) {
      Object.keys(rowChanges).forEach((id: string) => {
        if (
          !originalRowChanges[id] ||
          (originalRowChanges[id] &&
            JSON.stringify(originalRowChanges[id] !== JSON.stringify(rowChanges[id])))
        ) {
          const row = mappingSchema.find((r: MappingSchema) => r.id === id);
          //@ts-ignore
          if (row && row.id && rowChanges[row.id] && rowChanges[row.id]?.nueObject) {
            row.disableStaticValue = true;
            //@ts-ignore
            const nueMetadata = billingSettingsCtx?.metadataObjects.find(
              //@ts-ignore
              (m) => m.apiName === rowChanges[row.id]?.nueObject,
            );
            row.nueField = '';
            if (nueMetadata) {
              row.valueSet = {
                valuePairs: nueMetadata.fields,
              };
            } else if (!nueMetadata && rowChanges[row.id]?.nueObject === 'organization') {
              row.valueSet = {
                valuePairs: [
                  {
                    name: 'Legal Entity Name',
                    apiName: 'legalName',
                    type: 'text',
                    active: true,
                    readOnly: true,
                  },
                  {
                    name: 'Country',
                    apiName: 'country',
                    type: 'text',
                    active: true,
                    readOnly: false,
                  },
                ],
              };
              row.nueField = '';
            }
          } else if (row && row.id && rowChanges[row.id] && !rowChanges[row.id]?.nueObject) {
            row.disableStaticValue = false;
            row.nueField = '';
            if (row && row.id && rowChanges[row.id] && rowChanges[row.id]?.staticValue) {
              row.disableNueMappings = true;
            } else if (row && row.id && rowChanges[row.id] && !rowChanges[row.id]?.staticValue) {
              row.disableNueMappings = false;
            }
          } else if (row && row.id && rowChanges[row.id] && rowChanges[row.id]?.staticValue) {
            row.disableNueMappings = true;
          } else if (row && row.id && rowChanges[row.id] && !rowChanges[row.id]?.staticValue) {
            row.disableNueMappings = false;
            if (row && row.id && rowChanges[row.id] && rowChanges[row.id]?.nueObject) {
              row.disableStaticValue = true;
            } else if (row && row.id && rowChanges[row.id] && !rowChanges[row.id]?.nueObject) {
              row.disableStaticValue = false;
            }
          }
        }
      });
      setOriginalRowChanges(rowChanges);
      setMappingSchema([...mappingSchema]);
    }
  }, 1000);

  useEffect(() => {
    handleUpdateColumnConfiguration();
  }, [rowChanges]);

  if (!billingSettingsCtx) {
    return null;
  }

  return (
    <>
      <Dialog
        open={open}
        fullWidth
        fullScreen
        PaperProps={{
          style: {
            backgroundColor: '#F9F8FA',
          },
        }}
      >
        <DialogTitle className={classes.dialogTitle}>
          <ModalAppBar
            handleClose={() => {
              // setData([]);
              onClose();
            }}
          />
        </DialogTitle>
        <DialogContent>
          {loading ? (
            <Loading />
          ) : (
            <div
              style={{
                paddingBottom: 100,
              }}
            >
              <Grid container style={{ marginBottom: '8px' }}>
                <Grid item xs={12}>
                  <h1>{countryMandate?.countryMandateCode + ' Mapping'}</h1>
                </Grid>
              </Grid>
              <Grid container>
                <Grid item xs={12}>
                  <h3>{'Mapping Fields'}</h3>
                </Grid>
              </Grid>
              <Grid container style={{ marginBottom: '20px' }}>
                <Grid item xs={12}>
                  <RadioGroup
                    aria-label="metadata-radio"
                    row
                    name={'Mapping Field'}
                    value={mappingField}
                    onChange={(event, value) => {
                      setMappingField(event.target.value);
                    }}
                  >
                    <Grid item container xs={12} spacing={3}>
                      <Grid item style={{ display: 'flex', alignItems: 'center' }}>
                        <FormControlLabel
                          value={'All'}
                          control={<Radio size="small" />}
                          disabled={false}
                          label={'All'}
                          onChange={() => {
                            setMappingField('All');
                          }}
                        />
                      </Grid>
                      <Grid item style={{ display: 'flex', alignItems: 'center' }}>
                        <FormControlLabel
                          value={'Required'}
                          control={<Radio size="small" />}
                          disabled={false}
                          label={'Required'}
                          onChange={() => {
                            setMappingField('Required');
                          }}
                        />
                      </Grid>
                      <Grid item style={{ display: 'flex', alignItems: 'center' }}>
                        <FormControlLabel
                          value={'Conditional'}
                          control={<Radio size="small" />}
                          disabled={false}
                          label={'Conditional'}
                          onChange={() => {
                            setMappingField('Conditional');
                          }}
                        />
                      </Grid>
                      <Grid item style={{ display: 'flex', alignItems: 'center' }}>
                        <FormControlLabel
                          value={'Optional'}
                          control={<Radio size="small" />}
                          disabled={false}
                          label={'Optional'}
                          onChange={() => {
                            setMappingField('Optional');
                          }}
                        />
                      </Grid>
                    </Grid>
                  </RadioGroup>
                </Grid>
              </Grid>
              <AvalaraEInvoicingConnectionMappingsEditor
                columns={columns}
                rows={
                  mappingField !== 'All'
                    ? mappingSchema.filter((m) => m.optionality === mappingField)
                    : mappingSchema
                }
                setRows={setMappingSchema}
                deletedRowIds={deletedRowIds}
                setDeletedRowIds={setDeletedRowIds}
                //@ts-ignore
                validate={validate}
                rowChanges={rowChanges}
                setRowChanges={setRowChanges}
                editingRowIds={editingRowIds}
                setEditingRowIds={setEditingRowIds}
                columnWidths={columnWidths}
                activeFilterRow={activeFilterRow}
              />
            </div>
          )}
        </DialogContent>
        <RubyButtonBar
          variant="floating"
          fullScreen={true}
          style={{
            width: '100%',
          }}
          leftButtons={[
            {
              text: 'Cancel',
              onClick: handleClose,
              processing: isSubmitting,
            },
          ]}
          rightButtons={[
            {
              text: 'Save and Close',
              type: 'submit',
              formNoValidate: true,
              processing: isSubmitting,
              disabled: activeFilterRow?.active,
              onClick: async () => {
                try {
                  setIsSubmitting(true);
                  await doSave();
                  handleClose();
                } finally {
                  setIsSubmitting(false);
                }
              },
            },
            {
              text: 'Save',
              processing: isSubmitting,
              disabled: activeFilterRow?.active,
              onClick: async () => {
                try {
                  setIsSubmitting(true);
                  await doSave();
                } finally {
                  setIsSubmitting(false);
                }
              },
            },
            {
              text: 'Restore to Defaults',
              type: 'submit',
              formNoValidate: true,
              processing: isSubmitting,
              disabled: activeFilterRow?.active,
              onClick: async () => {
                doRestore();
              },
            },
          ]}
        />
        <Snackbar />
      </Dialog>
    </>
  );
};

// AvalaraEInvoicingEditingMappingsDialog.propTypes = {
//   handleClose: PropTypes.func,
//   product: PropTypes.object,
//   priceBook: PropTypes.object,
//   open: PropTypes.bool,
//   dialogOptions: PropTypes.object
// }

export default AvalaraEInvoicingEditingMappingsDialog;

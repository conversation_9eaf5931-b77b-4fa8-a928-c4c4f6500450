import React, { useEffect, useRef } from 'react';
import { Props } from './interface';

const defaultProps: Partial<Props> = {};

/**
 * This is a basic version ClickAwayListener inspired by MaterialUI's version, but without using `event.composedPath()` API which violates salesforce's locker service
 * @param userProps
 * @constructor
 */
const ClickAwayListener: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const { onClickAway } = props;
  const wrapperRef = useRef<HTMLDivElement>(null);

  const handleClickAway = (event: MouseEvent) => {
    if (
      wrapperRef &&
      wrapperRef.current &&
      //@ts-ignore
      !wrapperRef.current.contains(event.target)
    ) {
      onClickAway(event);
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClickAway);
    return () => {
      document.removeEventListener('click', handleClickAway);
    };
  }, []);

  return <div ref={wrapperRef}>{props.children}</div>;
};

export default ClickAwayListener;

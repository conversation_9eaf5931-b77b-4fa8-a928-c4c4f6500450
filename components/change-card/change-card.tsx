import React, { useContext } from 'react';

import { makeStyles } from '@material-ui/core/styles';
import CloseIcon from '@material-ui/icons/Close';

import type { User } from '../metadata';
import RubyButton from '../ruby-button';
import useConfirmDialog from '../use-confirm-dialog';
import { UserContext } from '../user-context';
import ChangeCardBody from './change-card-body';
import ChangeCardHeader from './change-card-header';
import type { Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  container: {
    position: 'relative',
    maxWidth: 600,
    backgroundColor: '#F5F5F5',
    borderRadius: '10px',
    paddingBottom: '20px',
  },
  btnContainer: {
    display: 'flex',
    justifyContent: 'flex-end',
    paddingRight: '40px',
  },
  closeBtn: {
    right: '-12px',
    top: '-15px',
    position: 'absolute',
    backgroundColor: '#6239EB',
    borderRadius: '25px',
    height: '32px',
    minWidth: '32px',
    fontWeight: 400,
    width: '32px',
    boxShadow: '0 12px 32px -8px rgba(98,57,235,0.64)',
  },
});

const ChangeCard: React.FC<Props> = (userProps: Props) => {
  const props = { ...defaultProps, ...userProps };
  const {
    changeGroup,
    changeGroupNumber,
    handleRemoveChangesFromCart,
    handleRemoveChangeItems,
    handleCheckout,
    onAddToCartComplete,
  } = props;

  const classes = useStyles();

  const { userContextService } = useContext(UserContext);
  const user = userContextService?.getUserConfig()?.user as User;
  const defaultCurrencyIsoCode = user.currencyIsoCode || 'USD';
  const { showConfirmDialog, ConfirmDialog } = useConfirmDialog({
    submitButtonText: 'Yes',
    cancelButtonText: 'No',
    onOk: async () => {
      try {
        await handleRemoveChangesFromCart(changeGroup.id);
      } catch (error) {
        // TODO: Handle error (e.g., show toast notification to user)
        console.error('Failed to remove changes from cart:', error);
      }
    },
    onCancel: () => {
      // do nothing
    },
  });

  return (
    <>
      <div className={classes.container}>
        <RubyButton
          text=""
          classNames={classes.closeBtn}
          onClick={() =>
            showConfirmDialog({
              title: 'Discard',
              message: `Are you sure you want to discard all changes made to Change ${changeGroupNumber}?`,
            })
          }
        >
          <CloseIcon fontSize="small" />
        </RubyButton>
        <ChangeCardHeader
          numberChangeItems={changeGroup.changeItems ? changeGroup.changeItems.length : 0}
          changeGroupNumber={changeGroupNumber}
          priceBook={changeGroup.priceBook}
          currencyIsoCode={changeGroup.currencyIsoCode || defaultCurrencyIsoCode}
          customer={changeGroup.customer}
        />
        <ChangeCardBody
          changeGroup={changeGroup}
          changeItems={changeGroup.changeItems || []}
          handleRemoveChangeItems={handleRemoveChangeItems}
          onAddToCartComplete={onAddToCartComplete}
        />
        <div className={classes.btnContainer}>
          <RubyButton text="Checkout" onClick={handleCheckout} />
        </div>
      </div>
      <ConfirmDialog />
    </>
  );
};

export default ChangeCard;

import React from 'react';
import { InputBase, TextField, withStyles } from '@material-ui/core';
const InputBaseComponent = withStyles((theme) => ({
  root: {
    fontFamily: 'inherit',
    'label + &': {},
  },
  input: {
    marginTop: theme.spacing(1),
    borderRadius: 4,
    position: 'relative',
    border: '1px solid #ced4da',
    fontSize: '.875rem',
    fontWeight: 500,
    color: '#4d4c50',
    width: '100%',
    padding: '12px 20px',
    transition: theme.transitions.create(['border-color', 'box-shadow']),
    '&:focus': {
      borderColor: theme.palette.secondary.main,
      borderRadius: 4,
    },
    '&:disabled': {
      backgroundColor: '#fafafb',
      color: 'rgba(0,0,0,0.2)',
    },
    '&:read-only': {
      backgroundColor: '#fafafb',
      color: 'rgba(0,0,0,0.2)',
      border: '1px solid #ced4da',
    },
  },
}))(InputBase);

export default InputBaseComponent;

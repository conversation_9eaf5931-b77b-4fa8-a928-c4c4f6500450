import React, { useState } from 'react';

import { Grid, Link } from '@material-ui/core';
import Grow from '@material-ui/core/Grow';
import { makeStyles } from '@material-ui/core/styles';

import CardActionBar from '../card-action-bar';
import CardActionsMenu from '../card-actions-menu';
import List from '../list';
import { CardAction } from '../metadata/interface';
import { Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  container: {
    boxShadow: '0 -6px 53px 0 rgba(0,0,0,0.08)',
    margin: '0 -30px -30px',
    height: '100%',
    display: 'flex',
    justifyContent: 'space-between',
  },
  menuContainer: {
    width: '100%',
    position: 'absolute',
    top: '100%',
    bottom: 0,
    display: 'flex',
    overflow: 'hidden',
  },
  itemsContainer: {
    display: 'flex',
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'space-between',
    padding: '16px 30px 16px 20px',
  },
  listContainer: {
    paddingBottom: '24px',
  },
  showDetailsButtonContainer: {
    marginTop: 20,
    cursor: 'pointer',
    textAlign: 'center',
  },
});

export const CardBody: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const [openActionsMenu, setOpenActionsMenu] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    currencyIsoCode,
    actions,
    actionEventHandler,
    numCardActionsPerRow,
    uom,
    moreActions,
    locale,
  } = props;

  const classes = useStyles();

  const { items, summaryItem, showSubmitBtn, showDetails, onShowDetails } = props;
  return (
    <div className={classes.container}>
      {moreActions && moreActions?.length > 0 && (
        <Grow
          in={openActionsMenu}
          style={
            openActionsMenu && moreActions
              ? {}
              : { visibility: 'hidden', width: 0, padding: 0, transformOrigin: 'bottom', height: 0 }
          }
          {...(openActionsMenu ? { timeout: 1000 } : {})}
        >
          <CardActionsMenu
            actions={moreActions}
            openActionsMenu={openActionsMenu}
            handleClose={() => {
              setOpenActionsMenu(false);
            }}
            handleClickAction={async (action: CardAction) => {
              await actionEventHandler(action);
            }}
          />
        </Grow>
      )}
      <div
        className={classes.itemsContainer}
        style={
          openActionsMenu && moreActions
            ? { visibility: 'hidden', width: 0, padding: 0, height: 0 }
            : {}
        }
      >
        <div className={classes.listContainer}>
          <List
            uom={uom}
            currencyIsoCode={currencyIsoCode}
            items={items}
            summaryItem={summaryItem}
            locale={locale}
          />
        </div>
        {actions && actions.length > 0 && (
          <CardActionBar
            numRows={2}
            numActionsPerRow={numCardActionsPerRow}
            maxNumActionsToShow={5}
            actions={actions}
            isProcessing={isProcessing}
            showSubmitBtn={showSubmitBtn === undefined ? true : showSubmitBtn}
            handleClickAction={async (action: CardAction) => {
              if (action.id === 'more') {
                setOpenActionsMenu(true);
              } else {
                try {
                  setIsProcessing(true);
                  await actionEventHandler(action);
                } finally {
                  setIsProcessing(false);
                }
              }
            }}
          />
        )}
        {showDetails && (
          <Grid className={classes.showDetailsButtonContainer}>
            <Link
              variant="body2"
              href="#"
              role="link"
              component="p"
              underline="none"
              onClick={() => {
                if (onShowDetails) {
                  onShowDetails();
                }
              }}
            >
              Show Details
            </Link>
          </Grid>
        )}
      </div>
    </div>
  );
};
export default CardBody;

import React, { useState, useContext, useEffect } from 'react';
import { Divider, Grid, Typography } from '@material-ui/core';
import RubyCheckbox from '../../checkbox';
import { PickListOption } from '../../pick-list/interface';
import { useRubySnackbar } from '../../ruby-notifier';
import { TemplateSchema } from '../../template-builder-context/template-builder-context';
import templateClassicSchema from '../../template-builder/template-classic';
import templateFriendlySchema from '../../template-builder/template-friendly';
import { SubcategoryEditorProps } from '../interface';
import { BillingSettingsContext } from '../interface';
import { RubySettingEditorPanel } from '../ruby-setting-editor-panel';
import SettingItemLabel from './setting-item-label';
import useStyles from './styles';
import TemplateListViewSection from './template-list-view-section';
import { UserContext } from '../../user-context';
import { User, UserRole } from '../../metadata';
import OrderFormFriendlySchema from '../../template-builder/structures/order-form-friendly';
import InlineInput from '../../inline-input';

interface OrderFormSettings {
  orderFormEmailTemplate: string;
  attachQuotePDFToQuote: boolean;
  attachQuotePDFToOpportunity: boolean;
}

const DEFAULT_SETTING_VALUES = {
  orderFormEmailTemplate: 'OrderFormEmailTemplate',
  attachQuotePDFToQuote: true,
  attachQuotePDFToOpportunity: false,
};

const OrderFormsSettingsEditor: React.FC<SubcategoryEditorProps> = (props: any) => {
  const classes = useStyles();
  const { category, onSubcategoryChange } = props;
  const { settings = { ...DEFAULT_SETTING_VALUES } } = category;
  const [orderFormSettings, setOrderFormSettings] = useState<OrderFormSettings>({
    orderFormEmailTemplate: settings.orderFormEmailTemplate,
    attachQuotePDFToQuote: settings.attachQuotePDFToQuote,
    attachQuotePDFToOpportunity: settings.attachQuotePDFToOpportunity,
  });

  const { userContextService } = useContext(UserContext);

  if (!userContextService) {
    throw Error(
      'OrderFormsSettings component requires userContextService to be declared in context provider',
    );
  }
  const user = userContextService.getUserConfig().user as User;

  const { showSnackbar, Snackbar } = useRubySnackbar();

  const billingSettingsCtx = useContext(BillingSettingsContext);

  const [showInvoiceTemplatePreview, setShowInvoiceTemplatePreview] = useState<TemplateSchema>();
  const [showInvoiceTemplateEdit, setShowInvoiceTemplateEdit] = useState<boolean>(false);
  const [invoiceTemplateEdit, setInvoiceTemplateEdit] = useState<TemplateSchema>();
  const [invoiceTemplateList, setInvoiceTemplateList] = useState<TemplateSchema[]>([
    templateFriendlySchema,
    templateClassicSchema,
  ]);
  const [invoiceList, setInvoiceList] = useState<any[]>([]);
  const [refreshFlag, setRefreshFlag] = useState(1);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [openNewTemplate, setOpenNewTemplate] = useState(false);

  function handleSettingItemChange(newValue: Partial<OrderFormSettings>) {
    setOrderFormSettings({
      ...orderFormSettings,
      ...newValue,
    });
    onSubcategoryChange({
      ...category,
      settings: newValue,
    });
  }

  const setup = async () => {
    if (billingSettingsCtx) {
      // const quoteList = await billingSettingsCtx.getQuoteList();
      const orderList = await billingSettingsCtx.getOrderList();
      setInvoiceList(orderList);
    }
  };

  useEffect(() => {
    setup();
  }, []);

  const getSuccessMsg = (ac: string, type: string, name: string) => {
    return 'Successfully ' + ac + ' ' + type + ' ' + name + '.';
  };

  const getAllOrderFormsTemplateList = async (currentPage: any, pageSize: any) => {
    const templateList = await billingSettingsCtx?.getInvoiceTemplateList(
      currentPage,
      pageSize,
      'order',
    );
    if (templateList) {
      setInvoiceTemplateList(templateList);
    }
    return templateList;
  };
  const handleDeleteInvoiceTemplate = async (row: TemplateSchema) => {
    if (row && row.id) {
      const res = await billingSettingsCtx?.deleteInvoiceTemplate(row.id, 'order');
      if (res.success) {
        showSnackbar(
          'confirm',
          'Success',
          getSuccessMsg('deleted', 'quote template', row?.name || ''),
        );
        setRefreshFlag(refreshFlag + 1);
      }
    }
  };
  const handleEditInvoiceTemplate = async (row: TemplateSchema) => {
    if (row && row.id) {
      const invoiceTemplate = await billingSettingsCtx?.getInvoiceTemplateById(row.id, 'order');
      // setInvoiceTemplateEdit(OrderFormFriendlySchema)
      setInvoiceTemplateEdit(invoiceTemplate);
      setShowInvoiceTemplateEdit(true);
    }
  };
  const handlePreviewInvoiceTemplate = async (row: TemplateSchema) => {
    if (row && row.id) {
      const invoiceTemplate = await billingSettingsCtx?.getInvoiceTemplateById(row.id, 'order');
      setShowInvoiceTemplatePreview(invoiceTemplate);
    }
  };
  const handleCopyInvoiceTemplate = async (row: TemplateSchema) => {
    if (row && row.id) {
      setIsSubmitting(true);
      const res = await billingSettingsCtx?.cloneInvoiceTemplate(row.id, 'order');
      if (res.success) {
        showSnackbar(
          'confirm',
          'Success',
          getSuccessMsg('duplicated', 'invoice template', row?.name || ''),
        );
        setIsSubmitting(false);
        setRefreshFlag(refreshFlag + 1);
        setOpenNewTemplate(true);
      }
    }
  };

  const handleMarkTemplateAsDefault = async (row: TemplateSchema) => {
    if (row && row.id && !row.isDefault) {
      setIsSubmitting(true);
      const res = await billingSettingsCtx?.markDefaultInvoiceTemplate(row.id, 'order');
      if (res.success) {
        showSnackbar(
          'confirm',
          'Success',
          getSuccessMsg('mark', 'quote template', (row?.name || '') + ' as default'),
        );
        setIsSubmitting(false);
        setRefreshFlag(refreshFlag + 1);
      }
    }
  };

  const setUp = async () => {
    if (billingSettingsCtx) {
      billingSettingsCtx.getQuoteList().then((quoteList) => {
        setInvoiceList(quoteList);
      });
    }
  };

  const handlePublishInvoiceTemplate = async (config: TemplateSchema) => {
    if (config) {
      const publishInvoiceTemplateRes = await billingSettingsCtx?.publishInvoiceTemplate(
        config,
        'order',
      );
      if (publishInvoiceTemplateRes.success) {
        showSnackbar(
          'confirm',
          'Success',
          getSuccessMsg('published', 'invoice template', config.name || ''),
        );
        setRefreshFlag && refreshFlag && setRefreshFlag(refreshFlag + 1);
      }
    }
  };

  const handleUnPublishInvoiceTemplate = async (config: TemplateSchema) => {
    if (config) {
      const unPublishInvoiceTemplateRes = await billingSettingsCtx?.unPublishInvoiceTemplate(
        config,
        'order',
      );
      if (unPublishInvoiceTemplateRes.success) {
        showSnackbar(
          'confirm',
          'Success',
          getSuccessMsg('unpublished', 'invoice template', config.name || ''),
        );
        setRefreshFlag && refreshFlag && setRefreshFlag(refreshFlag + 1);
      }
    }
  };

  const attachQuotePDFOptions: PickListOption[] = [
    {
      name: 'Quote',
      value: 'attachQuotePDFToQuote',
    },
    {
      name: 'Opportunity',
      value: 'attachQuotePDFToOpportunity',
    },
  ];

  useEffect(() => {
    if (openNewTemplate && invoiceTemplateList.length > 0) {
      handleEditInvoiceTemplate(invoiceTemplateList.filter((i) => !i.isSystem)[0]);
      setOpenNewTemplate(false);
    }
  }, [invoiceTemplateList]);

  return (
    <RubySettingEditorPanel title={category.name}>
      <Grid
        container
        spacing={2}
        className={classes.root}
        style={{
          marginTop: '-20px',
        }}
      >
        <Grid item xs={12}>
          <Typography component="h3" className={classes.sectionTitle}>
            General Settings{' '}
          </Typography>
          <Grid item xs={12}>
            <SettingItemLabel
              label={'Attach the generated PDF file to:'}
              style={{ marginTop: '1em', lineHeight: '2.5' }}
            />
          </Grid>
          <Grid item xs={12} style={{ marginBottom: '1.5em' }}>
            {attachQuotePDFOptions.map((option: PickListOption) => (
              <RubyCheckbox
                key={option.name}
                labelStyles={{
                  color: '#000000',
                }}
                readOnly={false}
                field={{
                  apiName: option.value,
                  name: option.name,
                  type: 'boolean',
                }}
                //@ts-ignore
                value={orderFormSettings[option.value]}
                handleInputChange={(newValue: boolean) => {
                  handleSettingItemChange({
                    [option.value]: newValue === true ? 'true' : 'false',
                  });
                }}
              />
            ))}
          </Grid>
        </Grid>
        <Grid item xs={12}>
          <Typography component="h3" className={classes.sectionTitle}>
            {' '}
            Email Notification{' '}
          </Typography>
          <Typography
            variant="body1"
            component="p"
            style={{ marginTop: '1em', lineHeight: '2.5' }}
            className={classes.inlineInputSurroundingText}
          >
            Use email template{' '}
            <InlineInput
              name={'orderFormEmailTemplate'}
              value={settings.orderFormEmailTemplate}
              handleInputChange={(newValue: string) =>
                handleSettingItemChange({ orderFormEmailTemplate: newValue })
              }
              type={'text'}
              inputProps={{ type: 'text' }}
              style={{ width: '250px' }}
            />
            to send the notification email.
          </Typography>
        </Grid>

        <Grid item xs={12} className={classes.dividerWrapper}>
          <Divider />
        </Grid>

        <Grid item xs={12}>
          <Typography component="h3" className={classes.sectionTitle}>
            Order Form Templates{' '}
          </Typography>
        </Grid>

        <Grid item xs={12} style={{ marginTop: '0', padding: '0' }}>
          <TemplateListViewSection
            label={''}
            rootStyle={{
              margin: '0 0',
              paddingLeft: 0,
              paddingTop: 0,
              paddingBottom: 0,
            }}
            mode="order"
            getAllInvoiceTemplateList={getAllOrderFormsTemplateList}
            handleEditInvoiceTemplate={handleEditInvoiceTemplate}
            handlePreviewInvoiceTemplate={handlePreviewInvoiceTemplate}
            invoiceList={invoiceList}
            getInvoiceById={billingSettingsCtx?.getInvoiceById}
            checkInvoiceTokenByInvoiceId={billingSettingsCtx?.checkInvoiceTokenByInvoiceId}
            handleMarkTemplateAsDefault={handleMarkTemplateAsDefault}
            handlePublishInvoiceTemplate={handlePublishInvoiceTemplate}
            handleDeleteInvoiceTemplate={handleDeleteInvoiceTemplate}
            handleUnPublishInvoiceTemplate={handleUnPublishInvoiceTemplate}
            handleCopyInvoiceTemplate={handleCopyInvoiceTemplate}
            invoiceTemplateEdit={invoiceTemplateEdit}
            showInvoiceTemplatePreview={showInvoiceTemplatePreview}
            setShowInvoiceTemplatePreview={setShowInvoiceTemplatePreview}
            refreshFlag={refreshFlag}
            setRefreshFlag={setRefreshFlag}
            showInvoiceTemplateEdit={showInvoiceTemplateEdit}
            setShowInvoiceTemplateEdit={setShowInvoiceTemplateEdit}
            editInvoiceTemplate={user.roles
              .find((role: UserRole) => !!role.effective)
              ?.functions.includes('ManageOrderForms')}
            formTemplateMetadata={
              billingSettingsCtx?.metadataObjects.find((m) => m.apiName === 'FormTemplate')!
            }
          />
        </Grid>
      </Grid>
    </RubySettingEditorPanel>
  );
};

export default OrderFormsSettingsEditor;

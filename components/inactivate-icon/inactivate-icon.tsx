import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { Props } from './interface';

const defaultProps = {};

const InactivateIcon: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  return (
    <SvgIcon viewBox={props.viewBox}>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>BB3E6B57-E5D5-4C36-AB50-30071FC54E6A</title>
        <g id="08-Orders" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="8-1-1-1-Orders-(Grid-View)" transform="translate(-853.000000, -1192.000000)">
            <g id="Product-Options" transform="translate(380.000000, 290.000000)">
              <g id="Group-17" transform="translate(409.000000, 430.000000)">
                <g id="Group-14" transform="translate(0.000000, 26.000000)">
                  <g id="Group-22" transform="translate(30.000000, 433.000000)">
                    <g id="Group-18" transform="translate(17.000000, 13.000000)">
                      <g id="focus-2-fill" transform="translate(17.000000, 0.000000)">
                        <polygon id="Path" points="0 0 24 0 24 24 0 24"></polygon>
                        <path
                          d="M12,2 C17.52,2 22,6.48 22,12 C22,17.52 17.52,22 12,22 C6.48,22 2,17.52 2,12 C2,6.48 6.48,2 12,2 Z M11.9999937,19.9999937 C16.427,19.9999937 19.9999937,16.427 19.9999937,11.9999937 C19.9999937,7.573 16.427,3.99999373 11.9999937,3.99999373 C9.8774543,3.99734183 7.84107819,4.83934292 6.34021055,6.34021055 C4.83934292,7.84107819 3.99734183,9.8774543 3.99999373,11.9999937 C3.99999373,16.427 7.573,19.9999937 11.9999937,19.9999937 L11.9999937,19.9999937 Z"
                          id="Shape"
                          fill="#6239EB"
                          fillRule="nonzero"
                        ></path>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default InactivateIcon;

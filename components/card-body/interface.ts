import { Item } from '../list/interface';
import { CardAction, Uom } from '../metadata/interface';

export interface Props {
  items: Array<Item>;
  summaryItem?: Item;
  currencyIsoCode: string;
  actions: Array<CardAction>;
  moreActions?: Array<CardAction>;
  actionEventHandler: (action: CardAction) => void | Promise<void>;
  numCardActionsPerRow: number;
  uom?: Uom;
  showSubmitBtn?: boolean;
  showDetails?: boolean;
  onShowDetails?: () => void;
  locale?: string;
}

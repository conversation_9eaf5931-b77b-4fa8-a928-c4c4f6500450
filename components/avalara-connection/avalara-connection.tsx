import React, { use<PERSON>allback, useContext, useEffect, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';

import {
  Box,
  Card,
  CardContent,
  FormControl,
  Grid,
  IconButton,
  Popper,
  Typography,
  makeStyles,
} from '@material-ui/core';
import CloseIcon from '@material-ui/icons/Close';
import HelpIcon from '@material-ui/icons/Help';
import InfoIcon from '@material-ui/icons/Info';

import Autocomplete from '../autocomplete/autocomplete';
import RubyCheckbox from '../checkbox/checkbox';
import type { CustomFormControlProps } from '../form-section';
import { CustomFieldControlRegistry } from '../form-section';
import FormSection from '../form-section/form-section';
import { buildValidationSchema, useYupValidationResolver } from '../form-validation';
import { ActiveIcon, InactiveIcon } from '../icons';
import LabelWithTooltip from '../label-with-tooltip/label-with-tooltip';
import Loading from '../loading';
import type { PickListOption } from '../pick-list';
import { RubyButtonBar } from '../ruby-button';
import { useRubySnackbar } from '../ruby-notifier';
import { RubySettingEditorPanel } from '../ruby-settings/ruby-setting-editor-panel';
import SimpleAttribute from '../simple-attribute';
import useConfirmDialog from '../use-confirm-dialog';
import AvalaraConnectionContext from './avalara-connection-context';
import type {
  AvalaraConfig,
  Company,
  TaxIntegrationConfig,
  TaxIntegrationResponse,
  UpsertTaxIntegrationRequestBody,
} from './interface';

const useStyle = makeStyles({
  activated: {
    display: 'flex',
    paddingLeft: '4px',
  },
  activatedText: {
    paddingLeft: '4px',
    fontSize: '.75rem',
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  caption: {
    color: '#000000',
    fontSize: '20px',
    fontWeight: 500,
    letterSpacing: '-0.77px',
    lineHeight: '25px',
  },
  subtitle: {
    marginTop: '8px',
    color: '#000000',
    opacity: 0.4,
    fontSize: '12px',
    letterSpacing: 0,
    lineHeight: '15px',
  },
});

const objectMetadata = {
  apiName: 'Connection',
  name: 'Connection',
  objectType: 'Entity',
  customizable: true,
  fields: [
    {
      apiName: 'accountId',
      name: 'Account Number',
      type: 'text',
      required: true,
      updatable: true,
      creatable: true,
      showTooltip: false,
      toolTipText: '',
      xs: 6 as 4 | 6 | 8 | 12 | null | undefined,
    },
    {
      apiName: 'licenseKey',
      name: 'License Key',
      type: 'text',
      subType: 'password',
      required: true,
      updatable: true,
      creatable: true,
      showTooltip: false,
      toolTipText: '',
      xs: 6 as 4 | 6 | 8 | 12 | null | undefined,
    },
    {
      apiName: 'companyCode',
      name: 'Company Code',
      type: 'text',
      required: false,
      updatable: true,
      creatable: true,
      showTooltip: false,
      toolTipText: '',
      xs: 6 as 4 | 6 | 8 | 12 | null | undefined,
    },
    {
      apiName: 'enableLogging',
      name: 'Enable Logging',
      type: 'boolean',
      required: false,
      updatable: true,
      creatable: true,
      showTooltip: true,
      toolTipText:
        'Enable to include additional logging of transactions to the Avalara web services.',
      xs: 6 as 4 | 6 | 8 | 12 | null | undefined,
    },
    {
      apiName: 'commitDocuments',
      name: 'Commit Tax Details',
      type: 'boolean',
      required: false,
      updatable: true,
      creatable: true,
      showTooltip: true,
      toolTipText:
        'A transaction represents a unique potentially taxable action that your company has recorded, and transactions include actions like orders, invoices and refunds. If enabled, taxes generated for invoices and refunds are committed in Ava Tax and are available to be reported to a tax authority by Avalara Managed Returns. Taxes for order transactions are not committed.',
      xs: 6 as 4 | 6 | 8 | 12 | null | undefined,
    },
    {
      apiName: 'environment',
      name: 'Avalara Sandbox',
      type: 'boolean',
      required: false,
      updatable: true,
      creatable: true,
      showTooltip: true,
      toolTipText: 'This sets the Avalara environment to Sandbox',
      xs: 6 as 4 | 6 | 8 | 12 | null | undefined,
    },
  ],
};

const emptyIntegration: TaxIntegrationConfig = {
  id: '',
  status: '',
  integrationType: '',
  environment: false,
  accountId: '',
  licenseKey: '',
  companyCode: '',
  enableLogging: false,
  commitDocuments: false,
};

const placeholder = 'secretPlaceholder';

export const CompanyCodeFieldControl: React.FC<CustomFormControlProps> = (props) => {
  const classes = useStyle();

  const { field, formContext, hidden } = props;
  const [loading, setLoading] = useState<boolean>(true);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [options, setOptions] = useState<PickListOption[]>([]);
  const [suggestion, setSuggestion] = useState<Company | undefined>();
  const [anchorEl, setAnchorEl] = React.useState<Element | null>(null);
  const [popperContent, setPopperContent] = React.useState<string>();
  const avalaraConnectionService = useContext(AvalaraConnectionContext);

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMouseEvent = (event: React.MouseEvent<HTMLButtonElement>, _popperContent: string) => {
    setPopperContent(_popperContent);
    if (anchorEl) {
      handleClose();
    } else {
      setAnchorEl(event.currentTarget as Element);
    }
  };

  const setUp = async () => {
    setLoading(true);
    if (avalaraConnectionService && !hidden) {
      const _companies: Company[] = await avalaraConnectionService.getCompanies();
      setOptions(
        _companies
          .sort((companyA, companyB) => {
            if (companyA.isDefault) {
              return -1;
            } else if (companyB.isDefault) {
              return 1;
            } else {
              return companyA.name > companyB.name ? 1 : -1;
            }
          })
          .map((company: Company) => ({
            name: company.name,
            value: company.code,
          })) || [],
      );
      setCompanies(_companies);
    }
    setLoading(false);
  };

  const tested = formContext?.watch('tested');

  useEffect(() => {
    if (!hidden || tested) setUp();
  }, [hidden, tested]);

  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  return (
    <Controller
      name={field.apiName}
      render={({ value, onChange }) => {
        return (
          <FormControl style={{ width: '100%', display: 'inline-flex' }}>
            <LabelWithTooltip
              label={field.name}
              required={field.required}
              shrink
              showTooltip={field.showTooltip}
              tooltipText={field.toolTipText}
              htmlFor={field.apiName}
            />
            <Grid container>
              <Grid item sm={10}>
                <Autocomplete
                  style={{
                    paddingTop: 6,
                    paddingBottom: 6,
                    backgroundColor: 'transparent',
                    border: '1px solid #ced4da',
                  }}
                  value={value}
                  placeholder={'Search Company'}
                  suggestions={options}
                  getSuggestionItemLabel={(x: PickListOption) => x.name}
                  getSuggestionItemValue={(x: PickListOption) => x.value}
                  getSelectedItemLabel={(x: PickListOption) => x.name}
                  onChange={(event) => {
                    const newValue = event.value;
                    onChange(newValue);
                    formContext?.setValue('options', []);
                  }}
                  readOnly={!options.length}
                  setSuggestionValue={(suggestedValue) => {
                    setSuggestion(
                      companies?.find((company) => company.code === (suggestedValue || value)),
                    );
                  }}
                />
              </Grid>
              <Grid item sm={2}>
                <div style={{ display: 'flex', justifyContent: 'center' }}>
                  {!loading && options?.length > 0 && (
                    <IconButton onClick={(e) => handleMouseEvent(e, 'company')}>
                      <InfoIcon fontSize="large" color="secondary" />
                    </IconButton>
                  )}
                  {!loading && options?.length === 0 && (
                    <IconButton
                      onPointerEnter={(e) => handleMouseEvent(e, 'error')}
                      onPointerLeave={(e) => handleMouseEvent(e, 'error')}
                    >
                      <HelpIcon fontSize="large" htmlColor="#FD9090" />
                    </IconButton>
                  )}
                </div>
                <Popper id={id} open={open} anchorEl={anchorEl} placement={'right-start'}>
                  <Card elevation={3} variant="outlined" style={{ maxWidth: '500px' }}>
                    {popperContent === 'company' && (
                      <CardContent>
                        {suggestion && (
                          <>
                            <Grid container>
                              <Grid item xs={11}>
                                <div className={classes.caption}>{suggestion.name}</div>
                                <div className={classes.subtitle}>ID: {suggestion.id}</div>
                              </Grid>
                              <Grid item xs={1}>
                                <IconButton onClick={handleClose}>
                                  <CloseIcon fontSize="medium" color="inherit" />
                                </IconButton>
                              </Grid>
                            </Grid>

                            <Grid container spacing={2} style={{ marginTop: '10px' }}>
                              <Grid item xs={6}>
                                <SimpleAttribute
                                  label={'Account Id'}
                                  value={suggestion.accountId}
                                />
                              </Grid>
                              <Grid item xs={6}>
                                <SimpleAttribute label={'Company Code'} value={suggestion.code} />
                              </Grid>
                              <Grid item xs={6}>
                                <SimpleAttribute
                                  label={'Taxpayer Id Number'}
                                  value={suggestion.taxpayerIdNumber}
                                />
                              </Grid>
                              <Grid item xs={6}>
                                <SimpleAttribute
                                  label={'Default Country'}
                                  value={suggestion.defaultCountry}
                                />
                              </Grid>
                              <Grid item xs={6}>
                                <SimpleAttribute
                                  label={'Base Currency Code'}
                                  value={suggestion.baseCurrencyCode}
                                />
                              </Grid>
                              <Box width="100%" />
                              <Grid item xs={6}>
                                <RubyCheckbox
                                  label={'Is Default'}
                                  field={{
                                    apiName: 'isDefault',
                                    name: 'isDefault',
                                    type: 'boolean',
                                  }}
                                  readOnly={true}
                                  value={suggestion.isDefault}
                                />
                              </Grid>
                              <Grid item xs={6}>
                                <RubyCheckbox
                                  label={'Is Reporting Entity'}
                                  field={{
                                    apiName: 'isReportingEntity',
                                    name: 'isReportingEntity',
                                    type: 'boolean',
                                  }}
                                  readOnly={true}
                                  value={suggestion.isReportingEntity}
                                />
                              </Grid>
                            </Grid>
                          </>
                        )}
                        {!suggestion && <div className={classes.caption}>No company suggested</div>}
                      </CardContent>
                    )}
                    {popperContent === 'error' && (
                      <CardContent>
                        <div>Invalid connection or no company set-up.</div>
                        <div>Check the license key and account number.</div>
                      </CardContent>
                    )}
                  </Card>
                </Popper>
              </Grid>
            </Grid>
          </FormControl>
        );
      }}
    />
  );
};

const AvalaraConnection: React.FC = () => {
  const classes = useStyle();
  const avalaraConnectionContext = useContext(AvalaraConnectionContext);
  if (!avalaraConnectionContext) {
    throw Error(
      'Avalara Connection requires avalaraConnectionContext to be declared in context provider',
    );
  }

  const {
    activateIntegration,
    disableIntegration,
    getIntegration,
    createIntegration,
    updateIntegration,
    testIntegration,
    forceActivateIntegration,
    getActiveIntegration,
  } = avalaraConnectionContext;

  const [loading, setLoading] = React.useState(false);
  const [initiated, setInitiated] = React.useState(false);
  const [integration, setIntegration] = React.useState<TaxIntegrationConfig>();
  const [hiddenFields, setHiddenFields] = React.useState<string[]>(['companyCode']);
  const [activeIntegration, setActiveIntegration] = React.useState<TaxIntegrationResponse | null>(
    null,
  );

  const initialConnectionValues: Record<string, any> = {
    environment: false,
    accountId: '',
    licenseKey: '',
    companyCode: '',
    enableLogging: false,
    commitDocuments: false,
  };

  const validationSchema = buildValidationSchema(objectMetadata);
  const resolver = useYupValidationResolver(validationSchema);
  const { showSnackbar, Snackbar } = useRubySnackbar();

  const integrationForm = useForm({
    defaultValues: initialConnectionValues,
    //@ts-ignore
    resolver,
  });

  const {
    showConfirmDialog: showIntegrationConflictDialog,
    ConfirmDialog: IntegrationConflictDialog,
  } = useConfirmDialog({
    submitButtonText: 'Yes',
    cancelButtonText: 'No',
    onOk: async () => {
      if (forceActivateIntegration) {
        const result = await forceActivateIntegration('Avalara');
        if (result.success) {
          showSnackbar('info', 'Toggle Avalara Tax', 'Avalara Tax is successfully enabled');
          setIntegration((previousState) => ({ ...previousState!, status: 'Activated' }));
        }
      }
    },
    onCancel: () => {},
  });

  const setup = useCallback(async () => {
    setLoading(true);
    if (getIntegration && typeof getIntegration === 'function') {
      const taxIntegrationResponse: TaxIntegrationResponse = await getIntegration();
      let taxIntegration: TaxIntegrationConfig = emptyIntegration;
      if (taxIntegrationResponse) {
        const avalaraConfig = taxIntegrationResponse.config as AvalaraConfig;
        taxIntegration = {
          id: taxIntegrationResponse.id,
          integrationType: taxIntegrationResponse.integrationType,
          status: taxIntegrationResponse.status,
          accountId: avalaraConfig?.accountId,
          environment: avalaraConfig?.environment === 'Sandbox',
          licenseKey: placeholder,
          companyCode: avalaraConfig?.companyCode,
          enableLogging: avalaraConfig?.enableLogging,
          commitDocuments: avalaraConfig?.commitDocuments,
        };
      }

      setIntegration(taxIntegration);

      integrationForm.setValue('accountId', taxIntegration.accountId);
      integrationForm.setValue('licenseKey', taxIntegration.licenseKey);
      integrationForm.setValue('environment', taxIntegration.environment);
      integrationForm.setValue('companyCode', taxIntegration.companyCode);
      integrationForm.setValue('enableLogging', taxIntegration.enableLogging);
      integrationForm.setValue('commitDocuments', taxIntegration.commitDocuments);
    }
    if (getActiveIntegration) {
      setActiveIntegration(await getActiveIntegration());
    }

    setInitiated(true);
    setLoading(false);
  }, [getIntegration]);

  const upsertPrep = useCallback((): UpsertTaxIntegrationRequestBody | null => {
    if (!integrationForm.getValues('accountId')) {
      showSnackbar('error', 'Error', 'Please provide an account ID');
      return null;
    }
    if (!integrationForm.getValues('licenseKey')) {
      showSnackbar('error', 'Error', 'Please provide a license key');
      return null;
    }
    const licenseKey = integrationForm.getValues('licenseKey');
    const request: UpsertTaxIntegrationRequestBody = {
      integrationType: 'Avalara',
      accountId: integrationForm.getValues('accountId'),
      licenseKey: licenseKey !== placeholder ? licenseKey : null,
      environment: integrationForm.getValues('environment') ? 'Sandbox' : 'Production',
      companyCode: integrationForm.getValues('companyCode'),
      enableLogging: integrationForm.getValues('enableLogging'),
      commitDocuments: integrationForm.getValues('commitDocuments'),
    };
    if (!!integration?.id) {
      request.id = integration?.id;
    }
    return request;
  }, [integration, integrationForm, showSnackbar]);

  const getLeftButtons = useCallback(() => {
    if (integration?.status === 'Activated') {
      return [];
    }
    return !!integration?.id
      ? []
      : [
          {
            text: 'Save',
            disabled: loading,
            onClick: async () => {
              const integrationRequest: UpsertTaxIntegrationRequestBody | null = upsertPrep();
              if (!integrationRequest) return;
              try {
                setLoading(true);
                const createResult = await createIntegration(integrationRequest);
                setIntegration((previousState) => ({
                  ...previousState,
                  ...integrationRequest,
                  id: createResult.id,
                  status: 'Draft',
                  environment: integrationRequest.environment === 'Sandbox',
                  licenseKey: placeholder,
                }));
                setLoading(false);
                showSnackbar('confirm', 'Success', 'Your connection has been updated.');
              } catch (err) {
                console.debug('err: ', err);
              }
            },
          },
        ];
  }, [integration, createIntegration, loading, showSnackbar, updateIntegration, upsertPrep]);

  const setDefaultCompanyCode = useCallback(async () => {
    const updatedIntegration = await getIntegration();
    if (!updatedIntegration.config) {
      return;
    }

    const newConfig = updatedIntegration.config as AvalaraConfig;
    integrationForm.setValue('companyCode', newConfig.companyCode);
  }, [getIntegration, integrationForm]);

  const getRightButtons = useCallback(() => {
    return integration?.status === 'Activated'
      ? [
          {
            text: 'Deactivate',
            onClick: async () => {
              if (!integration) return;
              try {
                setLoading(true);
                await disableIntegration(integration.id);
                showSnackbar('info', 'Disabled Taxation', 'Taxation has been disabled');
                setIntegration((previousState) => ({
                  ...previousState!,
                  status: 'Deactivated',
                }));
              } catch (err) {
                console.debug('err: ', err);
              } finally {
                setLoading(false);
              }
            },
          },
        ]
      : [
          {
            disabled: !integration || !integration?.id || loading,
            text: 'Activate',
            onClick: async () => {
              if (!integration) return;
              const integrationRequest: UpsertTaxIntegrationRequestBody | null = upsertPrep();
              if (!integrationRequest) return;
              const hasCompanyCode = !!integrationRequest.companyCode;
              try {
                setLoading(true);
                await updateIntegration(integrationRequest);
                setIntegration((previousState) => ({
                  ...previousState!,
                  ...integrationRequest,
                  environment: integrationRequest.environment === 'Sandbox',
                  licenseKey: placeholder,
                }));
                const response: { success: boolean; error: string } = await activateIntegration(
                  integration.id,
                );
                if (response.success) {
                  showSnackbar(
                    'info',
                    'Enabled Taxation',
                    'Taxation is successfully enabled, and Avalara Tax will be used to calculate taxes.',
                  );
                  setIntegration((previousState) => ({ ...previousState!, status: 'Activated' }));
                  if (!hasCompanyCode) {
                    setDefaultCompanyCode();
                  }
                } else {
                  if (
                    response.error ===
                    'There is already one integration activated, please disable it first.'
                  ) {
                    showIntegrationConflictDialog({
                      title: 'Active Integration Warning',
                      message: `An active ${activeIntegration?.integrationType} provider already exists.  Do you want to 
          disable the ${activeIntegration?.integrationType} integration and enable this Avalra Tax provider instead?`,
                    });
                  }
                }
              } catch (err) {
                console.debug('err: ', err);
              } finally {
                setLoading(false);
              }
            },
          },
          {
            text: 'Test',
            disabled: !integration || !integration?.id || loading,
            onClick: async () => {
              if (!integration) return;
              const integrationRequest: UpsertTaxIntegrationRequestBody | null = upsertPrep();
              if (!integrationRequest) return;
              try {
                setLoading(true);
                await updateIntegration(integrationRequest);
                setIntegration((previousState) => ({
                  ...previousState!,
                  ...integrationRequest,
                  environment: integrationRequest.environment === 'Sandbox',
                  licenseKey: placeholder,
                }));
                const response: { success: boolean; error: string } = await testIntegration(
                  integration.id,
                );
                if (response.success) {
                  integrationForm.register('tested');
                  integrationForm.setValue('tested', true);
                  showSnackbar(
                    'confirm',
                    'Success',
                    'Your connection is setup successfully. You may now activate your connection.',
                  );
                } else {
                  showSnackbar('error', 'Error', response.error);
                }
              } catch (err) {
                console.debug('err: ', err);
              } finally {
                setLoading(false);
              }
            },
          },
        ];
  }, [
    integration,
    loading,
    activateIntegration,
    disableIntegration,
    showSnackbar,
    testIntegration,
    setDefaultCompanyCode,
  ]);

  CustomFieldControlRegistry.register('companyCode', CompanyCodeFieldControl);

  useEffect(() => {
    if (!loading && !initiated) {
      setup();
    }
  }, [loading, initiated, setup, integrationForm]);

  useEffect(() => {
    if (integration && initiated) {
      if (!integration.id && !hiddenFields.length) {
        setHiddenFields(['companyCode']);
      } else if (integration.id && !!hiddenFields.length) {
        setHiddenFields([]);
      }
    }
  }, [integration, initiated, hiddenFields]);

  useEffect(() => {
    integrationForm.register('accountId');
    integrationForm.register('licenseKey');
    integrationForm.register('environment');
    integrationForm.register('companyCode');
    integrationForm.register('enableLogging');
    integrationForm.register('commitDocuments');
  }, [integrationForm.register]);

  return (
    <>
      <Snackbar />
      <RubySettingEditorPanel title="Avalara Connection">
        <FormProvider {...integrationForm}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormSection
                showDivider={false}
                fields={objectMetadata.fields}
                hiddenFields={hiddenFields}
                setHiddenFields={() => {}}
                values={initialConnectionValues}
              />
            </Grid>
            <Grid item xs={12}>
              <div className={classes.activated}>
                {integration?.status === 'Activated' ? (
                  <ActiveIcon style={{ color: '#17981D' }} />
                ) : (
                  <InactiveIcon style={{ height: '16px', width: '16px', color: '#999999' }} />
                )}
                <Typography
                  style={
                    integration?.status === 'Activated'
                      ? { color: '#17981d' }
                      : { color: '#999999' }
                  }
                  className={classes.activatedText}
                  variant="body2"
                  component="p"
                >
                  {integration?.status === 'Activated' ? 'Active' : 'Inactive'}
                </Typography>
              </div>
            </Grid>
            <Grid item xs={12} style={{ position: 'relative' }}>
              {loading && (
                <div
                  style={{
                    position: 'absolute',
                    top: '-300px',
                    left: '50%',
                    marginTop: '-12px',
                    marginLeft: '-12px',
                  }}
                >
                  <Loading size="50px" />
                </div>
              )}
            </Grid>
            <Grid item xs={12}>
              <RubyButtonBar
                variant="inPlace"
                processing={loading}
                buttonSize="large"
                leftButtons={getLeftButtons()}
                rightButtons={getRightButtons()}
                customButtonBarStyles={{
                  paddingLeft: '12px',
                  paddingRight: 0,
                  width: 'calc(100% + 16px)',
                }}
              />
            </Grid>
          </Grid>
        </FormProvider>
      </RubySettingEditorPanel>
      <IntegrationConflictDialog />
    </>
  );
};

export default AvalaraConnection;

import {
  getFromProductOptions,
  getBulkActionItems,
  getToProductOptions,
  getToUomOptions,
  getProductRelationsByActionType,
} from '../index';
import { mockSubscription } from '../../../../../stories/modals/mock';
import * as subscriptionCardUtil from '../../../../subscription-card/subscription-card-util';
import {
  ActionItemOption,
  ProductInfo,
  ProductRelations,
  ProductRelationWithSubscription,
  SelectedActionItem,
} from '../../types';
import { Subscription } from '@nue-apps/ruby-ui-component/esm/revenue-builder-types/interface';
import dayjs from 'dayjs';
import { defaultDateFormat } from '@nue-apps/ruby-ui-component/esm/customer-view/constant';

let productRelationsByActionType: ProductRelationWithSubscription[] = [];

const baseSubscription: Subscription = {
  ...mockSubscription,
};

const productRelationOptions: ProductRelations = {};

export const baseProduct: ProductInfo = {
  sku: 'test-sku',
  recordType: 'Product',
  productCategory: 'RecurringServices',
  priceModel: 'Usage',
  name: 'Base Product 1',
  id: 'product-1',
  freeTrialUnit: null,
  freeTrialType: 'Days',
};

const productRelationWithSubscription: ProductRelationWithSubscription = {
  subscriptionId: 'subscription-1',
  subscriptionName: 'subscription-1',
  subscriptionUOM: 'User/Month',
  toProductUOMs: ['User/Month', 'User/Quarter'],
  toProduct: baseProduct,
  fromProductUOMs: ['User/Month', 'User/Quarter'],
  fromProduct: baseProduct,
  startDate: '2025-04-23',
  sameUomOnly: false,
  samePriceSwap: true,
  relationshipType: 'Upgrade',
};

export const mockProducts = [
  {
    ...baseProduct,
    id: 'base-product-1',
    name: 'Base Product 1',
  },
  {
    ...baseProduct,
    id: 'base-product-2',
    name: 'Base Product 2',
  },
  {
    ...baseProduct,
    id: 'to-product-1',
    name: 'To Product 1',
  },
  {
    ...baseProduct,
    id: 'to-product-2',
    name: 'To Product 2',
  },
  {
    ...baseProduct,
    id: 'to-product-3',
    name: 'To Product 3',
  },
];

const testProductRelationsByActionType = [
  {
    ...productRelationWithSubscription,
    subscriptionId: 'subscription-1',
    subscriptionName: 'subscription-1',
    subscriptionUOM: 'User/Month',
    fromProduct: mockProducts[0], // base product 1
    toProduct: mockProducts[2], // to product 1
  },
  {
    ...productRelationWithSubscription,
    subscriptionId: 'subscription-1',
    subscriptionName: 'subscription-1',
    subscriptionUOM: 'User/Month',
    fromProduct: mockProducts[0], // base product 1
    toProduct: mockProducts[3], // to product 2
  },
  {
    ...productRelationWithSubscription,
    subscriptionId: 'subscription-2',
    subscriptionName: 'subscription-2',
    subscriptionUOM: 'User/Quarter',
    fromProduct: mockProducts[0], // base product 1
    toProduct: mockProducts[3], // to product 2
  },
  {
    ...productRelationWithSubscription,
    subscriptionId: 'subscription-2',
    subscriptionName: 'subscription-2',
    subscriptionUOM: 'User/Month',
    fromProduct: mockProducts[1], // base product 2
    toProduct: mockProducts[3], // to product 2
  },
];

describe('getFromProductOptions', () => {
  describe('empty check', () => {
    it('should return empty array if productRelationsByActionType is empty', () => {
      const result = getFromProductOptions([], []);
      expect(result).toEqual([]);
    });
  });

  describe('test get fromProductOptions for upgrade action type', () => {
    beforeEach(() => {
      productRelationsByActionType = testProductRelationsByActionType;
    });

    it('should return unique fromProductOptions for upgrade action type', () => {
      const result = getFromProductOptions(productRelationsByActionType, []);
      expect(result).toEqual([
        {
          key: 'subscription-1_base-product-1',
          value: {
            key: 'subscription-1_base-product-1',
            subscriptionId: 'subscription-1',
            subscriptionName: 'subscription-1',
            subscriptionUOM: 'User/Month',
            fromProductId: 'base-product-1',
            fromProductName: 'Base Product 1',
          },
        },
        {
          key: 'subscription-2_base-product-1',
          value: {
            key: 'subscription-2_base-product-1',
            subscriptionId: 'subscription-2',
            subscriptionName: 'subscription-2',
            subscriptionUOM: 'User/Quarter',
            fromProductId: 'base-product-1',
            fromProductName: 'Base Product 1',
          },
        },
        {
          key: 'subscription-2_base-product-2',
          value: {
            key: 'subscription-2_base-product-2',
            subscriptionId: 'subscription-2',
            subscriptionName: 'subscription-2',
            subscriptionUOM: 'User/Month',
            fromProductId: 'base-product-2',
            fromProductName: 'Base Product 2',
          },
        },
      ]);
    });

    it('should filter out upgrade relations if subscription uom is not in the fromProductUOMs', () => {
      productRelationsByActionType[2] = {
        ...productRelationsByActionType[2],
        subscriptionUOM: 'User/Year',
      };

      const result = getFromProductOptions(productRelationsByActionType, []);
      expect(result).toEqual([
        {
          key: 'subscription-1_base-product-1',
          value: {
            key: 'subscription-1_base-product-1',
            subscriptionId: 'subscription-1',
            subscriptionName: 'subscription-1',
            subscriptionUOM: 'User/Month',
            fromProductId: 'base-product-1',
            fromProductName: 'Base Product 1',
          },
        },
        {
          key: 'subscription-2_base-product-2',
          value: {
            key: 'subscription-2_base-product-2',
            subscriptionId: 'subscription-2',
            subscriptionName: 'subscription-2',
            subscriptionUOM: 'User/Month',
            fromProductId: 'base-product-2',
            fromProductName: 'Base Product 2',
          },
        },
      ]);
    });

    it('should return upgrade relations if subscription uom is in the fromProductUOMs', () => {
      productRelationsByActionType = [
        {
          ...productRelationsByActionType[2],
          subscriptionUOM: 'User/Quarter',
        },
      ];

      const result = getFromProductOptions(productRelationsByActionType, []);
      expect(result).toEqual([
        {
          key: 'subscription-2_base-product-1',
          value: {
            key: 'subscription-2_base-product-1',
            subscriptionId: 'subscription-2',
            subscriptionName: 'subscription-2',
            subscriptionUOM: 'User/Quarter',
            fromProductId: 'base-product-1',
            fromProductName: 'Base Product 1',
          },
        },
      ]);
    });

    it('should filter out upgrade relations if sameUomOnly is true and subscription uom is not in toProductUOMs', () => {
      productRelationsByActionType = [
        {
          ...productRelationsByActionType[0],
          subscriptionUOM: 'User/Year',
          sameUomOnly: true,
        },
      ];

      const result = getFromProductOptions(productRelationsByActionType, []);
      expect(result).toEqual([]);
    });

    it('should return upgrade relations if sameUomOnly is true and subscription uom is in toProductUOMs', () => {
      productRelationsByActionType = [
        {
          ...productRelationsByActionType[0],
          subscriptionUOM: 'User/Quarter',
          sameUomOnly: true,
        },
      ];

      const result = getFromProductOptions(productRelationsByActionType, []);
      expect(result).toEqual([
        {
          key: 'subscription-1_base-product-1',
          value: {
            key: 'subscription-1_base-product-1',
            subscriptionId: 'subscription-1',
            subscriptionName: 'subscription-1',
            subscriptionUOM: 'User/Quarter',
            fromProductId: 'base-product-1',
            fromProductName: 'Base Product 1',
          },
        },
      ]);
    });
  });

  describe('test get fromProductOptions for downgrade action type', () => {
    beforeEach(() => {
      productRelationsByActionType = [
        {
          ...productRelationWithSubscription,
          relationshipType: 'Downgrade',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProduct: mockProducts[0], // base product 1
          toProduct: mockProducts[2], // to product 1
        },
        {
          ...productRelationWithSubscription,
          relationshipType: 'Downgrade',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProduct: mockProducts[0], // base product 1
          toProduct: mockProducts[3], // to product 2
        },
        {
          ...productRelationWithSubscription,
          relationshipType: 'Downgrade',
          subscriptionId: 'subscription-2',
          subscriptionName: 'subscription-2',
          subscriptionUOM: 'User/Month',
          fromProduct: mockProducts[1], // base product 2
          toProduct: mockProducts[3], // to product 2
        },
      ];
    });

    it('should return unique fromProductOptions for downgrade action type', () => {
      const result = getFromProductOptions(productRelationsByActionType, []);
      expect(result).toEqual([
        {
          key: 'subscription-1_base-product-1',
          value: {
            key: 'subscription-1_base-product-1',
            subscriptionId: 'subscription-1',
            subscriptionName: 'subscription-1',
            subscriptionUOM: 'User/Month',
            fromProductId: 'base-product-1',
            fromProductName: 'Base Product 1',
          },
        },
        {
          key: 'subscription-2_base-product-2',
          value: {
            key: 'subscription-2_base-product-2',
            subscriptionId: 'subscription-2',
            subscriptionName: 'subscription-2',
            subscriptionUOM: 'User/Month',
            fromProductId: 'base-product-2',
            fromProductName: 'Base Product 2',
          },
        },
      ]);
    });
  });
});

describe('getToProductOptions', () => {
  describe('empty check', () => {
    it('should return empty array if productRelationsByActionType is empty', () => {
      const result = getToProductOptions([], {
        key: 'subscription-1_base-product-1',
        subscriptionId: 'subscription-1',
        subscriptionName: 'subscription-1',
        subscriptionUOM: 'User/Month',
        fromProductId: 'base-product-1',
        fromProductName: 'Base Product 1',
      });
      expect(result).toEqual([]);
    });

    it('should return empty array if selectedFromProduct is not selected', () => {
      const result = getToProductOptions([], {
        key: '',
        subscriptionId: '',
        subscriptionName: '',
        subscriptionUOM: '',
        fromProductId: '',
        fromProductName: '',
      });

      expect(result).toEqual([]);
    });
  });

  describe('test get toProductOptions for upgrade action type', () => {
    beforeEach(() => {
      productRelationsByActionType = testProductRelationsByActionType;
    });

    it('it should return all toProductOptions if subscription id and from product id in relation matches with selectedFromProduct', () => {
      // sub1 -> p1 -> p1
      // sub1 -> p1 -> p2
      // sub2 -> p1 -> p2
      // sub2 -> p2 -> p2

      const result1 = getToProductOptions(productRelationsByActionType, {
        key: 'subscription-1_base-product-1',
        subscriptionId: 'subscription-1',
        subscriptionName: 'subscription-1',
        subscriptionUOM: 'User/Month',
        fromProductId: 'base-product-1',
        fromProductName: 'Base Product 1',
      });

      const result2 = getToProductOptions(productRelationsByActionType, {
        key: 'subscription-2_base-product-1',
        subscriptionId: 'subscription-2',
        subscriptionName: 'subscription-2',
        subscriptionUOM: 'User/Month',
        fromProductId: 'base-product-1',
        fromProductName: 'Base Product 1',
      });

      const result3 = getToProductOptions(productRelationsByActionType, {
        key: 'subscription-2_base-product-2',
        subscriptionId: 'subscription-2',
        subscriptionName: 'subscription-2',
        subscriptionUOM: 'User/Month',
        fromProductId: 'base-product-2',
        fromProductName: 'Base Product 2',
      });

      expect(result1).toEqual([
        {
          value: 'to-product-1',
          name: 'To Product 1',
        },
        {
          value: 'to-product-2',
          name: 'To Product 2',
        },
      ]);

      expect(result2).toEqual([
        {
          value: 'to-product-2',
          name: 'To Product 2',
        },
      ]);

      expect(result3).toEqual([
        {
          value: 'to-product-2',
          name: 'To Product 2',
        },
      ]);
    });

    it('it should filter out toProductOptions if subscription id and from product id in relation does not match with selectedFromProduct', () => {
      const result = getToProductOptions(productRelationsByActionType, {
        key: 'subscription-2_base-product-2',
        subscriptionId: 'subscription-2',
        subscriptionName: 'subscription-2',
        subscriptionUOM: 'User/Month',
        fromProductId: 'base-product-2',
        fromProductName: 'Base Product 2',
      });
      expect(result).toEqual([
        {
          value: 'to-product-2',
          name: 'To Product 2',
        },
      ]);
    });
  });
});

describe('getToUomOptions', () => {
  describe('empty check', () => {
    it('should return empty array if productRelationsByActionType is empty', () => {
      const result = getToUomOptions(
        [],
        {
          key: 'subscription-1_base-product-1',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        'to-product-1',
      );
      expect(result).toEqual([]);
    });

    it('should return empty array if selectedFromProduct key is not provided', () => {
      const result = getToUomOptions(
        testProductRelationsByActionType,
        {
          key: '',
          subscriptionId: '',
          subscriptionName: '',
          subscriptionUOM: '',
          fromProductId: '',
          fromProductName: '',
        },
        'to-product-1',
      );
      expect(result).toEqual([]);
    });

    it('should return empty array if selectedToProductId is not provided', () => {
      const result = getToUomOptions(
        testProductRelationsByActionType,
        {
          key: 'subscription-1_base-product-1',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        '',
      );
      expect(result).toEqual([]);
    });
  });

  describe('test get toUomOptions for normal scenarios', () => {
    beforeEach(() => {
      productRelationsByActionType = testProductRelationsByActionType;
    });

    it('should return subscription UOM when sameUomOnly is true', () => {
      productRelationsByActionType = [
        {
          ...productRelationWithSubscription,
          subscriptionId: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProduct: mockProducts[0],
          toProduct: mockProducts[2],
          sameUomOnly: true,
        },
      ];

      const result = getToUomOptions(
        productRelationsByActionType,
        {
          key: 'subscription-1_base-product-1',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        'to-product-1',
      );

      expect(result).toEqual([
        {
          value: 'User/Month',
          name: 'User/Month',
        },
      ]);
    });

    it('should return all toProductUOMs when sameUomOnly is false', () => {
      const result = getToUomOptions(
        productRelationsByActionType,
        {
          key: 'subscription-1_base-product-1',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        'to-product-1',
      );

      expect(result).toEqual([
        {
          value: 'User/Month',
          name: 'User/Month',
        },
        {
          value: 'User/Quarter',
          name: 'User/Quarter',
        },
      ]);
    });

    it('should return empty array when no matching relation is found', () => {
      const result = getToUomOptions(
        productRelationsByActionType,
        {
          key: 'subscription-1_base-product-1',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        'non-existent-product',
      );

      expect(result).toEqual([]);
    });
  });

  describe('test get toUomOptions for different subscription combinations', () => {
    it('should return correct UOMs for subscription-1 with base-product-1 to to-product-1', () => {
      const result = getToUomOptions(
        testProductRelationsByActionType,
        {
          key: 'subscription-1_base-product-1',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        'to-product-1',
      );

      expect(result).toEqual([
        {
          value: 'User/Month',
          name: 'User/Month',
        },
        {
          value: 'User/Quarter',
          name: 'User/Quarter',
        },
      ]);
    });

    it('should return correct UOMs for subscription-2 with base-product-1 to to-product-2', () => {
      const result = getToUomOptions(
        testProductRelationsByActionType,
        {
          key: 'subscription-2_base-product-1',
          subscriptionId: 'subscription-2',
          subscriptionName: 'subscription-2',
          subscriptionUOM: 'User/Quarter',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        'to-product-2',
      );

      expect(result).toEqual([
        {
          value: 'User/Month',
          name: 'User/Month',
        },
        {
          value: 'User/Quarter',
          name: 'User/Quarter',
        },
      ]);
    });
  });
});

describe('getProductRelationsByActionType', () => {
  describe('empty check', () => {
    it('should return empty object with action types if productRelations is null', () => {
      const result = getProductRelationsByActionType(null, [baseSubscription]);
      expect(result).toEqual({
        upgrade: [],
        downgrade: [],
        swap: [],
      });
    });

    it('should return empty object with action types if subscriptions is empty', () => {
      const result = getProductRelationsByActionType(productRelationOptions, []);
      expect(result).toEqual({
        upgrade: [],
        downgrade: [],
        swap: [],
      });
    });
  });

  describe('test get product relations for eligible subscriptions', () => {
    beforeEach(() => {
      // Mock isUpDownSwapEligible to return true for all subscriptions
      jest.spyOn(subscriptionCardUtil, 'isUpDownSwapEligible').mockReturnValue(true);
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should return empty arrays if no matching product relations found', () => {
      const result = getProductRelationsByActionType(productRelationOptions, [baseSubscription]);
      expect(result).toEqual({
        upgrade: [],
        downgrade: [],
        swap: [],
      });
    });

    it('should return product relations with subscription info for matching subscription', () => {
      const mockProductRelations = {
        [baseSubscription.id]: {
          upgrade: [
            {
              fromProduct: mockProducts[0],
              toProduct: mockProducts[2],
              fromProductUOMs: ['User/Month', 'User/Quarter'],
              toProductUOMs: ['User/Month', 'User/Quarter'],
              relationshipType: 'Upgrade',
              sameUomOnly: false,
              samePriceSwap: true,
              startDate: '2025-01-01',
            },
          ],
          downgrade: [
            {
              fromProduct: mockProducts[0],
              toProduct: mockProducts[2],
              fromProductUOMs: ['User/Month', 'User/Quarter'],
              toProductUOMs: ['User/Month', 'User/Quarter'],
              relationshipType: 'Downgrade',
              sameUomOnly: false,
              samePriceSwap: true,
              startDate: '2025-01-01',
            },
          ],
          swap: [
            {
              fromProduct: mockProducts[0],
              toProduct: mockProducts[2],
              fromProductUOMs: ['User/Month', 'User/Quarter'],
              toProductUOMs: ['User/Month', 'User/Quarter'],
              relationshipType: 'Swap',
              sameUomOnly: false,
              samePriceSwap: true,
              startDate: '2025-01-01',
            },
          ],
        },
      };

      const result = getProductRelationsByActionType(mockProductRelations, [baseSubscription]);

      expect(result).toEqual({
        upgrade: [
          {
            fromProduct: mockProducts[0],
            toProduct: mockProducts[2],
            fromProductUOMs: ['User/Month', 'User/Quarter'],
            toProductUOMs: ['User/Month', 'User/Quarter'],
            relationshipType: 'Upgrade',
            sameUomOnly: false,
            samePriceSwap: true,
            subscriptionId: baseSubscription.id,
            subscriptionName: baseSubscription.name,
            subscriptionUOM: baseSubscription.uom.name,
            startDate: '2025-01-01',
          },
        ],
        downgrade: [
          {
            fromProduct: mockProducts[0],
            toProduct: mockProducts[2],
            fromProductUOMs: ['User/Month', 'User/Quarter'],
            toProductUOMs: ['User/Month', 'User/Quarter'],
            relationshipType: 'Downgrade',
            sameUomOnly: false,
            samePriceSwap: true,
            subscriptionId: baseSubscription.id,
            subscriptionName: baseSubscription.name,
            subscriptionUOM: baseSubscription.uom.name,
            startDate: '2025-01-01',
          },
        ],
        swap: [
          {
            fromProduct: mockProducts[0],
            toProduct: mockProducts[2],
            fromProductUOMs: ['User/Month', 'User/Quarter'],
            toProductUOMs: ['User/Month', 'User/Quarter'],
            relationshipType: 'Swap',
            sameUomOnly: false,
            samePriceSwap: true,
            subscriptionId: baseSubscription.id,
            subscriptionName: baseSubscription.name,
            subscriptionUOM: baseSubscription.uom.name,
            startDate: '2025-01-01',
          },
        ],
      });
    });

    it('should handle multiple subscriptions with different product relations', () => {
      const secondSubscription = {
        ...baseSubscription,
        id: 'subscription-2',
        name: 'subscription-2',
        uom: {
          ...baseSubscription.uom,
          name: 'User/Quarter',
        },
      };

      const mockProductRelations = {
        [baseSubscription.id]: {
          upgrade: [
            {
              fromProduct: mockProducts[0],
              toProduct: mockProducts[2],
              fromProductUOMs: ['User/Month'],
              toProductUOMs: ['User/Month'],
              relationshipType: 'Upgrade',
              sameUomOnly: true,
              samePriceSwap: true,
              startDate: '2025-01-01',
            },
          ],
        },
        [secondSubscription.id]: {
          upgrade: [
            {
              fromProduct: mockProducts[1],
              toProduct: mockProducts[3],
              fromProductUOMs: ['User/Quarter'],
              toProductUOMs: ['User/Quarter'],
              relationshipType: 'Upgrade',
              sameUomOnly: true,
              samePriceSwap: true,
              startDate: '2025-01-01',
            },
          ],
        },
      };

      const result = getProductRelationsByActionType(mockProductRelations, [
        baseSubscription,
        secondSubscription,
      ]);

      expect(result).toEqual({
        upgrade: [
          {
            fromProduct: mockProducts[0],
            toProduct: mockProducts[2],
            fromProductUOMs: ['User/Month'],
            toProductUOMs: ['User/Month'],
            relationshipType: 'Upgrade',
            sameUomOnly: true,
            startDate: '2025-01-01',
            samePriceSwap: true,
            subscriptionId: baseSubscription.id,
            subscriptionName: baseSubscription.name,
            subscriptionUOM: baseSubscription.uom.name,
          },
          {
            fromProduct: mockProducts[1],
            toProduct: mockProducts[3],
            fromProductUOMs: ['User/Quarter'],
            toProductUOMs: ['User/Quarter'],
            relationshipType: 'Upgrade',
            sameUomOnly: true,
            startDate: '2025-01-01',
            samePriceSwap: true,
            subscriptionId: secondSubscription.id,
            subscriptionName: secondSubscription.name,
            subscriptionUOM: secondSubscription.uom.name,
          },
        ],
        downgrade: [],
        swap: [],
      });
    });

    it('should filter out ineligible subscriptions', () => {
      // Mock isUpDownSwapEligible to return false for the subscription
      jest.spyOn(subscriptionCardUtil, 'isUpDownSwapEligible').mockReturnValue(false);

      const mockProductRelations = {
        [baseSubscription.id]: {
          upgrade: [
            {
              fromProduct: mockProducts[0],
              toProduct: mockProducts[2],
              fromProductUOMs: ['User/Month'],
              toProductUOMs: ['User/Month'],
              relationshipType: 'Upgrade',
              sameUomOnly: true,
              samePriceSwap: true,
              startDate: '2025-01-01',
            },
          ],
        },
      };

      const result = getProductRelationsByActionType(mockProductRelations, [baseSubscription]);

      expect(result).toEqual({
        upgrade: [],
        downgrade: [],
        swap: [],
      });
    });
  });
});

describe('getBulkActionItems', () => {
  describe('empty check', () => {
    it('should return null if relation does not match', () => {
      const subscription = {
        ...baseSubscription,
        productName: 'Different Product',
      };

      const selectedRule: SelectedActionItem = {
        toProduct: {
          id: 'to-product-1',
          name: 'To Product 1',
        },
        fromProduct: {
          key: 'subscription-1_base-product-1',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        withUom: 'User/Month',
        startDate: dayjs('2025-04-23').format(defaultDateFormat),
        samePriceSwap: true,
      };

      const targetProductRelation: ActionItemOption = {
        toProduct: {
          id: 'to-product-1',
          name: 'To Product 1',
          sku: 'test-sku',
          recordType: 'Product',
          productCategory: 'RecurringServices',
          priceModel: 'Usage',
          freeTrialUnit: null,
          freeTrialType: 'Days',
        },
        fromProduct: {
          id: 'base-product-1',
          name: 'Base Product 1',
          sku: 'test-sku',
          recordType: 'Product',
          productCategory: 'RecurringServices',
          priceModel: 'Usage',
          freeTrialUnit: null,
          freeTrialType: 'Days',
        },
        toProductUOMs: ['User/Month', 'User/Quarter'],
        fromProductUOMs: ['User/Month', 'User/Quarter'],
        startDate: '2025-04-23',
        sameUomOnly: false,
        samePriceSwap: true,
        relationshipType: 'Upgrade',
      };

      const result = getBulkActionItems(
        subscription,
        selectedRule,
        targetProductRelation,
        'upgrade',
      );

      expect(result).toBeNull();
    });
  });

  describe('test get bulk action items for normal scenarios', () => {
    it('should return correct item for upgrade action type', () => {
      const subscription = {
        ...baseSubscription,
        id: 'subscription-1',
        name: 'subscription-1',
        productName: 'Base Product 1',
      };

      const selectedRule: SelectedActionItem = {
        toProduct: {
          id: 'to-product-1',
          name: 'To Product 1',
        },
        fromProduct: {
          key: 'subscription-1_base-product-1',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        withUom: 'User/Month',
        startDate: dayjs('2025-04-23').format(defaultDateFormat),
      };

      const targetProductRelation: ActionItemOption = {
        toProduct: {
          id: 'to-product-1',
          name: 'To Product 1',
          sku: 'test-sku',
          recordType: 'Product',
          productCategory: 'RecurringServices',
          priceModel: 'Usage',
          freeTrialUnit: null,
          freeTrialType: 'Days',
        },
        fromProduct: {
          id: 'base-product-1',
          name: 'Base Product 1',
          sku: 'test-sku',
          recordType: 'Product',
          productCategory: 'RecurringServices',
          priceModel: 'Usage',
          freeTrialUnit: null,
          freeTrialType: 'Days',
        },
        toProductUOMs: ['User/Month', 'User/Quarter'],
        fromProductUOMs: ['User/Month', 'User/Quarter'],
        startDate: '2025-04-23',
        sameUomOnly: false,
        samePriceSwap: true,
        relationshipType: 'Upgrade',
      };

      const result = getBulkActionItems(
        subscription,
        selectedRule,
        targetProductRelation,
        'upgrade',
      );

      expect(result).toEqual({
        type: 'upgrade',
        subscriptionId: subscription.id,
        subscriptionName: subscription.name,
        fromProduct: 'Base Product 1',
        toProduct: { id: 'to-product-1', name: 'To Product 1' },
        fromUom: 'User/Month',
        withUom: 'User/Month',
        startDate: '2025-04-23',
      });
    });

    it('should return correct item for swap action type with samePriceSwap', () => {
      const subscription = {
        ...baseSubscription,
        id: 'subscription-1',
        name: 'subscription-1',
        productName: 'Base Product 1',
      };

      const selectedRule: SelectedActionItem = {
        toProduct: {
          id: 'to-product-1',
          name: 'To Product 1',
        },
        fromProduct: {
          key: 'subscription-1_base-product-1',
          subscriptionId: 'subscription-1',
          subscriptionName: 'subscription-1',
          subscriptionUOM: 'User/Month',
          fromProductId: 'base-product-1',
          fromProductName: 'Base Product 1',
        },
        withUom: 'User/Month',
        startDate: dayjs('2025-04-23').format(defaultDateFormat),
        samePriceSwap: true,
      };

      const targetProductRelation: ActionItemOption = {
        toProduct: {
          id: 'to-product-1',
          name: 'To Product 1',
          sku: 'test-sku',
          recordType: 'Product',
          productCategory: 'RecurringServices',
          priceModel: 'Usage',
          freeTrialUnit: null,
          freeTrialType: 'Days',
        },
        fromProduct: {
          id: 'base-product-1',
          name: 'Base Product 1',
          sku: 'test-sku',
          recordType: 'Product',
          productCategory: 'RecurringServices',
          priceModel: 'Usage',
          freeTrialUnit: null,
          freeTrialType: 'Days',
        },
        toProductUOMs: ['User/Month', 'User/Quarter'],
        fromProductUOMs: ['User/Month', 'User/Quarter'],
        startDate: '2025-04-23',
        sameUomOnly: false,
        samePriceSwap: true,
        relationshipType: 'Swap',
      };

      const result = getBulkActionItems(subscription, selectedRule, targetProductRelation, 'swap');

      expect(result).toEqual({
        type: 'swap',
        subscriptionId: subscription.id,
        subscriptionName: subscription.name,
        fromProduct: 'Base Product 1',
        toProduct: { id: 'to-product-1', name: 'To Product 1' },
        fromUom: 'User/Month',
        withUom: 'User/Month',
        startDate: '2025-04-23',
        samePriceSwap: true,
      });
    });
  });
});

import dayjs from 'dayjs';
import React from 'react';
import { RubySettings } from '../../ruby-settings/interface';
import { formatNumberForDisplay } from '../../list/list';
import { calculateBarItemWidth } from '../utils/utils';
import { StatsBarChart } from '../../stats-bar-chart/stats-bar-chart';

interface InvoiceBarChartProps {
  objects: any | null;
  rubySettings: RubySettings;
  currencyIsoCode: string;
  locale: string | undefined;
}

const InvoiceBarChart = (props: InvoiceBarChartProps) => {
  const { objects, rubySettings, currencyIsoCode, locale } = props;

  if (!objects || !objects.length) return <></>;

  const today = dayjs();

  const draft = objects
    .filter((x: any) => x.status === 'Draft')
    .reduce((x: number, y: any) => x + Number(y.balance), 0);

  const notYetDue = objects
    .filter(
      (x: any) =>
        x.status === 'Active' &&
        (dayjs(x.dueDate).isAfter(today) || dayjs(x.dueDate).isSame(today)),
    )
    .reduce((x: number, y: any) => x + Number(y.balance), 0);

  const overDue = objects
    .filter((x: any) => x.status === 'Active' && dayjs(x.dueDate).isBefore(today))
    .reduce((x: number, y: any) => x + Number(y.balance), 0);

  const paid = objects
    .filter((x: any) => x.status === 'Active' || x.status === 'Paid')
    .reduce((x: number, y: any) => x + (Number(y.amount) - Number(y.balance)), 0);

  const pendingActivation = objects
    .filter((x: any) => x.status === 'PendingActivation')
    .reduce((x: number, y: any) => x + Number(y.balance), 0);

  const eInvoices = objects.filter(
    (x: any) => x.status === 'E-Invoicing' || x.status === 'EInvoicing',
  );

  const eInvoice = eInvoices.reduce((x: number, y: any) => x + Number(y.balance), 0);

  let totalAmountWithDraft = draft + notYetDue + overDue + paid;

  let defaultWidth = null;
  if (totalAmountWithDraft === 0) {
    defaultWidth = '25%';
  }

  if (rubySettings.enableInvoicePendingActivation) {
    totalAmountWithDraft = totalAmountWithDraft + pendingActivation;
  }

  if (eInvoices.length > 0) {
    totalAmountWithDraft = totalAmountWithDraft + eInvoice;
  }

  const items = [
    {
      text: { value: formatNumberForDisplay(draft, currencyIsoCode, true, false, locale) },
      desc: { value: 'DRAFT' },
      width: defaultWidth || calculateBarItemWidth(draft, totalAmountWithDraft),
      backgroundColor: '#D6D6D6',
    },
    rubySettings.enableInvoicePendingActivation
      ? {
          text: {
            value: formatNumberForDisplay(pendingActivation, currencyIsoCode, true, false, locale),
          },
          desc: { value: 'PENDING ACTIVATION' },
          width: defaultWidth || calculateBarItemWidth(pendingActivation, totalAmountWithDraft),
          backgroundColor: '#6139eb',
        }
      : null,
    eInvoices.length > 0
      ? {
          text: { value: formatNumberForDisplay(eInvoice, currencyIsoCode, true, false, locale) },
          desc: { value: 'E-INVOICING' },
          width: defaultWidth || calculateBarItemWidth(eInvoice, totalAmountWithDraft),
          backgroundColor: '#095393',
        }
      : null,
    {
      text: { value: formatNumberForDisplay(notYetDue, currencyIsoCode, true, false, locale) },
      desc: { value: 'NOT DUE YET' },
      width: defaultWidth || calculateBarItemWidth(notYetDue, totalAmountWithDraft),
      backgroundColor: '#0CB2D2',
    },
    {
      text: { value: formatNumberForDisplay(overDue, currencyIsoCode, true, false, locale) },
      desc: { value: 'OVERDUE' },
      width: defaultWidth || calculateBarItemWidth(overDue, totalAmountWithDraft),
      backgroundColor: '#FA5454',
    },
    {
      text: { value: formatNumberForDisplay(paid, currencyIsoCode, true, false, locale) },
      desc: { value: 'PAID' },
      width: defaultWidth || calculateBarItemWidth(paid, totalAmountWithDraft),
      backgroundColor: '#B09CF5',
    },
  ].filter((x) => x);

  return <StatsBarChart items={items} />;
};

export default InvoiceBarChart;

import React, { useEffect, useRef } from 'react';

import { Box, ClickAwayListener, Paper, Slide } from '@material-ui/core';
import { Tooltip } from '@material-ui/core';
import IconButton from '@material-ui/core/IconButton';
import { makeStyles } from '@material-ui/core/styles';
import CancelOutlinedIcon from '@material-ui/icons/CancelOutlined';
import CloseIcon from '@material-ui/icons/Close';
import HelpOutlineIcon from '@material-ui/icons/HelpOutline';
import OpenInNewIcon from '@material-ui/icons/OpenInNew';

import type { DocsCenterProps } from './interface';

const useStyles = makeStyles({
  announcementContainer: {},
  announcementWrapper: {
    position: 'absolute',
    right: '560px',
    top: '60px',
    zIndex: 1999,
  },
  releaseNotesBox: {
    overflowY: 'scroll',
    position: 'fixed',
    display: 'flex',
    top: '102px',
    bottom: '0',
    right: '0',
    transition: 'all .5s ease',
  },
  openInNewIcon: {
    position: 'absolute',
    top: '15px',
    right: '60px',
  },
  closeIcon: {
    position: 'absolute',
    top: '15px',
    right: '25px',
  },
  iframeContainer: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  questionContainer: {
    position: 'fixed',
    top: '120px',
    right: '560px',
    width: '45px',
    height: '45px',
    background: '#6239EB',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    color: 'white',
    zIndex: 1999,
    transition: 'all .5s ease',
    borderRadius: '6px',
    border: '2px solid white',
  },
});

const openInNewTab = () => {
  window.open('https://docs.nue.io/');
};

type QuestionContainerProps = {
  click: React.Dispatch<React.SetStateAction<boolean>>;
  style?: React.CSSProperties;
  isOpen: boolean;
};

const QuestionContainer = ({ click, style, isOpen }: QuestionContainerProps) => {
  const classes = useStyles();
  return (
    <div className={classes.questionContainer} style={style} onClick={() => click(true)}>
      {isOpen ? <CancelOutlinedIcon fontSize="large" /> : <HelpOutlineIcon fontSize="large" />}
    </div>
  );
};
const DocsCenter = ({ isOpen, setIsOpen, isFromSalesConsole }: DocsCenterProps) => {
  const classes = useStyles();
  const closeWindow = () => {
    setIsOpen(false);
  };

  const handleOnClick = () => {
    if (isFromSalesConsole) {
      openInNewTab();
      return;
    }
    setIsOpen(!isOpen);
  };

  return (
    <div>
      <QuestionContainer
        isOpen={isOpen}
        style={{ right: `${isOpen ? '560px' : '0'}`, cursor: 'pointer' }}
        click={handleOnClick}
      />
      {isOpen ? (
        <div className={classes.announcementContainer}>
          <div className={classes.announcementWrapper}>
            <Paper
              elevation={3}
              className={classes.releaseNotesBox}
              style={{ width: `${isOpen ? '560px' : '0'}`, opacity: `${isOpen ? 1 : 0}` }}
            >
              <iframe
                id="askNue"
                title="askNue"
                src={`https://docs.nue.io`}
                className={classes.iframeContainer}
                frameBorder={0}
              />
              <Tooltip arrow title="Open in a new tab">
                <IconButton onClick={openInNewTab} className={classes.openInNewIcon}>
                  <OpenInNewIcon />
                </IconButton>
              </Tooltip>
              <Tooltip arrow title="Close">
                <IconButton onClick={closeWindow} className={classes.closeIcon}>
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            </Paper>
          </div>
        </div>
      ) : null}
    </div>
  );
};
export default DocsCenter;

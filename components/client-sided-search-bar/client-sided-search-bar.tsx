import { But<PERSON>, Grid, Icon<PERSON>utton, InputAdornment, makeStyles } from '@material-ui/core';
import React from 'react';
import TextInput from '../text-input';
import { Props } from './interface';
import ClearIcon from '@material-ui/icons/Clear';
import ArrowForwardIcon from '@material-ui/icons/ArrowForward';
import { SearchIcon } from '../icons';

const defaultProps = {};

const useStyles = makeStyles({
  searchBarWrapper: {
    display: 'flex',
  },
  searchBarRoot: {
    overflow: 'hidden',
    borderRadius: '4px 0 0 4px !important',
    backgroundColor: '#f1f0f2 !important',
  },
  searchBarInput: {
    borderRightColor: 'whitesmoke',
    fontSize: '.875rem',
    height: '100%',
    marginTop: '0 !important',
    paddingLeft: 0,
    backgroundColor: '#f1f0f2 !important',
    border: 'none',
    minHeight: '22px',
  },
  clearInputIcon: {
    padding: '0px',
    margin: '12px',
    '& svg': {
      fontSize: '1rem',
    },
  },
  startAdorn: {
    color: '#4d4c50',
    fontSize: 'small',
    paddingLeft: '20px',
    paddingRight: '2px',
  },
  searchButtonRoot: {
    width: '100%',
    minWidth: '46px',
    maxWidth: '46px',
    fontWeight: 500,
    borderRadius: '0px 4px 4px 0px',
    height: '45px',
    backgroundColor: '#6239eb',
    fontSize: '.875rem',
    color: '#fff',
    textTransform: 'none',
    cursor: 'pointer',
    padding: '0px 8px',
    '&:hover': {
      backgroundColor: '#6239eb',
    },
  },
  searchIcon: {
    margin: 0,
  },
});

const ClientSidedSearchBar: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const { handleSearch } = props;

  const [searchString, setSearchString] = React.useState('');

  const classes = useStyles();

  const onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch(searchString);
    }
  };

  return (
    <Grid item xs={4} className={classes.searchBarWrapper}>
      <TextInput
        classes={{
          input: classes.searchBarInput,
          startAdorment: classes.startAdorn,
          root: classes.searchBarRoot,
        }}
        value={searchString}
        disableLabel
        field={{
          apiName: 'search',
          type: 'text',
          name: 'Search',
        }}
        handleInputChange={(value) => {
          setSearchString(value);
        }}
        onKeyDown={onKeyDown}
        placeholder={'Search the list'}
        startAdornment={
          <InputAdornment position="start" className={classes.startAdorn}>
            <SearchIcon viewBox={'-2 -2 21 21'} />
          </InputAdornment>
        }
        endAdornment={
          searchString ? (
            <IconButton className={classes.clearInputIcon} onClick={() => setSearchString('')}>
              <ClearIcon />
            </IconButton>
          ) : null
        }
      />
      <Button
        classes={{
          startIcon: classes.searchIcon,
          root: classes.searchButtonRoot,
        }}
        onClick={() => handleSearch(searchString)}
        variant="contained"
        startIcon={<ArrowForwardIcon />}
      />
    </Grid>
  );
};

export default ClientSidedSearchBar;

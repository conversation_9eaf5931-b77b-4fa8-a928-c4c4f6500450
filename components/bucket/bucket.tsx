import React, { useEffect, useState } from 'react';
import { Grid, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { BucketObject } from '@nue-apps/ruby-ui-component';
import _ from 'lodash';
import {
  ActivatedIcon,
  AddressIcon,
  BucketIcon,
  CheckboxIcon,
  CollapseBucketIcon,
  CompanyIcon,
  CreateBucketIcon,
  CurrencyIcon,
  DateIcon,
  DeleteBucketIcon,
  EditBucketIcon,
  ImageIcon,
  MoreBucketIcon,
  MultipleSelectIcon,
  NumberIcon,
  OthersIcon,
  PercentageIcon,
  PreviewBucketIcon,
  PrimaryContactIcon,
  SelectIcon,
  TextAreaIcon,
  TextIcon,
  TotalAmountIcon,
} from '../icons';
import { formatNumberForDisplay } from '../list';
import { Item, ItemProps } from '../list/interface';
import { displayValuesFromFieldSet } from '../metadata/metadata-utils';
import { useUserLocale } from '../use-user-locale';
import { FieldProps, Props } from './interface';

const useStyles = makeStyles({
  bucketWrapper: {
    border: '1px solid #E5E5E5',
    borderLeftWidth: 0,
    width: '100%',
    minHeight: '300px',
    borderRadius: '8px',
    display: 'flex',
    overflow: 'hidden',
    // transform: 'none!important',
    position: 'relative',
    boxSizing: 'border-box',
    animation: `$myEffect 1000ms`,
    visibility: 'hidden',
  },
  bucketContainer: {
    padding: '32px',
    width: '100%',
  },
  bucketHeader: {
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    flexWrap: 'nowrap',
    width: '100%',
  },
  bucketName: {
    fontSize: '1.125rem',
    marginBottom: '30px',
    whiteSpace: 'nowrap',
  },
  fieldWrapper: {
    marginRight: '20px',
  },
  filedNameWrapper: {
    marginBottom: '10px',
  },
  fieldName: {
    fontSize: '0.75rem',
    color: '#999999',
    textTransform: 'uppercase',
    fontWeight: 500,
  },
  filedValue: {
    fontSize: '14px',
  },
  amoutFieldValue: {
    fontSize: '1rem',
    color: '#6239EB',
    fontWeight: 500,
  },
  fieldValue: {
    fontSize: '0.875rem',
  },
  fieldIconWrapper: {
    marginRight: '6px',
    paddingTop: '0.15em',
  },
  fieldIcon: {
    width: '15px',
  },
  statusBar: {
    width: '6px',
  },
  activated: {
    backgroundColor: '#12BB99',
  },
  finalized: {
    backgroundColor: '#FF9806',
  },
  draft: {
    backgroundColor: '#6239EB',
  },
  activatedBorder: {
    borderTopWidth: '2px',
    borderRightWidth: '2px',
    borderBottomWidth: '2px',
    borderColor: '#12BB99',
    boxShadow: '3px 6px 8px -2px #BBBBBB',
    backgroundColor: 'rgba(18,187,153,0.05);',
  },
  finalizedBorder: {
    borderTopWidth: '2px',
    borderRightWidth: '2px',
    borderBottomWidth: '2px',
    borderColor: '#FF9806',
    boxShadow: '3px 6px 8px -2px #BBBBBB',
    backgroundColor: 'rgba(242,172,73,0.07);',
  },
  draftBorder: {
    borderTopWidth: '2px',
    borderRightWidth: '2px',
    borderBottomWidth: '2px',
    borderColor: '#6239EB',
    boxShadow: '3px 6px 8px -2px #BBBBBB',
    backgroundColor: 'rgba(176,156,245,0.06)',
  },
  draftBorderForInActive: {
    borderTopWidth: '1px',
    borderRightWidth: '1px',
    borderBottomWidth: '1px',
    borderColor: '#BBBBBB',
  },
  activeDraft: {
    backgroundColor: '#6239EB',
  },
  actionIconWrapper: {
    padding: '0 16px',
    cursor: 'pointer',
  },
  bucketModified: {
    position: 'absolute',
    width: '65px',
    height: '25px',
    right: '-1.5px',
    top: '-1.5px',
    background: '#FC5455',
    borderRadius: '0 8px',
    color: 'white',
    fontSize: '10px',
    lineHeight: '25px',
    textAlign: 'center',
  },
  more: {
    position: 'absolute',
    bottom: '10px',
    right: '50px',
    cursor: 'pointer',
    fontSize: '0.875rem',
    color: '#6239EB',
    letterSpacing: '-0.5px',
    textAlign: 'center',
    fontWeight: 400,
  },
  collapse: {
    position: 'absolute',
    bottom: '10px',
    right: '50px',
    cursor: 'pointer',
    fontSize: '0.875rem',
    color: '#A1A5AF',
    letterSpacing: '-0.5px',
    textAlign: 'center',
    fontWeight: 400,
  },
  '@keyframes myEffect': {
    '0%': {
      opacity: 0,
      transform: 'translateX(200%)',
    },
    '100%': {
      opacity: 1,
      transform: 'translateX(0)',
    },
  },
});

const iconMap = new Map<string, React.FC>([
  ['name', BucketIcon],
  ['totalAmount', TotalAmountIcon],
  ['billingAccountId', CompanyIcon],
  ['primaryContactId', PrimaryContactIcon],
  ['shippingAddress', AddressIcon],
  ['billingAddress', AddressIcon],
]);

const iconMapFromType = new Map<string, React.FC>([
  // replace all the BucketIcon with the icons specified for the different type
  ['string', TextIcon],
  ['text', TextIcon],
  ['textarea', TextAreaIcon],
  ['boolean', CheckboxIcon],
  ['currency', CurrencyIcon],
  ['number', NumberIcon],
  ['double', NumberIcon],
  ['image', ImageIcon],
  ['multipicklist', MultipleSelectIcon],
  ['picklist', SelectIcon],
  ['date', DateIcon],
  ['datetime', DateIcon],
  ['time', DateIcon],
  ['percent', PercentageIcon],
]);

interface ActionListProps {
  status: string;
  isDefaultBucket: boolean;
  id: string;
  handleDeleteBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  handlePreviewBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  handleEditBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  handleCreateBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  handleActivatedBucket?: (event: React.MouseEvent<HTMLDivElement>, id: string) => void;
  splitProgress?: number;
}

const ActionList: React.FC<ActionListProps> = (props: ActionListProps) => {
  const {
    status,
    handleDeleteBucket,
    handlePreviewBucket,
    handleEditBucket,
    handleCreateBucket,
    handleActivatedBucket,
    isDefaultBucket,
    id,
    splitProgress,
  } = props;

  const classes = useStyles();

  return (
    <Grid container item justifyContent="flex-end">
      {status === 'Activated' && (
        <>
          <div
            className={classes.actionIconWrapper}
            onClick={(event: React.MouseEvent<HTMLDivElement>) => {
              event.stopPropagation();
              if (handleActivatedBucket) {
                handleActivatedBucket(event, id);
              }
            }}
            style={{
              cursor: splitProgress !== 0 && splitProgress !== 100 ? 'not-allowed' : 'pointer',
            }}
          >
            <ActivatedIcon />
          </div>
        </>
      )}
      {status === 'Finalized' && (
        <>
          <div
            className={classes.actionIconWrapper}
            onClick={(event: React.MouseEvent<HTMLDivElement>) => {
              event.stopPropagation();
              if (handlePreviewBucket) {
                handlePreviewBucket(event, id);
              }
            }}
            style={{
              cursor: splitProgress !== 0 && splitProgress !== 100 ? 'not-allowed' : 'pointer',
            }}
          >
            <PreviewBucketIcon />
          </div>
        </>
      )}
      {status === 'Draft' && (
        <>
          {isDefaultBucket ? null : (
            <div
              className={classes.actionIconWrapper}
              onClick={(event: React.MouseEvent<HTMLDivElement>) => {
                event.stopPropagation();
                if (handleDeleteBucket) {
                  handleDeleteBucket(event, id);
                }
              }}
              style={{
                cursor: splitProgress !== 0 && splitProgress !== 100 ? 'not-allowed' : 'pointer',
              }}
            >
              <DeleteBucketIcon />
            </div>
          )}
          <div
            className={classes.actionIconWrapper}
            onClick={(event: React.MouseEvent<HTMLDivElement>) => {
              event.stopPropagation();
              if (handleCreateBucket) {
                handleCreateBucket(event, id);
              }
            }}
            style={{
              cursor: splitProgress !== 0 && splitProgress !== 100 ? 'not-allowed' : 'pointer',
            }}
          >
            <CreateBucketIcon />
          </div>
          <div
            className={classes.actionIconWrapper}
            onClick={(event: React.MouseEvent<HTMLDivElement>) => {
              event.stopPropagation();
              if (handleEditBucket) {
                handleEditBucket(event, id);
              }
            }}
            style={{
              cursor: splitProgress !== 0 && splitProgress !== 100 ? 'not-allowed' : 'pointer',
            }}
          >
            <EditBucketIcon />
          </div>
        </>
      )}
    </Grid>
  );
};

interface BucketFieldProps {
  item: Item;
  currencyIsoCode?: string;
}

const BucketFiled: React.FC<BucketFieldProps> = (props: BucketFieldProps) => {
  const { item, currencyIsoCode } = props;
  const { getUserLocale } = useUserLocale();

  // if can not find icon from the iconMap(based on the apiName)
  // just find the icon from iconMapFromType(based on the type -> type is required in each field, so it cannot be null)
  const Icon = iconMap.get(item.apiName) || iconMapFromType.get(item.type) || OthersIcon;

  const classes = useStyles();

  const convertToCurrency = (i: Item, currencyIsoCode?: string) => {
    const currencyCode = currencyIsoCode || 'USD';
    if (i.value === null || i.value === undefined || i.value === 0) {
      const zero: number = 0;
      return zero.toLocaleString(getUserLocale(), {
        style: 'currency',
        currency: currencyCode,
      });
    }
    return formatNumberForDisplay(Number(i.value), currencyCode, false, false, getUserLocale());
  };

  const getItemValue = (value: Item) => {
    if (value.type.toLowerCase() === 'currency') {
      return convertToCurrency(value, currencyIsoCode);
    } else {
      return value.value && value.value.toString() === '[object Object]'
        ? Object.values(value.value).filter((values: any) => !!values).length > 0
          ? Object.values(value.value)
              .filter((values: any) => !!values)
              .join(', ')
          : ''
        : value.value || value.value === 0
          ? value.value
          : '';
    }
  };

  return (
    <Grid container style={{ flexWrap: 'nowrap', minHeight: '40px' }}>
      {Icon && (
        <span className={classes.fieldIconWrapper}>
          <Icon />
        </span>
      )}
      <Grid item>
        <Typography variant="caption" className={classes.fieldName} noWrap>
          {item.name}
        </Typography>
        <Typography
          variant="subtitle1"
          className={item.apiName === 'totalAmount' ? classes.amoutFieldValue : classes.fieldValue}
        >
          {getItemValue(item)}
        </Typography>
      </Grid>
    </Grid>
  );
};

const defaultProps = {
  modified: false,
};

export const Bucket: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const classes = useStyles();
  const {
    object,
    currencyIsoCode,
    fieldSetMetadata,
    objectMetadata,
    handleDeleteBucket,
    handlePreviewBucket,
    handleEditBucket,
    handleCreateBucket,
    handleActivatedBucket,
    activeBucket,
    bucketIndex,
    readOnly,
    splitProgress,
  } = props;
  const {
    name,
    totalAmount,
    status,
    billingAccountId,
    primaryContactId,
    shippingAddress,
    isDefaultBucket,
    id,
    modified,
  } = object;

  const [showMoreItems, setShowMoreItems] = useState(true);
  const { getUserLocale } = useUserLocale();

  const getStatusClass = (s: string) => {
    if (s === 'Activated') return classes.activated;
    if (s === 'Finalized') return classes.finalized;
    if (activeBucket?.id === object.id && s === 'Draft') return classes.activeDraft;
    if (s === 'Draft') return classes.draft;
    return classes.draft;
  };

  const getStatusBorder = (s: string) => {
    if (activeBucket?.id !== object.id) {
      return classes.draftBorderForInActive;
    }
    if (s === 'Activated') return classes.activatedBorder;
    if (s === 'Finalized') return classes.finalizedBorder;
    if (s === 'Draft') return classes.draftBorder;
    return classes.draftBorder;
  };

  const bucket = {
    ...object,
    billingAccountId: object.billingAccount?.name || object.billingAccountId,
    accountId: object.account?.name || object.accountId,
    primaryContactId: object.primaryContact?.name || object.primaryContactId,
  };
  // update the customized field
  const transformCustomizedFieldForObject = (object: BucketObject) => {
    const transformedObject = _.cloneDeep(object);
    Object.keys(transformedObject).forEach((key: any) => {
      if (key.endsWith('__c')) {
        const valueKey = key.replace('__c', '__r');
        //@ts-ignore
        if (transformedObject[valueKey]) {
          //@ts-ignore
          transformedObject[key] = transformedObject[valueKey].Name;
        }
      }
    });
    return transformedObject;
  };
  // generate items to display
  const items: Item[] = displayValuesFromFieldSet({
    objectFieldSet: fieldSetMetadata,
    objectMetadata,
    currencyIsoCode,
    object: transformCustomizedFieldForObject(bucket),
    locale: getUserLocale(),
  });

  let first6Items: Item[] = [];

  if (items.length > 6) {
    if (showMoreItems) {
      first6Items = items.slice(0, 6);
    } else {
      first6Items = items;
    }
  } else {
    first6Items = items;
  }

  useEffect(() => {
    setTimeout(() => {
      const buckets = document.querySelectorAll<HTMLDivElement>('[role="bucket"]');
      if (buckets.length > 0) {
        buckets.forEach((item: HTMLDivElement) => {
          item.style.visibility = 'visible';
        });
      }
    }, 150);
  }, []);

  return (
    <div
      className={classes.bucketWrapper + ' ' + getStatusBorder(status)}
      role="bucket"
      style={{ animationDelay: `${bucketIndex * 70}ms` }}
    >
      <div className={classes.statusBar + ' ' + getStatusClass(status)}></div>
      {modified ? <div className={classes.bucketModified}>Modified</div> : null}
      <div className={classes.bucketContainer}>
        <Grid container className={classes.bucketHeader}>
          <Grid item>
            <Typography className={classes.bucketName}>{name}</Typography>
          </Grid>
          {!readOnly && (
            <ActionList
              isDefaultBucket={isDefaultBucket}
              status={status}
              id={id}
              handleDeleteBucket={handleDeleteBucket}
              handlePreviewBucket={handlePreviewBucket}
              handleEditBucket={handleEditBucket}
              handleCreateBucket={handleCreateBucket}
              handleActivatedBucket={handleActivatedBucket}
              splitProgress={splitProgress}
            />
          )}
        </Grid>
        <Grid container>
          {first6Items.map((item: Item, index: number) => {
            return (
              <Grid
                item
                container
                xs={item.xs || 6}
                sm={item.xs || 4}
                justifyContent="space-between"
                key={item.name}
                style={{ padding: '3px' }}
              >
                <BucketFiled item={item} currencyIsoCode={currencyIsoCode} />
              </Grid>
            );
          })}
        </Grid>
        {items.length > 6 ? (
          showMoreItems ? (
            <div
              className={classes.more}
              onClick={(event: React.MouseEvent<HTMLDivElement>) => {
                event.stopPropagation();
                setShowMoreItems(false);
              }}
            >
              More
              <MoreBucketIcon style={{ position: 'relative', top: '6px', left: '10px' }} />
            </div>
          ) : (
            <div
              className={classes.collapse}
              onClick={(event: React.MouseEvent<HTMLDivElement>) => {
                event.stopPropagation();
                setShowMoreItems(true);
              }}
            >
              Collapse
              <CollapseBucketIcon style={{ position: 'relative', top: '6px', left: '10px' }} />
            </div>
          )
        ) : null}
      </div>
    </div>
  );
};

export default Bucket;

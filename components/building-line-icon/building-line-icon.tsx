import React from 'react';
import { SvgIcon } from '@material-ui/core';
import { Props } from './interface';

const BuildingLineIcon: React.FC<Props> = ({ width, height, viewBox }) => {
  return (
    <SvgIcon>
      <svg
        width={width || '16px'}
        height={height || '16px'}
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>DDF19DBE-680E-4774-A59F-FB3098FD7CE9</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-5-1-Create-Quote" transform="translate(-1014.000000, -769.000000)">
            <g id="Product-Options" transform="translate(361.000000, 283.000000)">
              <g id="Profile" transform="translate(633.000000, 165.000000)">
                <g id="Group-9" transform="translate(20.000000, 320.000000)">
                  <g id="building-line" transform="translate(0.000000, 1.000000)">
                    <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                    <path
                      d="M14,12.6666667 L15.3333333,12.6666667 L15.3333333,14 L0.666666667,14 L0.666666667,12.6666667 L2,12.6666667 L2,2.66666667 C2,2.29847683 2.29847683,2 2.66666667,2 L9.33333333,2 C9.70152317,2 10,2.29847683 10,2.66666667 L10,12.6666667 L12.6666667,12.6666667 L12.6666667,7.33333333 L11.3333333,7.33333333 L11.3333333,6 L13.3333333,6 C13.7015232,6 14,6.29847683 14,6.66666667 L14,12.6666667 Z M3.33333333,3.33333333 L3.33333333,12.6666667 L8.66666667,12.6666667 L8.66666667,3.33333333 L3.33333333,3.33333333 Z M4.66666667,7.33333333 L7.33333333,7.33333333 L7.33333333,8.66666667 L4.66666667,8.66666667 L4.66666667,7.33333333 Z M4.66666667,4.66666667 L7.33333333,4.66666667 L7.33333333,6 L4.66666667,6 L4.66666667,4.66666667 Z"
                      id="Shape"
                      fill="#6239EB"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default BuildingLineIcon;

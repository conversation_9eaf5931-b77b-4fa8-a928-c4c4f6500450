import dayjs from 'dayjs';
import { ChangeGroup, ChangeItem } from '../../change-cart/interface';
import { IntegrationResult } from '../../customer-view-page/interface';
import { ChangeModalProps } from '../../ruby-settings/interface';
import { convertTerm } from '../../util/term-converter';
import _ from 'lodash';
import {
  getCancellationDateByAction,
  getFields,
  getLatestSubscriptionEndDateOfAllSubscriptions,
} from '../../subscription-card/subscription-card-util';
import { RubyObject } from '../../metadata';
import { time } from '../constant';
import { RubyFilter } from '../../graph-ql-query-constructor/interface';
import { GraphqlQueryConstructor, QueryExecutor } from '@nue-apps/ruby-ui-component';
import { TransactionDetail } from '../../transaction-dialog/interface';
import { Subscription } from '../../revenue-builder-types/interface';
import { SubscriptionTableItem } from '../interface';
import TransactionDialog from '../../transaction-dialog/transaction-dialog';

const calculateBarItemWidth = (value: number, totalValue: number) => {
  return `${value > 0 ? Math.round((value / totalValue + Number.EPSILON) * 100) + 30 : 15}%`;
};

const extractErrorMessage = (error: any, fallback: string) => {
  if (error.message) {
    return error.message;
  }

  if (error.body) {
    return error.body.message;
  }

  return fallback;
};

const showTransactionHub = (integrations: IntegrationResult[] | undefined) => {
  return !!integrations?.some(
    (x) =>
      (x.integrationName === 'NetSuite' || x.integrationName === 'QuickBooks') &&
      x.status === 'Connected',
  );
};

const getAddMultipleGroupsToCartResult = (
  values: any,
  changeModalProps: ChangeModalProps,
  subscriptionMetadata: RubyObject,
  cotermDate: string,
) => {
  const result: any[] = [];
  let shouldCloseChangeDialog = true;
  if (changeModalProps.cardAction.id === 'bulkRenew') {
    changeModalProps.subscriptions.forEach((subscriptionToAddToChangeCart) => {
      if (!subscriptionToAddToChangeCart.evergreen) {
        const changeGroup: ChangeGroup = {
          id: `${subscriptionToAddToChangeCart.customerId}-${subscriptionToAddToChangeCart.priceBookId}-${subscriptionToAddToChangeCart.currencyIsoCode}`,
          customer: {
            name: subscriptionToAddToChangeCart.customer.name,
            id: subscriptionToAddToChangeCart.customerId,
          },
          name: subscriptionToAddToChangeCart.name,
          priceBook: {
            name: subscriptionToAddToChangeCart.priceBook.name,
            id: subscriptionToAddToChangeCart.priceBookId,
          },
          currencyIsoCode: subscriptionToAddToChangeCart.currencyIsoCode,
        };

        let renewalTerm = 0;

        renewalTerm = values.amount;
        if (
          subscriptionToAddToChangeCart.uom.termDimension?.toLowerCase() !==
          values.selectTerm.toLowerCase()
        ) {
          renewalTerm = convertTerm(
            renewalTerm,
            values.selectTerm,
            subscriptionToAddToChangeCart.uom.termDimension,
          );
        }

        if (_.isNil(renewalTerm) || renewalTerm <= 0) {
          shouldCloseChangeDialog = false;
        } else {
          const changeItem: ChangeItem = {
            id: `${subscriptionToAddToChangeCart.id}-Renew`,
            time: dayjs().format('MM/DD/YYYY hh:mm A'),
            asset: subscriptionToAddToChangeCart,
            //@ts-ignore
            cardAction: {
              id: 'renew',
              name: 'Renew',
            },
            subscriptionMetadata: { ...subscriptionMetadata, fields: getFields('renew') },
            requestWasEdited: false,
            request: {
              assetName: subscriptionToAddToChangeCart.name,
              changeType: 'Renew',
              objectType: 'Subscription',
              label: subscriptionMetadata.label,
              priceBookId: subscriptionToAddToChangeCart.priceBookId,
              renewalTerm: renewalTerm,
              termDimension: subscriptionToAddToChangeCart.uom.termDimension,
            },
          };

          result.push({ changeGroup: changeGroup, changeItems: [changeItem] });
        }
      }
    });
  } else if (changeModalProps.cardAction.id === 'bulkCoterm') {
    changeModalProps.subscriptions.forEach((subscriptionToAddToChangeCart) => {
      if (!subscriptionToAddToChangeCart.evergreen) {
        const changeGroup: ChangeGroup = {
          id: `${subscriptionToAddToChangeCart.customerId}-${subscriptionToAddToChangeCart.priceBookId}-${subscriptionToAddToChangeCart.currencyIsoCode}`,
          customer: {
            name: subscriptionToAddToChangeCart.customer.name,
            id: subscriptionToAddToChangeCart.customerId,
          },
          name: subscriptionToAddToChangeCart.name,
          priceBook: {
            name: subscriptionToAddToChangeCart.priceBook.name,
            id: subscriptionToAddToChangeCart.priceBookId,
          },
          currencyIsoCode: subscriptionToAddToChangeCart.currencyIsoCode,
        };

        const latestEndDate = getLatestSubscriptionEndDateOfAllSubscriptions(
          changeModalProps.subscriptions,
        );
        if (dayjs(cotermDate).isBefore(dayjs(latestEndDate))) {
          shouldCloseChangeDialog = false;
        } else {
          const changeItem: ChangeItem = {
            id: `${subscriptionToAddToChangeCart.id}-CoTerm`,
            time,
            asset: subscriptionToAddToChangeCart,
            //@ts-ignore
            cardAction: {
              description: '',
              id: 'bulkCoterm',
              name: 'Co-Term',
            },
            subscriptionMetadata,
            requestWasEdited: false,
            request: {
              assetName: subscriptionToAddToChangeCart.name,
              changeType: 'CoTerm',
              objectType: 'Subscription',
              label: subscriptionMetadata.label,
              priceBookId: subscriptionToAddToChangeCart.priceBookId,
              updateRenewalTerm: values.updateRenewalTerm,
              renewalTerm: values.renewalTerm,
              termDimension: values.termDimension,
              subscriptionStartDate: subscriptionToAddToChangeCart.subscriptionStartDate,
              subscriptionEndDate: cotermDate,
              changeAction: 'co-termed',
            },
          };
          result.push({
            changeGroup: changeGroup,
            changeItems: [changeItem],
          });
        }
      }
    });
  } else if (changeModalProps.cardAction.id === 'bulkCancel') {
    changeModalProps.subscriptions.forEach((subscriptionToAddToChangeCart) => {
      if (
        (!subscriptionToAddToChangeCart.evergreen ||
          (subscriptionToAddToChangeCart.evergreen &&
            values.action !== 'afterSubscriptionTermEnds')) &&
        subscriptionToAddToChangeCart.status !== 'Expired' &&
        !subscriptionToAddToChangeCart.orderProduct?.productOption?.required
      ) {
        const changeGroup: ChangeGroup = {
          id: `${subscriptionToAddToChangeCart.customerId}-${subscriptionToAddToChangeCart.priceBookId}-${subscriptionToAddToChangeCart.currencyIsoCode}`,
          customer: {
            name: subscriptionToAddToChangeCart.customer.name,
            id: subscriptionToAddToChangeCart.customerId,
          },
          name: subscriptionToAddToChangeCart.name,
          priceBook: {
            name: subscriptionToAddToChangeCart.priceBook.name,
            id: subscriptionToAddToChangeCart.priceBookId,
          },
          currencyIsoCode: subscriptionToAddToChangeCart.currencyIsoCode,
        };
        const changeItem: ChangeItem = {
          id: `${subscriptionToAddToChangeCart.id}-Cancel`,
          time: dayjs().format('MM/DD/YYYY hh:mm A'),
          asset: subscriptionToAddToChangeCart,
          //@ts-ignore
          cardAction: {
            description: '',
            id: 'cancel',
            name: 'cancel',
          },
          subscriptionMetadata,
          requestWasEdited: false,
          //@ts-ignore
          request: {
            assetName: subscriptionToAddToChangeCart.name,
            changeType: 'Cancel',
            action: values.action,
            objectType: 'Subscription',
            label: subscriptionMetadata.label,
            cancellationDate: getCancellationDateByAction(
              values.action,
              values.selectDay,
              subscriptionToAddToChangeCart.subscriptionEndDate,
            ),
            changeAction: 'cancel',
          },
        };
        result.push({ changeGroup: changeGroup, changeItems: [changeItem] });
      }
    });
  } else if (changeModalProps.cardAction.id === 'adjustPrice') {
    changeModalProps.subscriptions.forEach((subscriptionToAddToChangeCart) => {
      if (subscriptionToAddToChangeCart.status !== 'Expired') {
        const changeGroup: ChangeGroup = {
          id: `${subscriptionToAddToChangeCart.customerId}-${subscriptionToAddToChangeCart.priceBookId}-${subscriptionToAddToChangeCart.currencyIsoCode}`,
          customer: {
            name: subscriptionToAddToChangeCart.customer.name,
            id: subscriptionToAddToChangeCart.customerId,
          },
          name: subscriptionToAddToChangeCart.name,
          priceBook: {
            name: subscriptionToAddToChangeCart.priceBook.name,
            id: subscriptionToAddToChangeCart.priceBookId,
          },
          currencyIsoCode: subscriptionToAddToChangeCart.currencyIsoCode,
        };

        let request = {
          assetName: subscriptionToAddToChangeCart.name,
          changeType: 'AdjustPrice',
          objectType: 'Subscription',
          priceBookId: subscriptionToAddToChangeCart.priceBookId,
          ...values,
        };

        if (values.endDateRadio === 'untilSpecificDate') {
          request = {
            ...request,
            endDate: dayjs(values.specificDate).format('YYYY-MM-DD'),
          };
        } else if (values.endDateRadio === 'untilMonthLater') {
          request = {
            ...request,
            term: values.untilAmount,
          };
        }

        const changeItem: ChangeItem = {
          id: `${subscriptionToAddToChangeCart.id}-adjustPrice`,
          time: dayjs().format('MM/DD/YYYY hh:mm A'),
          asset: subscriptionToAddToChangeCart,
          cardAction: {
            description: '',
            id: 'AdjustPrice',
            name: 'AdjustPrice',
          },
          subscriptionMetadata,
          requestWasEdited: false,
          request,
          newValues: values,
          objects: subscriptionToAddToChangeCart,
        };

        result.push({ changeGroup: changeGroup, changeItems: [changeItem] });
      }
    });
  } else {
    console.error('No Bulk Action selected');
  }

  return {
    cartItems: result,
    shouldCloseChangeDialog,
  };
};

const loadTransactions = async (
  nueIds: string[],
  transactionType: string,
  transactionHubMetadata: RubyObject,
  executeQuery: QueryExecutor,
) => {
  if (!transactionHubMetadata) {
    return;
  }

  const idsIn = `["${nueIds.join('","')}"]`;
  const tools = ['NetSuite'];
  const toolsIn = `["${tools.join('","')}"]`;
  const whereCondition = `( where: { _and: [ { nueId: { _in : ${idsIn} } }, { externalSystem: {  _in : ${toolsIn}  } }, { transactionType: { _eq : "${transactionType !== 'Account' ? transactionType : 'Customer'}" } } ] } )`;

  const query = GraphqlQueryConstructor.construct().queryWithWhereCondition(
    transactionHubMetadata,
    whereCondition,
    '',
  );

  return (await executeQuery(query, transactionHubMetadata, [])) as TransactionDetail[];
};

const loadChildernAndBillingAccountsByIds = async (
  ids: string[],
  billingAccountIds: any,
  childAccountIds: any,
  accountDetails: any,
  executeQuery: QueryExecutor,
  customerMetadata: RubyObject,
  transactionHubMetadata: RubyObject,
) => {
  if (ids?.length) {
    const customerFilter: RubyFilter = {
      isApplied: true,
      conditions: [
        {
          apiName: 'id',
          name: 'Id',
          operand: ' _and ',
          operator: ' _in ',
          type: 'Picklist',
          value: JSON.stringify(ids),
        },
      ],
      name: 'Customer Filter',
      isExclusive: true,
      id: 'customer-filter',
    };

    const userQuery = GraphqlQueryConstructor.construct().query(
      customerMetadata,
      customerFilter.conditions || [],
    );
    const queryResult = await executeQuery(userQuery, customerMetadata, [customerFilter]);
    const _transactions = await loadTransactions(
      ids,
      'Account',
      transactionHubMetadata,
      executeQuery,
    );

    if (queryResult?.length) {
      if (_transactions && _transactions.length > 0) {
        _transactions.forEach((o: TransactionDetail, i: number) => {
          const queriedObject = queryResult?.find((s) =>
            [s.nueId, s.Ruby__NueId__c, s.id].includes(o.nueId),
          );
          if (!queriedObject.transactionHub) {
            queriedObject.transactionHub = [];
          }
          queriedObject.transactionHub.push(o);
        });
      }

      let billingAccountResult = null;
      let childAccountResult = null;
      let expandedIds = null;
      if (billingAccountIds?.length) {
        billingAccountResult = billingAccountIds
          .map((x: any) => {
            const a = queryResult.find((r: any) => {
              const f = r.id === x;
              return f;
            });
            return a;
          })
          .filter((x: any) => x);
      }
      if (childAccountIds?.length) {
        childAccountResult = childAccountIds
          .map((x: any) => {
            const a = queryResult.find((r: any) => {
              const f = r.id === x;
              return f;
            });
            return a;
          })
          .filter((x: any) => x);

        expandedIds = accountDetails.childStructures
          .map((asset: any) => {
            if (asset.children?.length) {
              return asset.id;
            }
          })
          .filter((x: any) => x);
      }

      return {
        billingAccountResult: billingAccountResult,
        childAccountResult: childAccountResult,
        expandedIds: expandedIds,
      };
    }
  }
  return null;
};

// TODO: add any[] for now for unit test mock, remove any once upgrade/downgrade feature branch is merged
// coz that branch has completed mock Subscription type
const buildSubscriptionHierarchy = (
  subscriptions: Subscription[] | any[],
  searchResult: string,
) => {
  const subscriptionMap = new Map();

  // save all subscriptions to map
  for (const subscription of subscriptions) {
    subscriptionMap.set(subscription.id, { ...subscription });
  }

  // check if there is an exact match
  const isExactMatch = (subscription: Subscription) => {
    const lowerCaseSearchResult = (searchResult ?? '').trim().toLowerCase();
    return (
      subscription.name.toLowerCase() === lowerCaseSearchResult ||
      subscription.productName.toLowerCase() === lowerCaseSearchResult
    );
  };

  // recursively collect all descendants
  const collectDescendants = (id: string, descendants: Set<string>) => {
    subscriptions.forEach((sub) => {
      if (sub.parentId === id) {
        descendants.add(sub.id);
        collectDescendants(sub.id, descendants);
      }
    });
  };

  // find all matching nodes
  const matchingNodes = new Set<string>();
  const matchingPaths = new Set<string>();
  const exactMatchDescendants = new Set<string>();
  let hasExactMatch = false;

  // find all exact matching nodes
  for (const subscription of subscriptions) {
    if (isExactMatch(subscription)) {
      hasExactMatch = true;
      matchingNodes.add(subscription.id);

      // get parent node path
      let current = subscription;
      while (current) {
        matchingPaths.add(current.id);
        current = current.parentId ? subscriptionMap.get(current.parentId) : null;
      }

      // recursively collect all children of exact match subscription
      collectDescendants(subscription.id, exactMatchDescendants);
    }
  }

  const visitedNodes = new Set<string>();

  // recursively build hierarchy
  const buildHierarchy = (
    parentId: string | null = null,
    depth: number = 0,
  ): SubscriptionTableItem[] => {
    // prevent inifinite recursion from circular references
    if (parentId && visitedNodes.has(parentId)) {
      return [];
    }

    if (parentId) {
      visitedNodes.add(parentId);
    }

    if (depth > 10) return [];

    const children = subscriptions.filter((item) => item.parentId === parentId);
    if (children.length === 0) return [];

    return children
      .filter(
        (child) =>
          !hasExactMatch || matchingPaths.has(child.id) || exactMatchDescendants.has(child.id),
      )
      .map((child) => {
        const item: SubscriptionTableItem = { ...subscriptionMap.get(child.id) };
        const childrenItems = buildHierarchy(child.id, depth + 1);

        if (childrenItems.length > 0) {
          item.children = childrenItems;
        }

        // if there is an exact match, and the current node is on the path but not a matching node, add disabled mark
        if (
          hasExactMatch &&
          matchingPaths.has(item.id) &&
          !matchingNodes.has(item.id) &&
          !exactMatchDescendants.has(item.id)
        ) {
          item.disabled = true;
        }

        return item;
      });
  };

  // get root nodes and build hierarchy
  const rootItems: SubscriptionTableItem[] = subscriptions
    .filter((item) => !item.parentId)
    .filter(
      (item) => !hasExactMatch || matchingPaths.has(item.id) || exactMatchDescendants.has(item.id),
    )
    .map((rootItem) => {
      const item = { ...subscriptionMap.get(rootItem.id) };
      const children = buildHierarchy(rootItem.id);

      if (children.length > 0) {
        item.children = children;
      }

      // add disabled mark to root nodes
      if (
        hasExactMatch &&
        matchingPaths.has(item.id) &&
        !matchingNodes.has(item.id) &&
        !exactMatchDescendants.has(item.id)
      ) {
        item.disabled = true;
      }

      return item;
    });

  return rootItems;
};

/**
 * Creates a PopoverButton configuration for transaction hub integration
 * @param params Configuration parameters for the PopoverButton
 * @returns PopoverButton configuration object
 */
const createTransactionHubPopoverButton = (params: {
  row: any;
  id: string;
  system: 'NetSuite' | 'QuickBooks';
  transactionType: string;
  transactionHubProps: any;
  handleTransfer: (row: any, system: string, transactionType: string) => void;
  netsuiteRedirectUrl?: string;
  snackbarService?: any;
}) => {
  const {
    row,
    id,
    system,
    transactionType,
    transactionHubProps,
    handleTransfer,
    netsuiteRedirectUrl,
  } = params;

  const isNetSuite = system === 'NetSuite';
  const iconType = isNetSuite ? 'NetSuite' : 'QuickBooks';
  const successColor = isNetSuite ? undefined : '#2CA01C';

  return {
    id: `view-${system}_${id}`,
    tooltipText: system,
    iconType, // Pass the icon type instead of the component
    iconAlt: system,
    style: {
      color:
        row.transactionHub?.find((s: TransactionDetail) => s.externalSystem === system)?.status ===
        'Transferred'
          ? successColor
          : '#D8D8D8',
    },
    PopoverComponent: TransactionDialog,
    popoverArgs: {
      ...transactionHubProps,
      transferItem: row,
      markTransferComplete: async (platform: string) => {
        if (params.snackbarService) {
          params.snackbarService.showSnackbar(
            'confirm',
            'Success',
            `The ${transactionType.toLowerCase()} ${row.name} is transferring to ${platform}, please refresh the page to see the transferring result in a few seconds`,
          );
        } else {
          console.log(
            `The ${transactionType.toLowerCase()} ${row.name} is transferring to ${platform}`,
          );
        }
      },
      handleTransferStatus: () => handleTransfer(row, system, transactionType),
      ...(isNetSuite && netsuiteRedirectUrl ? { netsuiteRedirectUrl } : {}),
      transferPlatform: system,
      transactionType,
    },
  };
};

const flattenSubscriptionHierarchy = (subscriptions: Subscription[]): SubscriptionTableItem[] => {
  const result: Subscription[] = [];

  const processObject = (subscription: SubscriptionTableItem) => {
    const { children, ...subscriptionWithoutChildren } = subscription;
    result.push(subscriptionWithoutChildren as Subscription);

    if (children && children.length > 0) {
      children.forEach((child) => {
        processObject(child);
      });
    }
  };

  subscriptions.forEach(processObject);

  return result;
};

export {
  calculateBarItemWidth,
  extractErrorMessage,
  showTransactionHub,
  getAddMultipleGroupsToCartResult,
  loadChildernAndBillingAccountsByIds,
  loadTransactions,
  buildSubscriptionHierarchy,
  createTransactionHubPopoverButton,
  flattenSubscriptionHierarchy,
};

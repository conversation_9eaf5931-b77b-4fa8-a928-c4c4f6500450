import {
  GetLookupOptionByValue,
  Lookup<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RubyField,
  RubyObject,
} from '../metadata/interface';

export type GetLookupObjectMetadata = (
  originObject: RubyObject,
  lookupField: RubyField,
) => Promise<RubyObject>;

export interface Props {
  open: boolean;
  originObject: RubyObject;
  lookupField: RubyField;
  lookupObject: RubyObject;
  onClose: () => void;
  lookupOptionsLoader: LookupOptionsLoader;
  getLookupOptionByValue: GetLookupOptionByValue;
  // getLookupObjectMetadata: GetLookupObjectMetadata
  // getInitialValue: () => Promise<any>
}

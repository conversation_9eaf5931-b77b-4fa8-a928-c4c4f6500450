import { ListViewLocalStateService } from '@nue-apps/ruby-ui-component';
import { RubyFilter } from '../graph-ql-query-constructor';
import GraphQLQueryConstructor from '../graph-ql-query-constructor';
import { RubyObject } from '../metadata';
import QueryExecutor from '../query-executor';
import { Subscription } from '../revenue-builder-types';
import { ExtraQuery } from '../ruby-list-view-local-state-context';
import { TabMode } from './interface';

export const filterKey = {
  subscriptions: {
    key: 'customer-view-grid-and-list-view-tab-2',
    id: 'subscription-order-filter',
  },
};

export const getOrderSubscriptionFilter = (subscriptionNumbers: string[]): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'name',
            name: 'Name',
            operand: ' _and ',
            operator: ' _in ',
            type: 'pickList',
            value: JSON.stringify(subscriptionNumbers),
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-order-subscription-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'orderOnDate',
        direction: 'desc',
      },
      {
        columnName: 'tcv',
        direction: 'desc',
      },
    ],
  };
};

export const getSubscriptionFilter = (subscriptionName: string): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'name',
            name: 'Subscription Number',
            operand: ' _and ',
            operator: ' _eq ',
            type: 'text',
            value: subscriptionName,
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-subscription-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'subscriptionStartDate',
        direction: 'desc',
      },
    ],
  };
};

export const getEntitlementFilter = (recordIds: string[]): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'originalEntitlementNumber',
            name: 'Original Entitlement Number',
            operand: ' _and ',
            operator: ' _in ',
            type: 'Picklist',
            value: JSON.stringify(recordIds),
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-entitlement-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'startDate',
        direction: 'desc',
      },
    ],
  };
};

export const getAssetsFilters = (recordIds: string[]): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'assetNumber',
            name: 'Asset Number',
            operand: ' _and ',
            operator: ' _in ',
            type: 'Picklist',
            value: JSON.stringify(recordIds),
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-assets-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'startDate',
        direction: 'desc',
      },
    ],
  };
};

export const getSubscriptionAssetsFilters = (recordIds: string[]): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'originalAssetNumber',
            name: 'Original Asset Number',
            operand: ' _and ',
            operator: ' _in ',
            type: 'Picklist',
            value: JSON.stringify(recordIds),
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-subscription-assets-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'startDate',
        direction: 'desc',
      },
    ],
  };
};

export const getInvoiceFilters = (invoiceIds: string[]): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'id',
            name: 'Record Id',
            operand: ' _and ',
            operator: ' _in ',
            type: 'Picklist',
            value:
              invoiceIds && invoiceIds.length > 0
                ? JSON.stringify(invoiceIds)
                : JSON.stringify(['Invalid']),
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-invoice-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'name',
        direction: 'desc',
      },
    ],
  };
};

export const getUsagefilter = (subscriptionNumbers: string[]): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'subscriptionNumber',
            name: 'Subscription Number',
            operand: ' _and ',
            operator: ' _in ',
            type: 'text',
            value: JSON.stringify(subscriptionNumbers),
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-usage-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'startTime',
        direction: 'desc',
      },
    ],
  };
};

export const getCreditsfilter = (subscriptionNumbers: string[]): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'assetNumber',
            name: 'Asset Number',
            operand: ' _and ',
            operator: ' _in ',
            type: 'text',
            value: JSON.stringify(subscriptionNumbers),
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-credits-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'creditStartDate',
        direction: 'desc',
      },
    ],
  };
};

export const getOrderUsagefilter = (orderNumber: string): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'orderNumber',
            name: 'Order Number',
            operand: ' _and ',
            operator: ' _eq ',
            type: 'text',
            value: `"${orderNumber}"`,
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-order-usage-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'startTime',
        direction: 'desc',
      },
    ],
  };
};

export const getOrderFilters = (orderId: string[]): ExtraQuery => {
  return {
    filtersToApply: [
      {
        isApplied: true,
        isUnsavedFilter: true,
        conditions: [
          {
            apiName: 'id',
            name: 'Order Id',
            operand: ' _and ',
            operator: ' _in ',
            type: 'pickList',
            value: JSON.stringify(orderId),
          },
        ],
        name: '',
        isExclusive: true,
        id: 'custom-order-filter',
      },
    ],
    orderByFields: [
      {
        columnName: 'activatedDate',
        direction: 'desc',
      },
    ],
  };
};

export const setupSubscriptionFilters = async (
  listViewLocalStateProvider: ListViewLocalStateService,
  subscriptionNumbers: Array<string>,
) => {
  const filterObject = filterKey['subscriptions'];

  const key = filterObject.key;
  if (
    !listViewLocalStateProvider.getLocalStateByKey ||
    !listViewLocalStateProvider.setLocalStateByKey
  ) {
    return;
  }
  let localState = await listViewLocalStateProvider.getLocalStateByKey(key);
  if (!localState) {
    return;
  }
  const subscriptionNumbersPicklistValue = `[${subscriptionNumbers.map(
    (subNumber) => `"${subNumber}"`,
  )}]`.replace(/,/g, ', ');
  localState.lastSearching = '';
  localState.filtersToApply = [
    {
      isApplied: true,
      isUnsavedFilter: true,
      conditions: [
        {
          apiName: 'name',
          name: 'Name',
          operand: ' _and ',
          operator: ' _in ',
          type: 'pickList',
          value: subscriptionNumbersPicklistValue,
        },
      ],
      name: 'Subscription Order Filter',
      isExclusive: true,
      id: filterObject.id,
    },
  ];
  localState.orderByFields = [
    {
      columnName: 'orderOnDate',
      direction: 'desc',
    },
    {
      columnName: 'tcv',
      direction: 'desc',
    },
  ];
  await listViewLocalStateProvider.setLocalStateByKey(key, localState);
};

export const getOriginalSubscriptionNumbers = async (
  executeQuery: QueryExecutor,
  orderNumber: string,
  customerId: string,
  subscriptionMetadata: RubyObject,
) => {
  const subscriptionFilter: RubyFilter = {
    isApplied: true,
    conditions: [
      {
        apiName: 'customerId',
        name: 'Customer Id',
        operand: ' _and ',
        operator: ' _eq ',
        type: 'id',
        value: `"${customerId}"`,
      },
      {
        apiName: 'orderNumber',
        name: 'Order Number',
        type: 'text',
        value: `"${orderNumber}"`,
        operator: ' _eq ',
        operand: ' _and ',
        nestedConditions: [],
      },
    ],
    name: 'Order Filter',
    isExclusive: true,
    id: 'order-filter',
  };
  const extraRelationFields = 'originalSubscription { name }';
  const query = GraphQLQueryConstructor.construct().query(
    subscriptionMetadata,
    subscriptionFilter.conditions || [],
    extraRelationFields,
  );

  const subscriptions = await executeQuery(
    query,
    subscriptionMetadata,
    [subscriptionFilter],
    extraRelationFields,
  );
  return subscriptions.map((sub: Subscription) =>
    sub.originalSubscription ? sub.originalSubscription.name : sub.name,
  );
};

export const getOriginalAssetNumbers = async (
  executeQuery: QueryExecutor,
  orderNumber: string,
  customerId: string,
  assetMetadata: RubyObject,
) => {
  const assetFilter: RubyFilter = {
    isApplied: true,
    conditions: [
      {
        apiName: 'customerId',
        name: 'Customer Id',
        operand: ' _and ',
        operator: ' _eq ',
        type: 'id',
        value: `"${customerId}"`,
      },
      {
        apiName: 'orderNumber',
        name: 'Order Number',
        type: 'text',
        value: `"${orderNumber}"`,
        operator: ' _eq ',
        operand: ' _and ',
        nestedConditions: [],
      },
    ],
    name: 'Order Filter',
    isExclusive: true,
    id: 'order-filter',
  };
  const query = GraphQLQueryConstructor.construct().query(
    assetMetadata,
    assetFilter.conditions || [],
  );

  const assets = await executeQuery(query, assetMetadata, [assetFilter]);

  return assets.map((sub: any) => sub.originalAssetNumber);
};

export const getOriginalEntitlementNumbers = async (
  executeQuery: QueryExecutor,
  orderNumber: string,
  customerId: string,
  entitlementMetadata: RubyObject,
) => {
  const entitlementFilter: RubyFilter = {
    isApplied: true,
    conditions: [
      {
        apiName: 'customerId',
        name: 'Customer Id',
        operand: ' _and ',
        operator: ' _eq ',
        type: 'id',
        value: `"${customerId}"`,
      },
      {
        apiName: 'orderNumber',
        name: 'Order Number',
        type: 'text',
        value: `"${orderNumber}"`,
        operator: ' _eq ',
        operand: ' _and ',
        nestedConditions: [],
      },
    ],
    name: 'Order Filter',
    isExclusive: true,
    id: 'order-filter',
  };
  const query = GraphQLQueryConstructor.construct().query(
    entitlementMetadata,
    entitlementFilter.conditions || [],
  );

  const entitlements = await executeQuery(query, entitlementMetadata, [entitlementFilter]);
  return entitlements.map((sub: any) => sub.originalEntitlementNumber);
};

export const getVersionedSubscriptions = async (
  executeQuery: QueryExecutor,
  subscriptionId: string,
  subscriptionMetadata: RubyObject,
  orderMetadata: RubyObject,
) => {
  const filter: RubyFilter = {
    isApplied: true,
    conditions: [
      {
        apiName: 'originalSubscriptionId',
        name: 'Original Subscription Id',
        type: 'text',
        value: `"${subscriptionId}"`,
        operator: ' _eq ',
        operand: ' _or ',
        nestedConditions: [],
      },
      {
        apiName: 'id',
        name: 'Id',
        type: 'text',
        value: `"${subscriptionId}"`,
        operator: ' _eq ',
        operand: ' _and ',
        nestedConditions: [],
      },
    ],
    name: 'Subscription Change History Filter',
    isExclusive: true,
    id: 'subscription-change-history-filter',
  };
  const orderByFields = [
    {
      columnName: 'subscriptionVersion',
      direction: 'ASC',
    },
  ];
  const extraRelationFields =
    'orderProduct { orderId subscriptionStartDate id productName orderProductNumber } originalSubscription { name }';
  const query = GraphQLQueryConstructor.construct().query(
    subscriptionMetadata,
    filter.conditions,
    extraRelationFields,
    orderByFields,
  );
  const versionedSubs = await executeQuery(
    query,
    subscriptionMetadata,
    [filter],
    extraRelationFields,
    orderByFields,
  );
  return versionedSubs.map((x: any) => x.orderProduct?.orderId);
};

export const getVersionedEntitlements = async (
  executeQuery: QueryExecutor,
  entitlementId: string,
  entitlementsMetadata: RubyObject,
  orderMetadata: RubyObject,
) => {
  const filter: RubyFilter = {
    isApplied: true,
    conditions: [
      {
        apiName: 'originalEntitlementId',
        name: 'Original Entitlement Id',
        type: 'text',
        value: `"${entitlementId}"`,
        operator: ' _eq ',
        operand: ' _or ',
        nestedConditions: [],
      },
      {
        apiName: 'id',
        name: 'Id',
        type: 'text',
        value: `"${entitlementId}"`,
        operator: ' _eq ',
        operand: ' _and ',
        nestedConditions: [],
      },
    ],
    name: 'Entitlement Change History Filter',
    isExclusive: true,
    id: 'entitlement-change-history-filter',
  };
  const orderByFields = [
    {
      columnName: 'entitlementVersion',
      direction: 'ASC',
    },
  ];
  const extraRelationFields =
    'orderProduct { orderId subscriptionStartDate id productName orderProductNumber }';
  const query = GraphQLQueryConstructor.construct().query(
    entitlementsMetadata,
    filter.conditions,
    extraRelationFields,
    orderByFields,
  );
  const versionedEnts = await executeQuery(
    query,
    entitlementsMetadata,
    [filter],
    extraRelationFields,
    orderByFields,
  );
  return versionedEnts.map((x: any) => x.orderProduct?.orderId);
};

export const getVersionedAssets = async (
  executeQuery: QueryExecutor,
  assetId: string,
  assetMetadata: RubyObject,
  orderMetadata: RubyObject,
) => {
  const filter: RubyFilter = {
    isApplied: true,
    conditions: [
      {
        apiName: 'originalAssetId',
        name: 'Original Asset Id',
        type: 'text',
        value: `"${assetId}"`,
        operator: ' _eq ',
        operand: ' _or ',
        nestedConditions: [],
      },
      {
        apiName: 'id',
        name: 'Id',
        type: 'text',
        value: `"${assetId}"`,
        operator: ' _eq ',
        operand: ' _and ',
        nestedConditions: [],
      },
    ],
    name: 'Asset Change History Filter',
    isExclusive: true,
    id: 'asset-change-history-filter',
  };
  const orderByFields = [
    {
      columnName: 'assetVersion',
      direction: 'ASC',
    },
  ];
  const extraRelationFields =
    'orderProduct { orderId subscriptionStartDate id productName orderProductNumber }';
  const query = GraphQLQueryConstructor.construct().query(
    assetMetadata,
    filter.conditions,
    extraRelationFields,
    orderByFields,
  );
  const versionedAssets = await executeQuery(
    query,
    assetMetadata,
    [filter],
    extraRelationFields,
    orderByFields,
  );
  return versionedAssets.map((x: any) => x.orderProduct?.orderId);
};

export const processInvoiceQuery = async (
  tabMode: TabMode,
  executeQuery: QueryExecutor,
  getInvoiceIdsForAsset: any,
  object: any,
  customerId: string,
  subscriptionMetadata: RubyObject,
  milestoneInvoiceIds?: string[],
) => {
  let shouldContinue = true;
  let extraQuery = null;
  if (tabMode === 'Subscription') {
    const invoiceIds = await getInvoiceIdsForAsset({
      assetNumbers: [object.name || ''],
      assetType: 'Subscription',
    });
    if (!invoiceIds || !invoiceIds.length) {
      shouldContinue = false;
    }
    extraQuery = getInvoiceFilters(invoiceIds);
  } else if (tabMode === 'Asset') {
    if (milestoneInvoiceIds && milestoneInvoiceIds?.length > 0) {
      extraQuery = getInvoiceFilters(milestoneInvoiceIds);
    } else {
      const invoiceIds = await getInvoiceIdsForAsset({
        assetNumbers: [object.assetNumber],
        assetType: 'Asset',
      });
      if (!invoiceIds || !invoiceIds.length) {
        shouldContinue = false;
      } else {
        shouldContinue = true;
      }
      extraQuery = getInvoiceFilters(invoiceIds);
    }
  } else if (tabMode === 'Entitlement') {
    if (milestoneInvoiceIds && milestoneInvoiceIds?.length > 0) {
      extraQuery = getInvoiceFilters(milestoneInvoiceIds);
    } else {
      const invoiceIds = await getInvoiceIdsForAsset({
        assetNumbers: [object.entitlementNumber],
        assetType: 'Entitlement',
      });
      if (!invoiceIds || !invoiceIds.length) {
        shouldContinue = false;
      } else {
        shouldContinue = true;
      }
      extraQuery = getInvoiceFilters(invoiceIds);
    }
  } else if (tabMode === 'Order') {
    const subscriptionNumbers = await getOriginalSubscriptionNumbers(
      executeQuery,
      object.number || object.orderNumber,
      customerId,
      subscriptionMetadata,
    );
    if (subscriptionNumbers && subscriptionNumbers.length) {
      const invoiceIds = await getInvoiceIdsForAsset({
        assetNumbers: subscriptionNumbers,
        assetType: 'Subscription',
      });
      if (!invoiceIds || !invoiceIds.length) {
        shouldContinue = false;
      } else {
        shouldContinue = true;
      }
      extraQuery = getInvoiceFilters(invoiceIds);
    } else {
      shouldContinue = false;
    }
  }
  return {
    shouldContinue,
    extraQuery,
  };
};

export const processOrderQuery = async (
  tabMode: TabMode,
  object: any,
  executeQuery: QueryExecutor,
  subscriptionMetadata: RubyObject,
  orderMetadata: RubyObject,
  entitlementMetadata: RubyObject,
  assetMetadata: RubyObject,
) => {
  let shouldContinue = true;
  let extraQuery = null;
  if (tabMode === 'Subscription') {
    const orderIds = await getVersionedSubscriptions(
      executeQuery,
      object.id,
      subscriptionMetadata,
      orderMetadata,
    );
    if (orderIds && orderIds.length) {
      extraQuery = getOrderFilters(orderIds);
    } else {
      shouldContinue = false;
    }
  } else if (tabMode === 'Asset') {
    const orderIds = await getVersionedAssets(
      executeQuery,
      object.id,
      assetMetadata,
      orderMetadata,
    );
    if (orderIds && orderIds.length) {
      extraQuery = getOrderFilters(orderIds);
    } else {
      shouldContinue = false;
    }
  } else if (tabMode === 'Entitlement') {
    const orderIds = await getVersionedEntitlements(
      executeQuery,
      object.id,
      entitlementMetadata,
      orderMetadata,
    );
    if (orderIds && orderIds.length) {
      extraQuery = getOrderFilters(orderIds);
    } else {
      shouldContinue = false;
    }
  } else {
    extraQuery = getOrderFilters([object.id]);
  }
  return {
    shouldContinue,
    extraQuery,
  };
};

export const processSubscriptionQuery = async (
  tabMode: TabMode,
  object: any,
  executeQuery: any,
  customerId: string,
  subscriptionMetadata: RubyObject,
) => {
  let shouldContinue = true;
  let extraQuery = null;
  if (tabMode === 'Subscription') {
    extraQuery = getSubscriptionFilter(object.name);
  } else if (tabMode === 'Entitlement' || 'Asset') {
    const subscriptionNumbers = await getOriginalSubscriptionNumbers(
      executeQuery,
      object.orderNumber,
      customerId,
      subscriptionMetadata,
    );
    if (subscriptionNumbers && subscriptionNumbers.length) {
      extraQuery = getOrderSubscriptionFilter(subscriptionNumbers);
    } else {
      shouldContinue = false;
    }
  } else {
    const subscriptionNumbers = await getOriginalSubscriptionNumbers(
      executeQuery,
      object.number,
      customerId,
      subscriptionMetadata,
    );
    if (subscriptionNumbers && subscriptionNumbers.length) {
      extraQuery = getOrderSubscriptionFilter(subscriptionNumbers);
      shouldContinue = true;
    } else {
      shouldContinue = false;
    }
  }
  return {
    shouldContinue,
    extraQuery,
  };
};

export const processAssetQuery = async (
  tabMode: TabMode,
  assetHierarchy: any,
  executeQuery: QueryExecutor,
  object: any,
  assetMetadata: RubyObject,
  customerId: string,
) => {
  let shouldContinue = true;
  let extraQuery = null;
  if (tabMode === 'Subscription') {
    if (assetHierarchy) {
      const recordIds = assetHierarchy
        .filter((x: any) => {
          return x.type === 'Asset';
        })
        .map((x: any) => x.id);
      if (recordIds && recordIds.length) {
        extraQuery = getSubscriptionAssetsFilters(recordIds || '');
      } else {
        shouldContinue = false;
      }
    } else {
      shouldContinue = false;
    }
  } else if (tabMode === 'Asset') {
    const result = await getOriginalAssetNumbers(
      executeQuery,
      object.orderNumber,
      customerId,
      assetMetadata,
    );
    if (result && result.length) {
      extraQuery = getAssetsFilters(result || '');
    } else {
      shouldContinue = false;
    }
  } else if (tabMode === 'Entitlement') {
    const result = await getOriginalAssetNumbers(
      executeQuery,
      object.orderNumber,
      customerId,
      assetMetadata,
    );
    if (result && result.length) {
      extraQuery = getAssetsFilters(result || '');
    } else {
      shouldContinue = false;
    }
  } else {
    const result = await getOriginalAssetNumbers(
      executeQuery,
      object.number,
      customerId,
      assetMetadata,
    );
    if (result && result.length) {
      extraQuery = getAssetsFilters(result || '');
    } else {
      shouldContinue = false;
    }
  }
  return {
    shouldContinue,
    extraQuery,
  };
};

export const processEntitlementQuery = async (
  tabMode: TabMode,
  assetHierarchy: any,
  executeQuery: QueryExecutor,
  object: any,
  entitlementMetadata: RubyObject,
  customerId: string,
) => {
  let shouldContinue = true;
  let extraQuery = null;

  if (tabMode === 'Subscription') {
    if (assetHierarchy) {
      const recordIds = assetHierarchy
        .filter((x: any) => {
          return x.type === 'Entitlement';
        })
        .map((x: any) => x.id);

      if (recordIds && recordIds.length) {
        extraQuery = getEntitlementFilter(recordIds || '');
      } else {
        shouldContinue = false;
      }
    } else {
      shouldContinue = false;
    }
  } else if (tabMode === 'Asset' || tabMode === 'Entitlement') {
    const result = await getOriginalEntitlementNumbers(
      executeQuery,
      object.orderNumber,
      customerId,
      entitlementMetadata,
    );
    if (result && result.length) {
      extraQuery = getEntitlementFilter(result || '');
    } else {
      shouldContinue = false;
    }
  } else {
    const result = await getOriginalEntitlementNumbers(
      executeQuery,
      object.number,
      customerId,
      entitlementMetadata,
    );
    if (result && result.length) {
      extraQuery = getEntitlementFilter(result || '');
    } else {
      shouldContinue = false;
    }
  }
  return {
    shouldContinue,
    extraQuery,
  };
};

export const processUsageQuery = async (
  tabMode: TabMode,
  object: any,
  executeQuery: QueryExecutor,
  customerId: string,
  subscriptionMetadata: RubyObject,
) => {
  let shouldContinue = true;
  let extraQuery = null;

  if (tabMode === 'Subscription') {
    extraQuery = getUsagefilter([object.name || '']);
  } else if (tabMode === 'Asset' || tabMode === 'Entitlement') {
    const subscriptionNumbers = await getOriginalSubscriptionNumbers(
      executeQuery,
      object.orderNumber,
      customerId,
      subscriptionMetadata,
    );

    if (!subscriptionNumbers || !subscriptionNumbers.length) {
      shouldContinue = false;
    }

    extraQuery = getUsagefilter(subscriptionNumbers);
  } else {
    const subscriptionNumbers = await getOriginalSubscriptionNumbers(
      executeQuery,
      object.number,
      customerId,
      subscriptionMetadata,
    );

    if (!subscriptionNumbers || !subscriptionNumbers.length) {
      shouldContinue = false;
    }

    extraQuery = getUsagefilter(subscriptionNumbers);
  }

  return {
    shouldContinue,
    extraQuery,
  };
};

export const processCreditQuery = async (
  tabMode: TabMode,
  object: any,
  executeQuery: QueryExecutor,
  customerId: string,
  subscriptionMetadata: RubyObject,
) => {
  let shouldContinue = true;
  let extraQuery = null;

  if (tabMode === 'Subscription') {
    extraQuery = getCreditsfilter([object.name || '']);
  } else if (tabMode === 'Asset' || tabMode === 'Entitlement') {
    const subscriptionNumbers = await getOriginalSubscriptionNumbers(
      executeQuery,
      object.orderNumber,
      customerId,
      subscriptionMetadata,
    );

    if (!subscriptionNumbers || !subscriptionNumbers.length) {
      shouldContinue = false;
    }

    extraQuery = getCreditsfilter(subscriptionNumbers);
  } else {
    const subscriptionNumbers = await getOriginalSubscriptionNumbers(
      executeQuery,
      object.number,
      customerId,
      subscriptionMetadata,
    );

    if (!subscriptionNumbers || !subscriptionNumbers.length) {
      shouldContinue = false;
    }

    extraQuery = getCreditsfilter(subscriptionNumbers);
  }

  return {
    shouldContinue,
    extraQuery,
  };
};

export const processQueryForIndividualTabMode = async (
  filterTabName: string,
  activeTabDetail: any,
  executeQuery: QueryExecutor,
  customerId: string,
  getInvoiceIdsForAsset: any,
  subscriptionMetadata: RubyObject,
  orderMetadata: RubyObject,
  assetMetadata: RubyObject,
  entitlementMetadata: RubyObject,
) => {
  let result: { shouldContinue: boolean; extraQuery: ExtraQuery | null } = {
    extraQuery: null,
    shouldContinue: true,
  };
  if (filterTabName === 'invoices') {
    result = await processInvoiceQuery(
      activeTabDetail.tabMode,
      executeQuery,
      getInvoiceIdsForAsset,
      activeTabDetail.argument.object,
      customerId,
      subscriptionMetadata,
      activeTabDetail.argument.action?.invoiceIds || [],
    );
  } else if (filterTabName === 'usages') {
    result = await processUsageQuery(
      activeTabDetail.tabMode,
      activeTabDetail.argument.object,
      executeQuery,
      customerId,
      subscriptionMetadata,
    );
  } else if (filterTabName === 'orders') {
    result = await processOrderQuery(
      activeTabDetail.tabMode,
      activeTabDetail.argument.object,
      executeQuery,
      subscriptionMetadata,
      orderMetadata,
      entitlementMetadata,
      assetMetadata,
    );
  } else if (filterTabName === 'subscriptions') {
    result = await processSubscriptionQuery(
      activeTabDetail.tabMode,
      activeTabDetail.argument.object,
      executeQuery,
      customerId,
      subscriptionMetadata,
    );
  } else if (filterTabName === 'assets') {
    result = await processAssetQuery(
      activeTabDetail.tabMode,
      activeTabDetail.argument.assetHierarchy,
      executeQuery,
      activeTabDetail.argument.object,
      assetMetadata,
      customerId,
    );
  } else if (filterTabName === 'entitlements') {
    result = await processEntitlementQuery(
      activeTabDetail.tabMode,
      activeTabDetail.argument.assetHierarchy,
      executeQuery,
      activeTabDetail.argument.object,
      entitlementMetadata,
      customerId,
    );
  } else if (filterTabName === 'credits') {
    result = await processCreditQuery(
      activeTabDetail.tabMode,
      activeTabDetail.argument.object,
      executeQuery,
      customerId,
      subscriptionMetadata,
    );
  }

  return result;
};

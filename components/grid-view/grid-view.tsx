import React, { useMemo } from 'react';

import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import { Pagination } from '@material-ui/lab';

import type { CellRendererRowProps } from '../grid-actions';
import type { Props } from './interface';
import { useProductRelations } from '../customer-view/hooks';
import { Subscription } from '../revenue-builder-types/interface';
import { flattenSubscriptionHierarchy } from '../customer-view/utils/utils';

const defaultProps = {};

const useStyles = makeStyles({
  container: {
    display: 'flex',
  },
  noItems: {
    display: 'flex',
    justifyContent: 'center',
    color: '#333',
    fontSize: '1rem',
  },
});

export const GridView: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const {
    objects,
    objectMetadata,
    gridViewMetadata,
    fieldSetMetadata,
    currencyIsoCode,
    handleCardClick,
    orderGridViewMetadata,
    orderFieldSetMetadata,
    subscriptionPricingFieldSet,
    actionEventHandler,
    getFieldSetMetadata,
    GridViewItem,
    showDetails,
    onShowDetails,
    rubySettings,
    locale,
    enablePagination,
    totalCount,
    currentPage,
    pageSize,
    handlePageSizeChange,
    handleCurrentPageChange,
    addTransactionHubColumn,
    currentTabName,
    getSwapUpgradeDowngradeOptions,
  } = props;

  const classes = useStyles();

  const flattenedObjects = useMemo(() => {
    if (currentTabName === 'Subscriptions') {
      return flattenSubscriptionHierarchy(objects);
    }

    return [];
  }, [currentTabName, objects]);

  const { productRelationsByActionType } = useProductRelations(
    currentTabName === 'Subscriptions' ? flattenedObjects : [],
    getSwapUpgradeDowngradeOptions,
  );

  const handleChange = (_: any, value: any) => {
    if (handleCurrentPageChange) {
      handleCurrentPageChange(value - 1);
    }
  };

  const getObject = () => {
    if (!enablePagination) {
      return objects;
    }

    const newObj = [...objects];
    const nCurrentPage = currentPage || 0;
    const nPageSize = pageSize || 0;
    return newObj.slice(nCurrentPage * nPageSize, (nCurrentPage + 1) * nPageSize);
  };

  const getPagination = () => {
    if (!enablePagination) {
      return null;
    }

    const totalPage = Math.ceil((totalCount || 0) / (pageSize || 1));

    return (
      <div style={{ width: '100%', display: 'flex', justifyContent: 'center', marginTop: 20 }}>
        <Pagination
          count={totalPage}
          defaultPage={(currentPage || 0) + 1}
          color="primary"
          onChange={handleChange}
        />
      </div>
    );
  };

  if (objects.length === 0) {
    return <p className={classes.noItems}>No data</p>;
  }

  return (
    <>
      <Grid container className={classes.container} role="grid-view">
        {getObject().map((object: any) => (
          <GridViewItem
            handleCardClick={handleCardClick}
            key={object.id}
            rubySettings={rubySettings}
            fieldSetMetadata={fieldSetMetadata}
            gridViewMetadata={gridViewMetadata}
            object={object}
            handlePageSizeChange={handlePageSizeChange}
            pageSize={pageSize}
            subscriptionPricingFieldSet={subscriptionPricingFieldSet}
            currencyIsoCode={currencyIsoCode}
            objectMetadata={objectMetadata}
            orderGridViewMetadata={orderGridViewMetadata}
            orderFieldSetMetadata={orderFieldSetMetadata}
            actionEventHandler={actionEventHandler}
            getFieldSetMetadata={getFieldSetMetadata}
            showDetails={showDetails}
            onShowDetails={(object: any) => {
              if (onShowDetails) {
                onShowDetails(object);
              }
            }}
            locale={locale}
            transactionHub={addTransactionHubColumn?.().cellRenderer({
              row: object,
            } as CellRendererRowProps)}
            productRelationsByActionType={productRelationsByActionType}
          />
        ))}
      </Grid>
      {getPagination()}
    </>
  );
};

export default GridView;

import React, { useState } from 'react';
import { ChangeSet, CustomPaging, EditingState, PagingState } from '@devexpress/dx-react-grid';
import { DataTypeProvider, TableColumnWidthInfo } from '@devexpress/dx-react-grid';
import {
  PagingPanel,
  Table,
  TableColumnResizing,
  TableEditRow,
  Grid as TableGrid,
  TableHeaderRow,
} from '@devexpress/dx-react-grid-material-ui';
import { ColumnConfig } from '@nue-apps/ruby-ui-component';
import { DateTimeFormatter, DateTimeTypeProvider } from '../ruby-grid/data-providers';
import TableComponent from '../table-component';
import EditColumn from './edit-column';
import { QBRowEditStateFlag, ValidationStatusType } from './interface';
import TableRow from './table-row';

let clicked = {};

export interface Props {
  columns: ColumnConfig[];
  rows: any[];
  setRows: React.Dispatch<React.SetStateAction<any[]>>;
  columnWidths?: TableColumnWidthInfo[];
  handleCurrentPageChange?: (newPage: number) => void;
  totalCount?: number;
  pageSize: number;
  defaultPageSizes?: number[];
  handlePageSizeChange?: (pageSize: number) => void;
  handleEditRows: (rows: any[]) => void;
  handleDeleteRow: (row: any, type: string) => void;
  type: string;
}

const notNullFaileds = ['sourceCustomerId', 'paymentCustomerId'];
const notNullProductFaileds = ['sourceProductId', 'paymentProductId'];

const EditCell = (props: any) => {
  const { value, row } = props;
  let style: React.CSSProperties = {};
  //@ts-ignore
  const isRowClicked = clicked[row.id] && notNullFaileds.find((x) => x === props.column.apiName);

  style = {
    ...(!value ? { border: '1px solid red' } : null),
  };

  if (props.column.apiName === 'createdTime') {
    return (
      <Table.Cell {...props} style={style}>
        {/* @ts-ignore */}
        {DateTimeFormatter({ value: value })}
      </Table.Cell>
    );
  }

  if (
    props.column.apiName === 'sourceCustomerName' ||
    props.column.apiName === 'paymentCustomerName' ||
    props.column.apiName === 'sourceProductName' ||
    props.column.apiName === 'paymentProductName'
  ) {
    return (
      <Table.Cell {...props} style={style}>
        {value}
      </Table.Cell>
    );
  }

  return <TableEditRow.Cell {...props} style={isRowClicked ? style : {}} />;
};

const QuickbooksMappingGrid: React.FC<Props> = (userProps) => {
  const {
    columns,
    rows,
    setRows,
    columnWidths,
    handleCurrentPageChange,
    pageSize,
    handlePageSizeChange,
    totalCount,
    defaultPageSizes,
    handleEditRows,
    handleDeleteRow,
    type,
  } = userProps;
  const [rowChanges, setRowChanges] = useState({});
  const [editingRowIds, setEditingRowIds] = useState([]);
  const [validationStatus, setValidationStatus] = useState<ValidationStatusType>(new Map());
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSizes, setPageSizes] = useState([5, 10, 20, 40, 100]);
  const [deletedRowIds, setDeletedRowIds] = React.useState<(number | string)[]>([]);

  const onCurrentPageChange = (newPage: number) => {
    setCurrentPage(newPage);
    if (handleCurrentPageChange) {
      handleCurrentPageChange(newPage);
    }
  };

  React.useEffect(() => {
    if (defaultPageSizes && defaultPageSizes.length) {
      setPageSizes(defaultPageSizes);
    }
  }, [defaultPageSizes]);

  const getRowId = (row: any) => {
    return row.id;
  };

  const commitChanges = async (changes: ChangeSet) => {
    const { changed } = changes;
    let changedRows = [];
    const rowsForUpdate: any[] = [];
    if (changed) {
      changedRows = rows.map((row) => {
        if (changed[row.id]) {
          const next = { ...row, ...changed[row.id] };
          rowsForUpdate.push(next);
          return next;
        } else {
          return row;
        }
      });
    }

    rowsForUpdate.forEach((x) => {
      if (!x[QBRowEditStateFlag]) {
        x[QBRowEditStateFlag] = 'changed';
      }
    });

    if (rowsForUpdate.length > 0) {
      try {
        await handleEditRows(rowsForUpdate);
        setRows(changedRows);
      } catch (err) {}
    }
  };

  const forTypes = (types: string[]) => {
    return columns.filter((_) => types.indexOf(_.type) >= 0).map((_) => _.name);
  };

  const columnExtensions: EditingState.ColumnExtension[] = columns.map((col) => {
    return {
      columnName: col.name,
      editingEnabled: true,
    };
  });

  return (
    <TableGrid rows={rows} columns={columns} getRowId={getRowId}>
      <DateTimeTypeProvider for={forTypes(['dateTime'])} />
      <PagingState
        currentPage={currentPage}
        onCurrentPageChange={onCurrentPageChange}
        pageSize={pageSize}
        onPageSizeChange={(newPageSize) => {
          if (handlePageSizeChange) {
            handlePageSizeChange(newPageSize);
          }
        }}
      />
      <EditingState
        editingRowIds={editingRowIds}
        rowChanges={rowChanges}
        onRowChangesChange={(data) => {
          setRowChanges(data);
        }}
        onEditingRowIdsChange={(newEditingRowIds) => {
          //@ts-ignore
          setEditingRowIds(newEditingRowIds);
        }}
        onCommitChanges={commitChanges}
        onDeletedRowIdsChange={(toDeleteRowIds) => {
          setDeletedRowIds([...deletedRowIds, ...toDeleteRowIds]);
        }}
        onAddedRowsChange={() => {
          //no need for now
        }}
        columnExtensions={columnExtensions}
      />
      <CustomPaging totalCount={totalCount} />

      <Table tableComponent={TableComponent} rowComponent={TableRow} />
      {columnWidths && <TableColumnResizing defaultColumnWidths={columnWidths} />}
      <TableHeaderRow />

      <TableEditRow cellComponent={EditCell} />
      <EditColumn
        onDeleteRow={async ({ row }) => {
          if (row) {
            await handleDeleteRow(row, type);
            const newRows = rows.filter((x) => x.id !== row.id);
            setRows(newRows);
          }
        }}
        onCommitRow={({ row }) => {
          //@ts-ignore
          const changedRow = rowChanges[row.id];
          if (changedRow) {
            const rowAfterEdilt = {
              ...row,
              ...changedRow,
            };
            const newValidationStatus = new Map();

            if (type === 'customer') {
              notNullFaileds.forEach((key) => {
                if (!rowAfterEdilt[key]) {
                  newValidationStatus.set(rowAfterEdilt.id, {
                    key: {
                      isvalid: false,
                    },
                  });
                }
              });
            } else {
              notNullProductFaileds.forEach((key) => {
                if (!rowAfterEdilt[key]) {
                  newValidationStatus.set(rowAfterEdilt.id, {
                    key: {
                      isvalid: false,
                    },
                  });
                }
              });
            }

            if (newValidationStatus.size > 0) {
              setValidationStatus(newValidationStatus);
              clicked = {
                ...clicked,
                [row.id]: true,
              };
              return false;
            }
          }
          clicked = {
            ...clicked,
            [row.id]: false,
          };
          return true;
        }}
      />
      <PagingPanel pageSizes={pageSizes} />
    </TableGrid>
  );
};

export default QuickbooksMappingGrid;

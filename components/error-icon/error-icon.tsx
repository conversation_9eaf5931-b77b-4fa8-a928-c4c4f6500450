import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { Props } from './interface';

const defaultProps = {};

const ErrorIcon: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  return (
    <SvgIcon viewBox={props.viewBox}>
      <svg width="24px" height="24px" viewBox="0 0 32 32" version="1.1">
        <title>1F0C05E5-E9E0-4B83-8E0B-28CE869F89DA</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-7-1-Product-Configurator" transform="translate(-117.000000, -1503.000000)">
            <g id="error" transform="translate(81.000000, 1479.000000)">
              <g id="Group-15" transform="translate(36.000000, 24.000000)">
                <circle
                  id="Oval"
                  fill={props.styles?.color || '#E12D38'}
                  opacity="0.100000001"
                  cx="16"
                  cy="16"
                  r="16"
                />
                <g id="close-line" transform="translate(4.000000, 4.000000)">
                  <polygon id="Path" points="0 0 24 0 24 24 0 24" />
                  <polygon
                    id="Path"
                    fill={props.styles?.color || '#E12D38'}
                    fillRule="nonzero"
                    points="12 10.586 16.95 5.636 18.364 7.05 13.414 12 18.364 16.95 16.95 18.364 12 13.414 7.05 18.364 5.636 16.95 10.586 12 5.636 7.05 7.05 5.636"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default ErrorIcon;

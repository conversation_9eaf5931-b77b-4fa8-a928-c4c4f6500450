import React from 'react';

export type Placement = 'top' | 'bottom' | 'right' | 'left';

export interface HintStep {
  anchorEl: (() => HTMLElement) | string | null;
  message: string;
  placement?: Placement;
}

export interface HintProps {
  message: string;
  open: boolean;
  anchorEl: null | HTMLElement;
  title?: string;
  hintIcon?: React.ReactNode;
  backgroundColor?: string;
  action?: React.ReactNode | null;
  onClose?: () => void;
  placement?: Placement;
}

export interface HintFlowProps {
  open: boolean;
  steps: HintStep[];
  onFlowEnd: () => void;
  autoScroll?: boolean;
}

import React, { ChangeEvent, useContext, useEffect, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import {
  FormControl,
  Typography,
  Grid,
  TextField,
  Chip,
  IconButton,
  Tooltip,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import AddCircleOutlineIcon from '@material-ui/icons/AddCircleOutline';
import HighlightOffIcon from '@material-ui/icons/HighlightOff';
import shortUUID from 'short-uuid';
import * as yup from 'yup';
import ConditionsEmpty from '../../static/images/webhook-eventlogs-empty.png';
import Autocomplete from '../autocomplete';
import ClickAwayListener from '../click-away-listener';
import DialogComponent from '../dialog-component';
import FilterBuilder, { InvokeObject } from '../filter-builder';
import FilterIcon from '../filter-icon';
import FormSection, { CustomFieldControlRegistry, CustomFormControlProps } from '../form-section';
import { useYupValidationResolver } from '../form-validation';
import { RubyFilter } from '../graph-ql-query-constructor';
import { DeleteBucketIcon as DeleteIcon, EditBucketIcon as EditIcon, InfoIcon } from '../icons';
import LabelWithTooltip from '../label-with-tooltip';
import { RubyObject } from '../metadata';
import { PickListOption } from '../pick-list';
import { RubyButtonBar } from '../ruby-button';
import { useRubySnackbar } from '../ruby-notifier';
import { BillingSettingsContext } from '../ruby-settings';
import { RubyField } from '../metadata';
import {
  GroupingAttribute,
  Props,
  EmptyGroupingAttributeConditionsProps,
  GroupingAttributeConditionItemProps,
  GroupingConditionItem,
  GroupingAttributeFilterProps,
} from './interface';
import WarningIcon from '@material-ui/icons/Warning';

const useStyles = makeStyles({
  sectionTitle: {
    opacity: 0.7,
    color: '#000000',
    fontSize: '18px',
    fontWeight: 500,
    letterSpacing: '-0.77px',
    lineHeight: '25px',
    marginBottom: '10px',
  },
  emptyImg: {
    width: '100px',
    height: '100px',
    marginBottom: '20px',
  },
  emptyContainer: {
    marginTop: '20px',
  },
  addConditions: {
    color: '#6239eb',
    textDecoration: 'underline',
    cursor: 'pointer',
  },
  conditionItemWrapper: {
    border: '1px solid #E5E5E5',
    padding: '20px 25px',
    borderRadius: '5px',
    overflow: 'hidden',
    minHeight: '60px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-around',
    marginTop: '8px',
    marginBottom: '8px',
    position: 'relative',
  },
  statusBar: {
    width: '5px',
    position: 'absolute',
    left: '0',
    top: '0',
    bottom: '0',
    borderRadius: '5px 0 0 5px',
    backgroundColor: '#6239EB',
  },
  conditionItemContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  conditionName: {},
  flex: {
    flex: 1,
  },
  conditionLabel: {
    width: '150px',
    textTransform: 'uppercase',
    fontWeight: 'bold',
  },
  conditionLabelName: {
    fontWeight: 'bold',
    fontSize: '0.8rem',
  },
  deleteConditionItem: {
    position: 'absolute',
    top: '0',
    right: '-7px',
    cursor: 'pointer',
    color: 'red',
    background: 'white',
  },
  addNewConditionContainer: {
    display: 'flex',
    justifyContent: 'center',
  },
  inputText: {
    borderRadius: 0,
    border: 0,
    borderBottom: '1px solid #6239eb',
    fontSize: '1.5rem',
    fontWeight: 400,
    lineHeight: 1.5,
    '&:focus': {
      borderRadius: 0,
    },
  },
});

const defaultProps = {
  value: {
    id: '',
    name: '',
    description: '',
    groupingField: '',
    status: 'Inactive',
    isStandard: false,
    conditions: [],
  },
  mode: 'create',
};

const groupingAttributeMetadata: RubyObject = {
  apiName: 'pricingAttribute',
  name: 'Pricing Attribute',
  fields: [
    {
      name: 'Id',
      type: 'String',
      apiName: 'id',
    },
    {
      name: 'Status',
      type: 'String',
      apiName: 'status',
    },
    {
      name: 'Is Standard',
      type: 'boolean',
      apiName: 'isStandard',
    },
    {
      name: 'Name',
      apiName: 'name',
      type: 'text',
      required: true,
      updatable: true,
      creatable: true,
    },
    {
      name: 'Grouping Field',
      apiName: 'groupingField',
      type: 'text',
      required: true,
      creatable: true,
      updatable: true,
    },
    {
      name: 'Description',
      apiName: 'description',
      type: 'longText',
      updatable: true,
      creatable: true,
    },
    {
      name: 'Conditions',
      apiName: 'conditions',
      type: 'conditions',
      updatable: true,
      creatable: true,
    },
  ],
};

interface Condition {
  property: string;
  value: string;
}

interface AdvancedFilter {
  id: string;
  name: string;
  isApplied: boolean;
  conditions: Condition[];
}

interface FilterForUi {
  simple: any[];
  advanced: AdvancedFilter[];
}

interface Values {
  customer: {
    filter: string;
    filterForUi: FilterForUi;
  };
}

const validationSchema = yup.object().shape({
  apiName: yup.string().required(),
  name: yup.string().required(),
  groupingField: yup.string().required(),
  description: yup.string().nullable(),
});

export const EmptyGroupingAttributeConditions: React.FC<EmptyGroupingAttributeConditionsProps> = (
  props,
) => {
  const classes = useStyles();
  const { initGroupingAttributeConditions } = props;
  return (
    <Grid
      className={classes.emptyContainer}
      container
      direction="column"
      justifyContent="center"
      alignItems="center"
    >
      <Grid direction="row" container justifyContent="center" alignItems="center">
        <img alt="image" src={ConditionsEmpty} className={classes.emptyImg} />
      </Grid>
      <Grid item>
        <Typography
          className={classes.addConditions}
          onClick={() => initGroupingAttributeConditions()}
        >
          Add Conditions
        </Typography>
      </Grid>
    </Grid>
  );
};

export const GroupingAttributeFilter = (props: GroupingAttributeFilterProps) => {
  const {
    open,
    onClose,
    groupingField,
    condition,
    handleChange,
    conditionArray,
    field,
    customFieldsOfAssetData,
  } = props;
  const classes = useStyles();
  const [label, setLabel] = useState(condition.name);

  useEffect(() => {
    if (!open) {
      setLabel('');
    } else {
      setLabel(condition.name);
    }
  }, [open]);

  const billingSettingsCtx = useContext(BillingSettingsContext);
  let billingAssetMetadata: RubyObject;
  let updateBillingAssetMetdata: RubyObject;
  if (billingSettingsCtx) {
    //@ts-ignore
    billingAssetMetadata = billingSettingsCtx.metadataObjects.find(
      (metadataObject: any) => metadataObject.apiName === 'BillingAsset',
    );
    if (groupingField) {
      if (customFieldsOfAssetData) {
        const addedApiNames = new Set();
        const allFields = [
          ...(customFieldsOfAssetData.subscriptionFields?.filter((field: RubyField) => {
            if (addedApiNames.has(field.apiName)) return false;
            addedApiNames.add(field.apiName);
            return true;
          }) || []),
          ...(customFieldsOfAssetData.assetFields?.filter((field: RubyField) => {
            if (addedApiNames.has(field.apiName)) return false;
            addedApiNames.add(field.apiName);
            return true;
          }) || []),
          ...(customFieldsOfAssetData.entitlementFields?.filter((field: RubyField) => {
            if (addedApiNames.has(field.apiName)) return false;
            addedApiNames.add(field.apiName);
            return true;
          }) || []),
        ];
        billingAssetMetadata = {
          ...billingAssetMetadata,
          fields: [...(billingAssetMetadata?.fields || []), ...allFields],
        };
      }
      updateBillingAssetMetdata = {
        ...billingAssetMetadata,
        fields: billingAssetMetadata.fields?.filter(
          (f) => f.apiName === groupingField.split('.')[1],
        ),
      };
    }
  }

  const [isSubmitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const { Snackbar, showSnackbar } = useRubySnackbar();

  const [referencedCustomerFilter, setReferencedCustomerFilter] = useState<RubyFilter | null>(null);
  const [customerSavedFilters, setCustomerSavedFilters] = useState<Array<RubyFilter>>([
    {
      name: condition.name,
      id: condition.id || shortUUID().generate().toString() + '_temp',
      conditions: condition.filters || [],
      isApplied: true,
    },
  ]);
  const [isEditing, setIsEditing] = useState(false);

  const [customerFilter, setCustomerFilter] = useState({
    simple: [],
    advanced: [],
  });
  const customerInvokeObject: InvokeObject = {
    validateAllConditions: () => {},
    graphqlGenerator: () => {},
  };
  const [temporaryCustomerFilter, setTemporaryCustomerFilter] = useState();
  return (
    <DialogComponent
      open={open}
      handleClose={onClose}
      width="md"
      //@ts-ignore
      title={' '}
      loading={open && loading}
      loadingText="Retrieving..."
    >
      <Grid item xs={12} lg={12}>
        {isEditing ? (
          <ClickAwayListener onClickAway={() => setIsEditing(false)}>
            <TextField
              value={label}
              inputProps={{
                style: {
                  fontSize: '1.5rem',
                  fontWeight: 400,
                  lineHeight: 1.5,
                },
                maxLength: 60,
              }}
              autoFocus={true}
              onChange={(e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
                setLabel(e.target.value);
              }}
              maxRows={5}
              placeholder="Filter Name"
            />
          </ClickAwayListener>
        ) : (
          <Grid
            style={{
              display: 'flex',
              padding: '6px 0 7px',
              fontSize: '1.5rem',
              fontWeight: 400,
            }}
          >
            <span style={{ color: label ? 'black' : 'grey' }}>{label || 'Filter Name'}</span>
            <span
              onClick={() => {
                if (field.creatable) {
                  setIsEditing(true);
                }
              }}
              style={{
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                marginLeft: '10px',
              }}
            >
              <EditIcon />
            </span>
          </Grid>
        )}
        <FilterBuilder
          objectMetadata={updateBillingAssetMetdata!}
          referencedFilter={referencedCustomerFilter}
          filterName={''}
          savedFilters={customerSavedFilters}
          searchResult=""
          hiddenButton={true}
          graphqlGenerator={true}
          setGraphqlGeneratorResult={setCustomerFilter}
          disabled={!field.creatable}
          setTemporaryFilter={setTemporaryCustomerFilter}
          temporaryFilter={temporaryCustomerFilter}
          invokeObject={customerInvokeObject}
          hideNestedCondition={true}
        />
      </Grid>
      <Grid item xs={12}>
        <RubyButtonBar
          variant="inPlace"
          processing={isSubmitting}
          leftButtons={[
            {
              text: 'Cancel',
              onClick: onClose,
            },
          ]}
          rightButtons={[
            {
              text: 'Save and Close',
              disabled: isSubmitting,
              onClick: async () => {
                if (!label) {
                  showSnackbar('error', 'Error', 'The filter name cannot be empty.');
                  setIsEditing(true);
                  return;
                }
                const customerConditions = customerInvokeObject.validateAllConditions();
                if (customerConditions?.length === 0) {
                  showSnackbar('error', 'Error', 'The filter conditions cannot be empty.');
                  return;
                }
                setSubmitting(true);
                condition.name = label;
                const graphql = customerInvokeObject.graphqlGenerator();
                condition.filters = customerConditions;
                try {
                  handleChange([...conditionArray]);
                } catch (e) {
                } finally {
                  setSubmitting(false);
                  onClose();
                }
              },
            },
          ]}
        />
      </Grid>
      <Snackbar />
    </DialogComponent>
  );
};

export const GroupingAttributeConditionItem: React.FC<GroupingAttributeConditionItemProps> = (
  props,
) => {
  const {
    condition,
    conditionArray,
    index,
    onChange,
    groupingField,
    field,
    customFieldsOfAssetData,
  } = props;
  const [open, setOpen] = useState(false);
  const { name, filters } = condition;
  const classes = useStyles();
  const [isEditing, setIsEditing] = useState(false);
  const isFirstCondition = index === 0;
  const isLastCondition = index === conditionArray.length - 1;
  const isMiddleCondition = index !== 0 && index !== conditionArray.length - 1;
  const getConditionLabel = () => {
    if (isFirstCondition) {
      return ['if', 'then'];
    }
    if (isMiddleCondition) {
      return ['else if', 'then'];
    }
    if (isLastCondition) {
      return ['otherwise'];
    }
    return ['else if', 'then'];
  };
  const addNewCondition = () => {
    if (field.creatable) {
      conditionArray.splice(index + 1, 0, {
        name: '',
        filters: [],
        id: shortUUID().generate().toString() + '_temp',
      });
      onChange([...conditionArray]);
    }
  };

  const deleteCondition = () => {
    if (field.creatable) {
      if (index === 0) {
        onChange([]);
        return;
      }
      conditionArray.splice(index, 1);
      if (conditionArray.length === 1) {
        onChange([]);
      } else {
        onChange([...conditionArray]);
      }
    }
  };

  const openFiltersPage = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  return (
    <div
      style={{
        position: 'relative',
      }}
    >
      <Grid className={classes.conditionItemWrapper}>
        <GroupingAttributeFilter
          open={open}
          onClose={onClose}
          groupingField={groupingField}
          condition={condition}
          handleChange={onChange}
          conditionArray={conditionArray}
          field={field}
          customFieldsOfAssetData={customFieldsOfAssetData}
        />
        <Grid className={classes.statusBar} />
        <Grid container className={classes.conditionItemContainer}>
          <Grid className={classes.conditionLabel}>
            <Typography className={classes.conditionLabelName}>{getConditionLabel()[0]}</Typography>
          </Grid>
          <Grid className={classes.flex}>
            <Typography className={classes.conditionName}>
              {name || '< Please add filters >'}
            </Typography>
          </Grid>
          {index !== conditionArray.length - 1 && (
            <Grid
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
              }}
            >
              <Chip
                variant="outlined"
                size="small"
                avatar={<FilterIcon />}
                label="Filters"
                classes={
                  {
                    // root: classes.root,
                    // outlined: classes.outlined,
                  }
                }
                style={{
                  order: 2,
                  fontSize: '.75rem',
                  fontWeight: 'bold',
                  padding: '0 8px',
                  marginTop: '-4px',
                  marginLeft: '5px',
                }}
                // className={
                //   isHaveRenewalNotificationFilters ? classes.filterApplied : classes.filterUnapplied
                // }
                onClick={openFiltersPage}
              />
            </Grid>
          )}
        </Grid>
        {getConditionLabel().length > 1 && (
          <Grid container className={classes.conditionItemContainer}>
            <Grid className={classes.conditionLabel}>
              <Typography className={classes.conditionLabelName}>
                {getConditionLabel()[1]}
              </Typography>
            </Grid>
            <Grid className={classes.flex}>
              <Typography className={classes.conditionName}>
                {conditionArray[conditionArray.length - 1].name}
              </Typography>
            </Grid>
            <Grid></Grid>
          </Grid>
        )}
      </Grid>
      {getConditionLabel().length > 1 && (
        <HighlightOffIcon className={classes.deleteConditionItem} onClick={deleteCondition} />
      )}
      {!isLastCondition && (
        <Grid className={classes.addNewConditionContainer}>
          <AddCircleOutlineIcon
            style={{
              color: '#6239eb',
              cursor: 'pointer',
            }}
            onClick={addNewCondition}
          />
        </Grid>
      )}
    </div>
  );
};

export const GroupingAttributeGroupingFieldControl: React.FC<CustomFormControlProps> = (props) => {
  const { field, formContext } = props;
  const excludedApiNames = [
    'orderProductId',
    'billingAccountId',
    'entityId',
    'salesAccountId',
    'paymentTerm',
  ];
  const [loading, setLoading] = useState<boolean>(false);
  const [options, setOptions] = useState<PickListOption[]>([]);
  const billingSettingsService = useContext(BillingSettingsContext);
  const generateCustomFieldsOptions = (customFieldsData: any): PickListOption[] => {
    const addedApiNames = new Set();
    const {
      subscriptionFields,
      assetFields,
      entitlementFields,
      fieldNameToRelatedObjects,
      fieldNameToFieldTypes,
    } = customFieldsData;

    const allFields = [
      ...(subscriptionFields?.filter((field: RubyField) => {
        if (addedApiNames.has(field.apiName)) return false;
        addedApiNames.add(field.apiName);
        return true;
      }) || []),
      ...(assetFields?.filter((field: RubyField) => {
        if (addedApiNames.has(field.apiName)) return false;
        addedApiNames.add(field.apiName);
        return true;
      }) || []),
      ...(entitlementFields?.filter((field: RubyField) => {
        if (addedApiNames.has(field.apiName)) return false;
        addedApiNames.add(field.apiName);
        return true;
      }) || []),
    ];

    const options: PickListOption[] = allFields
      .map((field) => {
        const fieldTypes = fieldNameToFieldTypes?.[field.apiName] || [];
        const relatedObjects = fieldNameToRelatedObjects?.[field.apiName] || [];

        if (fieldTypes.length > 1) {
          const desc = `The field cannot be used as a grouping field because the field type is not the same on the ${
            relatedObjects.length === 1
              ? relatedObjects[0]
              : relatedObjects.length === 2
                ? relatedObjects.join(' and ')
                : relatedObjects.slice(0, -1).join(', ') +
                  ' and ' +
                  relatedObjects[relatedObjects.length - 1]
          }.`;
          return {
            name: `BillingAsset.${field.name} (${field.apiName})`,
            value: `BillingAsset.${field.apiName}`,
            icon: (
              <Tooltip title={desc} arrow>
                <IconButton edge="end">
                  <WarningIcon style={{ fontSize: '16px', color: '#ff9800' }} />
                </IconButton>
              </Tooltip>
            ),
            isDisabled: true,
          };
        } else if (relatedObjects.length === 3) {
          return {
            name: `BillingAsset.${field.name} (${field.apiName})`,
            value: `BillingAsset.${field.apiName}`,
          };
        } else if (relatedObjects.length < 3) {
          const desc = `This field only exists in ${
            relatedObjects.length === 1
              ? relatedObjects[0]
              : relatedObjects.length === 2
                ? relatedObjects.join(' and ')
                : relatedObjects.slice(0, -1).join(', ') +
                  ' and ' +
                  relatedObjects[relatedObjects.length - 1]
          }, so the billing group won’t apply to other types of billing assets.`;
          return {
            name: `BillingAsset.${field.name} (${field.apiName})`,
            value: `BillingAsset.${field.apiName}`,
            icon: (
              <Tooltip title={desc} arrow>
                <IconButton edge="end">
                  <InfoIcon style={{ fontSize: '16px', opacity: 0.2 }} />
                </IconButton>
              </Tooltip>
            ),
          };
        }
        return undefined;
      })
      .filter((option): option is PickListOption => option !== undefined);

    return options;
  };
  const customFieldsOfAssetData = formContext?.watch('customFieldsOfAssetData');

  useEffect(() => {
    if (customFieldsOfAssetData) {
      setUp();
    }
  }, [customFieldsOfAssetData]);

  const setUp = async () => {
    setLoading(true);
    if (billingSettingsService) {
      const billingAssetMetadata = billingSettingsService.metadataObjects.find(
        (metadataObject) => metadataObject.apiName === 'BillingAsset',
      );
      const options: PickListOption[] =
        billingAssetMetadata?.fields
          ?.filter((field) => !excludedApiNames.includes(field.apiName))
          .map((field) => ({
            name: `${billingAssetMetadata.apiName}.${field.name} (${field.apiName})`,
            value: `${billingAssetMetadata.apiName}.${field.apiName}`,
          })) || [];

      if (customFieldsOfAssetData) {
        const customFieldsOptions = customFieldsOfAssetData
          ? generateCustomFieldsOptions(customFieldsOfAssetData)
          : [];
        options.push(...customFieldsOptions);
      }
      setOptions(options);
    }
    setLoading(false);
  };

  if (loading) {
    return null;
  }
  const isReadOnly = field.creatable ? false : true;
  return (
    <Controller
      name={field.apiName}
      render={({ value, onChange }) => {
        const label = field.name;
        return (
          <FormControl style={{ width: '100%' }}>
            <LabelWithTooltip
              label={label}
              required={field.required}
              shrink
              showTooltip={field.showTooltip}
              tooltipText={field.toolTipText}
              htmlFor={field.apiName}
            />
            <Autocomplete
              style={{ paddingTop: '5px', paddingRight: 0, paddingBottom: '5px' }}
              value={value}
              placeholder={`Search ${label}`}
              suggestions={options}
              getSuggestionItemLabel={(x: PickListOption) => x.name}
              getSuggestionItemValue={(x: PickListOption) => x.value}
              getSelectedItemLabel={(x: PickListOption) => x.name}
              onChange={(event) => {
                const newValue = event.value;
                onChange(newValue);
                formContext?.setValue('options', []);
                if (newValue !== value) {
                  formContext?.setValue('conditions', []);
                }
              }}
              disabled={isReadOnly}
              readOnly={isReadOnly}
              isClickToShowDirectly={true}
            />
            {formContext?.errors?.groupingField && (
              <div
                role="alert"
                style={{
                  fontSize: '14px',
                  fontWeight: 400,
                  color: '#ca0035',
                  width: '100%',
                  paddingTop: '5px',
                }}
              >
                grouping field is a required field
              </div>
            )}
          </FormControl>
        );
      }}
    />
  );
};

export const GroupingAttributeConditionsControl: React.FC<CustomFormControlProps> = (props) => {
  const { field, formContext, mode } = props;
  const groupingField = formContext?.watch('groupingField');
  const { Snackbar, showSnackbar } = useRubySnackbar();
  const customFieldsOfAssetData = formContext?.watch('customFieldsOfAssetData');

  return (
    <Controller
      name={field.apiName}
      render={({ value, onChange }) => {
        const label = field.name;
        return (
          <FormControl style={{ width: '100%' }}>
            <Snackbar />
            <LabelWithTooltip
              label={label}
              required={field.required}
              shrink
              showTooltip={field.showTooltip}
              tooltipText={field.toolTipText}
              htmlFor={field.apiName}
            />
            {value.length > 0 ? (
              value.map((v: GroupingConditionItem, index: number) => (
                <GroupingAttributeConditionItem
                  condition={v}
                  conditionArray={value}
                  key={v.id}
                  index={index}
                  onChange={onChange}
                  groupingField={groupingField}
                  field={field}
                  customFieldsOfAssetData={customFieldsOfAssetData}
                />
              ))
            ) : (
              <EmptyGroupingAttributeConditions
                initGroupingAttributeConditions={() => {
                  if (!formContext?.getValues('groupingField')) {
                    showSnackbar('error', 'Error', 'Please select groupingField first.');
                  }
                  if (formContext?.getValues('groupingField') && field.creatable) {
                    onChange([
                      {
                        id: shortUUID().generate().toString() + '_temp',
                        name: '',
                        filters: '',
                      },
                      {
                        id: shortUUID().generate().toString() + '_temp',
                        name: 'Group in the same invoice',
                        filters: '',
                      },
                    ]);
                  }
                }}
              />
            )}
          </FormControl>
        );
      }}
    />
  );
};

export const GroupingAttributeForm: React.FC<Props> = (userProps) => {
  const { onSubmit, mode, values, onCancel } = { ...defaultProps, ...userProps };
  const resolver = useYupValidationResolver(validationSchema);
  const methods = useForm<GroupingAttribute>({
    defaultValues: values,
    //@ts-ignore
    resolver,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const classes = useStyles();
  if (mode === 'view') {
    groupingAttributeMetadata?.fields?.forEach((field) => {
      if (field.hasOwnProperty('creatable')) {
        field.creatable = false;
      }
    });
  } else {
    groupingAttributeMetadata?.fields?.forEach((field) => {
      if (field.hasOwnProperty('creatable')) {
        field.creatable = true;
      }
    });
  }
  const billingSettingsService = useContext(BillingSettingsContext);

  const fetchCustomFields = async () => {
    if (billingSettingsService) {
      const data = await billingSettingsService.getCustomFieldsOfAsset();
      methods.register('customFieldsOfAssetData');
      methods.setValue('customFieldsOfAssetData', data);
    }
  };

  useEffect(() => {
    fetchCustomFields();
  }, [billingSettingsService]);

  CustomFieldControlRegistry.register('groupingField', GroupingAttributeGroupingFieldControl);
  CustomFieldControlRegistry.register('conditions', GroupingAttributeConditionsControl);

  return (
    <FormProvider {...methods}>
      <form>
        <Grid item xs={12}>
          <Typography component="h3" className={classes.sectionTitle}>
            General
          </Typography>
        </Grid>
        <FormSection
          {...(mode === 'create' || mode === 'edit' ? { mode: mode as 'create' | 'edit' } : {})}
          fields={groupingAttributeMetadata.fields}
          values={methods.getValues()}
          hiddenFields={['id', 'status', 'isStandard']}
          showDivider={false}
          setHiddenFields={() => {}}
        />
        <br />
        <RubyButtonBar
          variant={'inPlace'}
          processing={isSubmitting}
          leftButtons={[
            {
              text: 'Cancel',
              onClick: onCancel,
            },
          ]}
          rightButtons={
            mode === 'view'
              ? []
              : [
                  {
                    text: 'Save',
                    onClick: async () => {
                      setIsSubmitting(true);
                      try {
                        const isValid = await methods.trigger();
                        if (!isValid) {
                          setIsSubmitting(false);
                          return;
                        }
                        await onSubmit(methods.getValues());
                      } catch (e) {
                        setIsSubmitting(false);
                      }
                    },
                  },
                ]
          }
        />
      </form>
    </FormProvider>
  );
};

export default GroupingAttributeForm;

import React, { useState } from 'react';
import FormControl from '@material-ui/core/FormControl';
import MenuItem from '@material-ui/core/MenuItem';
import Select from '@material-ui/core/Select';
import { FilterBuilderCondition, Operand } from '../graph-ql-query-constructor/interface';
import { RubyField, RubyObject } from '../metadata/interface';
import PickList from '../pick-list';
import { operatorTypes } from './operator-map';
import { TypeDisplay } from './type-display';
import ValueInput from './value-input';
import NestedPickList from '../nested-pick-list';

/**
 * Note: much of this code was taken from https://github.com/logipro/logi-filter-builder and refractored for our case to construct filters into GraphQL
 * TODO: Refractor to make this into a functional component and clean up code
 */

interface Props {
  classes: any;
  conditions: Array<FilterBuilderCondition>;
  updateConditions: (conditionsCopy: Array<FilterBuilderCondition>) => void;
  fields: Array<RubyField>;
  index: number;
  condition: FilterBuilderCondition;
  handleChange: (
    selectedField: FilterBuilderCondition,
    index: number,
    value: string | RubyField,
    changeType: string,
    translateValue: string | null,
    conditions: Array<FilterBuilderCondition>,
    updateConditions: (newConditions: Array<FilterBuilderCondition>) => void,
    fieldValue?: RubyField,
  ) => void;
  objectMetadata: RubyObject;
  allMetadatas?: RubyObject[];
  disabled?: boolean;
  isNested?: boolean;
  specifyRelatedObjects?: Record<string, string[]>;
}

function getOptionName(field: RubyField) {
  if (field.lookupRelation) {
    return field.lookupRelation.relationLabel;
  }
  if (field.masterDetailRelation) {
    return field.masterDetailRelation.relationLabel;
  }
  return field.name;
}

function ConditionLine(props: Props) {
  const {
    classes,
    condition,
    index,
    handleChange,
    fields,
    conditions,
    updateConditions,
    objectMetadata,
    disabled,
    isNested = false,
    allMetadatas,
    specifyRelatedObjects,
  }: Props = props;

  return (
    <React.Fragment>
      <FormControl className={classes.formControl}>
        {allMetadatas ? (
          <NestedPickList
            required={false}
            disabled={disabled}
            value={condition.field?.apiName || null}
            label={
              TypeDisplay.get(condition.field?.field?.type) ||
              TypeDisplay.get(condition.field?.nestedType) ||
              TypeDisplay.get(condition.field?.type) ||
              'Field'
            }
            name="field"
            // @ts-ignore
            field={condition.field}
            options={fields
              // @ts-ignore  nestable indicates where the field can be used in a nested condition
              .filter((_) => isNested === false || _.nestable !== false)
              .map((field: RubyField) => ({
                ...field,
                name: getOptionName(field),
                value: field.apiName,
              }))}
            handleInputChange={(newValue: string, type, fieldFieldName) => {
              if (fieldFieldName) {
                const fieldValue = Object.values(allMetadatas)
                  .find(
                    (m) =>
                      m.apiName ===
                      objectMetadata?.fields?.find((f) => f.apiName === newValue)?.lookupRelation
                        ?.referenceTo,
                  )
                  ?.fields?.map((field: RubyField) => ({
                    ...field,
                    name: getOptionName(field),
                    value: field.apiName,
                  }))
                  .find((val) => val.apiName === fieldFieldName);
                if (fieldValue) {
                  handleChange(
                    condition,
                    index,
                    newValue,
                    type,
                    null,
                    conditions,
                    updateConditions,
                    fieldValue,
                  );
                }
              } else {
                handleChange(condition, index, newValue, type, null, conditions, updateConditions);
              }
            }}
            allMetadatas={allMetadatas}
            nestedMenuItemClassName={classes.nestedMenuItemClassName}
            specifyRelatedObjects={specifyRelatedObjects}
          />
        ) : (
          <PickList
            required={false}
            disabled={disabled}
            value={condition.field && condition.field.apiName ? condition.field.apiName : null}
            label={
              condition.field && condition.field.type
                ? TypeDisplay.get(condition.field.type)
                : 'Field'
            }
            name="field"
            // @ts-ignore
            field={condition.field}
            options={fields
              // @ts-ignore  nestable indicates where the field can be used in a nested condition
              .filter((_) => isNested === false || _.nestable !== false)
              .map((field: RubyField) => ({
                ...field,
                name: getOptionName(field),
                value: field.apiName,
              }))}
            handleInputChange={(newValue: string) => {
              handleChange(condition, index, newValue, 'field', null, conditions, updateConditions);
            }}
          />
        )}
      </FormControl>
      {(condition.field && condition.field.type !== 'bLookup') ||
      (condition.field &&
        condition.field.type === 'bLookup' &&
        !(condition.field.field || condition.field?.nestedApiName)) ? (
        <FormControl className={classes.formControl}>
          <Select
            disableUnderline
            displayEmpty
            disabled={disabled}
            classes={{
              // @ts-ignore
              root: classes.operatorRoot,
            }}
            value={condition.operator && condition.operator.id ? condition.operator.id : ''}
            name="operator"
            onChange={(event: any) => {
              handleChange(
                condition,
                index,
                event.target.value,
                'operator',
                null,
                conditions,
                updateConditions,
              );
            }}
          >
            <MenuItem value="">None</MenuItem>
            {operatorTypes.get(condition.field.type)?.map((option: Operand, i: number) => (
              <MenuItem key={i} value={option.id} disabled={disabled}>
                {option.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      ) : condition.field &&
        condition.field.type === 'bLookup' &&
        (condition.field.field || condition.field?.nestedApiName) ? (
        <FormControl className={classes.formControl}>
          <Select
            disableUnderline
            displayEmpty
            disabled={disabled}
            classes={{
              // @ts-ignore
              root: classes.operatorRoot,
            }}
            value={condition.operator && condition.operator.id ? condition.operator.id : ''}
            name="operator"
            onChange={(event: any) => {
              handleChange(
                condition,
                index,
                event.target.value,
                'operator',
                null,
                conditions,
                updateConditions,
              );
            }}
          >
            <MenuItem value="">None</MenuItem>
            {operatorTypes
              .get(
                condition.field.nestedType || condition.field.field?.type || condition.field?.type,
              )
              ?.map((option: Operand, i: number) => (
                <MenuItem key={i} value={option.id} disabled={disabled}>
                  {option.name}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
      ) : null}
      {(condition.operator &&
        condition.field &&
        condition.field.type &&
        condition.field.type !== 'bLookup') ||
      (condition.operator &&
        condition.field &&
        condition.field.type &&
        condition.field.type === 'bLookup' &&
        !(condition.field.field || condition.field.nestedApiName)) ? (
        <ValueInput
          classes={classes}
          conditionField={condition}
          conditionFieldIndex={index}
          type={condition.field.type}
          conditions={conditions}
          updateConditions={updateConditions}
          objectMetadata={objectMetadata}
          disabled={disabled}
          forFilter={true}
          handleChange={(
            selectedField,
            currentIndex,
            value,
            changeType,
            translateValue,
            currentConditions,
            updateConditions,
          ) =>
            handleChange(
              selectedField,
              currentIndex,
              value,
              changeType,
              translateValue,
              currentConditions,
              updateConditions,
            )
          }
        />
      ) : condition.operator &&
        condition.field &&
        condition.field.type &&
        condition.field.type === 'bLookup' &&
        (condition.field.field || condition.field.nestedApiName) ? (
        <ValueInput
          classes={classes}
          conditionField={condition}
          useNestedField
          conditionFieldIndex={index}
          type={condition.field.field?.type || condition.field.nestedType || condition.field.type}
          conditions={conditions}
          updateConditions={updateConditions}
          objectMetadata={objectMetadata}
          disabled={disabled}
          forFilter={true}
          handleChange={(
            selectedField,
            currentIndex,
            value,
            changeType,
            translateValue,
            currentConditions,
            updateConditions,
          ) =>
            handleChange(
              selectedField,
              currentIndex,
              value,
              changeType,
              translateValue,
              currentConditions,
              updateConditions,
            )
          }
        />
      ) : null}
    </React.Fragment>
  );
}

export default ConditionLine;

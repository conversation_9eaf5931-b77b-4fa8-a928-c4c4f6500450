import Link from '@material-ui/core/Link';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import dayjs from 'dayjs';
import React from 'react';

import type { RenewalRequest, RenewalUpdateQuantityRequest } from '../../change-cart';
import { calculateEndDate } from '../../change-cart/change-order-util';
import { formatTerm } from '../../util/term-converter';
import type { ChangeItemText } from '../interface';
import TextWrapper from '../text-wrapper';
import TextUnderline from '../text-underline';
import TextHighlight from '../text-highlight';
import { formatDimension } from '../../subscription-card/info-section-text-registry/convert-free-trial-text/utils';

const RenewUpdateQuantityText: React.FC<ChangeItemText> = (props) => {
  const { changeItem } = props;

  const request = changeItem.request as RenewalUpdateQuantityRequest;
  const renewalTerm = request.renewalTerm;
  const termDimension = (request.termDimension || changeItem.asset.uom.termDimension).toLowerCase();
  const dayAfterSubscriptionEndDate = dayjs(
    dayjs(changeItem.asset.subscriptionEndDate).add(1, 'day'),
  );
  const action = request.quantity >= 0 ? 'adds' : 'reduces';
  const absQuantity = Math.abs(request.quantity);
  const endDate = calculateEndDate(
    renewalTerm,
    dayjs(request.renewalDate || dayAfterSubscriptionEndDate).toDate(),
    termDimension,
    'MM/DD/YYYY',
  );

  return (
    <TextWrapper>
      {request.objectType} <TextUnderline>{request.assetName}</TextUnderline> renews for another{' '}
      <TextHighlight>{formatTerm(renewalTerm)}</TextHighlight>{' '}
      {formatDimension(termDimension, renewalTerm)} and {action}{' '}
      <TextHighlight>{absQuantity}</TextHighlight> user{absQuantity === 1 ? '' : 's'} on{' '}
      <TextHighlight>
        {dayjs(request.renewalDate || dayAfterSubscriptionEndDate).format('MM/DD/YYYY')}
      </TextHighlight>
      . The new subscription end date will be <TextHighlight>{endDate}</TextHighlight>
    </TextWrapper>
  );
};

export default RenewUpdateQuantityText;

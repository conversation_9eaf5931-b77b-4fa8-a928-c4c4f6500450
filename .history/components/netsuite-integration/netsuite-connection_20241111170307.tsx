import React, { useContext, useEffect, useState } from 'react';

import { Checkbox, Grid, Paper, Typography, makeStyles } from '@material-ui/core';
import FormControlLabel from '@material-ui/core/FormControlLabel';

import { ActiveIcon, InactiveIcon, QuickbookStyleIcon } from '../icons';
import Loading from '../loading';
import { RubyButtonBar } from '../ruby-button';
import { useRubySnackbar } from '../ruby-notifier';
import { RubySettingEditorPanel } from '../ruby-settings/ruby-setting-editor-panel';
import type { NetSuiteIntegration, NetSuiteTrayDetail } from './interface';
import NetSuiteConnectionContext from './netsuite-connection-context';
import { NetSuiteConnectionMapping } from './netsuite-connection-mapping';

const useStyle = makeStyles({
  activated: {
    display: 'flex',
    marginBottom: '10px',
  },
  activatedText: {
    paddingLeft: '4px',
    fontSize: '.75rem',
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  activatedTime: {
    paddingLeft: '4px',
    fontSize: '.8rem',
    marginLeft: '10px',
    color: '#6e6e6e',
  },
  caption: {
    color: '#000000',
    fontSize: '20px',
    fontWeight: 500,
    letterSpacing: '-0.77px',
    lineHeight: '25px',
  },
  subtitle: {
    marginTop: '8px',
    color: '#000000',
    opacity: 0.4,
    fontSize: '12px',
    letterSpacing: 0,
    lineHeight: '15px',
  },
  papper: {
    flexGrow: 2,
    paddingLeft: 32,
    paddingTop: 34,
    paddingBottom: 24,
    paddingRight: 24,
  },
  heading: {
    opacity: '0.7',
    color: '#000000',
    fontWeight: 500,
    lineHeight: '32px',
  },
  header: {
    marginTop: 12,
    display: 'flex',
    alignItems: 'center',
    '& h2': {
      color: '#000000',
      fontSize: '26px',
      fontWeight: 500,
      letterSpacing: '-0.3px',
      lineHeight: '32px',
      marginTop: 0,
      marginBottom: 0,
    },
    '& span': {
      textTransform: 'Uppercase',
      minWidth: '61px',
      padding: '6px 12px 6px 12px',
      fontSize: '10px',
      fontWeight: 'bold',
      letterSpacing: 0,
      lineHeight: '12px',
      textAlign: 'center',
      borderRadius: '8px',
    },
  },
  netSuiteHeader: {
    fontSize: '1.5em',
    color: '#000',
  },
  subHeader: {
    marginBottom: 28,
    marginTop: 8,
    color: '#000',
    opacity: 0.8,
    fontSize: '14px !important',
  },
  subCompany: {
    color: '#000',
    opacity: 0.8,
    fontSize: '14px !important',
    display: 'flex',
    alignItems: 'center',
    marginBottom: 28,
    marginTop: 8,
    height: '30px',
  },
  buttonBarContainer: {
    marginTop: '10px',
  },
  initializingContainer: {
    marginTop: '10px',
    display: 'flex',
    alignItems: 'center',
  },
});

const NetSuiteConnection: React.FC = () => {
  const classes = useStyle();

  const netsuiteConnectionContext = useContext(NetSuiteConnectionContext);
  if (!netsuiteConnectionContext) {
    throw Error(
      'NetSuite Connection requires netsuiteConnectionContext to be declared in context provider',
    );
  }

  const {
    queryNetSuiteConfigure,
    createNetSuiteIntegration,
    configureNetSuiteIntegration,
    updateNetSuiteIntegration,
    handleUploadFiles,
    loadMappingData,
    handleUpdateRows,
    handleDeleteRow,
  } = netsuiteConnectionContext;

  const [loading, setLoading] = React.useState(false);
  const [initializing, setInitializing] = React.useState(false);
  const [integration, setIntegration] = React.useState<NetSuiteIntegration | null>();
  const [integrationUrl, setIntegrationUrl] = React.useState('');
  const [renderTrayIo, setRenderTrayIo] = React.useState(false);
  const [rightButtons, setRightButtons] = React.useState<any>([]);
  const [sandBox, setSandBox] = useState(false);

  const { showSnackbar, Snackbar } = useRubySnackbar();

  const getRightButtonsAccordingEnabled = () => {
    const nRightButtons: any = [];
    if (integration?.enabled && integration.companyName) {
      nRightButtons.push({
        disabled: false,
        text: 'Deactivate',
        onClick: async () => {
          await updateNetSuiteIntegration({
            appCategory: 'Payment',
            integrationType: 'NetSuite',
            configuration: {
              environment: 'Production',
            },
            enabled: false,
          });
          const res = await queryNetSuiteConfigure('Production');
          setIntegration(res);
        },
      });
    } else if (integration && !integration?.enabled && integration.companyName) {
      nRightButtons.push({
        disabled: false,
        text: 'Activate',
        onClick: async () => {
          await updateNetSuiteIntegration({
            appCategory: 'Payment',
            integrationType: 'NetSuite',
            configuration: {
              environment: 'Production',
            },
            enabled: true,
          });
          const res = await queryNetSuiteConfigure('Production');
          setIntegration(res);
          showSnackbar('confirm', 'Success', 'Your NetSuite Integration is activated.');
        },
      });
    }
    if (!integration) {
      setRightButtons([]);
    }
    if (nRightButtons.length > 0) {
      setRightButtons(nRightButtons);
    }
  };

  React.useEffect(() => {
    getRightButtonsAccordingEnabled();
  }, [integration]);

  const closeIframe = () => {
    setRenderTrayIo(false);
    setInitializing(false);
    setIntegrationUrl('');
  };

  const onmessage = async (e: any) => {
    switch (e.data.type) {
      case 'tray.configPopup.error':
        showSnackbar('error', 'Error', e.data.data.err);
        closeIframe();
        return;
      case 'tray.configPopup.finish':
        const configRes = await configureNetSuiteIntegration({
          configuration: {
            endConfig: true,
            environment: 'Production',
          },
        });
        if ((configRes as NetSuiteTrayDetail).trayUsername) {
          setIntegration(configRes as NetSuiteIntegration);
          showSnackbar('confirm', 'Success', 'Your NetSuite Integration is activated.');
        }
        closeIframe();
        return;
      case 'tray.configPopup.cancel':
        closeIframe();
        return;
    }
  };

  useEffect(() => {
    window.addEventListener('message', onmessage);
    return () => window.removeEventListener('message', onmessage);
  }, [sandBox]);

  const setup = async () => {
    setLoading(true);
    window.addEventListener('message', onmessage);
    const res = await queryNetSuiteConfigure();
    if (res?.environment == 'Sandbox') {
      setSandBox(true);
    }
    setIntegration(res);
    setLoading(false);
  };

  const loadIntegration = () => {
    setInitializing(false);
    setRenderTrayIo(true);
  };

  useEffect(() => {
    if (!loading && !initializing) {
      setup();
    }
    return () => {
      window.removeEventListener('message', onmessage);
    };
  }, []);

  const handleNetSuiteEnvChange = async (checked: boolean) => {
    setSandBox(checked);
    setLoading(true);
    const res = await queryNetSuiteConfigure(checked ? 'Sandbox' : 'Production');
    if (res?.environment == 'Sandbox') {
      setSandBox(true);
    }
    setIntegration(res);
    setLoading(false);
  };

  return (
    <>
      <Snackbar />
      <RubySettingEditorPanel title="NetSuite Integration">
        <Typography variant="h6" className={classes.heading} gutterBottom>
          Connect to NetSuite
        </Typography>
        <div className={classes.subHeader}>
          <span>
            To integrate with NetSuite, a notification endpoint URL is required need to setup in
            NetSuite webhooks, You can find the webhook setting page by logging into the &nbsp;
            <a
              href="https://developer.intuit.com/app/developer/dashboard"
              target={'_blank'}
              rel="noreferrer"
              style={{ textDecoration: 'none' }}
            >
              NetSuite App Dashboard
            </a>{' '}
            , click one of the apps which associated with your  and navigating to the Production
            Settings &gt; Webhooks. Once there, you’ll see an Endpoint URL input box there.
          </span>
        </div>
        {integration?.companyName && (
          <div>
            <span className={classes.subCompany}>
              {integration.companyName}
              <QuickbookStyleIcon
                style={{ width: '60', color: integration.enabled ? 'rgb(44, 160, 28)' : 'grey' }}
                viewBox={'0 0 150 55'}
              />
            </span>
          </div>
        )}
        {
          <>
            <Grid item xs={12}>
              <div className={classes.activated}>
                {integration?.enabled ? (
                  <ActiveIcon style={{ color: '#17981D' }} />
                ) : (
                  <InactiveIcon style={{ height: '16px', width: '16px', color: '#999999' }} />
                )}
                <Typography
                  style={integration?.enabled ? { color: '#17981d' } : { color: '#999999' }}
                  className={classes.activatedText}
                  variant="body2"
                  component="p"
                >
                  {integration?.enabled ? 'Active' : 'Inactive'}
                </Typography>
                <Typography className={classes.activatedTime} variant="body2" component="p">
                  {integration?.enabled
                    ? `Activated on ${new Date(integration.lastModifiedDate).toString()}`
                    : ''}
                </Typography>
              </div>
            </Grid>
            {!renderTrayIo}
            {!initializing && !renderTrayIo && (
              <Grid item xs={12} className={classes.buttonBarContainer}>
                <RubyButtonBar
                  variant="inPlace"
                  processing={false}
                  buttonSize="large"
                  leftButtons={[
                    {
                      disabled: false,
                      text: 'Configure',
                      onClick: async () => {
                        setInitializing(true);
                        if (integration) {
                          const res = await configureNetSuiteIntegration({
                            configuration: {
                              endConfig: false,
                              environment: 'Production',
                            },
                          });
                          setIntegrationUrl(res.trayEmbeddedUrl!);
                        } else {
                          const trayDetail = await createNetSuiteIntegration(
                            'Production',
                          );
                          setIntegrationUrl(trayDetail.trayEmbeddedUrl);
                        }
                      },
                    },
                  ]}
                  rightButtons={rightButtons}
                />
              </Grid>
            )}
            {initializing && !renderTrayIo && (
              <Typography className={classes.initializingContainer} variant="body2" component="p">
                Initializing...
                <div
                  style={{
                    marginLeft: '10px',
                  }}
                >
                  <Loading size="15px" />
                </div>
              </Typography>
            )}
            {integrationUrl && (
              <Grid item xs={12} className={classes.buttonBarContainer}>
                <iframe
                  src={integrationUrl}
                  frameBorder="0"
                  width={'100%'}
                  height={'550px'}
                  onLoad={loadIntegration}
                />
              </Grid>
            )}
          </>
        }
        {integration?.enabled && (
          <NetSuiteConnectionMapping
            handleUploadFiles={handleUploadFiles}
            loadMappingData={loadMappingData}
            handleUpdateRows={handleUpdateRows}
            handleDeleteRow={handleDeleteRow}
            isSandBox={sandBox}
            columns={[
              {
                name: 'createdTime',
                title: 'Created Date',
                apiName: 'createdTime',
                type: 'dateTime',
              },
              {
                name: 'sourceCustomerId',
                title: 'Salesforce Account ID',
                apiName: 'sourceCustomerId',
                type: 'text',
              },
              {
                name: 'sourceCustomerName',
                title: 'Salesforce Account Name',
                apiName: 'sourceCustomerName',
                type: 'text',
              },
              {
                name: 'paymentCustomerName',
                title: 'NetSuite Customer Name',
                apiName: 'paymentCustomerName',
                type: 'text',
              },
              {
                name: 'paymentCustomerId',
                title: 'NetSuite Customer ID',
                apiName: 'paymentCustomerId',
                type: 'text',
              },
            ]}
            columnWidths={[
              { columnName: 'createdTime', width: 220 },
              { columnName: 'sourceCustomerId', width: 220 },
              { columnName: 'sourceCustomerName', width: 220 },
              { columnName: 'paymentCustomerName', width: 220 },
              { columnName: 'paymentCustomerId', width: 220 },
            ]}
            type="customer"
            title="Existing Customer Mapping"
          />
        )}
        {integration?.enabled && (
          <NetSuiteConnectionMapping
            handleUploadFiles={handleUploadFiles}
            loadMappingData={loadMappingData}
            handleUpdateRows={handleUpdateRows}
            handleDeleteRow={handleDeleteRow}
            isSandBox={sandBox}
            columns={[
              {
                name: 'createdTime',
                title: 'Created Date',
                apiName: 'createdTime',
                type: 'dateTime',
              },
              {
                name: 'sourceProductId',
                title: 'Salesforce Product ID',
                apiName: 'sourceProductId',
                type: 'text',
              },
              {
                name: 'sourceProductName',
                title: 'Salesforce Product Name',
                apiName: 'sourceProductName',
                type: 'text',
              },
              {
                name: 'paymentProductName',
                title: 'NetSuite Product Name',
                apiName: 'paymentProductName',
                type: 'text',
              },
              {
                name: 'paymentProductId',
                title: 'NetSuite Product ID',
                apiName: 'paymentProductId',
                type: 'text',
              },
            ]}
            columnWidths={[
              { columnName: 'createdTime', width: 220 },
              { columnName: 'sourceProductId', width: 220 },
              { columnName: 'sourceProductName', width: 220 },
              { columnName: 'paymentProductName', width: 220 },
              { columnName: 'paymentProductId', width: 220 },
            ]}
            type="product"
            title="Existing Product Mapping"
          />
        )}
      </RubySettingEditorPanel>
    </>
  );
};

export default NetSuiteConnection;

import React, { useContext, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import {
  <PERSON>ton,
  Chip,
  Divider,
  FormControlLabel,
  Grid,
  TextField,
  Tooltip,
  Typography,
  makeStyles,
} from '@material-ui/core';
import FavoriteBorderIcon from '@material-ui/icons/FavoriteBorder';
import GraphQLQueryConstructor from '../graph-ql-query-constructor';
import DialogComponent from '../dialog-component';
import { FormSectionWrapper as FormSection } from '../form-section/form-section';
import { buildValidationSchema, useYupValidationResolver } from '../form-validation';
import { ActiveIcon, InactiveIcon } from '../icons/icons';
import RubyButton, { RubyButtonBar } from '../ruby-button';
import { useRubySnackbar } from '../ruby-notifier';
import { RubySettingEditorPanel } from '../ruby-settings/ruby-setting-editor-panel';
import type {
  AvalaraEInvoicingConnectionRequest,
  CompanyType,
  CountryMandates,
  DocumentType,
  MappingSchema,
} from './avalara-e-invoicing-connection-context';
import AvalaraEInvoicingConnectionContext from './avalara-e-invoicing-connection-context';
import PickList from '../pick-list';
import RubyGrid from '../ruby-grid';
import { MetadataObject } from '../object-list';
import { CellRendererRowProps } from '../grid-actions';
import HelpIcon from '../help-icon';
import { HelpOutline, Rowing } from '@material-ui/icons';
import CategoryOutlinedIcon from '@material-ui/icons/CategoryOutlined';
import FormatListBulletedOutlinedIcon from '@material-ui/icons/FormatListBulletedOutlined';
import RubySwitch from '../ruby-switch';
import FilterBuilder from '../filter-builder';
import { Condition, RubyFilter } from '../graph-ql-query-constructor';
import { BillingSettingsContext } from '../ruby-settings';
import { buildExtraRelationFields } from '../ruby-list-view';
import AvalaraEInvoicingEditingMappingsDialog from './avalara-e-invoicing-editing-mappings-dialog';
import shortUUID, { uuid } from 'short-uuid';
import Loading from '../loading';
import { RubyField } from '../metadata';
import _ from 'lodash';
import { Autocomplete } from '@material-ui/lab';

const useStyle = makeStyles({
  buttonBarContainer: {
    marginTop: '20px',
  },
  card: {
    padding: '24px',
  },
  accordionRoot: {
    boxShadow: 'none',
    marginTop: 16,
    width: '100%',
    paddingBottom: 24,
    '&:before': {
      backgroundColor: 'unset',
    },
  },
  accordionSummaryRoot: {
    display: '-webkit-inline-flex',
    padding: '0px',
    width: '100%',
    alignItems: 'center',
  },
  accordionSummaryContainer: {
    display: 'flex',
    justifyContent: 'space-between',
  },
  papper: {
    flexGrow: 2,
    paddingLeft: 32,
    paddingTop: 34,
    paddingBottom: 24,
    paddingRight: 24,
  },
  heading: {
    opacity: '0.7',
    color: '#000000',
    fontWeight: 500,
    lineHeight: '32px',
    marginBottom: '28px',
  },
  header: {
    marginTop: 12,
    display: 'flex',
    alignItems: 'center',
    '& span': {
      textTransform: 'Uppercase',
      minWidth: '61px',
      padding: '6px 12px 6px 12px',
      fontSize: '10px',
      fontWeight: 'bold',
      letterSpacing: 0,
      lineHeight: '12px',
      textAlign: 'center',
      borderRadius: '8px',
    },
  },
  activated: {
    display: 'flex',
    paddingLeft: '4px',
    marginTop: 12,
  },
  activatedText: {
    paddingLeft: '4px',
    fontSize: '.75rem',
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  subHeader: {
    marginBottom: 28,
    marginTop: 8,
    color: '#000',
    fontSize: '14px !important',
  },
  flexCenterStart: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  filterContainer: {
    marginBottom: 15,
  },
  filter: {
    color: '#6239EB',
    textDecoration: 'underline',
    marginLeft: '5px',
    cursor: 'pointer',
  },
  questionMarkIcon: {
    fontSize: '16px',
    color: 'grey',
    cursor: 'pointer',
  },
  helpIcon: {
    fontSize: '18px',
    color: '#6239EB',
  },
  autocompleteOption: {
    fontSize: '.875rem',
    color: '#4d4c50',
  },
  inputRoot: {
    background: 'white',
    border: '1px solid #ced4da',
    borderRadius: '4px',
    '&>div': {
      padding: '0!important',
      fontSize: '.875rem',
      background: 'white!important',
      borderColor: 'white!important',
    },
    '& input': {
      fontSize: '.875rem',
      padding: '12px 20px!important',
      color: '#4d4c50',
      fontWeight: 'normal',
    },
    '& fieldset': {
      border: 0,
      color: '#4d4c50',
    },
  },
});

interface Props {
  countryMandates: CountryMandates[];
  setCountryMandates: any;
  handleUpdateCountryMandatesStatus: any;
  companyList: CompanyType[];
  documentTypeList: DocumentType[];
  loading?: boolean;
  company?: CompanyType;
  setCompany: any;
  documentType?: DocumentType;
  setDocumentType: any;
  onUpdateAvalaraEInvoicingCountryMandatesFilter: any;
  onGetAvalaraEInvoicingCountryMandatesFilter: any;
  onCreateNewAvalaraEInvoicingCountryMandates: any;
  updateCountryMandates: any;
  getCountryMandatesMappings: any;
  getDefaultCountryMandates: any;
  setCountryMandatesMappings: any;
  onResotreFiledMappings: any;
  onGetAvalaraEInvoicingAllowedXmlPath: any;
  reloadCountryMandates: any;
}

const objectMetadata = {
  apiName: 'Tenant',
  name: 'Tenant',
  objectType: 'Entity',
  customizable: true,
  fields: [
    {
      apiName: 'clientId',
      name: 'Client Id',
      type: 'text',
      required: false,
      updatable: true,
      creatable: true,
      showTooltip: false,
      toolTipText: '',
    },
    {
      apiName: 'clientSecret',
      name: 'Client Secret',
      type: 'text',
      required: false,
      updatable: true,
      creatable: true,
      showTooltip: false,
      toolTipText: '',
    },
  ],
};

const AvalaraEInvoicingCountryMandates: React.FC<Props> = ({
  countryMandates,
  setCountryMandates,
  handleUpdateCountryMandatesStatus,
  companyList,
  documentTypeList,
  loading,
  company,
  setCompany,
  documentType,
  setDocumentType,
  onUpdateAvalaraEInvoicingCountryMandatesFilter,
  onGetAvalaraEInvoicingCountryMandatesFilter,
  onCreateNewAvalaraEInvoicingCountryMandates,
  updateCountryMandates,
  getCountryMandatesMappings,
  getDefaultCountryMandates,
  setCountryMandatesMappings,
  onResotreFiledMappings,
  onGetAvalaraEInvoicingAllowedXmlPath,
  reloadCountryMandates,
}: Props) => {
  const billingSettingsCtx = useContext(BillingSettingsContext);

  const classes = useStyle();

  const [referencedFilter, setReferencedFilter] = useState<RubyFilter | null>(null);
  const [search, setSearch] = useState('');
  const [allFiltersState, setAllFiltersState] = useState<RubyFilter[]>([]);
  const [showDialog, setShowDialog] = React.useState(false);
  const [lastModifiedDate, setLastModifiedDate] = React.useState('');
  const [showFilterDialog, setShowFilterDialog] = React.useState(false);
  const [showMappingsDialog, setShowMappingsDialog] = React.useState(false);
  const [filterName, setFilterName] = useState('');
  const [activeFilterRow, setActiveFilterRow] = useState<CountryMandates>();
  const [mappingSchema, setMappingSchema] = useState<MappingSchema[]>([]);
  const [deletedRowIds, setDeletedRowIds] = useState<(string | number)[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showRestoreDialog, setShowRestoreDialog] = React.useState(false);
  const [rowChanges, setRowChanges] = useState({});
  const [editingRowIds, setEditingRowIds] = useState<(string | number)[]>([]);
  const [mappingField, setMappingField] = useState('Required');
  const [fetchingFilter, setFetchingFilter] = useState(false);
  const [fecthingMapping, setFecthingMapping] = useState(false);
  const [allowedXmlPath, setAllowedXmlPath] = React.useState<any[]>([]);
  const [showCreateCountryMandateDialog, setShowCreateCountryMandateDialog] = useState(false);
  const [isCreatingNewCountryMandate, setIsCreatingNewCountryMandate] = useState(false);
  const [countryMandatesOptionList, setCountryMandatesOptionList] = useState([]);
  const [selectActivateCountryMandate, setSelectActivateCountryMandate] = useState<
    CountryMandates | undefined
  >();

  const [conditions, setConditions] = useState<Condition[]>([]);

  const avalarEInvoicingConnectionContext = useContext(AvalaraEInvoicingConnectionContext);

  if (!avalarEInvoicingConnectionContext) {
    throw Error(
      'Avalara E-Invoicing Connection requires AvalaraEInvoicingConnectionContext to be declared in context provider',
    );
  }

  const { showSnackbar, Snackbar } = useRubySnackbar();

  // filter the metadata field based on the field type
  const filterMetadataFieldFromFieldType = (field: RubyField, mapping: MappingSchema) => {
    switch (mapping.dataType) {
      case 'currency':
        return field.type === 'currency';
      case 'percentage':
        return field.type === 'percent';
      case 'percent':
        return field.type === 'percent';
      case 'decimal':
        return field.type === 'decimal';
      case 'number':
        return field.type === 'pickList'
          ? _.isNumber(field.valueSet?.valuePairs[0].name)
          : ['decimal', 'percent', 'integer', 'number', 'currency'].includes(field.type);
      case 'numeric':
        return field.type === 'pickList'
          ? _.isNumber(field.valueSet?.valuePairs[0].name)
          : ['decimal', 'percent', 'integer', 'number', 'currency'].includes(field.type);
      case 'string':
        return field.type === 'pickList'
          ? _.isString(field.valueSet?.valuePairs[0].name)
          : ['autoNumber', 'text', 'bId', 'longText', 'id', 'url', 'string'].includes(field.type);
      case 'text':
        return field.type === 'pickList'
          ? _.isString(field.valueSet?.valuePairs[0].name)
          : ['autoNumber', 'text', 'bId', 'longText', 'id', 'url', 'string'].includes(field.type);
      case 'checkbox':
        return field.type === 'boolean';
      case 'boolean':
        return field.type === 'boolean';
      case 'date':
        return field.type === 'date';
      case 'dateTime':
        return field.type === 'dateTime';
      default:
        return false;
    }
  };

  const showFilteringDialog = async (row: CountryMandates) => {
    let rowId;
    setActiveFilterRow(row);
    setFetchingFilter(true);
    if (!row.id) {
      const req = {
        companyId: company?.id,
        countryName: row?.countryName,
        countryCode: row?.countryCode,
        countryMandateCode: row?.countryMandateCode,
        documentType: documentType?.apiName,
      };
      const res = await onCreateNewAvalaraEInvoicingCountryMandates(req);
      rowId = res.data.id;
    }
    const res = await onGetAvalaraEInvoicingCountryMandatesFilter(row.id || rowId);
    if (res.data && res.data.length > 0) {
      const filter = JSON.parse(res.data[0].condition)[0];
      filter.name = 'Mappings Filter';
      filter.id = shortUUID().generate().toString();
      setReferencedFilter(filter);
      setAllFiltersState([filter]);
    } else {
      setReferencedFilter(null);
      setAllFiltersState([]);
    }
    setFetchingFilter(false);
    setShowFilterDialog(true);
  };

  const showMappingingsDialog = async (row: CountryMandates, showLoading: boolean) => {
    setActiveFilterRow(row);
    if (showLoading) {
      setFecthingMapping(true);
    }
    let rowId;
    if (!row.id) {
      const req = {
        companyId: company?.id,
        countryName: row?.countryName,
        countryCode: row?.countryCode,
        countryMandateCode: row?.countryMandateCode,
        documentType: documentType?.apiName,
      };
      const res = await onCreateNewAvalaraEInvoicingCountryMandates(req);
      rowId = res.data.id;
    }
    const res = await getCountryMandatesMappings(
      row.countryMandateCode,
      documentType?.apiName,
      row.id || rowId,
    );
    if (res && res.data) {
      res.data.forEach((d: MappingSchema, index: number) => {
        if (!d.id) {
          d.id = shortUUID().generate().toString() + '_system';
        }
        if (d.nueObject) {
          const nueMetadata = billingSettingsCtx?.metadataObjects.find(
            (m) => m.apiName === d.nueObject,
          );
          if (nueMetadata) {
            d.valueSet = {
              valuePairs: nueMetadata.fields,
            };
          }
          if (d.nueObject) {
            d.disableStaticValue = true;
          }
        }
        if (d.staticValue) {
          d.disableNueMappings = true;
        }
      });
      setMappingSchema(res.data);
    }
    const allowedPath = await onGetAvalaraEInvoicingAllowedXmlPath(
      row?.countryMandateCode,
      documentType?.apiName,
    );
    setAllowedXmlPath(allowedPath.data);
    setFecthingMapping(false);
    setShowMappingsDialog(true);
  };

  const closeFilteringDialog = () => {
    setActiveFilterRow(undefined);
    setShowFilterDialog(false);
  };

  useEffect(() => {
    if (activeFilterRow) {
      setFilterName(`${activeFilterRow?.countryMandateCode} Filter`);
    }
  }, [activeFilterRow]);

  const setup = async () => {
    const res = await getDefaultCountryMandates(company?.id);
    console.log('getDefaultCountryMandates', res);
    if (res.data && res.data.length > 0) {
      setCountryMandatesOptionList(res.data);
    }
  };

  useEffect(() => {
    setup();
  }, []);

  const handleUpdateReferencedFilter = (value: RubyFilter) => {
    setReferencedFilter(value || null);
  };

  const handleUpdateFilterNameWithValue = (value: string) => {
    setFilterName(value);
  };

  const columns = [
    {
      name: 'name',
      title: 'COUNTRY',
      apiName: 'countryName',
      type: 'text',
      cellRenderer: ({ row }: CellRendererRowProps) => {
        return (
          <Grid>
            <Tooltip title={row.countryName} arrow placement="top">
              <span>{row.countryName}</span>
            </Tooltip>
          </Grid>
        );
      },
    },
    {
      name: 'mandateId',
      title: 'MANDATE ID',
      apiName: 'countryMandateCode',
      type: 'text',
      cellRenderer: ({ row }: CellRendererRowProps) => {
        return (
          <Grid className={classes.flexCenterStart}>
            <Tooltip title={row.countryMandateCode} arrow placement="top">
              <span>{row.countryMandateCode}</span>
            </Tooltip>
            <Grid
              style={{
                display: 'flex',
                marginLeft: '5px',
              }}
            >
              <Tooltip title={row.description || row.countryMandateCode} arrow placement="right">
                <HelpOutline className={classes.questionMarkIcon} />
              </Tooltip>
            </Grid>
          </Grid>
        );
      },
    },
    {
      name: 'filter',
      title: 'EXECUTION ROLE',
      apiName: 'filter',
      type: 'text',
      cellRenderer: ({ row }: CellRendererRowProps) => {
        return (
          <Grid className={classes.flexCenterStart} onClick={() => showFilteringDialog(row)}>
            <CategoryOutlinedIcon className={classes.helpIcon} />
            <Grid className={classes.filter}>{!row.active ? 'Edit Filter' : 'View Filter'}</Grid>
          </Grid>
        );
      },
    },
    {
      name: 'mappings',
      title: 'MANDATE MAPPINGS',
      apiName: 'mappings',
      type: 'text',
      cellRenderer: ({ row }: CellRendererRowProps) => {
        return (
          <Grid
            className={classes.flexCenterStart}
            onClick={() => showMappingingsDialog(row, true)}
          >
            <FormatListBulletedOutlinedIcon className={classes.helpIcon} />
            <Grid className={classes.filter}>{!row.active ? 'Edit Mapping' : 'View Mapping'}</Grid>
          </Grid>
        );
      },
    },
    {
      name: 'active',
      title: 'ACTIVE',
      apiName: 'active',
      type: 'text',
      cellRenderer: ({ row }: CellRendererRowProps) => {
        return (
          <RubySwitch
            value={row.active}
            name={'Active'}
            labelPlacement="end"
            label=""
            handleInputChange={async (value) => {
              let rowId;
              if (!row.id) {
                const req = {
                  companyId: company?.id,
                  countryName: row?.countryName,
                  countryCode: row?.countryCode,
                  countryMandateCode: row?.countryMandateCode,
                  documentType: documentType?.apiName,
                };
                const res = await onCreateNewAvalaraEInvoicingCountryMandates(req);
                rowId = res.data.id;
              }
              const res = await onGetAvalaraEInvoicingCountryMandatesFilter(row.id || rowId);
              if (res.data.length === 0) {
                showSnackbar(
                  'error',
                  'Cannot activate the mandate',
                  'Please create a filter before activating the country mandate.',
                );
                return;
              } else {
                const action = value ? 'activate' : 'deactivate';
                handleUpdateCountryMandatesStatus(action, row.id || rowId);
              }
            }}
          />
        );
      },
    },
  ];
  const columnWidths = [
    { columnName: 'name', width: 150 },
    { columnName: 'mandateId', width: 200 },
    { columnName: 'filter', width: 150 },
    { columnName: 'mappings', width: 150 },
    { columnName: 'active', width: 150 },
  ];

  const handleUpdateCountryMandatesFilter = async (searchResult: string, filters: RubyFilter[]) => {
    const filterConditions = GraphQLQueryConstructor.construct().conditionsFromFilters(filters);

    const whereCondition = `( where: ${filterConditions})`;
    const relationFields = buildExtraRelationFields(columns);

    const query = GraphQLQueryConstructor.construct().queryWithWhereCondition(
      (billingSettingsCtx?.metadataObjects.find((m) => m.apiName === documentType?.apiName) ||
        billingSettingsCtx?.metadataObjects[0])!,
      whereCondition,
      relationFields,
    );
    if (!activeFilterRow?.id) {
      const req = {
        companyId: company?.id,
        countryName: activeFilterRow?.countryName,
        countryCode: activeFilterRow?.countryCode,
        countryMandateCode: activeFilterRow?.countryMandateCode,
        documentType: documentType?.apiName,
      };
      const res = await onCreateNewAvalaraEInvoicingCountryMandates(req);
      if (res.data.id) {
        const r = {
          templateId: res.data.id,
          name: filterName,
          graphqlFilter: query,
          condition: JSON.stringify(filters),
        };
        await onUpdateAvalaraEInvoicingCountryMandatesFilter(r);
        showSnackbar('confirm', 'Success', 'The filter has been updated.');
        await updateCountryMandates(company, documentType);
      }
    } else {
      const r = {
        templateId: activeFilterRow.id,
        name: filterName,
        graphqlFilter: query,
        condition: JSON.stringify(filters),
      };
      await onUpdateAvalaraEInvoicingCountryMandatesFilter(r);
      showSnackbar('confirm', 'Success', 'The filter has been updated.');
      await updateCountryMandates(company, documentType);
    }
  };

  const handleCloseMappingsDialog = () => {
    setActiveFilterRow(undefined);
    setShowMappingsDialog(false);
  };

  const updateFieldMappingsParams = (fieldMapping: MappingSchema) => {
    if (
      fieldMapping.id &&
      fieldMapping.id?.includes('update') &&
      fieldMapping.id?.includes('system')
    ) {
      delete fieldMapping.id;
    }

    if (
      fieldMapping.id &&
      fieldMapping.id?.includes('update') &&
      !fieldMapping.id?.includes('system')
    ) {
      fieldMapping.id = fieldMapping.id.replace('_update', '');
    }

    if (fieldMapping.valueSet) {
      delete fieldMapping.valueSet;
    }
    fieldMapping.templateId = activeFilterRow?.id;
    if (fieldMapping.elrXpath) {
      //@ts-ignore
      fieldMapping.optionality = allowedXmlPath.find(
        (path: any) => path.path === fieldMapping.elrXpath,
      )!.optionality;
    }
    return fieldMapping;
  };

  const handleSaveFieldMappings = async () => {
    const request = [...mappingSchema];
    const updatedRequest = request.filter((r) => r.id && r.id?.includes('update'));
    console.log('updatedRequest', updatedRequest);
    // const duplicatedRequest = request.filter(r => r.id && r.id?.includes('dup'))
    // const deletedRequest = request.filter(r => r.id && deletedRowIds.includes(r.id))
    const req = updatedRequest.map((u) => updateFieldMappingsParams(u));
    // duplicatedRequest.forEach(u => updateFieldMappingsParams(u))
    // request.forEach(r => updateFieldMappingsParams(r))
    const updateRes = await setCountryMandatesMappings(req);
    const res = await getCountryMandatesMappings(
      activeFilterRow?.countryMandateCode,
      documentType?.apiName,
      activeFilterRow?.id,
    );
    if (res && res.data) {
      res.data.forEach((d: MappingSchema, index: number) => {
        if (!d.id) {
          d.id = shortUUID().generate().toString() + '_system';
        }
        if (d.nueObject) {
          const nueMetadata = billingSettingsCtx?.metadataObjects.find(
            (m) => m.apiName === d.nueObject,
          );
          if (nueMetadata) {
            d.valueSet = {
              valuePairs: nueMetadata.fields,
            };
          }
        }
      });
      setMappingSchema(res.data);
    }
  };

  const handleRestoreFieldMappings = async () => {
    setShowRestoreDialog(true);
  };

  const handleNewCountryMandate = async () => {
    setShowCreateCountryMandateDialog(true);
  };

  if (!billingSettingsCtx) {
    return null;
  }

  return (
    <>
      <Snackbar />
      <Typography variant="h6" className={classes.heading} gutterBottom>
        Configure Country Mandates
      </Typography>
      <Grid item xs={12} className={classes.subHeader}>
        <Grid
          container
          spacing={3}
          className={classes.filterContainer}
          style={{
            alignItems: 'flex-end',
          }}
        >
          <Grid item xs={3}>
            {
              <PickList
                value={company?.id}
                label="COMPANY"
                field={{
                  apiName: 'statusFilter',
                  name: 'Status Filter',
                  type: 'text',
                }}
                name="params"
                defaultText={companyList[0]?.companyName}
                options={companyList.map((c: CompanyType) => ({
                  name: c.companyName,
                  value: c.id,
                  label: c.companyName,
                }))}
                handleInputChange={async (id) => {
                  const activeCompany = companyList.find((c: CompanyType) => c.id === id);
                  setCompany(activeCompany);
                  const res = await getDefaultCountryMandates(activeCompany?.id);
                  console.log('getDefaultCountryMandates', res);
                  if (res.data && res.data.length > 0) {
                    setCountryMandatesOptionList(res.data);
                  }
                }}
              />
            }
          </Grid>
          <Grid item xs={3}>
            {
              <PickList
                value={documentType?.id}
                label="DOCUMENT TYPE"
                field={{
                  apiName: 'eventFilter',
                  name: 'Event Filter',
                  type: 'text',
                }}
                name="params"
                defaultText={documentTypeList[0]?.name}
                options={documentTypeList.map((d: DocumentType) => ({
                  ...d,
                  value: d?.id,
                  name: d?.name || '',
                  label: d?.name,
                }))}
                handleInputChange={(value) => {
                  if (setDocumentType) {
                    setDocumentType(documentTypeList.find((d: DocumentType) => d.id === value)!);
                  }
                }}
              />
            }
          </Grid>
          <Grid
            item
            xs={6}
            style={{
              textAlign: 'right',
            }}
          >
            {<RubyButton text="New Country Mandate" onClick={handleNewCountryMandate} />}
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} className={classes.buttonBarContainer}>
        <RubyGrid
          enablePaging={false}
          // sort={sort}
          // columnHeaders={columnHeaders}
          rows={countryMandates}
          // disabledSortFields={columnSortableState}
          // hiddenColumns={hiddenObjectColumns}
          columnWidths={columnWidths}
          // columnOrder={columnOrder}
          columnReordering={true}
          columns={columns}
          // TODO:
          // onColumnOrderChange={async (nextOrder) => {
          //   await updateListViewLocalState({ columnOrder: nextOrder });
          // } }
          // onHiddenColumnsChange={async (nextHiddenColumns) => {
          //   const nextSelectedColumns = columns
          //     .filter((_) => !nextHiddenColumns.includes(_.name))
          //     .map((_) => _.name);
          //   await updateListViewLocalState({ selectedColumns: nextSelectedColumns });
          // } }
          // sortingList={sortingList.map((_) => {
          //   return {
          //     columnName: _.field,
          //     direction: _.order.toLowerCase() === 'desc' ? 'desc' : 'asc',
          //   };
          // })}
          loading={loading}
          pageSize={0}
          columnOrder={[]}
        />
      </Grid>
      {showFilterDialog && activeFilterRow && (
        <DialogComponent
          width="md"
          open={showFilterDialog}
          handleClose={closeFilteringDialog}
          title={`${activeFilterRow?.countryMandateCode} Filter`}
        >
          {
            <FilterBuilder
              disabled={activeFilterRow.active}
              filterName={activeFilterRow?.countryMandateCode || ''}
              handleUpdateFilterNameWithValue={handleUpdateFilterNameWithValue}
              //@ts-ignore
              referencedFilter={referencedFilter}
              handleUpdateReferencedFilter={handleUpdateReferencedFilter}
              searchResult={search}
              handleSearch={handleUpdateCountryMandatesFilter}
              handleClose={closeFilteringDialog}
              objectMetadata={
                billingSettingsCtx?.metadataObjects.find(
                  (m) => m.apiName === documentType?.apiName,
                ) || billingSettingsCtx?.metadataObjects[0]
              }
              allMetadatas={billingSettingsCtx?.metadataObjects}
              savedFilters={allFiltersState}
              disableDeleteButton={true}
              disableApplyButton={true}
              disableSave={activeFilterRow?.active}
            />
          }
        </DialogComponent>
      )}
      {
        <DialogComponent open={fetchingFilter} title={' '} width={'md'}>
          <Loading loadingText="Loading ..." />
        </DialogComponent>
      }
      {
        <DialogComponent open={fecthingMapping} title={' '} width={'md'}>
          <Loading loadingText="Loading ..." />
        </DialogComponent>
      }
      {showMappingsDialog && (
        <AvalaraEInvoicingEditingMappingsDialog
          open={showMappingsDialog}
          onClose={() => {
            setShowMappingsDialog(false);
          }}
          mappingSchema={mappingSchema}
          documentType={documentType}
          setMappingSchema={setMappingSchema}
          countryMandate={activeFilterRow}
          handleClose={handleCloseMappingsDialog}
          doSave={handleSaveFieldMappings}
          doRestore={handleRestoreFieldMappings}
          deletedRowIds={deletedRowIds}
          setDeletedRowIds={setDeletedRowIds}
          rowChanges={rowChanges}
          setRowChanges={setRowChanges}
          editingRowIds={editingRowIds}
          setEditingRowIds={setEditingRowIds}
          mappingField={mappingField}
          setMappingField={setMappingField}
          //@ts-ignore
          activeFilterRow={activeFilterRow}
          allowedXmlPath={allowedXmlPath}
        />
      )}
      <DialogComponent
        title={`Restore to Default`}
        open={showRestoreDialog}
        submitButtonText="Yes"
        cancelButtonText="No"
        width={'sm'}
        handleSubmit={async () => {
          setIsSubmitting(true);
          await onResotreFiledMappings(activeFilterRow?.id || '');
          if (activeFilterRow) {
            await showMappingingsDialog(activeFilterRow, false);
          }
          setIsSubmitting(false);
          setDeletedRowIds([]);
          setRowChanges({});
          setEditingRowIds([]);
          setMappingField('Required');
          setShowRestoreDialog(false);
          showSnackbar(
            'confirm',
            'Success',
            'The mapping has been restored to the system defaults.',
          );
        }}
        handleClose={() => {
          setShowRestoreDialog(false);
        }}
        processing={isSubmitting}
      >
        <p>The custom mapping will be restored to the system defaults. Do you wish to proceed?</p>
      </DialogComponent>

      <DialogComponent
        title={`New Country Mandate`}
        open={showCreateCountryMandateDialog}
        submitButtonText="Save and Close"
        cancelButtonText="Cancel"
        width={'sm'}
        handleSubmit={async () => {
          if (!selectActivateCountryMandate) {
            return;
          }
          setIsCreatingNewCountryMandate(true);
          const req = {
            companyId: company?.id,
            countryName: selectActivateCountryMandate?.countryName,
            countryCode: selectActivateCountryMandate?.countryCode,
            countryMandateCode: selectActivateCountryMandate?.countryMandateCode,
            documentType: documentType?.apiName,
          };
          const res = await onCreateNewAvalaraEInvoicingCountryMandates(req);
          setIsCreatingNewCountryMandate(false);
          setSelectActivateCountryMandate(undefined);
          setShowCreateCountryMandateDialog(false);
          showSnackbar('confirm', 'Success', 'The Country Mandate has been successfully created.');
          const result = await updateCountryMandates(company, documentType);
        }}
        handleClose={() => {
          setShowCreateCountryMandateDialog(false);
        }}
        processing={isCreatingNewCountryMandate}
      >
        <h3
          style={{
            marginBottom: '20px',
          }}
        >
          {'Country Mandate Id'}
        </h3>
        <Autocomplete
          id="combo-box-demo"
          options={countryMandatesOptionList || []}
          disableClearable={true}
          getOptionLabel={(option: CountryMandates) => option.countryMandateCode}
          style={{
            width: '50%',
            padding: 0,
            background: 'white',
            borderRadius: '5px',
            marginBottom: '20px',
          }}
          classes={{ option: classes.autocompleteOption }}
          onChange={(event: object, value: any, reason: string) => {
            const newValue = value;
            console.log('onChange', newValue);
            setSelectActivateCountryMandate(newValue);
          }}
          renderInput={(params: any) => (
            <TextField {...params} label="" variant="outlined" className={classes.inputRoot} />
          )}
        />
      </DialogComponent>
    </>
  );
};

export default AvalaraEInvoicingCountryMandates;

import React from 'react';

import AppBar from '@material-ui/core/AppBar';
import Button from '@material-ui/core/Button';
import Toolbar from '@material-ui/core/Toolbar';
import { makeStyles } from '@material-ui/core/styles';
import HighlightOffOutlined from '@material-ui/icons/HighlightOffOutlined';

import { Props } from './interface';

const defaultProps = {
  showColoseButton: true,
};

const useStyles = makeStyles({
  appBar: {
    backgroundColor: '#E9E4F8',
    boxShadow: 'none',
  },
  btn: {
    borderRadius: 0,
    minWidth: '112px',
    backgroundColor: '#6239eb',
    fontSize: '.875rem',
    color: '#fff',
    textTransform: 'none',
    cursor: 'pointer',
    minHeight: '45px',
    '&:hover': {
      // you want this to be the same as the backgroundColor above
      backgroundColor: '#6239eb',
    },
  },
  toolbar: {
    display: 'flex',
    padding: 0,
    minHeight: '45px',
    justifyContent: 'space-between',
  },
});

const ModalAppBar: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const { showColoseButton, titleStyle } = props;
  const classes = useStyles();

  return (
    //@ts-ignore
    <AppBar position={userProps.position || 'static'} className={classes.appBar}>
      <Toolbar className={classes.toolbar}>
        <div style={titleStyle}>{userProps.title}</div>
        {showColoseButton && (
          <Button
            disableRipple
            className={classes.btn}
            onClick={() => props.handleClose()}
            endIcon={<HighlightOffOutlined />}
          >
            Close
          </Button>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default ModalAppBar;

import SvgIcon, { type SvgIconProps } from '@material-ui/core/SvgIcon';
import React from 'react';
import type { Props } from './interface';

export const MaterialSymbolsDragPan: React.FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="m12 22l-4.25-4.25l1.425-1.425L11 18.15V13H5.875L7.7 14.8l-1.45 1.45L2 12l4.225-4.225L7.65 9.2L5.85 11H11V5.85L9.175 7.675L7.75 6.25L12 2l4.25 4.25l-1.425 1.425L13 5.85V11h5.125L16.3 9.2l1.45-1.45L22 12l-4.25 4.25l-1.425-1.425L18.15 13H13v5.125l1.8-1.825l1.45 1.45z"
        />
      </svg>
    </SvgIcon>
  );
};

export const RoundRefreshIcon: React.FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        preserveAspectRatio="xMidYMid meet"
      >
        <title>Round Settings Icon</title>
        <g id="action" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="backup" transform="translate(-1819.000000, -888.000000)">
            <g id="group-10" transform="translate(1819.000000, 888.000000)">
              <rect
                id="rectangle"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
              <path
                d="M21.636275,9.09674668 C21.2528843,8.82795641 20.7173874,8.91963682 20.4465135,9.30511123 L19.7589104,10.2865083 C18.9712925,6.14422103 15.3290802,3 10.9617592,3 C6.01935214,3 2,7.02143578 2,11.9617592 C2,16.9020827 6.01935214,20.9235184 10.9617592,20.9235184 C13.0329028,20.9235184 15.0540389,20.2004935 16.6501114,18.8857132 C17.0147493,18.5856682 17.0668405,18.0480877 16.7667955,17.6834497 C16.4667506,17.3188118 15.9291701,17.2667206 15.5645321,17.5667656 C14.2726719,18.6294248 12.6370102,19.2149291 10.9596756,19.2149291 C6.96115989,19.2170128 3.70650565,15.9623585 3.70650565,11.9617592 C3.70650565,7.96115989 6.96115989,4.70650565 10.9617592,4.70650565 C14.5643823,4.70650565 17.5523299,7.34648448 18.1128305,10.7928341 L16.881396,9.92812123 C16.4980053,9.65933096 15.9625084,9.75101136 15.6916345,10.1364858 C15.4228442,10.5198765 15.5145246,11.0553734 15.899999,11.3262473 L18.6941676,13.2890414 C19.0775584,13.5578317 19.6130553,13.4661513 19.8839292,13.0806768 L21.8467232,10.2865083 C22.1155135,9.90103384 22.0217494,9.36762059 21.636275,9.09674668 Z"
                id="path"
                stroke="#787878"
                strokeWidth="0.2"
                fill="#787878"
                fillRule="nonzero"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RoundSettingsIcon: React.FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg
        width="24px"
        height="24px"
        viewBox="-1 -2 20 21"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Round Settings Icon</title>
        <g id="action" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="backup" transform="translate(-1876.000000, -891.000000)" fill="#787878">
            <g id="Action-setting_24-o-240027" transform="translate(1873.000000, 888.000000)">
              <path
                d="M14.8844743,3.44173738 C16.0104974,3.82260715 17.0495969,4.42496877 17.9410381,5.21361471 C18.195114,5.43813429 18.2783158,5.80027149 18.1478614,6.11381715 C17.8464762,6.84230994 17.8999769,7.67010756 18.292606,8.35340112 C18.6852352,9.03669469 19.3722711,9.497655 20.1513632,9.60051674 C20.488645,9.64401428 20.7602736,9.89944398 20.8255124,10.2344621 C21.0581625,11.4036369 21.0581625,12.6074572 20.8255124,13.7766319 C20.7602736,14.1116501 20.488645,14.3670798 20.1513632,14.4105773 C19.3722711,14.513439 18.6852352,14.9743994 18.292606,15.6576929 C17.8999769,16.3409865 17.8464762,17.1687841 18.1478614,17.8972769 C18.27869,18.2104912 18.1961368,18.5725697 17.9426169,18.7974793 C17.0497564,19.582339 16.0101687,20.1809012 14.8844743,20.5582626 C14.5633103,20.6671831 14.2087285,20.5581039 14.0035018,20.287251 C13.5253898,19.6622572 12.7850012,19.2959097 12,19.2959097 C11.2149988,19.2959097 10.4746102,19.6622572 9.99649818,20.287251 C9.846765,20.484554 9.61364049,20.6 9.36655553,20.5994691 C9.28121011,20.5994459 9.1964332,20.5855298 9.11552575,20.5582626 C7.98950258,20.1773928 6.95040314,19.5750312 6.0589619,18.7863853 C5.80488595,18.5618657 5.72168422,18.1997285 5.85213862,17.8861828 C6.15352382,17.1576901 6.10002314,16.3298924 5.70739399,15.6465989 C5.31476484,14.9633053 4.62772885,14.502345 3.8486368,14.3994833 C3.51135502,14.3559857 3.23972638,14.100556 3.17448765,13.7655379 C2.94183745,12.5963631 2.94183745,11.3925428 3.17448765,10.2233681 C3.23972638,9.88834994 3.51135502,9.63292023 3.8486368,9.5894227 C4.62772885,9.48656096 5.31476484,9.02560064 5.70739399,8.34230708 C6.10002314,7.65901352 6.15352382,6.8312159 5.85213862,6.10272311 C5.72130996,5.78950878 5.80386318,5.42743025 6.05738309,5.20252067 C6.95024359,4.41766105 7.98983127,3.81909884 9.11552575,3.44173738 C9.43668966,3.33281687 9.79127145,3.44189611 9.99649818,3.71274903 C10.4746102,4.33774282 11.2149988,4.70409026 12,4.70409026 C12.7850012,4.70409026 13.5253898,4.33774282 14.0035018,3.71274903 C14.2087285,3.44189611 14.5633103,3.33281687 14.8844743,3.44173738 Z M9.05699418,5.61318898 C8.67518205,5.78886272 8.31041587,5.99870952 7.9678769,6.23955564 C8.16481952,7.29245157 7.98503578,8.3928221 7.44149675,9.33874453 C6.89830055,10.2840703 6.03941589,10.9922114 5.02939779,11.3505856 C4.99041033,11.7780192 4.99043749,12.2086135 5.02963658,12.6377857 C6.0379837,12.9955141 6.89784773,13.7040476 7.44149675,14.6501614 C7.98502643,15.5960676 8.16481334,16.6964165 7.96717217,17.7510191 C8.31117769,17.9951255 8.67645297,18.2072024 9.05824985,18.3845573 C9.8701248,17.6887604 10.9109065,17.2959097 12,17.2959097 C13.0885019,17.2959097 14.1287446,17.6883337 14.9430058,18.386811 C15.3248179,18.2111373 15.6895841,18.0012905 16.0321231,17.7604444 C15.8351805,16.7075484 16.0149642,15.6071779 16.5585032,14.6612555 C17.1016994,13.7159297 17.9605841,13.0077886 18.9706022,12.6494144 C19.0095897,12.2219808 19.0095625,11.7913865 18.9703634,11.3622143 C17.9620163,11.0044859 17.1021523,10.2959524 16.5585032,9.34983858 C16.0149775,8.40393934 15.8351893,7.30359957 16.0328235,6.24900405 C15.6890564,6.00510542 15.323859,5.79310087 14.9416169,5.61555686 C14.1297613,6.31128262 13.0890339,6.70409026 12,6.70409026 C10.9114981,6.70409026 9.87125545,6.31166629 9.05699418,5.61318898 Z M12,9 C13.6568542,9 15,10.3431458 15,12 C15,13.6568542 13.6568542,15 12,15 C10.3431458,15 9,13.6568542 9,12 C9,10.3431458 10.3431458,9 12,9 Z M12,11 C11.4477153,11 11,11.4477153 11,12 C11,12.5522847 11.4477153,13 12,13 C12.5522847,13 13,12.5522847 13,12 C13,11.4477153 12.5522847,11 12,11 Z"
                id="Shape"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RoundDownloadIcon: React.FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Round Download Icon</title>
        <g id="action" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="backup" transform="translate(-1711.000000, -888.000000)">
            <g id="group-8" transform="translate(1711.000000, 888.000000)">
              <rect
                id="rectanglebackup-2"
                stroke="#979797"
                fill={props.fill ? props.fill : '#D8D8D8'}
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
              <g
                id="group-6backup"
                transform="translate(2.000000, 4.000000)"
                stroke={props.stroke ? props.stroke : '#787878'}
                strokeLinecap="round"
                strokeWidth="2"
              >
                <g id="download" transform="translate(0.000000, 6.666667)">
                  <path
                    d="M0,0 C0,5.5228475 4.4771525,10 10,10 C15.5228475,10 20,5.5228475 20,0"
                    id="path"
                  />
                </g>
                <line
                  x1="10.4166667"
                  y1="0.416666667"
                  x2="10.4166667"
                  y2="10.4166667"
                  id="straightline"
                />
                <path
                  d="M5.85869084,7.51647774 L10.1017975,11.7595844 C10.1994286,11.8572155 10.3577198,11.8572155 10.4553509,11.7595844 L14.6984576,7.51647774 L14.6984576,7.51647774"
                  id="path-2"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DownloadIcon: React.FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props} style={{ color: '#6239EB' }}>
      <svg
        width="20px"
        height="20px"
        viewBox="-4 -4 28 28"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="action" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="backup" transform="translate(-1711.000000, -888.000000)">
            <g id="group-8" transform="translate(1711.000000, 888.000000)">
              <rect
                id="rectanglebackup-2"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
              <g
                id="group-6backup"
                transform="translate(2.000000, 4.000000)"
                stroke="#6239EB"
                strokeLinecap="round"
                strokeWidth="2"
              >
                <g id="download" transform="translate(0.000000, 6.666667)">
                  <path
                    d="M0,0 C0,5.5228475 4.4771525,10 10,10 C15.5228475,10 20,5.5228475 20,0"
                    id="path"
                  />
                </g>
                <line
                  x1="10.4166667"
                  y1="0.416666667"
                  x2="10.4166667"
                  y2="10.4166667"
                  id="straightline"
                />
                <path
                  d="M5.85869084,7.51647774 L10.1017975,11.7595844 C10.1994286,11.8572155 10.3577198,11.8572155 10.4553509,11.7595844 L14.6984576,7.51647774 L14.6984576,7.51647774"
                  id="path-2"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const InvoiceDownloadIcon: React.FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props} viewBox={props.viewBox ? props.viewBox : '0 0 17 16'}>
      <svg
        width={props.width ? props.width : '17'}
        height={props.height ? props.height : '16'}
        viewBox={props.viewBox ? props.viewBox : '0 0 17 16'}
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.5 12L3.5 7L4.9 5.55L7.5 8.15V0H9.5V8.15L12.1 5.55L13.5 7L8.5 12ZM2.5 16C1.95 16 1.47917 15.8042 1.0875 15.4125C0.695833 15.0208 0.5 14.55 0.5 14V11H2.5V14H14.5V11H16.5V14C16.5 14.55 16.3042 15.0208 15.9125 15.4125C15.5208 15.8042 15.05 16 14.5 16H2.5Z"
          fill={props.fill ? props.fill : '#6239EB'}
        />
      </svg>
    </SvgIcon>
  );
};

export const RoundUploadIcon: React.FC<SvgIconProps> = (props) => {
  return (
    <SvgIcon {...props}>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Round Upload Icon</title>
        <g id="action" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="backup" transform="translate(-1765.000000, -888.000000)">
            <g id="group-9" transform="translate(1765.000000, 888.000000)">
              <rect
                id="rectanglebackup"
                stroke="#979797"
                fill={props.fill ? props.fill : '#D8D8D8'}
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
              <g
                id="group-6"
                transform="translate(2.000000, 4.000000)"
                stroke={props.stroke ? props.stroke : '#787878'}
                strokeLinecap="round"
                strokeWidth="2"
              >
                <g id="download" transform="translate(0.000000, 6.666667)">
                  <path
                    d="M0,0 C0,5.5228475 4.4771525,10 10,10 C15.5228475,10 20,5.5228475 20,0"
                    id="path"
                  />
                </g>
                <g
                  id="group-7"
                  transform="translate(10.278574, 5.916404) scale(1, -1) translate(-10.278574, -5.916404) translate(5.858691, 0.000000)"
                >
                  <line
                    x1="4.55797582"
                    y1="0.416666667"
                    x2="4.55797582"
                    y2="10.4166667"
                    id="straightline"
                  />
                  <path
                    d="M0,7.51647774 L4.24310667,11.7595844 C4.34073774,11.8572155 4.49902898,11.8572155 4.59666006,11.7595844 L8.83976672,7.51647774 L8.83976672,7.51647774"
                    id="path-2"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const HintIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-6-1-Edit-Lines-(Tutorial)" transform="translate(-1096.000000, -788.000000)">
            <g id="Group-2" transform="translate(1096.000000, 772.000000)">
              <g id="Group-15" transform="translate(0.000000, 16.000000)">
                <circle id="Oval" fill="#FFFFFF" cx="16" cy="16" r="16" />
                <g id="lightbulb-line" transform="translate(6.000000, 6.000000)">
                  <polygon id="Path" points="0 0 20 0 20 20 0 20" />
                  <path
                    d="M8.31083333,15 L9.16666667,15 L9.16666667,10.8333333 L10.8333333,10.8333333 L10.8333333,15 L11.6891667,15 C11.7991667,13.9983333 12.31,13.1716667 13.1391667,12.2691667 C13.2333333,12.1675 13.8325,11.5466667 13.9033333,11.4583333 C15.5396652,9.41405109 15.3216167,6.45356542 13.4034324,4.67108377 C11.4852481,2.88860212 8.51674339,2.8879685 6.59779832,4.66963113 C4.67885324,6.45129375 4.45954096,9.41168607 6.095,11.4566667 C6.16666667,11.5458333 6.7675,12.1675 6.86,12.2683333 C7.69,13.1716667 8.20083333,13.9983333 8.31083333,15 L8.31083333,15 Z M8.********,16.6666667 L8.********,17.5 L11.6666667,17.5 L11.6666667,16.6666667 L8.********,16.6666667 Z M4.795,12.5 C2.61297734,9.77375759 2.90436317,5.82571106 5.46298367,3.44935213 C8.02160416,1.07299321 11.9803889,1.07362712 14.5382482,3.45080534 C17.0961075,5.82798356 17.386229,9.7761232 15.2033333,12.5016667 C14.6866667,13.145 13.3333333,14.1666667 13.3333333,15.4166667 L13.3333333,17.5 C13.3333333,18.4204746 12.5871412,19.1666667 11.6666667,19.1666667 L8.********,19.1666667 C7.41285875,19.1666667 6.********,18.4204746 6.********,17.5 L6.********,15.4166667 C6.********,14.1666667 5.3125,13.145 4.795,12.5 Z"
                    id="Shape"
                    fill="#000000"
                    fillRule="nonzero"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const OrganizationIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-445.000000, -416.000000)">
            <g id="Group" transform="translate(380.000000, 372.000000)">
              <g id="building-4-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M28,26.6666667 L30.6666667,26.6666667 L30.6666667,29.3333333 L1.********,29.3333333 L1.********,26.6666667 L4,26.6666667 L4,4 C4,3.26362033 4.********,2.******** 5.********,2.******** L26.6666667,2.******** C27.4030463,2.******** 28,3.26362033 28,4 L28,26.6666667 Z M25.3333333,26.6666667 L25.3333333,5.******** L6.********,5.******** L6.********,26.6666667 L25.3333333,26.6666667 Z M10.6666667,14.6666667 L14.6666667,14.6666667 L14.6666667,17.3333333 L10.6666667,17.3333333 L10.6666667,14.6666667 Z M10.6666667,9.******** L14.6666667,9.******** L14.6666667,12 L10.6666667,12 L10.6666667,9.******** Z M10.6666667,20 L14.6666667,20 L14.6666667,22.6666667 L10.6666667,22.6666667 L10.6666667,20 Z M17.3333333,20 L21.3333333,20 L21.3333333,22.6666667 L17.3333333,22.6666667 L17.3333333,20 Z M17.3333333,14.6666667 L21.3333333,14.6666667 L21.3333333,17.3333333 L17.3333333,17.3333333 L17.3333333,14.6666667 Z M17.3333333,9.******** L21.3333333,9.******** L21.3333333,12 L17.3333333,12 L17.3333333,9.******** Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const UsersIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-775.000000, -416.000000)">
            <g id="Group" transform="translate(710.000000, 372.000000)">
              <g id="group-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M2.********,29.3333333 C2.********,23.442296 7.442296,18.6666667 13.3333333,18.6666667 C19.2243707,18.6666667 24,23.442296 24,29.3333333 L21.3333333,29.3333333 C21.3333333,24.9150553 17.7516113,21.3333333 13.3333333,21.3333333 C8.91505533,21.3333333 5.********,24.9150553 5.********,29.3333333 L2.********,29.3333333 L2.********,29.3333333 Z M13.3333333,17.3333333 C8.91333333,17.3333333 5.********,13.7533333 5.********,9.******** C5.********,4.91333333 8.91333333,1.******** 13.3333333,1.******** C17.7533333,1.******** 21.3333333,4.91333333 21.3333333,9.******** C21.3333333,13.7533333 17.7533333,17.3333333 13.3333333,17.3333333 Z M13.3333333,14.6666667 C16.28,14.6666667 18.6666667,12.28 18.6666667,9.******** C18.6666667,6.38666667 16.28,4 13.3333333,4 C10.3866667,4 8,6.38666667 8,9.******** C8,12.28 10.3866667,14.6666667 13.3333333,14.6666667 Z M24.3786667,19.604 C28.2060998,21.3277718 30.6670981,25.1356395 30.6666667,29.3333333 L28,29.3333333 C28.0006243,26.1848922 26.1548003,23.3287443 23.284,22.036 L24.3773333,19.604 L24.3786667,19.604 Z M23.4613333,4.55066667 C26.2087344,5.68313483 28.0011039,8.36168467 28.0000005,11.3333333 C28.0011637,15.1257426 25.1100964,18.293196 21.3333333,18.6373333 L21.3333333,15.9533333 C23.3613531,15.6628744 24.9627747,14.0836751 25.2815427,12.0599119 C25.6003106,10.0361487 24.5618716,8.04113847 22.7213333,7.14133333 L23.4613333,4.55066667 L23.4613333,4.55066667 Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const NotificationIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-445.000000, -560.000000)">
            <g id="Group" transform="translate(380.000000, 516.000000)">
              <g id="notification-4-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M24,13.3333333 C24,8.91505533 20.418278,5.******** 16,5.******** C11.581722,5.******** 8,8.91505533 8,13.3333333 L8,24 L24,24 L24,13.3333333 Z M26.6666628,24.8893333 L27.2,25.6 C27.3515088,25.8020118 27.3758795,26.0722864 27.2629515,26.2981424 C27.1500235,26.5239984 26.9191814,26.6666667 26.6666628,26.6666667 L5.********,26.6666667 C5.08081863,26.6666667 4.84997655,26.5239984 4.73704854,26.2981424 C4.62412053,26.0722864 4.64849118,25.8020118 4.8,25.6 L5.********,24.8893333 L5.********,13.3333333 C5.********,7.442296 10.1089627,2.******** 16,2.******** C21.8910373,2.******** 26.6666628,7.442296 26.6666628,13.3333333 L26.6666628,24.8893333 L26.6666628,24.8893333 Z M12.6666667,28 L19.3333333,28 C19.3333333,29.8409492 17.8409492,31.3333333 16,31.3333333 C14.1590508,31.3333333 12.6666667,29.8409492 12.6666667,28 L12.6666667,28 Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SalesforceIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 40 28'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="40px" height="28px" viewBox="0 0 40 28" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="7-1-1-4-Salesforce-Connection"
            transform="translate(-775.000000, -562.000000)"
            fill="#FFFFFF"
            fillRule="nonzero"
          >
            <g id="Group" transform="translate(710.000000, 516.000000)">
              <g id="salesforce" transform="translate(65.000000, 46.000000)">
                <path
                  d="M15.555625,13.3525 L13.90875,13.3525 C13.951875,13.03 14.11625,12.47 14.76125,12.47 C15.183125,12.47 15.509375,12.70875 15.555625,13.3525 L15.555625,13.3525 Z M24.096875,12.485 C24.0675,12.485 23.215,12.374375 23.215,13.735 C23.215,15.095625 24.066875,14.985 24.096875,14.985 C24.909375,14.985 24.97875,14.13875 24.97875,13.735 C24.97875,12.375 24.125,12.485 24.096875,12.485 Z M8.895625,13.97 C8.75659611,14.0790302 8.67978052,14.2492386 8.69,14.425625 C8.69,14.724375 8.82,14.80375 8.895625,14.86625 C9.189375,15.0975 9.8375,14.99875 10.20375,14.925625 L10.20375,13.866875 C9.87125,13.8 9.158125,13.744375 8.895625,13.97 L8.895625,13.97 Z M40,12.5 C40,17.97375 35,22.149375 29.665,21.026875 C28.516875,23.089375 25.244375,25.44875 21.4025,23.62875 C18.83,29.631875 10.284375,29.39 8.039375,23.305625 C0.556875,24.79875 -3.136875,14.6575 3.335,10.850625 C1.163125,5.88625 4.75,0 10.479375,0 C12.8942998,0.00149983215 15.1710605,1.12648205 16.639375,3.04375 C17.933125,1.70625 19.726875,0.868125 21.71125,0.868125 C24.3575,0.868125 26.64875,2.338125 27.88625,4.52875 C33.6875,1.98625 40,6.293125 40,12.5 Z M7.528125,14.4875 C7.528125,13.7525 6.7975,13.539375 6.41125,13.414375 C6.081875,13.2825 5.573125,13.195 5.573125,12.855625 C5.573125,12.264375 6.635625,12.439375 7.14625,12.723125 C7.14625,12.723125 7.219375,12.7675 7.24875,12.69375 C7.26375,12.65 7.39625,12.2825 7.410625,12.238125 C7.42226674,12.2017528 7.40288618,12.1627148 7.366875,12.15 C6.59625,11.673125 4.823125,11.618125 4.823125,12.94375 C4.823125,13.7225 5.54125,13.90875 5.940625,14.016875 C6.235625,14.115625 6.76375,14.204375 6.76375,14.560625 C6.76375,14.810625 6.543125,15.001875 6.190625,15.001875 C5.76234197,15.001155 5.34577827,14.861935 5.003125,14.605 C4.97375,14.590625 4.914375,14.560625 4.9,14.649375 L4.75,15.11625 C4.720625,15.175 4.764375,15.19 4.764375,15.204375 C4.87375,15.291875 5.408125,15.61625 6.190625,15.61625 C7.01375,15.61625 7.528125,15.175 7.528125,14.484375 L7.528125,14.4875 Z M9.528125,11.82625 C8.895,11.82625 8.361875,12.024375 8.190625,12.15 C8.17679112,12.1595738 8.16740109,12.1743205 8.16457809,12.1909056 C8.1617551,12.2074907 8.16573756,12.2245135 8.175625,12.238125 L8.3375,12.679375 C8.34662549,12.7111822 8.37896124,12.7303644 8.41125,12.723125 C8.451875,12.723125 8.83625,12.473125 9.469375,12.473125 C9.719375,12.473125 9.910625,12.5175 10.043125,12.620625 C10.268125,12.795625 10.234375,13.13875 10.234375,13.281875 C9.935,13.263125 9.04,13.066875 8.39625,13.516875 C8.09800386,13.7214211 7.92496753,14.0641934 7.9375,14.425625 C7.9375,14.794375 8.031875,15.075625 8.349375,15.3225 C9.114375,15.8325 10.616875,15.4475 10.730625,15.410625 C10.829375,15.390625 10.95125,15.369375 10.95125,15.293125 L10.95125,13.175625 C10.95375,12.8875 10.97125,11.823125 9.5275,11.823125 L9.528125,11.82625 Z M12.4375,10.515 C12.438802,10.4950696 12.4314511,10.4755449 12.4173281,10.4614219 C12.4032051,10.4472989 12.3836804,10.439948 12.36375,10.44125 L11.75,10.44125 C11.7301747,10.4401351 11.710822,10.4475688 11.6968411,10.4616692 C11.6828602,10.4757696 11.6755915,10.4951849 11.676875,10.515 L11.676875,15.4525 C11.6755915,15.4723151 11.6828602,15.4917304 11.6968411,15.5058308 C11.710822,15.5199312 11.7301747,15.5273649 11.75,15.52625 L12.3675,15.52625 C12.3874304,15.527552 12.4069551,15.5202011 12.4210781,15.5060781 C12.4352011,15.4919551 12.442552,15.4724304 12.44125,15.4525 L12.4375,10.515 Z M15.921875,12.323125 C15.790625,12.17875 15.4975,11.8525 14.81875,11.8525 C14.599375,11.8525 13.93375,11.866875 13.525,12.41125 C13.128125,12.888125 13.11375,13.543125 13.11375,13.749375 C13.11375,13.944375 13.123125,14.640625 13.555,15.0725 C13.72,15.254375 14.12125,15.586875 14.980625,15.586875 C15.656875,15.586875 16.01,15.44 16.141875,15.351875 C16.17125,15.336875 16.18625,15.3075 16.156875,15.234375 L16.01,14.8075 C15.9944702,14.7741032 15.9578665,14.7559312 15.921875,14.76375 C15.76,14.8225 15.525,14.94 14.96625,14.94 C13.8775,14.94 13.913125,14.01875 13.9075,13.89625 L16.230625,13.89625 C16.2655078,13.8955299 16.2955313,13.8714084 16.30375,13.8375 C16.285625,13.8375 16.433125,12.91875 15.923125,12.323125 L15.921875,12.323125 Z M18.215,15.61625 C19.038125,15.61625 19.553125,15.175 19.553125,14.484375 C19.553125,13.749375 18.821875,13.53625 18.435625,13.41125 C18.176875,13.3075 17.5975,13.2 17.5975,12.8525 C17.5975,12.6175 17.803125,12.455625 18.126875,12.455625 C18.4904103,12.4629272 18.8474483,12.5533626 19.170625,12.72 C19.170625,12.72 19.244375,12.764375 19.27375,12.690625 C19.288125,12.646875 19.420625,12.279375 19.435,12.235 C19.4466417,12.1986278 19.4272612,12.1595898 19.39125,12.146875 C18.896875,11.840625 18.345,11.838125 18.126875,11.838125 C17.376875,11.838125 16.848125,12.29375 16.848125,12.940625 C16.848125,13.719375 17.565625,13.905625 17.965,14.01375 C18.346875,14.13875 18.788125,14.2175 18.788125,14.5575 C18.788125,14.8075 18.568125,14.99875 18.215,14.99875 C17.7867463,14.997851 17.3702375,14.8586494 17.0275,14.601875 C17.0093911,14.5863363 16.9840149,14.5824647 16.9620963,14.5918963 C16.9401777,14.6013279 16.9255421,14.6224168 16.924375,14.64625 L16.7775,15.11625 C16.748125,15.175 16.791875,15.19 16.791875,15.204375 C16.899375,15.291875 17.4375,15.61625 18.21625,15.61625 L18.215,15.61625 Z M22.318125,12 C22.318125,11.955625 22.303125,11.92625 22.244375,11.92625 L21.509375,11.92625 C21.509375,11.9175 21.568125,11.3675 21.78875,11.146875 C22.04875,10.8875 22.52375,11.044375 22.53875,11.044375 C22.611875,11.07375 22.626875,11.044375 22.64125,11.015 L22.818125,10.529375 C22.861875,10.470625 22.818125,10.45625 22.803125,10.44125 C22.485,10.31625 21.71875,10.261875 21.274375,10.70625 C20.931875,11.04875 20.836875,11.57625 20.774375,11.92625 L20.245,11.92625 C20.2058119,11.9295197 20.174811,11.9607855 20.171875,12 L20.083125,12.485 C20.083125,12.52875 20.098125,12.558125 20.156875,12.558125 L20.67125,12.558125 C20.139375,15.551875 20.124375,15.69625 20.024375,16.028125 C19.956875,16.254375 19.81875,16.459375 19.656875,16.513125 C19.65125,16.513125 19.414375,16.618125 19.054375,16.498125 C19.054375,16.498125 18.995625,16.46875 18.96625,16.5425 C18.95125,16.586875 18.804375,16.96875 18.789375,17.013125 C18.774375,17.0575 18.789375,17.10125 18.81875,17.10125 C19.138125,17.22625 19.63125,17.211875 19.93625,17.10125 C20.32875,16.95875 20.54375,16.608125 20.656875,16.2925 C20.82875,15.810625 20.8325,15.680625 21.391875,12.55875 L22.15625,12.55875 C22.1955649,12.5555188 22.2267688,12.5243149 22.23,12.485 L22.318125,12 Z M25.655,13 C25.62,12.895 25.33625,11.868125 24.081875,11.868125 C23.12875,11.868125 22.644375,12.493125 22.509375,13 C22.446875,13.1875 22.310625,13.875 22.509375,14.47 C22.515,14.48875 22.785,15.6025 24.081875,15.6025 C25.01625,15.6025 25.513125,15.001875 25.655,14.47 C25.855625,13.869375 25.718125,13.1875 25.655,13 L25.655,13 Z M28.4925,11.95625 C28.18,11.853125 27.45375,11.8375 27.110625,12.294375 L27.110625,12.015 C27.1117399,11.9951747 27.1043062,11.975822 27.0902058,11.9618411 C27.0761054,11.9478602 27.0566901,11.9405915 27.036875,11.941875 L26.449375,11.941875 C26.4295599,11.9405915 26.4101446,11.9478602 26.3960442,11.9618411 C26.3819438,11.975822 26.3745101,11.9951747 26.375625,12.015 L26.375625,15.47 C26.3745284,15.4898757 26.3819456,15.5092782 26.3960212,15.5233538 C26.4100968,15.5374294 26.4294993,15.5448466 26.449375,15.54375 L27.051875,15.54375 C27.0717507,15.5448466 27.0911532,15.5374294 27.1052288,15.5233538 C27.1193044,15.5092782 27.1267216,15.4898757 27.125625,15.47 L27.125625,13.734375 C27.125625,13.5525 27.12875,13.02375 27.404375,12.79375 C27.710625,12.4875 28.154375,12.58375 28.2425,12.6025 C28.2808292,12.6018123 28.3152482,12.5788663 28.330625,12.54375 C28.4039752,12.3809156 28.4678321,12.2139694 28.521875,12.04375 C28.5346591,12.0111359 28.5222957,11.9740459 28.4925,11.955625 L28.4925,11.95625 Z M31.418125,15.3375 L31.285625,14.881875 C31.25625,14.808125 31.1975,14.8375 31.1975,14.8375 C30.933125,14.95125 30.563125,14.955625 30.491875,14.955625 C30.201875,14.955625 29.41875,14.885 29.41875,13.720625 C29.41875,13.33125 29.534375,12.485625 30.448125,12.485625 C30.6922086,12.4794018 30.9356014,12.5142628 31.168125,12.58875 C31.168125,12.58875 31.226875,12.618125 31.241875,12.544375 C31.300625,12.3825 31.344375,12.265 31.40375,12.07375 C31.418125,12.015 31.374375,12.000625 31.359375,12.000625 C30.635,11.75875 29.963125,11.8425 29.624375,12.000625 C29.525,12.046875 28.61,12.40625 28.61,13.720625 C28.61,13.901875 28.57375,15.6025 30.41875,15.6025 C30.7500432,15.60185 31.0785493,15.5419484 31.38875,15.425625 C31.4162845,15.4052014 31.4280373,15.369693 31.418125,15.336875 L31.418125,15.3375 Z M34.785,12.8675 C34.735,12.68 34.449375,11.853125 33.388125,11.853125 C32.388125,11.853125 31.918125,12.485 31.785625,13.015 C31.7135237,13.2532096 31.6787367,13.5011461 31.6825,13.75 C31.6825,15.366875 32.86,15.5875 33.55,15.5875 C34.22625,15.5875 34.57875,15.440625 34.71125,15.3525 C34.740625,15.3375 34.755625,15.308125 34.72625,15.235 L34.57875,14.808125 C34.5632202,14.7747282 34.5266165,14.7565562 34.490625,14.764375 C34.32875,14.823125 34.09375,14.940625 33.535,14.940625 C32.44625,14.940625 32.481875,14.019375 32.476875,13.896875 L34.799375,13.896875 C34.8343371,13.8960063 34.8644625,13.8720081 34.873125,13.838125 C34.858125,13.8375 34.931875,13.39625 34.785,12.866875 L34.785,12.8675 Z M33.329375,12.470625 C32.68375,12.470625 32.516875,13.033125 32.476875,13.353125 L34.125,13.353125 C34.07,12.608125 33.64875,12.47 33.329375,12.47 L33.329375,12.470625 Z"
                  id="Shape"
                  fill="currentColor"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SalesforceSyncSuccessIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 45 45'}
      style={style}
      className={userProps.className || ''}
    >
      <svg
        width="45px"
        height="45px"
        viewBox="0 0 20 15"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>salesforce</title>
        <g id="logo" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="10" fillRule="nonzero">
            <g id="salesforca-seeklogo.com" transform="translate(0, 0)">
              <path
                fill="currentColor"
                d="M8.322792,12.5212537 C8.96780623,13.1959833 9.86581191,13.6144358 10.8589743,13.6144358 C12.1792023,13.6144358 13.3310541,12.8753509 13.9444444,11.7781645 C14.4774929,12.0172802 15.0675214,12.1502812 15.688319,12.1502812 C18.0695157,12.1502812 20,10.1953101 20,7.78384537 C20,5.37209465 18.0695157,3.41712357 15.688319,3.41712357 C15.3977208,3.41712357 15.1136752,3.44629796 14.8390313,3.50207256 C14.2988603,2.53474086 13.2695157,1.88117701 12.0880342,1.88117701 C11.5934473,1.88117701 11.125641,1.99587245 10.7091168,2.19980726 C10.1615384,0.90669471 8.88575497,0 7.39886038,0 C5.85042735,0 4.53076923,0.983635052 4.02421652,2.36312672 C3.80284901,2.31593283 3.57350427,2.29133479 3.33817663,2.29133479 C1.49458689,2.29133479 0,3.80725971 0,5.67756782 C0,6.93092308 0.671509971,8.02524923 1.66923077,8.61073953 C1.46381766,9.08525258 1.34957265,9.60896175 1.34957265,10.1595571 C1.34957265,12.3104544 3.08888888,14.0540541 5.23418802,14.0540541 C6.49373218,14.0540541 7.61310541,13.4528325 8.322792,12.5212537"
                id="路径"
                transform="translate(10.000000, 7.027027) scale(-1, 1) rotate(-180.000000) translate(-10.000000, -7.027027) "
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ImportExportIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-445.000000, -704.000000)">
            <g id="Group" transform="translate(380.000000, 660.000000)">
              <g id="share-box-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M13.3333333,4 L13.3333333,6.******** L6.********,6.******** L6.********,25.3333333 L25.3333333,25.3333333 L25.3333333,18.6666667 L28,18.6666667 L28,26.6666667 C28,27.4030463 27.4030463,28 26.6666667,28 L5.********,28 C4.********,28 4,27.4030463 4,26.6666667 L4,5.******** C4,4.******** 4.********,4 5.********,4 L13.3333333,4 Z M23.448,6.******** L17.3333333,6.******** L17.3333333,4 L28,4 L28,14.6666667 L25.3333333,14.6666667 L25.3333333,8.552 L16,17.8853333 L14.1146667,16 L23.448,6.******** Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ScheduledJobIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-445.000000, -848.000000)">
            <g id="Group" transform="translate(380.000000, 804.000000)">
              <g id="briefcase-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M9.********,6.******** L9.********,2.******** C9.********,1.930287 9.930287,1.******** 10.6666667,1.******** L21.3333333,1.******** C22.069713,1.******** 22.6666667,1.930287 22.6666667,2.******** L22.6666667,6.******** L28,6.******** C28.7363797,6.******** 29.3333333,7.26362033 29.3333333,8 L29.3333333,26.6666667 C29.3333333,27.4030463 28.7363797,28 28,28 L4,28 C3.26362033,28 2.********,27.4030463 2.********,26.6666667 L2.********,8 C2.********,7.26362033 3.26362033,6.******** 4,6.******** L9.********,6.******** Z M5.********,21.3333333 L5.********,25.3333333 L26.6666667,25.3333333 L26.6666667,21.3333333 L5.********,21.3333333 Z M5.********,18.6666667 L26.6666667,18.6666667 L26.6666667,9.******** L5.********,9.******** L5.********,18.6666667 Z M12,4 L12,6.******** L20,6.******** L20,4 L12,4 Z M14.6666667,14.6666667 L17.3333333,14.6666667 L17.3333333,17.3333333 L14.6666667,17.3333333 L14.6666667,14.6666667 Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const BusinessObjectsIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon viewBox="0 0 32 32" style={style} className={userProps.className || ''}>
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-775.000000, -704.000000)">
            <g id="Group" transform="translate(710.000000, 660.000000)">
              <g id="building-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M28,25.3333333 L30.6666667,25.3333333 L30.6666667,28 L1.********,28 L1.********,25.3333333 L4,25.3333333 L4,5.******** C4,4.******** 4.********,4 5.********,4 L18.6666667,4 C19.4030463,4 20,4.******** 20,5.******** L20,25.3333333 L25.3333333,25.3333333 L25.3333333,14.6666667 L22.6666667,14.6666667 L22.6666667,12 L26.6666667,12 C27.4030463,12 28,12.5969537 28,13.3333333 L28,25.3333333 Z M6.********,6.******** L6.********,25.3333333 L17.3333333,25.3333333 L17.3333333,6.******** L6.********,6.******** Z M9.********,14.6666667 L14.6666667,14.6666667 L14.6666667,17.3333333 L9.********,17.3333333 L9.********,14.6666667 Z M9.********,9.******** L14.6666667,9.******** L14.6666667,12 L9.********,12 L9.********,9.******** Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};
export const FolderIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon viewBox="0 0 32 32" style={style} className={userProps.className || ''}>
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-775.000000, -848.000000)">
            <g id="Group" transform="translate(710.000000, 804.000000)">
              <g id="folder-3-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M16.552,6.******** L28,6.******** C28.7363797,6.******** 29.3333333,7.26362033 29.3333333,8 L29.3333333,26.6666667 C29.3333333,27.4030463 28.7363797,28 28,28 L4,28 C3.26362033,28 2.********,27.4030463 2.********,26.6666667 L2.********,5.******** C2.********,4.******** 3.26362033,4 4,4 L13.8853333,4 L16.552,6.******** Z M5.********,9.******** L5.********,25.3333333 L26.6666667,25.3333333 L26.6666667,9.******** L5.********,9.******** Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const StackIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon viewBox="0 0 32 32" style={style} className={userProps.className || ''}>
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-445.000000, -1040.000000)">
            <g id="Group" transform="translate(380.000000, 996.000000)">
              <g id="stack-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M26.7773333,20.2666667 L28.38,21.228 C28.5811235,21.3484044 28.7042335,21.5655903 28.7042335,21.8 C28.7042335,22.0344097 28.5811235,22.2515956 28.38,22.372 L16.6866667,29.388 C16.26409,29.6418841 15.73591,29.6418841 15.3133333,29.388 L3.62,22.372 C3.41887651,22.2515956 3.29576646,22.0344097 3.29576646,21.8 C3.29576646,21.5655903 3.41887651,21.3484044 3.62,21.228 L5.22266667,20.2666667 L16,26.7333333 L26.7773333,20.2666667 L26.7773333,20.2666667 Z M26.7773333,14 L28.38,14.9613333 C28.5811235,15.0817378 28.7042335,15.2989237 28.7042335,15.5333333 C28.7042335,15.767743 28.5811235,15.9849289 28.38,16.1053333 L16,23.5333333 L3.62,16.1053333 C3.41887651,15.9849289 3.29576646,15.767743 3.29576646,15.5333333 C3.29576646,15.2989237 3.41887651,15.0817378 3.62,14.9613333 L5.22266667,14 L16,20.4666667 L26.7773333,14 Z M16.6853333,1.74533333 L28.38,8.76133333 C28.5811235,8.88173778 28.7042335,9.09892367 28.7042335,9.******** C28.7042335,9.56774299 28.5811235,9.78492888 28.38,9.90533333 L16,17.3333333 L3.62,9.90533333 C3.41887651,9.78492888 3.29576646,9.56774299 3.29576646,9.******** C3.29576646,9.09892367 3.41887651,8.88173778 3.62,8.76133333 L15.3133333,1.74533333 C15.73591,1.4914492 16.26409,1.4914492 16.6866667,1.74533333 L16.6853333,1.74533333 Z M16,4.44266667 L7.84933333,9.******** L16,14.224 L24.1506667,9.******** L16,4.44266667 Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DollarCircleIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon viewBox="0 0 32 32" style={style} className={userProps.className || ''}>
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-775.000000, -1040.000000)">
            <g id="Group" transform="translate(710.000000, 996.000000)">
              <g id="money-dollar-circle-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M16,29.3333333 C8.636,29.3333333 2.********,23.364 2.********,16 C2.********,8.636 8.636,2.******** 16,2.******** C23.364,2.******** 29.3333333,8.636 29.3333333,16 C29.3333333,23.364 23.364,29.3333333 16,29.3333333 Z M16,26.6666667 C21.8910373,26.6666667 26.6666667,21.8910373 26.6666667,16 C26.6666667,10.1089627 21.8910373,5.******** 16,5.******** C10.1089627,5.******** 5.********,10.1089627 5.********,16 C5.********,21.8910373 10.1089627,26.6666667 16,26.6666667 Z M11.3333333,18.6666667 L18.6666667,18.6666667 C19.0348565,18.6666667 19.3333333,18.3681898 19.3333333,18 C19.3333333,17.6318102 19.0348565,17.3333333 18.6666667,17.3333333 L13.3333333,17.3333333 C11.4923842,17.3333333 10,15.8409492 10,14 C10,12.1590508 11.4923842,10.6666667 13.3333333,10.6666667 L14.6666667,10.6666667 L14.6666667,8 L17.3333333,8 L17.3333333,10.6666667 L20.6666667,10.6666667 L20.6666667,13.3333333 L13.3333333,13.3333333 C12.9651435,13.3333333 12.6666667,13.6318102 12.6666667,14 C12.6666667,14.3681898 12.9651435,14.6666667 13.3333333,14.6666667 L18.6666667,14.6666667 C20.5076158,14.6666667 22,16.1590508 22,18 C22,19.8409492 20.5076158,21.3333333 18.6666667,21.3333333 L17.3333333,21.3333333 L17.3333333,24 L14.6666667,24 L14.6666667,21.3333333 L11.3333333,21.3333333 L11.3333333,18.6666667 Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const LayoutIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-445.000000, -1184.000000)">
            <g id="Group" transform="translate(380.000000, 1140.000000)">
              <g id="layout-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M6.********,10.6666667 L25.3333333,10.6666667 L25.3333333,6.******** L6.********,6.******** L6.********,10.6666667 Z M18.6666667,25.3333333 L18.6666667,13.3333333 L6.********,13.3333333 L6.********,25.3333333 L18.6666667,25.3333333 Z M21.3333333,25.3333333 L25.3333333,25.3333333 L25.3333333,13.3333333 L21.3333333,13.3333333 L21.3333333,25.3333333 Z M5.********,4 L26.6666667,4 C27.4030463,4 28,4.******** 28,5.******** L28,26.6666667 C28,27.4030463 27.4030463,28 26.6666667,28 L5.********,28 C4.********,28 4,27.4030463 4,26.6666667 L4,5.******** C4,4.******** 4.********,4 5.********,4 Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AccountCircleIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-445.000000, -1376.000000)">
            <g id="Group" transform="translate(380.000000, 1332.000000)">
              <g id="account-circle-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M16,29.3333333 C8.636,29.3333333 2.********,23.364 2.********,16 C2.********,8.636 8.636,2.******** 16,2.******** C23.364,2.******** 29.3333333,8.636 29.3333333,16 C29.3333333,23.364 23.364,29.3333333 16,29.3333333 Z M9.********,24.3413333 C11.2380646,25.8502634 13.5835716,26.6705089 16,26.6666801 C18.6266667,26.6666801 21.0306667,25.7173333 22.8893333,24.144 C21.1349554,22.3438839 18.7269462,21.3300865 16.2133333,21.3333256 C13.605396,21.3303251 11.1158149,22.4215423 9.********,24.3413333 L9.********,24.3413333 Z M7.488,22.4266667 C9.********,20.0229763 12.9106414,18.6622417 16.2133333,18.6666667 C19.3954514,18.6625261 22.448089,19.9263814 24.696,22.1786667 C27.8642442,17.7195558 27.1458872,11.5865381 23.0329493,7.******** C18.9200114,4.******** 12.7457148,4.******** 8.********,8.******** C4.********,11.9112978 4.19189339,18.0625536 7.488,22.428 L7.488,22.4266667 Z M16,17.3333333 C13.0544813,17.3333333 10.6666667,14.9455187 10.6666667,12 C10.6666667,9.05448133 13.0544813,6.******** 16,6.******** C18.9455187,6.******** 21.3333333,9.05448133 21.3333333,12 C21.3333333,14.9455187 18.9455187,17.3333333 16,17.3333333 Z M16,14.6666667 C17.4727593,14.6666667 18.6666667,13.4727593 18.6666667,12 C18.6666667,10.5272407 17.4727593,9.******** 16,9.******** C14.5272407,9.******** 13.3333333,10.5272407 13.3333333,12 C13.3333333,13.4727593 14.5272407,14.6666667 16,14.6666667 Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SearchIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 16 16'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1">
        <g
          id="07-Settings"
          stroke="none"
          strokeWidth="1"
          fill="none"
          fillRule="evenodd"
          opacity="0.5"
        >
          <g id="7-1-1-3-Order-Settings" transform="translate(-405.000000, -259.000000)">
            <g id="cta" transform="translate(380.000000, 242.000000)">
              <g id="search-line" transform="translate(25.000000, 17.000000)">
                <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                <path
                  d="M12.0206667,11.078 L14.876,13.9326667 L13.9326667,14.876 L11.078,12.0206667 C10.0158255,12.8721474 8.69466738,13.3352701 7.********,13.3333333 C4.02133333,13.3333333 1.********,10.6453333 1.********,7.******** C1.********,4.02133333 4.02133333,1.******** 7.********,1.******** C10.6453333,1.******** 13.3333333,4.02133333 13.3333333,7.******** C13.3352701,8.69466738 12.8721474,10.0158255 12.0206667,11.078 Z M10.6833333,10.5833333 C11.5294086,9.7132571 12.0019113,8.54695285 12.0000058,7.******** C12.0000058,4.75466667 9.91133333,2.******** 7.********,2.******** C4.75466667,2.******** 2.********,4.75466667 2.********,7.******** C2.********,9.91133333 4.75466667,12.0000058 7.********,12.0000058 C8.54695285,12.0019113 9.7132571,11.5294086 10.5833333,10.6833333 L10.6833333,10.5833333 L10.6833333,10.5833333 Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const MultiBoxIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="7-1-1-4-Salesforce-Connection-(Scrolled)"
            transform="translate(-445.000000, -780.000000)"
          >
            <g id="Group-20" transform="translate(283.000000, -408.000000)">
              <g id="Group-4" transform="translate(97.000000, 0.000000)">
                <g id="Group" transform="translate(0.000000, 1144.000000)">
                  <g id="checkbox-multiple-blank-line" transform="translate(65.000000, 44.000000)">
                    <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                    <path
                      d="M9.********,9.******** L9.********,4 C9.********,3.26362033 9.930287,2.******** 10.666666,2.******** L28,2.******** C28.7363797,2.******** 29.3333333,3.26362033 29.3333333,4 L29.3333333,21.3333333 C29.3333333,22.069713 28.7363797,22.6666667 28,22.6666667 L22.666666,22.6666667 L22.666666,27.9906667 C22.666666,28.732 22.068,29.333334 21.324,29.333334 L4.00933333,29.333334 C3.65312751,29.3336876 3.31140912,29.1923422 3.05953344,28.9404666 C2.80765776,28.6885909 2.66631241,28.3468725 2.666666,27.9906667 L2.67066667,10.676 C2.67066667,9.93466667 3.26933333,9.******** 4.01333333,9.******** L9.********,9.******** Z M12,9.******** L21.324,9.******** C22.0653333,9.******** 22.6666667,9.932 22.6666667,10.676 L22.6666667,20 L26.6666667,20 L26.6666667,5.******** L12,5.******** L12,9.******** Z M5.33733333,12 L5.********,26.6666667 L20,26.6666667 L20,12 L5.33733333,12 Z"
                      id="Shape"
                      fill="currentColor"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ClipboardIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-775.000000, -1376.000000)">
            <g id="Group" transform="translate(710.000000, 1332.000000)">
              <g id="clipboard-line" transform="translate(65.000000, 44.000000)">
                <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                <path
                  d="M9.********,5.******** L9.********,2.******** L22.6666667,2.******** L22.6666667,5.******** L26.676,5.******** C27.4066667,5.******** 28,5.92666667 28,6.65733333 L28,28.0093333 C27.999265,28.7402537 27.4069204,29.3325984 26.676,29.3333333 L5.324,29.3333333 C4.59307964,29.3325984 4.00073496,28.7402537 4,28.0093333 L4,6.65733333 C4,5.92666667 4.59333333,5.******** 5.324,5.******** L9.********,5.******** Z M9.********,8 L6.********,8 L6.********,26.6666667 L25.3333333,26.6666667 L25.3333333,8 L22.6666667,8 L22.6666667,10.6666667 L9.********,10.6666667 L9.********,8 Z M12,5.******** L12,8 L20,8 L20,5.******** L12,5.******** Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FundsBoxIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="7-1-1-4-Salesforce-Connection-(Scrolled)"
            transform="translate(-775.000000, -780.000000)"
          >
            <g id="Group-20" transform="translate(283.000000, -408.000000)">
              <g id="Group-4" transform="translate(97.000000, 0.000000)">
                <g id="Group" transform="translate(330.000000, 1144.000000)">
                  <g id="funds-box-line" transform="translate(65.000000, 44.000000)">
                    <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                    <path
                      d="M5.********,6.******** L5.********,25.3333333 L26.6666667,25.3333333 L26.6666667,6.******** L5.********,6.******** Z M4,4 L28,4 C28.7363797,4 29.3333333,4.******** 29.3333333,5.******** L29.3333333,26.6666667 C29.3333333,27.4030463 28.7363797,28 28,28 L4,28 C3.26362033,28 2.********,27.4030463 2.********,26.6666667 L2.********,5.******** C2.********,4.******** 3.26362033,4 4,4 Z M19.724,13.0573333 L17.3333333,10.6666667 L24,10.6666667 L24,17.3333333 L21.6093333,14.9426667 L16.4573333,20.0946667 L13.6293333,17.2666667 L9.85733333,21.0373333 L7.972,19.152 L13.6293333,13.4946667 L16.4573333,16.324 L19.724,13.0573333 Z"
                      id="Shape"
                      fill="currentColor"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};
export const FoldersIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="7-1-1-4-Salesforce-Connection-(Scrolled)"
            transform="translate(-445.000000, -924.000000)"
          >
            <g id="Group-20" transform="translate(283.000000, -408.000000)">
              <g id="Group-4" transform="translate(97.000000, 0.000000)">
                <g id="Group" transform="translate(0.000000, 1288.000000)">
                  <g id="folders-line" transform="translate(65.000000, 44.000000)">
                    <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                    <path
                      d="M8,9.******** L8,5.******** C8,4.******** 8.********,4 9.********,4 L17.8853333,4 L20.552,6.******** L28,6.******** C28.7363797,6.******** 29.3333333,7.26362033 29.3333333,8 L29.3333333,21.3333333 C29.3333333,22.069713 28.7363797,22.6666667 28,22.6666667 L24,22.6666667 L24,26.6666667 C24,27.4030463 23.4030463,28 22.6666667,28 L4,28 C3.26362033,28 2.********,27.4030463 2.********,26.6666667 L2.********,10.6666667 C2.********,9.930287 3.26362033,9.******** 4,9.******** L8,9.******** Z M8,12 L5.********,12 L5.********,25.3333333 L21.3333333,25.3333333 L21.3333333,22.6666667 L8,22.6666667 L8,12 Z M10.6666667,6.******** L10.6666667,20 L26.6666667,20 L26.6666667,9.******** L19.448,9.******** L16.7813333,6.******** L10.6666667,6.******** Z"
                      id="Shape"
                      fill="currentColor"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};
export const BuildingsIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="7-1-1-4-Salesforce-Connection-(Scrolled)"
            transform="translate(-775.000000, -924.000000)"
          >
            <g id="Group-20" transform="translate(283.000000, -408.000000)">
              <g id="Group-4" transform="translate(97.000000, 0.000000)">
                <g id="Group" transform="translate(330.000000, 1288.000000)">
                  <g id="building-2-line" transform="translate(65.000000, 44.000000)">
                    <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                    <path
                      d="M4,25.3333333 L4,7.6 C3.99983178,7.03931174 4.35045457,6.53842204 4.87733333,6.34666667 L17.772,1.65866667 C17.9764247,1.58426711 18.2042917,1.61428748 18.3824721,1.73909339 C18.5606525,1.86389931 18.6667319,2.0677908 18.6666667,2.28533333 L18.6666667,8.88933333 L27.088,11.696 C27.6327237,11.8774292 28.0001383,12.3871901 28,12.9613333 L28,25.3333333 L30.6666667,25.3333333 L30.6666667,28 L1.********,28 L1.********,25.3333333 L4,25.3333333 Z M6.********,25.3333333 L16,25.3333333 L16,5.14 L6.********,8.53466667 L6.********,25.3333333 Z M25.3333333,25.3333333 L25.3333333,13.9226667 L18.6666667,11.7 L18.6666667,25.3333333 L25.3333333,25.3333333 Z"
                      id="Shape"
                      fill="currentColor"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};
export const UserVoiceIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="7-1-1-4-Salesforce-Connection-(Scrolled)"
            transform="translate(-445.000000, -1068.000000)"
          >
            <g id="Group-20" transform="translate(283.000000, -408.000000)">
              <g id="Group-4" transform="translate(97.000000, 0.000000)">
                <g id="Group" transform="translate(0.000000, 1432.000000)">
                  <g id="user-voice-line" transform="translate(65.000000, 44.000000)">
                    <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                    <path
                      d="M1.********,29.3333333 C1.********,23.442296 6.10896267,18.6666667 12,18.6666667 C17.8910373,18.6666667 22.6666667,23.442296 22.6666667,29.3333333 L20,29.3333333 C20,24.9150553 16.418278,21.3333333 12,21.3333333 C7.581722,21.3333333 4,24.9150553 4,29.3333333 L1.********,29.3333333 L1.********,29.3333333 Z M12,17.3333333 C7.58,17.3333333 4,13.7533333 4,9.******** C4,4.91333333 7.58,1.******** 12,1.******** C16.42,1.******** 20,4.91333333 20,9.******** C20,13.7533333 16.42,17.3333333 12,17.3333333 Z M12,14.6666667 C14.9466667,14.6666667 17.3333333,12.28 17.3333333,9.******** C17.3333333,6.38666667 14.9466667,4 12,4 C9.05333333,4 6.********,6.38666667 6.********,9.******** C6.********,12.28 9.05333333,14.6666667 12,14.6666667 Z M28.7306667,1.04533333 C30.008269,3.62110322 30.6709703,6.45812209 30.6666875,9.******** C30.6666875,12.3106667 29.9693333,15.1253333 28.7306667,17.6213333 L26.5373333,16.0266667 C27.5042784,13.927948 28.0033607,11.6440889 28,9.******** C28,6.944 27.476,4.676 26.5373333,2.64 L28.7306667,1.04533333 Z M24.328,4.24533333 C24.9939154,5.85887844 25.335527,7.58777715 25.3333438,9.******** C25.335527,11.0788895 24.9939154,12.8077882 24.328,14.4213333 L22.092,12.7946667 C22.4735453,11.6805805 22.667734,10.5109426 22.6666667,9.******** C22.6681632,8.15579874 22.4744253,6.98616284 22.0933333,5.872 L24.328,4.24533333 L24.328,4.24533333 Z"
                      id="Shape"
                      fill="currentColor"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const WalletIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="7-1-1-4-Salesforce-Connection-(Scrolled)"
            transform="translate(-445.000000, -1260.000000)"
          >
            <g id="Group-20" transform="translate(283.000000, -408.000000)">
              <g id="Group-4" transform="translate(97.000000, 0.000000)">
                <g id="Group" transform="translate(0.000000, 1624.000000)">
                  <g id="wallet-line" transform="translate(65.000000, 44.000000)">
                    <polygon id="Path" points="0 0 32 0 32 32 0 32" />
                    <path
                      d="M24,9.******** L28,9.******** C28.7363797,9.******** 29.3333333,9.930287 29.3333333,10.6666667 L29.3333333,26.6666667 C29.3333333,27.4030463 28.7363797,28 28,28 L4,28 C3.26362033,28 2.********,27.4030463 2.********,26.6666667 L2.********,5.******** C2.********,4.******** 3.26362033,4 4,4 L24,4 L24,9.******** Z M5.********,12 L5.********,25.3333333 L26.6666667,25.3333333 L26.6666667,12 L5.********,12 Z M5.********,6.******** L5.********,9.******** L21.3333333,9.******** L21.3333333,6.******** L5.********,6.******** Z M20,17.3333333 L24,17.3333333 L24,20 L20,20 L20,17.3333333 Z"
                      id="Shape"
                      fill="currentColor"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const NetSuiteIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 32 32'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="7-1-1-4-Salesforce-Connection-(Scrolled)"
            transform="translate(-445.000000, -1453.000000)"
          >
            <g id="Group-20" transform="translate(283.000000, -408.000000)">
              <g id="Group-4" transform="translate(97.000000, 0.000000)">
                <g id="Group" transform="translate(0.000000, 1816.000000)">
                  <g id="netsuite" transform="translate(65.000000, 45.000000)">
                    <polygon
                      id="Path"
                      points="0 0.00426666667 32 0.00426666667 32 32.0042667 0 32.0042667"
                    />
                    <path
                      d="M4.288,10.1376 L9.344,10.1376 L9.344,22.1482667 L11.8613333,22.1482667 L11.8613333,26.8416 L4.288,26.8416 L4.288,10.1376 Z M27.6906667,21.1882667 L22.6346667,21.1882667 L22.6346667,9.1776 L20.1173333,9.1776 L20.1173333,4.48426667 L27.6906667,4.48426667 L27.6906667,21.1882667 Z"
                      id="Shape"
                      fillOpacity="0.5"
                      fill="currentColor"
                      fillRule="nonzero"
                    />
                    <path
                      d="M3.11466667,3.33226667 L19.0933333,3.33226667 L19.0933333,17.0496 L12.9493333,9.13493333 L3.11466667,9.13493333 L3.11466667,3.33226667 Z M28.8426667,28.0149333 L12.864,28.0149333 L12.864,14.2976 L19.008,22.2122667 L28.8426667,22.2122667"
                      id="Shape"
                      fill="currentColor"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const InfoIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 16 16'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1">
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="7-1-1-4-Salesforce-Connection-(Scrolled)"
            transform="translate(-1244.000000, -451.000000)"
          >
            <g id="Group-5" transform="translate(1090.000000, 242.000000)">
              <g id="description" transform="translate(45.000000, 209.000000)">
                <g id="information-fill" transform="translate(109.000000, 0.000000)">
                  <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                  <path
                    d="M8,14.6666667 C4.318,14.6666667 1.********,11.682 1.********,8 C1.********,4.318 4.318,1.******** 8,1.******** C11.682,1.******** 14.6666667,4.318 14.6666667,8 C14.6666667,11.682 11.682,14.6666667 8,14.6666667 Z M7.********,7.******** L7.********,11.3333333 L8.********,11.3333333 L8.********,7.******** L7.********,7.******** Z M7.********,4.******** L7.********,6 L8.********,6 L8.********,4.******** L7.********,4.******** Z"
                    id="Shape"
                    fill="currentColor"
                    fillRule="nonzero"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ActiveIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon viewBox={userProps.viewBox} style={style} className={userProps.className || ''}>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="07-Settings" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="7-1-1-4-Salesforce-Connection" transform="translate(-1135.000000, -537.000000)">
            <g id="Group-5" transform="translate(1090.000000, 242.000000)">
              <g id="focus-2-fill" transform="translate(45.000000, 295.000000)">
                <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                <path
                  d="M8,1.******** C11.68,1.******** 14.6666667,4.32 14.6666667,8 C14.6666667,11.68 11.68,14.6666667 8,14.6666667 C4.32,14.6666667 1.********,11.68 1.********,8 C1.********,4.32 4.32,1.******** 8,1.******** Z M8,13.3333333 C10.9513333,13.3333333 13.3333333,10.9513333 13.3333333,8 C13.3333333,5.04866667 10.9513333,2.66666248 8,2.66666248 C6.58496953,2.66489455 5.22738546,3.22622861 4.22680704,4.22680704 C3.22622861,5.22738546 2.66489455,6.58496953 2.66666248,8 C2.66666248,10.9513333 5.04866667,13.3333333 8,13.3333333 L8,13.3333333 Z M8,12 C5.78666667,12 4,10.2133333 4,8 C4,5.78666667 5.78666667,4 8,4 C10.2133333,4 12,5.78666667 12,8 C12,10.2133333 10.2133333,12 8,12 Z M8,6.******** C7.26666667,6.******** 6.********,7.26666667 6.********,8 C6.********,8.73333333 7.26666667,9.******** 8,9.******** C8.73333333,9.******** 9.********,8.73333333 9.********,8 C9.********,7.26666667 8.73333333,6.******** 8,6.******** Z"
                  id="Shape"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const InactiveIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon style={style} className={userProps.className || ''}>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="Visual-System" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Visual-System-(Icons)" transform="translate(-532.000000, -440.000000)">
            <g id="Inactive" transform="translate(532.000000, 440.000000)">
              <polygon id="Path" points="0 0 24 0 24 24 0 24" />
              <path
                d="M12,22 C6.477,22 2,17.523 2,12 C2,6.477 6.477,2 12,2 C17.523,2 22,6.477 22,12 C22,17.523 17.523,22 12,22 Z M12,20 C16.418278,20 20,16.418278 20,12 C20,7.581722 16.418278,4 12,4 C7.581722,4 4,7.581722 4,12 C4,16.418278 7.581722,20 12,20 Z"
                id="Shape"
                fill="currentColor"
                fillRule="nonzero"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CalendarIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 20 20'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1">
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-5-1-Create-Quote" transform="translate(-621.000000, -747.000000)">
            <g id="Product-Options" transform="translate(361.000000, 283.000000)">
              <g id="expiration-date" transform="translate(45.000000, 332.000000)">
                <g id="Group-49" transform="translate(0.000000, 117.000000)">
                  <g id="calendar-line" transform="translate(215.000000, 15.000000)">
                    <polygon id="Path" points="0 0 20 0 20 20 0 20" />
                    <path
                      d="M14.1666667,2.5 L17.5,2.5 C17.9602373,2.5 18.3333333,2.87309604 18.3333333,3.******** L18.3333333,16.6666667 C18.3333333,17.126904 17.9602373,17.5 17.5,17.5 L2.5,17.5 C2.03976271,17.5 1.********,17.126904 1.********,16.6666667 L1.********,3.******** C1.********,2.87309604 2.03976271,2.5 2.5,2.5 L5.83333333,2.5 L5.83333333,0.8******** L7.5,0.8******** L7.5,2.5 L12.5,2.5 L12.5,0.8******** L14.1666667,0.8******** L14.1666667,2.5 Z M12.5,4.16666667 L7.5,4.16666667 L7.5,5.83333333 L5.83333333,5.83333333 L5.83333333,4.16666667 L3.********,4.16666667 L3.********,7.5 L16.6666667,7.5 L16.6666667,4.16666667 L14.1666667,4.16666667 L14.1666667,5.83333333 L12.5,5.83333333 L12.5,4.16666667 Z M16.6666667,9.16666667 L3.********,9.16666667 L3.********,15.8333333 L16.6666667,15.8333333 L16.6666667,9.16666667 Z"
                      id="Shape"
                      fill="currentColor"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FocusIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 16 16'}
      style={style}
      className={userProps.className || ''}
    >
      <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1">
        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g transform="translate(-1807.000000, -750.000000)">
            <g transform="translate(1089.000000, 242.000000)">
              <g transform="translate(0.000000, 123.000000)">
                <g transform="translate(46.000000, 277.000000)" id="row">
                  <g transform="translate(1.000000, 87.500000)">
                    <g id="Actions" transform="translate(597.000000, 20.500000)">
                      <g id="focus-2-fill" transform="translate(74.000000, 0.000000)">
                        <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                        <path
                          d="M8,1.******** C11.68,1.******** 14.6666667,4.32 14.6666667,8 C14.6666667,11.68 11.68,14.6666667 8,14.6666667 C4.32,14.6666667 1.********,11.68 1.********,8 C1.********,4.32 4.32,1.******** 8,1.******** Z M8,13.3333333 C10.9513333,13.3333333 13.3333333,10.9513333 13.3333333,8 C13.3333333,5.04866667 10.9513333,2.66666248 8,2.66666248 C6.58496953,2.66489455 5.22738546,3.22622861 4.22680704,4.22680704 C3.22622861,5.22738546 2.66489455,6.58496953 2.66666248,8 C2.66666248,10.9513333 5.04866667,13.3333333 8,13.3333333 L8,13.3333333 Z"
                          id="Shape"
                          fill="currentColor"
                          fillRule="nonzero"
                        />
                        <circle id="Oval" fill="currentColor" cx="8" cy="8" r="2" />
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AlertIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <SvgIcon viewBox={userProps.viewBox} style={style}>
      <svg
        width="20px"
        height="20px"
        viewBox="0 0 20 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Alert Icon</title>
        <g id="08-Orders" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="8-1-4-3-Subscriptions-(Modals)" transform="translate(-1359.000000, -872.000000)">
            <g id="Popup" transform="translate(1314.000000, 653.000000)">
              <g id="alert-fill" transform="translate(45.000000, 219.000000)">
                <polygon id="Path" points="0 0 20 0 20 20 0 20" />
                <path
                  d="M10.7216667,2.5 L18.66,16.25 C18.8088588,16.507831 18.8088608,16.8254913 18.6600053,17.0833242 C18.5111498,17.341157 18.2360509,17.5 17.9383333,17.5 L2.06166667,17.5 C1.76394912,17.5 1.4888502,17.341157 1.33999471,17.0833242 C1.19113921,16.8254913 1.19114123,16.507831 1.34,16.25 L9.27833333,2.5 C9.42719953,2.24218643 9.7022938,2.08337 10,2.08337 C10.2977062,2.08337 10.5728005,2.24218643 10.7216667,2.5 Z M9.16666667,13.3333333 L9.16666667,15 L10.8333333,15 L10.8333333,13.3333333 L9.16666667,13.3333333 Z M9.16666667,7.5 L9.16666667,11.6666667 L10.8333333,11.6666667 L10.8333333,7.5 L9.16666667,7.5 Z"
                  id="Shape"
                  fill="#FF3B30"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CheckCircleIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <SvgIcon viewBox={userProps.viewBox} style={style}>
      <svg
        width="30px"
        height="30px"
        viewBox={userProps.viewBox || '0 0 30 30'}
        className={userProps.className || ''}
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Check Circle</title>
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
      </svg>
    </SvgIcon>
  );
};

export const FileListIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <SvgIcon viewBox={userProps.viewBox || '-2 -1 24 24'} style={style}>
      <svg
        width="20px"
        height="20px"
        viewBox="0 0 20 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>file-list-icon</title>
        <g id="Quotes-and-Order" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            transform="translate(-882.000000, -456.000000)"
            fill="#63686F"
            fillRule="nonzero"
            id="file-list--icon"
          >
            <g transform="translate(882.000000, 456.000000)">
              <path
                d="M13.3333333,0 C14.3534264,0 15.186228,0.801889748 15.2357641,1.80969507 L15.2380952,1.9047619 L15.238,9.523 L18.5714286,9.52380952 C19.0598437,9.52380952 19.4623878,9.89146685 19.5174022,10.3651227 L19.5238095,10.4761905 L19.5238095,16.9047619 C19.5238095,18.6142147 18.1380242,20 16.4285714,20 C16.3484994,20 16.2691375,19.9969595 16.1905949,19.9909877 L16.1904762,20 L3.********,20 C1.53073727,20 0.0623339605,18.5691508 0.00193286586,16.7812618 L0,16.6666667 L0,1.9047619 C0,0.88466886 0.801889748,0.0518672019 1.80969507,0.00233109868 L1.9047619,0 L13.3333333,0 Z M13.3333333,1.9047619 L1.9047619,1.9047619 L1.9047619,16.6666667 C1.9047619,17.4274671 2.4994857,18.0493623 3.24939391,18.092813 L3.********,18.0952381 L13.5706369,18.095437 C13.4177582,17.7289112 13.3333333,17.3266958 13.3333333,16.9047619 L13.3333333,16.9047619 L13.3333333,1.9047619 Z M17.6190476,11.4285714 L15.2380952,11.4285714 L15.2380952,16.9047619 C15.2380952,17.5359445 15.7293028,18.0523994 16.3502973,18.0927059 L16.3502973,18.0927059 L16.4285714,18.0952381 C17.0860533,18.0952381 17.6190476,17.5622437 17.6190476,16.9047619 L17.6190476,16.9047619 L17.6190476,11.4285714 Z M7.14285714,8.0952381 C7.66884262,8.0952381 8.0952381,8.52163357 8.0952381,9.04761905 C8.0952381,9.54969609 7.70672486,9.96103101 7.21393445,9.99738774 L7.14285714,10 L4.76190476,10 C4.23591929,10 3.80952381,9.57360452 3.80952381,9.04761905 C3.80952381,8.545542 4.19803704,8.13420708 4.69082745,8.09785035 L4.76190476,8.0952381 L7.14285714,8.0952381 Z M10.4761905,4.28571429 C11.002176,4.28571429 11.4285714,4.71210976 11.4285714,5.23809524 C11.4285714,5.74017228 11.0400582,6.1515072 10.5472678,6.18786394 L10.4761905,6.19047619 L4.76190476,6.19047619 C4.23591929,6.19047619 3.80952381,5.76408071 3.80952381,5.23809524 C3.80952381,4.73601819 4.19803704,4.32468328 4.69082745,4.28832654 L4.76190476,4.28571429 L10.4761905,4.28571429 Z"
                id="file-list-icon-path"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const BulletListIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <SvgIcon viewBox={userProps.viewBox || '-3 -3 24 24'} style={style}>
      <svg
        width="20px"
        height="18px"
        viewBox="0 0 20 18"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>bullet-list-icon</title>
        <g id="Quotes-and-Order" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            transform="translate(-1393.000000, -456.000000)"
            fill="#63686F"
            fillRule="nonzero"
            id="bullet-list-icon"
          >
            <g transform="translate(1393.000000, 456.000000)">
              <path
                d="M17.7272727,-1.24344979e-13 C18.9824653,-1.24344979e-13 20,1.01753466 20,2.27272727 L20,2.27272727 L20,15 C20,16.2551926 18.9824653,17.2727273 17.7272727,17.2727273 L17.7272727,17.2727273 L2.27272727,17.2727273 C1.01753466,17.2727273 0,16.2551926 0,15 L0,15 L0,2.27272727 C0,1.01753466 1.01753466,-1.24344979e-13 2.27272727,-1.24344979e-13 L2.27272727,-1.24344979e-13 Z M17.7272727,1.81818182 L2.27272727,1.81818182 C2.02168875,1.81818182 1.81818182,2.02168875 1.81818182,2.27272727 L1.81818182,2.27272727 L1.81818182,15 C1.81818182,15.2510385 2.02168875,15.4545455 2.27272727,15.4545455 L2.27272727,15.4545455 L17.7272727,15.4545455 C17.9783112,15.4545455 18.1818182,15.2510385 18.1818182,15 L18.1818182,15 L18.1818182,2.27272727 C18.1818182,2.02168875 17.9783112,1.81818182 17.7272727,1.81818182 L17.7272727,1.81818182 Z M15,10 C15.502077,10 15.9090909,10.4070139 15.9090909,10.9090909 C15.9090909,11.3883463 15.5382374,11.7809841 15.0678465,11.8156883 L15,11.8181818 L8.63636364,11.8181818 C8.13428659,11.8181818 7.72727273,11.411168 7.72727273,10.9090909 C7.72727273,10.4298355 8.09812627,10.0371977 8.56851712,10.0024935 L8.63636364,10 L15,10 Z M5.45454545,10 C5.9566225,10 6.36363636,10.4070139 6.36363636,10.9090909 C6.36363636,11.3883463 5.99278282,11.7809841 5.52239197,11.8156883 L5.45454545,11.8181818 L5,11.8181818 C4.49792295,11.8181818 4.09090909,11.411168 4.09090909,10.9090909 C4.09090909,10.4298355 4.46176263,10.0371977 4.93215348,10.0024935 L5,10 L5.45454545,10 Z M15,5.45454545 C15.502077,5.45454545 15.9090909,5.86155932 15.9090909,6.36363636 C15.9090909,6.84289173 15.5382374,7.2355296 15.0678465,7.27023376 L15,7.27272727 L8.63636364,7.27272727 C8.13428659,7.27272727 7.72727273,6.86571341 7.72727273,6.36363636 C7.72727273,5.884381 8.09812627,5.49174313 8.56851712,5.45703897 L8.63636364,5.45454545 L15,5.45454545 Z M5.45454545,5.45454545 C5.9566225,5.45454545 6.36363636,5.86155932 6.36363636,6.36363636 C6.36363636,6.84289173 5.99278282,7.2355296 5.52239197,7.27023376 L5.45454545,7.27272727 L5,7.27272727 C4.49792295,7.27272727 4.09090909,6.86571341 4.09090909,6.36363636 C4.09090909,5.884381 4.46176263,5.49174313 4.93215348,5.45703897 L5,5.45454545 L5.45454545,5.45454545 Z"
                id="bullet-list-icon-path"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RightArrowIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>right-icon</title>
        <g id="Quotes-and-Order" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            transform="translate(-1334.000000, -513.000000)"
            fill="#9174F1"
            fillRule="nonzero"
            id="right-icon"
          >
            <path d="M1342,513 C1346.41839,513 1350,516.581611 1350,521 C1350,525.418389 1346.41839,529 1342,529 C1337.58161,529 1334,525.418389 1334,521 C1334,516.581611 1337.58161,513 1342,513 Z M1341.37709,517.170529 C1341.09135,516.929782 1340.6639,516.943944 1340.39483,517.213013 L1340.39483,517.213013 L1340.35235,517.259273 C1340.1116,517.545014 1340.12576,517.972463 1340.39483,518.241532 L1340.39483,518.241532 L1343.15309,521 L1340.39483,523.758468 L1340.35235,523.804728 C1340.1116,524.090469 1340.12576,524.517917 1340.39483,524.786987 C1340.67885,525.071004 1341.13933,525.071004 1341.42335,524.786987 L1341.42335,524.786987 L1344.69608,521.514259 L1344.73856,521.468 C1344.97931,521.182259 1344.96515,520.75481 1344.69608,520.485741 L1344.69608,520.485741 L1341.42335,517.213013 Z" />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CancelIcon: React.FC<Props> = (userProps) => {
  return (
    <SvgIcon>
      <svg
        width={userProps.width || '24px'}
        height={userProps.height || '24px'}
        viewBox={userProps.viewBox || '0 0 24 24'}
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="Visual-System" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Visual-System-(Icons)" transform="translate(-398.000000, -439.000000)">
            <g id="focus-2-fill" transform="translate(398.000000, 439.000000)">
              <polygon id="Path" points="0 0 24 0 24 24 0 24" />
              <path
                d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"
                id="Shape"
                fill="#6239EB"
                fillRule="nonzero"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DataFlowManagerIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon viewBox={props.viewBox}>
      <svg
        width={props.width}
        height={props.height}
        viewBox="0 0 30 30"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>data-flow-manager</title>
        <g id="Data-Flow-Manager" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="1.1-Data-Flow-Manager"
            transform="translate(-870.000000, -809.000000)"
            fill="currentColor"
            fillRule="nonzero"
          >
            <path
              d="M875.25,809 C878.149495,809 880.5,811.350505 880.5,814.25 C880.5,816.628101 878.918835,818.636901 876.75045,819.282457 L876.75,837.5 C876.75,838.328427 876.078427,839 875.25,839 C874.459229,839 873.811376,838.388092 873.754114,837.611947 L873.75,837.5 L873.750557,819.282757 C871.581654,818.637523 870,816.628469 870,814.25 C870,811.350505 872.350505,809 875.25,809 Z M885,809 C885.790771,809 886.438624,809.611908 886.495886,810.388053 L886.5,810.5 L886.50045,818.967543 C888.668835,819.613099 890.25,821.621899 890.25,824 C890.25,826.378101 888.668835,828.386901 886.50045,829.032457 L886.5,837.5 C886.5,838.328427 885.828427,839 885,839 C884.209229,839 883.561376,838.388092 883.504114,837.611947 L883.5,837.5 L883.500557,829.032757 C881.331654,828.387523 879.75,826.378469 879.75,824 C879.75,821.621531 881.331654,819.612477 883.500557,818.967243 L883.5,810.5 C883.5,809.671573 884.171573,809 885,809 Z M894.75,809 C895.540771,809 896.188624,809.611908 896.245886,810.388053 L896.25,810.5 L896.25045,828.717543 C898.418835,829.363099 900,831.371899 900,833.75 C900,836.649495 897.649495,839 894.75,839 C891.850505,839 889.5,836.649495 889.5,833.75 C889.5,831.371531 891.081654,829.362477 893.250557,828.717243 L893.25,810.5 C893.25,809.671573 893.921573,809 894.75,809 Z M894.75,831.5 C893.507359,831.5 892.5,832.507359 892.5,833.75 C892.5,834.992641 893.507359,836 894.75,836 C895.992641,836 897,834.992641 897,833.75 C897,832.507359 895.992641,831.5 894.75,831.5 Z M885,821.75 C883.757359,821.75 882.75,822.757359 882.75,824 C882.75,825.242641 883.757359,826.25 885,826.25 C886.242641,826.25 887.25,825.242641 887.25,824 C887.25,822.757359 886.242641,821.75 885,821.75 Z M875.25,812 C874.007359,812 873,813.007359 873,814.25 C873,815.492641 874.007359,816.5 875.25,816.5 C876.492641,816.5 877.5,815.492641 877.5,814.25 C877.5,813.007359 876.492641,812 875.25,812 Z"
              id="data-flow-manager"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const PreviewIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 24 24'}
      style={style}
      className={userProps.className || ''}
    >
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Shape</title>
        <g id="page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="6.Finalize-and-Activate-"
            transform="translate(-1578.000000, -878.000000)"
            fill="currentColor"
            fillRule="nonzero"
          >
            <g id="edit-6" transform="translate(1308.000000, 766.000000)">
              <path
                d="M276,112 C278.990295,112 281.47814,114.15177 282,116.991219 C281.478695,119.830668 278.990295,121.982438 276,121.982438 C273.009705,121.982438 270.52186,119.830668 270,116.991219 C270.521305,114.15177 273.009705,112 276,112 Z M276,120.873278 C278.329304,120.872774 280.348919,119.262052 280.867548,116.991219 C280.347028,114.722266 278.327894,113.113889 276,113.113889 C273.672106,113.113889 271.652972,114.722266 271.132452,116.991219 C271.651081,119.262052 273.670696,120.872774 276,120.873278 Z M276,119.486829 C274.621713,119.486829 273.50439,118.369506 273.50439,116.991219 C273.50439,115.612932 274.621713,114.49561 276,114.49561 C277.378287,114.49561 278.49561,115.612932 278.49561,116.991219 C278.49561,118.369506 277.378287,119.486829 276,119.486829 Z M276,118.377669 C276.765715,118.377669 277.38645,117.756934 277.38645,116.991219 C277.38645,116.225504 276.765715,115.604769 276,115.604769 C275.234285,115.604769 274.61355,116.225504 274.61355,116.991219 C274.61355,117.756934 275.234285,118.377669 276,118.377669 L276,118.377669 Z"
                id="Shape"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FinalizeIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 17 18'}
      style={style}
      className={userProps.className || ''}
    >
      <svg
        width="14px"
        height="18px"
        viewBox="0 0 17 18"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>finalizeIcon</title>
        <g id="page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="6.Finalize-and-Activate-"
            transform="translate(-1419.000000, -1337.000000)"
            fill="currentColor"
          >
            <g id="edit-17" transform="translate(1359.000000, 1326.000000)">
              <g id="finalize-icon" transform="translate(60.888889, 11.000000)">
                <path
                  d="M6.85,16.5 L6.85,18 L1.14166667,18 C0.511141577,18 0,17.4963203 0,16.875 L0,2.625 C0,2.00367966 0.511141577,1.5 1.14166667,1.5 L3.80555556,1.5 L3.80555556,1.125 C3.80555556,0.503679656 4.31669713,0 4.94722222,0 L10.275,0 C10.9055251,0 11.4166667,0.503679656 11.4166667,1.125 L11.4166667,1.5 L14.0805556,1.5 C14.7110806,1.5 15.2222222,2.00367966 15.2222222,2.625 L15.2222222,8.25 L13.7,8.25 L13.7,3 L11.4166667,3 L11.4166667,3.375 C11.4166667,3.99632034 10.9055251,4.5 10.275,4.5 L4.94722222,4.5 C4.31669713,4.5 3.80555556,3.99632034 3.80555556,3.375 L3.80555556,3 L1.52222222,3 L1.52222222,16.5 L6.85,16.5 Z M4.19345546,7.5 C3.97922426,7.5 3.80555556,7.33947372 3.80555556,7.12148666 L3.80555556,6.37851334 C3.80555556,6.16946619 3.97882462,6 4.19345546,6 L11.0287668,6 C11.242998,6 11.4166667,6.16052628 11.4166667,6.37851334 L11.4166667,7.12148666 C11.4166667,7.33053381 11.2433976,7.5 11.0287668,7.5 L4.19345546,7.5 Z M10.1146995,17.4969811 C9.93291829,17.6761085 9.64156696,17.6794335 9.44603735,17.4867583 L8.77959697,16.8300661 C8.59209589,16.6452832 8.58711211,16.3506175 8.76922364,16.1711471 L14.5691864,10.4558727 C14.7509676,10.2767453 15.0423189,10.2734203 15.2378485,10.4660955 L15.9042889,11.1227877 C16.09179,11.3075706 16.0967738,11.6022363 15.9146622,11.7817067 L10.1146995,17.4969811 Z M4.18871619,10.5 C3.97710242,10.5 3.80555556,10.3394737 3.80555556,10.1214867 L3.80555556,9.37851334 C3.80555556,9.16946619 3.98282026,9 4.18871619,9 L8.75017269,9 C8.96178647,9 9.13333333,9.16052628 9.13333333,9.37851334 L9.13333333,10.1214867 C9.13333333,10.3305338 8.95606863,10.5 8.75017269,10.5 L4.18871619,10.5 Z M5.32777778,1.5 L5.32777778,3 L9.89444444,3 L9.89444444,1.5 L5.32777778,1.5 Z"
                  id="Shape"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FinalizeAndActivateIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 17 18'}
      style={style}
      className={userProps.className || ''}
    >
      <svg
        width="17px"
        height="18px"
        viewBox="0 0 17 18"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>finalizeActivateIcon</title>
        <g id="page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="6.Finalize-and-Activate-"
            transform="translate(-1553.000000, -1337.000000)"
            fill="currentColor"
          >
            <g id="edit-17" transform="translate(1359.000000, 1326.000000)">
              <g id="finalizeActivateIcon" transform="translate(194.844444, 11.000000)">
                <path
                  d="M11.4166667,3 L11.4166667,3.375 C11.4166667,3.99632034 10.9055251,4.5 10.275,4.5 L4.94722222,4.5 C4.31669713,4.5 3.80555556,3.99632034 3.80555556,3.375 L3.80555556,3 L1.52222222,3 L1.52222222,16.5 L13.7,16.5 L13.7,3 L11.4166667,3 Z M3.80555556,1.5 L3.80555556,1.125 C3.80555556,0.503679656 4.31669713,0 4.94722222,0 L10.275,0 C10.9055251,0 11.4166667,0.503679656 11.4166667,1.125 L11.4166667,1.5 L14.0805556,1.5 C14.7110806,1.5 15.2222222,2.00367966 15.2222222,2.625 L15.2222222,16.875 C15.2222222,17.4963203 14.7110806,18 14.0805556,18 L1.14166667,18 C0.511141577,18 0,17.4963203 0,16.875 L0,2.625 C0,2.00367966 0.511141577,1.5 1.14166667,1.5 L3.80555556,1.5 Z M6.79211038,12.8919571 L4.14045996,10.2403066 L5.21683361,9.16393297 L6.80695963,10.7543733 L10.5751317,6.98761543 L11.6357918,8.04827561 L6.79211038,12.8919571 Z M5.32777778,1.5 L5.32777778,3 L9.89444444,3 L9.89444444,1.5 L5.32777778,1.5 Z"
                  id="Shape"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ActivatedIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 48 48"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Activated</title>
        <g id="active" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="active">
            <polygon
              id="active-p-1"
              fillOpacity="0.01"
              fill="#FFFFFF"
              fillRule="nonzero"
              points="0 0 48 0 48 48 0 48"
            />
            <path
              d="M32.591,8 L38,8 C39.657,8 41,9.242 41,10.775 L41,40.225 C41,41.758 39.657,43 38,43 L10,43 C8.343,43 7,41.758 7,40.225 L7,10.775 C7,9.242 8.343,8 10,8 L16.391,8"
              id="active-p-2"
              stroke="#00B08D"
              strokeWidth="4"
            />
            <polyline
              id="active-p-2"
              stroke="#00B08D"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
              points="17.343 26.343 22 31 32 20.751"
            />
            <path
              d="M17,4 L31,4 C31.5522847,4 32,4.44771525 32,5 L32,11 C32,11.5522847 31.5522847,12 31,12 L17,12 C16.4477153,12 16,11.5522847 16,11 L16,5 C16,4.44771525 16.4477153,4 17,4 Z"
              id="active-p-4"
              stroke="#00B08D"
              strokeWidth="4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const NextIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 20 20'}
      style={style}
      className={userProps.className || ''}
    >
      <svg
        width="20px"
        height="20px"
        viewBox="0 0 20 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>CA516FE8-801D-4AB0-AFD6-9C667F630CCA</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-4-2-Create-Business" transform="translate(-1283.000000, -1059.000000)">
            <g id="Popup" transform="translate(614.000000, 484.000000)">
              <g id="Group-13" transform="translate(496.000000, 561.000000)">
                <g id="arrow-right-line" transform="translate(173.000000, 14.000000)">
                  <polygon id="Path" points="0 0 20 0 20 20 0 20" />
                  <polygon
                    id="Path"
                    fill="#FFFFFF"
                    fillRule="nonzero"
                    points="13.4766667 9.16666667 9.00666667 4.69666667 10.185 3.51833333 16.6666667 10 10.185 16.4816667 9.00666667 15.3033333 13.4766667 10.8333333 3.******** 10.8333333 3.******** 9.16666667"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const BucketIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="15px"
        height="16px"
        viewBox="0 0 15 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>bucket</title>
        <g id="bucket-g-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="bucket-g-2"
            transform="translate(-971.000000, -441.000000)"
            fill="#6239EB"
            fillRule="nonzero"
          >
            <g id="bucket-g-3" transform="translate(971.000000, 441.000000)">
              <path
                d="M8.09485822,0 C9.4707965,0 10.6883002,0.990139384 11.036013,2.24344656 L11.0763757,2.4083541 L11.0826337,2.4392518 L11.1464163,2.44053355 L11.2851975,2.4426285 L11.4954091,2.44421312 L11.693708,2.44446203 L11.9401989,2.44356111 L12.3617909,2.43983637 C13.1316164,2.43983637 13.7803631,2.99493396 13.8540225,3.72514147 L13.8609835,3.86401467 L13.8612926,4.16785215 C14.3107755,4.45404721 14.6088201,4.95620697 14.6086956,5.52781003 C14.6086956,5.60112924 14.6036613,5.67431897 14.5936664,5.74687102 L14.5749627,5.8551573 L12.7312074,14.7161869 C12.5853436,15.417204 11.9960942,15.9321654 11.2925586,15.9937972 L11.1504252,16 L4.93802467,16 C4.22283153,16 3.59914181,15.531568 3.39385851,14.8591848 L3.35847747,14.7220672 L2.579,11.0447926 L2.3989627,11.1241986 L2.20849975,11.2058619 C0.594239781,11.8913548 -0.500461021,10.7555141 0.232159536,9.05225284 L0.261711936,8.98525247 C0.266857077,8.97385911 0.272112136,8.96235229 0.277477185,8.95073182 L0.310987941,8.87964355 L0.38594257,8.72924552 L0.427393353,8.6499165 L0.47149772,8.56782133 L0.518259126,8.48295037 L0.567681026,8.395294 L0.619766873,8.30484261 L0.674520122,8.21158656 L0.731944228,8.11551625 L0.792042645,8.01662203 L0.854818827,7.9148943 L0.920276228,7.81032343 L0.988418303,7.70289979 L1.05924851,7.59261377 L1.17054161,7.42179671 L1.28790243,7.24448516 L1.41134263,7.06064664 L1.49701929,6.93444549 L1.63061769,6.73965652 L1.657,6.6997926 L1.47971273,5.85888137 C1.33440488,5.17338282 1.64905924,4.49750153 2.21516506,4.15106337 C2.21380754,4.12646276 2.21341044,4.10136528 2.21341044,4.07617435 C2.21341044,2.69278641 3.41097989,1.59119763 4.86582807,1.59119763 C4.97229615,1.59119763 5.07801082,1.59709493 5.18254485,1.60876103 L5.338407,1.63057072 L5.36794225,1.63591367 L5.38035594,1.61055384 C5.83338259,0.708248106 6.7718963,0.0933024906 7.84411684,0.00973994246 L8.04663918,0.000358685913 L8.09485822,0 Z M11.72,13.913 L4.365,13.913 L4.48672547,14.4837972 C4.52620357,14.6700375 4.67524255,14.8099051 4.85790405,14.841962 L4.93802467,14.8489209 L11.1504252,14.8489209 C11.3417082,14.8489209 11.5100072,14.7316574 11.5788736,14.5592172 L11.6020773,14.4821171 L11.72,13.913 Z M3.05936024,5.06487173 C3.0272373,5.06487173 2.99520148,5.06820698 2.96377426,5.07484396 C2.74222256,5.12163254 2.59173656,5.31989667 2.5980681,5.53810519 L2.60796074,5.62061138 L3.406,9.3867926 L3.48698975,9.34761713 C3.55413197,9.31422024 3.62162195,9.28038535 3.68946192,9.2461073 L3.89403402,9.14193856 C4.20247381,8.98367579 4.51804598,8.81633482 4.84095358,8.63944607 L5.05731427,8.5201006 L5.27586179,8.39789524 L5.38595979,8.33571043 L5.60781262,8.20915725 L5.83188571,8.07966688 L6.05819242,7.9472084 C6.20981107,7.8579065 6.36292772,7.76660525 6.51756019,7.67326342 L6.75064798,7.53171509 L6.98602287,7.38707497 L7.22369824,7.23931215 C7.30330826,7.18953398 7.38330391,7.13923022 7.46368744,7.08839569 L7.70600385,6.93429469 C7.97414687,6.76282244 8.33078461,6.84078511 8.50257653,7.10842917 C8.67436844,7.37607324 8.59626044,7.73204734 8.32811741,7.90351959 C8.24499935,7.95667198 8.16226841,8.00928203 8.07992241,8.0613549 L7.834037,8.21596714 L7.59044802,8.3673872 C7.55004049,8.39235959 7.50972809,8.41720025 7.46951056,8.44190984 L7.22934149,8.58859962 C7.1895023,8.61278748 7.14975742,8.63684556 7.11010658,8.6607745 L6.87332775,8.80280356 L6.75578055,8.87266546 L6.52236232,9.01010336 L6.291168,9.14451912 C6.21447241,9.18882412 6.13814528,9.23263058 6.06218441,9.27594366 L5.83539837,9.4044079 C5.79778309,9.42557364 5.76025883,9.44661731 5.72282531,9.46753957 L5.49931087,9.59162133 L5.27796103,9.71282007 C5.24124904,9.73278132 5.20462669,9.75262374 5.16809371,9.77234797 L4.94996603,9.88928015 L4.73396998,10.0034066 C4.69814754,10.0221956 4.66241338,10.040869 4.6267672,10.0594274 L4.41394389,10.1694032 C4.308058,10.2237064 4.20295911,10.2769865 4.09863981,10.3292608 L3.89103847,10.4324735 L3.68550284,10.5330351 L3.652,10.5487926 L4.07240525,12.5290919 C4.10554029,12.5242476 4.13943373,12.5217391 4.17391304,12.5217391 L11.826087,12.5217391 C11.888009,12.5217391 11.9480414,12.5298296 12.0051848,12.5450112 L13.4458326,5.62108753 C13.4522342,5.59032154 13.4554706,5.55898225 13.4554706,5.52755974 C13.4554706,5.30152474 13.2923683,5.11349648 13.0771949,5.07446414 L12.9942808,5.06702798 L3.05936024,5.06487173 Z M1.991,8.2737926 L1.92633181,8.37605231 L1.80839156,8.56435145 L1.75324919,8.65421783 L1.70066298,8.7412167 L1.65063618,8.82533899 L1.60317205,8.90657568 L1.53678793,9.02300039 L1.49574464,9.09698348 L1.43900702,9.20248539 L1.40440488,9.26915834 L1.34295028,9.39367555 C1.33826014,9.40355999 1.33367786,9.41332118 1.32920351,9.42295894 L1.30365308,9.47930273 C1.2996107,9.48844598 1.2956764,9.49746542 1.29185022,9.50636086 C1.18845253,9.74674897 1.14238142,9.94597878 1.14488949,10.091346 C1.14635491,10.1762817 1.1609328,10.2160194 1.17496486,10.2305788 C1.18579017,10.241811 1.21566839,10.2539684 1.28348203,10.2570856 C1.40240765,10.2625524 1.56540049,10.2280317 1.75703111,10.1466561 L1.94120553,10.0676851 C1.97206586,10.0543257 2.00300868,10.0408672 2.03403426,10.0273088 L2.2211831,9.94475531 L2.31550656,9.90257035 L2.334,9.8927926 L1.991,8.2737926 Z M8.09485822,1.15107914 C7.33826023,1.15107914 6.67727539,1.56205653 6.39453726,2.16092627 L6.33984301,2.2920501 L6.33154084,2.3136753 C6.20895704,2.61714774 5.83989512,2.89784434 5.47425097,2.84432788 L5.37480233,2.82121222 L5.32063506,2.80481726 C5.17524947,2.76358156 5.0224918,2.74227677 4.86582807,2.74227677 C4.09141229,2.74227677 3.46802167,3.26163672 3.37785074,3.91317142 L12.707,3.915 L12.7077584,3.86401467 C12.7077584,3.74413869 12.6013356,3.6291201 12.4484624,3.59868006 L12.3684326,3.59087732 L12.0502309,3.59392758 L11.7619231,3.59541377 L11.5459394,3.59545716 L11.2757786,3.59367697 L11.0884728,3.59067886 L11.0043431,3.58864772 L10.8899663,3.58492829 L10.7896788,3.58039349 L10.7147708,3.57586291 L10.663749,3.57195988 L10.616842,3.56755253 L10.5602224,3.56078053 L10.5342667,3.55696792 C10.4786045,3.54821557 10.4294944,3.53708129 10.3765719,3.5177285 C10.3192156,3.49675433 10.2645266,3.46915593 10.2068929,3.42405386 C10.0771773,3.32254299 9.98196053,3.16864131 9.98196053,2.96451569 C9.98196053,2.03049825 9.0920604,1.15107914 8.09485822,1.15107914 Z"
                id="bucket-p-3"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CompanyIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="14px"
        viewBox="0 0 16 14"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Company</title>
        <g id="company-g-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.Empty" transform="translate(-1201.000000, -443.000000)" fill="#CA303D">
            <path
              d="M1202.75,455.625 L1202.75,444.125 C1202.75,443.572715 1203.19772,443.125 1203.75,443.125 L1208.625,443.125 C1209.17728,443.125 1209.625,443.572715 1209.625,444.125 L1209.625,445.625 L1209.625,445.625 L1214.25,445.625 C1214.80228,445.625 1215.25,446.072715 1215.25,446.625 L1215.25,455.625 L1215.25,455.625 L1216.5,455.625 L1216.5,456.875 L1201.5,456.875 L1201.5,455.625 L1202.75,455.625 Z M1204,444.375 L1204,455.625 L1208.375,455.625 L1208.375,444.375 L1204,444.375 Z M1209.625,455.625 L1214,455.625 L1214,446.875 L1209.625,446.875 L1209.625,455.625 Z M1205.25,451.875 L1207.125,451.875 L1207.125,453.125 L1205.25,453.125 L1205.25,451.875 Z M1210.875,451.875 L1212.75,451.875 L1212.75,453.125 L1210.875,453.125 L1210.875,451.875 Z M1205.25,449.375 L1207.125,449.375 L1207.125,450.625 L1205.25,450.625 L1205.25,449.375 Z M1210.875,449.375 L1212.75,449.375 L1212.75,450.625 L1210.875,450.625 L1210.875,449.375 Z M1205.25,446.875 L1207.125,446.875 L1207.125,448.125 L1205.25,448.125 L1205.25,446.875 Z"
              id="Shape"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const PrimaryContactIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Primary Contact</title>
        <g id="primary-contact-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.Empty" transform="translate(-1463.000000, -442.000000)" fill="#39B9EB">
            <path
              d="M1477.25,456.25 L1477.25,454.583333 C1477.25,453.806772 1474.41866,452.5 1471,452.5 C1467.58134,452.5 1464.75,453.806772 1464.75,454.583333 L1464.75,456.25 L1477.25,456.25 Z M1478.5,454.583333 L1478.5,456.875 C1478.5,457.220178 1478.22018,457.5 1477.875,457.5 L1464.125,457.5 C1463.77982,457.5 1463.5,457.220178 1463.5,456.875 L1463.5,454.583333 C1463.5,452.916667 1466.83333,451.25 1471,451.25 C1475.16667,451.25 1478.5,452.916667 1478.5,454.583333 Z M1471,448.75 C1472.38071,448.75 1473.5,447.630712 1473.5,446.25 C1473.5,444.869288 1472.38071,443.75 1471,443.75 C1469.61929,443.75 1468.5,444.869288 1468.5,446.25 C1468.5,447.630712 1469.61929,448.75 1471,448.75 Z M1471,450 C1468.92893,450 1467.25,448.321068 1467.25,446.25 C1467.25,444.178932 1468.92893,442.5 1471,442.5 C1473.07107,442.5 1474.75,444.178932 1474.75,446.25 C1474.75,448.321068 1473.07107,450 1471,450 Z"
              id="Shape"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AddressIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="18px"
        viewBox="0 0 16 18"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>address</title>
        <g id="address-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.Empty" transform="translate(-970.000000, -514.000000)" fill="#81D4CA">
            <path
              d="M972.991139,526.90509 C971.663796,525.56663 970.916667,523.740905 970.916667,521.835801 C970.916667,519.930697 971.663796,518.104972 972.991139,516.766511 C975.767807,513.966718 980.232193,513.966718 983.008861,516.766511 C984.336204,518.104972 985.083333,519.930697 985.083333,521.835801 C985.083333,523.740905 984.336204,525.56663 983.008861,526.90509 C981.625749,528.304901 978,531.333333 978,531.333333 C978,531.333333 974.374644,528.304901 972.991139,526.90509 Z M979.63759,527.748373 C979.747039,527.652041 979.747039,527.652041 979.856164,527.555656 C980.730718,526.782496 981.414732,526.152086 981.83037,525.730235 C982.844508,524.704688 983.416667,523.302549 983.416667,521.837955 C983.416667,520.373361 982.844508,518.971221 981.8304,517.945705 C979.704391,515.795881 976.295609,515.795881 974.16963,517.945675 C973.155492,518.971221 972.583333,520.373361 972.583333,521.837955 C972.583333,523.302549 973.155492,524.704688 974.16963,525.730235 C974.585288,526.151987 975.269406,526.782445 976.144017,527.55566 C976.252953,527.651877 976.252953,527.651877 976.36221,527.748043 C976.884434,528.207314 977.439803,528.687713 978.00005,529.166667 C978.560196,528.687833 979.115462,528.207549 979.63759,527.748373 Z M976.333333,521.736259 C976.340908,520.817554 977.090524,520.078239 978.009261,520.083333 C978.927999,520.088481 979.669327,520.836106 979.666667,521.754838 C979.663992,522.673571 978.918337,523.41688 977.999585,523.416667 C977.076287,523.41227 976.33103,522.660876 976.33422,521.737589 L976.333333,521.736259 Z"
              id="Shape"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const TotalAmountIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="14px"
        height="16px"
        viewBox="0 0 14 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Total amount</title>
        <g id="total-amount-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.Empty" transform="translate(-1463.000000, -515.000000)" fill="#FF9600">
            <path
              d="M1475.625,515.5 C1475.97018,515.5 1476.25,515.779822 1476.25,516.125 L1476.25,529.875 C1476.25,530.220178 1475.97018,530.5 1475.625,530.5 L1464.375,530.5 C1464.02982,530.5 1463.75,530.220178 1463.75,529.875 L1463.75,516.125 C1463.75,515.779822 1464.02982,515.5 1464.375,515.5 L1475.625,515.5 Z M1475,516.75 L1465,516.75 L1465,529.25 L1475,529.25 L1475,516.75 Z M1470,518.625 C1470.34518,518.625 1470.625,518.904822 1470.625,519.25 L1470.625,519.875 L1471.5625,519.875 C1471.90768,519.875 1472.1875,520.154822 1472.1875,520.5 C1472.1875,520.845178 1471.90768,521.125 1471.5625,521.125 L1469.375,521.125 C1469.02982,521.125 1468.75,521.404822 1468.75,521.75 C1468.75,522.095178 1469.02982,522.375 1469.375,522.375 L1470.625,522.375 C1471.66053,522.375 1472.5,523.214466 1472.5,524.25 C1472.5,525.285534 1471.66053,526.125 1470.625,526.125 L1470.625,526.75 C1470.625,527.095178 1470.34518,527.375 1470,527.375 C1469.65482,527.375 1469.375,527.095178 1469.375,526.75 L1469.375,526.125 L1468.4375,526.125 C1468.09232,526.125 1467.8125,525.845178 1467.8125,525.5 C1467.8125,525.154822 1468.09232,524.875 1468.4375,524.875 L1470.625,524.875 C1470.97018,524.875 1471.25,524.595178 1471.25,524.25 C1471.25,523.904822 1470.97018,523.625 1470.625,523.625 L1469.375,523.625 C1468.33947,523.625 1467.5,522.785534 1467.5,521.75 C1467.5,520.714466 1468.33947,519.875 1469.375,519.875 L1469.375,519.25 C1469.375,518.904822 1469.65482,518.625 1470,518.625 Z"
              id="totalAmount"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CheckboxIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>checkbox</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M14,0 C15.1045695,0 16,0.8954305 16,2 L16,2 L16,14 C16,15.1045695 15.1045695,16 14,16 L14,16 L2,16 C0.8954305,16 0,15.1045695 0,14 L0,14 L0,2 C0,0.8954305 0.8954305,0 2,0 L2,0 Z M14,1.6 L2,1.6 C1.7790861,1.6 1.6,1.7790861 1.6,2 L1.6,2 L1.6,14 C1.6,14.2209139 1.7790861,14.4 2,14.4 L2,14.4 L14,14.4 C14.2209139,14.4 14.4,14.2209139 14.4,14 L14.4,14 L14.4,2 C14.4,1.7790861 14.2209139,1.6 14,1.6 L14,1.6 Z M12.3656854,5.03431458 C12.6616617,5.33029088 12.6772394,5.80048454 12.4124185,6.11479952 L12.3656854,6.16568542 L7.56568542,10.9656854 C7.26970912,11.2616617 6.79951546,11.2772394 6.48520048,11.0124185 L6.43431458,10.9656854 L4.03431458,8.56568542 C3.72189514,8.25326599 3.72189514,7.74673401 4.03431458,7.43431458 C4.33029088,7.13833827 4.80048454,7.12276057 5.11479952,7.38758147 L5.16568542,7.43431458 L7,9.2684 L11.2343146,5.03431458 C11.546734,4.72189514 12.053266,4.72189514 12.3656854,5.03431458 Z"
            id="checkbox"
            fill="#A33CEF"
            fillRule="nonzero"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CurrencyIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="18px"
        height="18px"
        viewBox="0 0 18 18"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Cost</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M9,18 C4.02943725,18 0,13.9705627 0,9 C0,4.02943725 4.02943725,0 9,0 C13.9705627,0 18,4.02943725 18,9 C18,13.9705627 13.9705627,18 9,18 Z M9,16.6153846 C13.2058608,16.6153846 16.6153846,13.2058608 16.6153846,9 C16.6153846,4.79413921 13.2058608,1.38461538 9,1.38461538 C4.79413921,1.38461538 1.38461538,4.79413921 1.38461538,9 C1.38461538,13.2058608 4.79413921,16.6153846 9,16.6153846 Z M9.69230769,12.4615385 L9.69230769,13.8461538 L8.30769231,13.8461538 L8.30769231,12.4615385 L5.88461538,12.4615385 L5.88461538,11.0769231 L10.3846154,11.0769231 C10.7669664,11.0769231 11.0769231,10.7669664 11.0769231,10.3846154 C11.0769231,10.0022644 10.7669664,9.69230769 10.3846154,9.69230769 L7.61538462,9.69230769 C6.46833167,9.69230769 5.53846154,8.76243756 5.53846154,7.61538462 C5.53846154,6.46833167 6.46833167,5.53846154 7.61538462,5.53846154 L8.30769231,5.53846154 L8.30769231,4.15384615 L9.69230769,4.15384615 L9.69230769,5.53846154 L12.1153846,5.53846154 L12.1153846,6.92307692 L7.61538462,6.92307692 C7.23303363,6.92307692 6.92307692,7.23303363 6.92307692,7.61538462 C6.92307692,7.9977356 7.23303363,8.30769231 7.61538462,8.30769231 L10.3846154,8.30769231 C11.5316683,8.30769231 12.4615385,9.23756244 12.4615385,10.3846154 C12.4615385,11.5316683 11.5316683,12.4615385 10.3846154,12.4615385 L9.69230769,12.4615385 Z"
            id="Cost"
            fill="#FFD166"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DateIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="15px"
        viewBox="0 0 16 15"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Created Date</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M12.4468803,1.46666667 L15.2163065,1.46666667 C15.6491285,1.46666667 16,1.79499118 16,2.2 L16,14.0875107 C16,14.4925195 15.6491285,14.820844 15.2163065,14.820844 L0.783693474,14.820844 C0.35087152,14.820844 0,14.4925195 0,14.0875107 L0,2.2 C0,1.79499118 0.35087152,1.46666667 0.783693474,1.46666667 L4.27817464,1.46666667 L4.27817464,-9.23705556e-14 L5.80028405,-9.23705556e-14 L5.80028405,1.46666667 L10.9812948,1.46666667 L10.9812948,-9.23705556e-14 L12.4468803,-9.23705556e-14 L12.4468803,1.46666667 Z M10.9570482,2.80948911 L5.78028594,2.80948911 L5.78028594,4.40708396 L4.27817464,4.40708396 L4.27817464,2.80948911 L1.32949918,2.80948911 L1.32949918,6.14282244 L14.6628325,6.14282244 L14.6628325,2.80948911 L12.4468803,2.80948911 L12.4468803,4.40708396 L10.9570482,4.40708396 L10.9570482,2.80948911 Z M14.6486266,7.46812795 L1.31529324,7.46812795 L1.31529324,13.468128 L14.6486266,13.468128 L14.6486266,7.46812795 Z"
            id="Created-Date"
            fill="#0EB3D3"
            fillRule="nonzero"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ImageIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="12px"
        viewBox="0 0 16 12"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Image</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M1.********,1.******** L1.********,10.6666667 L14.6666667,10.6666667 L14.6666667,1.******** L1.********,1.******** Z M1,0 L15,0 C15.5522847,0 16,0.44771525 16,1 L16,11 C16,11.5522847 15.5522847,12 15,12 L1,12 C0.44771525,12 0,11.5522847 0,11 L0,1 C0,0.44771525 0.44771525,0 1,0 Z M5.07879559,9.******** L3.00259903,9.******** C2.81850411,9.******** 2.66926569,9.18409492 2.66926569,9 L2.66926569,8.******** C2.66926569,8.14923842 2.81850411,8 3.00259903,8 L4.25453774,8 L5.70084821,5.10571018 C5.78313997,4.94103169 5.98334892,4.87424406 6.1480274,4.95653582 C6.21253459,4.98877082 6.26484858,5.04105587 6.29711926,5.10554521 L7.28673655,7.08318422 L9.02633692,3.02248624 C9.09883086,2.85326577 9.2947791,2.77485342 9.46399957,2.84734736 C9.54268198,2.88105486 9.60538156,2.94373969 9.63910756,3.02241418 L11.7728878,8 L13,8 C13.1840949,8 13.3333333,8.14923842 13.3333333,8.******** L13.3333333,9 C13.3333333,9.18409492 13.1840949,9.******** 13,9.******** L10.8937788,9.******** L9.33303624,5.69250375 L7.66349286,9.58966978 C7.59099892,9.75889024 7.39505068,9.83730259 7.22583021,9.76480865 C7.15337875,9.7337705 7.0942699,9.67806259 7.05899781,9.60757528 L5.99964358,7.4905748 L5.07879559,9.******** Z"
            id="Image"
            fill="#67D5C9"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const MultipleSelectIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Configurable</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M1.********,1.******** L1.********,14.6666667 L14.6666667,14.6666667 L14.6666667,1.******** L1.********,1.******** Z M1.********,0 L14.6666667,0 C15.4030463,0 16,0.596953667 16,1.******** L16,14.6666667 C16,15.4030463 15.4030463,16 14.6666667,16 L1.********,16 C0.596953667,16 0,15.4030463 0,14.6666667 L0,1.******** C0,0.596953667 0.596953667,0 1.********,0 Z M9,10 L12.3333333,10 C12.5174282,10 12.6666667,10.1492384 12.6666667,10.3333333 L12.6666667,11 C12.6666667,11.1840949 12.5174282,11.3333333 12.3333333,11.3333333 L9,11.3333333 C8.81590508,11.3333333 8.********,11.1840949 8.********,11 L8.********,10.3333333 C8.********,10.1492384 8.81590508,10 9,10 Z M9,4.******** L12.3333333,4.******** C12.5174282,4.******** 12.6666667,4.81590508 12.6666667,5 L12.6666667,5.******** C12.6666667,5.85076158 12.5174282,6 12.3333333,6 L9,6 C8.81590508,6 8.********,5.85076158 8.********,5.******** L8.********,5 C8.********,4.81590508 8.81590508,4.******** 9,4.******** Z M8.06868413,3.27539333 C8.19996116,3.40172072 8.20549333,3.61004457 8.081106,3.74316129 L5.25752075,6.76490323 C5.13772094,6.88044415 4.98017828,6.93813312 4.82297262,6.93813312 C4.66542996,6.93813312 4.50839279,6.88044415 4.38825599,6.76490323 L2.89015572,5.15642897 C2.76613767,5.02327377 2.7718207,4.81526511 2.90292421,4.68908012 L3.28760082,4.31883516 C3.29027725,4.31625914 3.29299655,4.31372804 3.29575759,4.31124292 C3.43258972,4.18808486 3.6433534,4.19916988 3.76651147,4.336002 L4.82297262,5.50976054 L7.20531108,2.91777285 C7.20703366,2.91589869 7.20877764,2.91404432 7.2105427,2.91221011 C7.33819271,2.77955863 7.54920875,2.77550415 7.68186023,2.90315416 L8.06868413,3.27539333 Z M8.06868413,8.60872666 C8.19996116,8.73505405 8.20549333,8.9433779 8.081106,9.07649462 L5.25752075,12.0982366 C5.13772094,12.2137775 4.98017828,12.2714665 4.82297262,12.2714665 C4.66542996,12.2714665 4.50839279,12.2137775 4.38825599,12.0982366 L2.89015572,10.4897623 C2.76613767,10.3566071 2.7718207,10.1485984 2.90292421,10.0224134 L3.28760082,9.65216849 C3.29027725,9.64959247 3.29299655,9.64706137 3.29575759,9.64457625 C3.43258972,9.52141819 3.6433534,9.53250321 3.76651147,9.66933534 L4.82297262,10.8430939 L7.20531108,8.25110619 C7.20703366,8.24923202 7.20877764,8.24737765 7.2105427,8.24554344 C7.33819271,8.11289197 7.54920875,8.10883748 7.68186023,8.23648749 L8.06868413,8.60872666 Z"
            id="Configurable"
            fill="#0EB3D3"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const NumberIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Number</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M4.36363636,2.90909091 L14.5454545,2.90909091 C15.3487778,2.90909091 16,3.56031309 16,4.36363636 L16,14.5454545 C16,15.3487778 15.3487778,16 14.5454545,16 L4.36363636,16 C3.56031309,16 2.90909091,15.3487778 2.90909091,14.5454545 L2.90909091,4.36363636 C2.90909091,3.56031309 3.56031309,2.90909091 4.36363636,2.90909091 Z M13.8539601,4.14409684 C14.4062448,4.14409684 14.8539601,4.59181209 14.8539601,5.14409684 L14.8539601,13.9050878 C14.8539601,14.4573725 14.4062448,14.9050878 13.8539601,14.9050878 L5.16862457,14.9050878 C4.61633982,14.9050878 4.16862457,14.4573725 4.16862457,13.9050878 L4.16862457,5.14409684 C4.16862457,4.59181209 4.61633982,4.14409684 5.16862457,4.14409684 L13.8539601,4.14409684 Z M10.1818182,5.81818182 L8.72727273,5.81818182 L7.27272727,6.54545455 L7.27272727,7.27272727 L8.72727273,7.27272727 L8.72727273,11.6363636 L8.0011355,11.9994323 L7.27272727,12.3636364 L7.27272727,13.0909091 L11.6363636,13.0909091 L11.6363636,12.3636364 L10.1818182,11.6363636 L10.1818182,5.81818182 Z M10.1818182,0 C10.9851415,0 11.5832777,0.464834506 11.5832777,1.26815778 L11.5832777,1.26815778 L10.1287323,1.26815778 L1.24483705,1.24604392 L1.29792297,10.1597043 L1.29792297,11.6142498 C0.494599699,11.6142498 2.98427949e-13,10.9851415 2.98427949e-13,10.1818182 L2.98427949e-13,10.1818182 L2.98427949e-13,1.45454545 C2.98427949e-13,0.651222182 0.651222182,0 1.45454545,0 L1.45454545,0 Z"
            id="Free-Trial-Unit"
            fill="#A33CEF"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const PercentageIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>percentage</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M12.8888889,9.77777777 C14.6071081,9.77777777 16,11.1706697 16,12.8888889 C16,14.6071081 14.6071081,16 12.8888889,16 C11.1706697,16 9.77777779,14.6071081 9.77777779,12.8888889 C9.77777779,11.1706697 11.1706697,9.77777777 12.8888889,9.77777777 L12.8888889,9.77777777 Z M15.0222222,0.977777771 C15.1891352,1.14450511 15.2829215,1.37074733 15.2829215,1.60666667 C15.2829215,1.842586 15.1891352,2.06882822 15.0222222,2.23555556 L2.44977778,14.8062222 C2.10099649,15.1430861 1.54659154,15.1382683 1.2037172,14.7953939 C0.860842866,14.4525196 0.856025073,13.8981146 1.19288891,13.5493333 L13.7644444,0.977777771 C14.1115554,0.630771603 14.6742224,0.630771603 15.0213333,0.977777771 L15.0222222,0.977777771 Z M12.8888889,11.5555556 C12.1525092,11.5555556 11.5555556,12.1525092 11.5555556,12.8888889 C11.5555556,13.6252686 12.1525092,14.2222222 12.8888889,14.2222222 C13.6252686,14.2222222 14.2222222,13.6252686 14.2222222,12.8888889 C14.2222222,12.1525092 13.6252686,11.5555556 12.8888889,11.5555556 Z M3.11111111,0 C4.82933034,0 6.22222223,1.39289189 6.22222223,3.11111111 C6.22222223,4.82933034 4.82933034,6.22222223 3.11111111,6.22222223 C1.39289189,6.22222223 0,4.82933034 0,3.11111111 C0,1.39289189 1.39289189,0 3.11111111,0 Z M3.11111111,1.77777777 C2.37473145,1.77777777 1.77777778,2.37473144 1.77777778,3.1111111 C1.77777778,3.84749077 2.37473145,4.44444444 3.11111111,4.44444444 C3.84749078,4.44444444 4.44444445,3.84749077 4.44444445,3.1111111 C4.44444445,2.37473144 3.84749078,1.77777777 3.11111111,1.77777777 Z"
            id="percentage"
            fill="#2F62EF"
            fillRule="nonzero"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SelectIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>select</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M1.********,1.******** L1.********,14.6666667 L14.6666667,14.6666667 L14.6666667,1.******** L1.********,1.******** Z M1.********,0 L14.6666667,0 C15.4030463,0 16,0.596953667 16,1.******** L16,14.6666667 C16,15.4030463 15.4030463,16 14.6666667,16 L1.********,16 C0.596953667,16 0,15.4030463 0,14.6666667 L0,1.******** C0,0.596953667 0.596953667,0 1.********,0 Z M5,13.3333333 C3.71133558,13.3333333 2.********,12.2886644 2.********,11 C2.********,9.71133558 3.71133558,8.******** 5,8.******** C6.28866442,8.******** 7.********,9.71133558 7.********,11 C7.********,12.2886644 6.28866442,13.3333333 5,13.3333333 Z M5,7.******** C3.71133558,7.******** 2.********,6.28866442 2.********,5 C2.********,3.71133558 3.71133558,2.******** 5,2.******** C6.28866442,2.******** 7.********,3.71133558 7.********,5 C7.********,6.28866442 6.28866442,7.******** 5,7.******** Z M5,12 C5.55228475,12 6,11.5522847 6,11 C6,10.4477153 5.55228475,10 5,10 C4.44771525,10 4,10.4477153 4,11 C4,11.5522847 4.44771525,12 5,12 Z M9,10 L12.3333333,10 C12.5174282,10 12.6666667,10.1492384 12.6666667,10.3333333 L12.6666667,11 C12.6666667,11.1840949 12.5174282,11.3333333 12.3333333,11.3333333 L9,11.3333333 C8.81590508,11.3333333 8.********,11.1840949 8.********,11 L8.********,10.3333333 C8.********,10.1492384 8.81590508,10 9,10 Z M9,4.******** L12.3333333,4.******** C12.5174282,4.******** 12.6666667,4.81590508 12.6666667,5 L12.6666667,5.******** C12.6666667,5.85076158 12.5174282,6 12.3333333,6 L9,6 C8.81590508,6 8.********,5.85076158 8.********,5.******** L8.********,5 C8.********,4.81590508 8.81590508,4.******** 9,4.******** Z"
            id="Free-Trial-Type"
            fill="#FC5455"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const TextAreaIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>TextArea</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M14.6666667,0 C15.4030463,0 16,0.596953667 16,1.******** L16,14.6666667 C16,15.4030463 15.4030463,16 14.6666667,16 L1.********,16 C0.596953667,16 0,15.4030463 0,14.6666667 L0,1.******** C0,0.596953667 0.596953667,0 1.********,0 L14.6666667,0 Z M14.6666667,1.******** L1.********,1.******** L1.********,14.6666667 L14.6666667,14.6666667 L14.6666667,1.******** Z M12.7991596,6.74616483 L13.0245857,6.97159091 C13.2849352,7.23194044 13.2849352,7.65405042 13.0245857,7.91439995 L7.80229651,13.1366891 L6.39972301,13.3737553 L6.63406139,11.968454 L11.8563506,6.74616483 C12.1167001,6.4858153 12.5388101,6.4858153 12.7991596,6.74616483 Z M6.99771818,6.******** C7.18307331,6.******** 7.********,6.80935669 7.********,7.00312297 L7.********,7.6635437 C7.********,7.84936338 7.17806498,8 6.99771818,8 L3.00228182,8 C2.81692669,8 2.********,7.85730998 2.********,7.6635437 L2.********,7.00312297 C2.********,6.81730328 2.82193502,6.******** 3.00228182,6.******** L6.99771818,6.******** Z M9.62625702,4 C9.83266957,4 10,4.14269002 10,4.3364563 L10,4.99687703 C10,5.18269672 9.83305462,5.******** 9.62625702,5.******** L3.04040964,5.******** C2.8339971,5.******** 2.********,5.19064331 2.********,4.99687703 L2.********,4.3364563 C2.********,4.15063662 2.83361204,4 3.04040964,4 L9.62625702,4 Z"
            id="Price-Book"
            fill="#67D5C9"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const TextIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Text</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M14.2222222,1.******** L1.77777778,1.******** C1.53637967,1.******** 1.********,1.53637967 1.********,1.77777778 L1.********,14.2222222 C1.********,14.4636203 1.53637967,14.6666667 1.77777778,14.6666667 L14.2222222,14.6666667 C14.4636203,14.6666667 14.6666667,14.4636203 14.6666667,14.2222222 L14.6666667,1.77777778 C14.6666667,1.53637967 14.4636203,1.******** 14.2222222,1.******** Z M14.2222222,0 C15.2,0 16,0.8 16,1.77777778 L16,14.2222222 C16,15.2 15.2,16 14.2222222,16 L1.77777778,16 C0.8,16 0,15.2 0,14.2222222 L0,1.77777778 C0,0.8 0.8,0 1.77777778,0 L14.2222222,0 Z M8.********,5.******** L8.********,12 L7.********,12 L7.********,5.******** L4,5.******** L4,4 L12,4 L12,5.******** L8.********,5.******** Z"
            id="Extermal-ID"
            fill="#6239EB"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const OthersIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="14px"
        height="16px"
        viewBox="0 0 14 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Others</title>
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M1.********,0 L10,0 L13.3333333,3.******** L13.3333333,14.6666667 C13.3333333,15.4 12.7333333,16 12,16 L1.32666667,16 C0.593333333,16 -2.84217094e-14,15.4 -2.84217094e-14,14.6666667 L0.006********,1.******** C0.006********,0.6 0.6,0 1.********,0 Z M1.********,14.6666667 L12,14.6666667 L12,3.88561808 L9.44771525,1.******** L1.33934605,1.******** L1.********,14.6666667 Z M6,10.6666667 L6,9.******** L10,9.******** L10,10.6666667 L6,10.6666667 Z M6,6.******** L6,5.******** L10,5.******** L10,6.******** L6,6.******** Z M4.********,6.******** L3.********,6.******** L3.********,5.******** L4.********,5.******** L4.********,6.******** Z M4.********,10.6666667 L3.********,10.6666667 L3.********,9.******** L4.********,9.******** L4.********,10.6666667 Z"
            id="Record-Type"
            fill="#6239EB"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DeleteBucketIcon: React.FC<Props> = (props) => {
  const style = props.style || {};
  return (
    <SvgIcon style={style}>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 48 48"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Delete Bucket</title>
        <g id="promotion" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="delete">
            <polygon
              id="delete-p-1"
              fillOpacity="0.01"
              fill="#FFFFFF"
              fillRule="nonzero"
              points="0 0 48 0 48 48 0 48"
            />
            <g
              id="delete-g-2"
              strokeLinecap="round"
              strokeLinejoin="round"
              transform="translate(5.000000, 4.500000)"
              fill={style?.color || '#FF3939'}
            >
              <path
                d="M0,9.5 C-1.1045695,9.5 -2,8.6045695 -2,7.5 C-2,6.4456382 -1.18412221,5.58183488 -0.149262345,5.50548574 L0,5.5 L38,5.5 C39.1045695,5.5 40,6.3954305 40,7.5 C40,8.5543618 39.1841222,9.41816512 38.1492623,9.49451426 L38,9.5 L35,9.5 L35,33.86 C35,37.387107 32.5007052,40.3649282 29.2245573,40.495534 L29,40.5 L9,40.5 C5.68943033,40.5 3.11656727,37.600559 3.00385265,34.0996064 L3,33.86 L3,9.5 L0,9.5 Z M7,9.5 L7,33.86 C7,35.3158482 7.88733063,36.405904 8.87118832,36.4942145 L9,36.5 L29,36.5 C29.9907564,36.5 30.9193686,35.4706045 30.9950329,34.0477665 L31,33.86 L31,9.5 L7,9.5 Z M19,15.5 C20.0543618,15.5 20.9181651,16.3158778 20.9945143,17.3507377 L21,17.5 L21,28.5 C21,29.6045695 20.1045695,30.5 19,30.5 C17.9456382,30.5 17.0818349,29.6841222 17.0054857,28.6492623 L17,28.5 L17,17.5 C17,16.3954305 17.8954305,15.5 19,15.5 Z M23.5,-1.5 C24.6045695,-1.5 25.5,-0.6045695 25.5,0.5 C25.5,1.5543618 24.6841222,2.41816512 23.6492623,2.49451426 L23.5,2.5 L14.5,2.5 C13.3954305,2.5 12.5,1.6045695 12.5,0.5 C12.5,-0.554361795 13.3158778,-1.41816512 14.3507377,-1.49451426 L14.5,-1.5 L23.5,-1.5 Z"
                id="delete-p-3"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const EditBucketIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 48 48"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Edit Bucket</title>
        <g id="promotion" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="write">
            <polygon id="edit-p-1" points="0 0 48 0 48 48 0 48" />
            <path
              d="M32.8768207,4.40075486 C33.6582348,3.61716399 34.9274387,3.61706963 35.7089693,4.40054431 L35.7089693,4.40054431 L43.3469693,12.0575443 C44.1256048,12.8381168 44.1256874,14.1015962 43.347154,14.8822705 L43.347154,14.8822705 L15.345154,42.9612705 C14.9699246,43.3375317 14.4603846,43.549 13.929,43.549 L13.929,43.549 L6.293,43.549 C5.1884305,43.549 4.293,42.6535695 4.293,41.549 L4.293,41.549 L4.293,33.891 C4.293,33.3615309 4.50295068,32.8536664 4.87682075,32.4787549 L4.87682075,32.4787549 Z M26.656,16.303 L8.293,34.717 L8.293,39.548 L13.098,39.548 L31.467,21.128 L26.656,16.303 Z M34.293,8.645 L29.481,13.47 L34.293,18.296 L39.106,13.469 L34.293,8.645 Z"
              id="edit-p-2"
              fill="#6138EB"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const PreviewBucketIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 48 48"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Preview Bucket</title>
        <g id="preview" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="note-edit">
            <polygon
              id="preview-p-1"
              fillOpacity="0.01"
              fill="#FFFFFF"
              fillRule="nonzero"
              points="0 0 48 0 48 48 0 48"
            />
            <path
              d="M32.591,8 L38,8 C39.657,8 41,9.242 41,10.775 L41,20.309 M20.424,43 L10,43 C8.343,43 7,41.758 7,40.225 L7,10.775 C7,9.242 8.343,8 10,8 L15.391,8 M16.5,19 L32.5,19 M29.094,42.22 L41.74,30 M16.5,26 L26,26"
              id="preview-p-2"
              stroke="#FF7D00"
              strokeWidth="4"
              strokeLinecap="round"
            />
            <path
              d="M17,4 L31,4 C31.5522847,4 32,4.44771525 32,5 L32,11 C32,11.5522847 31.5522847,12 31,12 L17,12 C16.4477153,12 16,11.5522847 16,11 L16,5 C16,4.44771525 16.4477153,4 17,4 Z"
              id="preview-p-3"
              stroke="#FF7D00"
              strokeWidth="4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CreateBucketIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 48 48"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Finalize Order</title>
        <g id="create" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="note-add">
            <polygon
              id="create-p-1"
              fillOpacity="0.01"
              fill="#FFFFFF"
              fillRule="nonzero"
              points="0 0 48 0 48 48 0 48"
            />
            <path
              d="M32.591,8 L38,8 C39.657,8 41,9.242 41,10.775 L41,40.225 C41,41.758 39.657,43 38,43 L10,43 C8.343,43 7,41.758 7,40.225 L7,10.775 C7,9.242 8.343,8 10,8 L16.391,8"
              id="create-p-2"
              stroke="#AAAAAA"
              strokeWidth="4"
            />
            <path
              d="M17.5,27 L31.5,27 M24.5,34 L24.5,20"
              id="create-p-3"
              stroke="#AAAAAA"
              strokeWidth="4"
              strokeLinecap="round"
            />
            <path
              d="M17,4 L31,4 C31.5522847,4 32,4.44771525 32,5 L32,11 C32,11.5522847 31.5522847,12 31,12 L17,12 C16.4477153,12 16,11.5522847 16,11 L16,5 C16,4.44771525 16.4477153,4 17,4 Z"
              id="create-p-4"
              stroke="#AAAAAA"
              strokeWidth="4"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const MoreBucketIcon: React.FC<Props> = (props) => {
  const style = props.style || {};
  return (
    <SvgIcon style={style}>
      <svg
        width="10px"
        height="9px"
        viewBox="0 0 10 9"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>More</title>
        <g id="more-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.Empty" transform="translate(-1801.000000, -573.000000)" fill="#6239EB">
            <g id="more-2" transform="translate(1759.500000, 565.000000)">
              <g id="Shape-2" transform="translate(41.833333, 8.000000)">
                <path
                  d="M0.896061046,4.45572081 L4.********,7.50067842 L8.43727229,4.45572081 C8.67223229,4.26597855 9.01807402,4.30073147 9.20973142,4.53334362 C9.40138883,4.76595578 9.36628514,5.10834168 9.13132514,5.29808394 L5.1872063,8.48316237 C4.88419159,8.72786231 4.44914175,8.72786231 4.14612703,8.48316237 L0.202008197,5.29808394 C-0.0329518017,5.10834168 -0.0680554922,4.76595578 0.12360191,4.53334362 C0.315259312,4.30073147 0.661101048,4.26597855 0.896061046,4.45572081 Z M0.896061046,0.122366815 L4.********,3.16732442 L8.43727229,0.122366815 C8.67223229,-0.0673754461 9.01807402,-0.0326225301 9.20973142,0.199989625 C9.40138883,0.432601781 9.36628514,0.774987685 9.13132514,0.964729946 L5.1872063,4.14980838 C4.88419159,4.39450832 4.44914175,4.39450832 4.14612703,4.14980838 L0.202008197,0.964729946 C-0.0329518017,0.774987685 -0.0680554922,0.432601781 0.12360191,0.199989625 C0.315259312,-0.0326225301 0.661101048,-0.0673754461 0.896061046,0.122366815 Z"
                  id="Shape"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CollapseBucketIcon: React.FC<Props> = (props) => {
  const style = props.style || {};
  return (
    <SvgIcon style={style}>
      <svg
        width="10px"
        height="9px"
        viewBox="0 0 10 9"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Collapse</title>
        <g id="collapse-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="6.Finalize-and-Activate-"
            transform="translate(-1801.000000, -994.000000)"
            fill="#A1A5AF"
          >
            <g id="collapse-2" transform="translate(1737.000000, 986.000000)">
              <g
                id="Shape-2"
                transform="translate(69.000000, 12.333344) rotate(-180.000000) translate(-69.000000, -12.333344) translate(64.333333, 8.000000)"
              >
                <path
                  d="M0.896061046,4.45572081 L4.********,7.50067842 L8.43727229,4.45572081 C8.67223229,4.26597855 9.01807402,4.30073147 9.20973142,4.53334362 C9.40138883,4.76595578 9.36628514,5.10834168 9.13132514,5.29808394 L5.1872063,8.48316237 C4.88419159,8.72786231 4.44914175,8.72786231 4.14612703,8.48316237 L0.202008197,5.29808394 C-0.0329518017,5.10834168 -0.0680554922,4.76595578 0.12360191,4.53334362 C0.315259312,4.30073147 0.661101048,4.26597855 0.896061046,4.45572081 Z M0.896061046,0.122366815 L4.********,3.16732442 L8.43727229,0.122366815 C8.67223229,-0.0673754461 9.01807402,-0.0326225301 9.20973142,0.199989625 C9.40138883,0.432601781 9.36628514,0.774987685 9.13132514,0.964729946 L5.1872063,4.14980838 C4.88419159,4.39450832 4.44914175,4.39450832 4.14612703,4.14980838 L0.202008197,0.964729946 C-0.0329518017,0.774987685 -0.0680554922,0.432601781 0.12360191,0.199989625 C0.315259312,-0.0326225301 0.661101048,-0.0673754461 0.896061046,0.122366815 Z"
                  id="Shape"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FileUploadIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon viewBox={props.viewBox} style={{ marginLeft: '4px' }}>
      <svg
        width={props.width ? props.width : '24px'}
        height={props.height ? props.height : '24px'}
        viewBox="0 0 48 48"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title />
        <g
          id="File-Upload"
          stroke="none"
          strokeWidth="1"
          fill="none"
          fillRule="evenodd"
          opacity={'0.400000006'}
        >
          <g id="6-1-1-1-Subscribers" transform="translate(-624.000000, -343.000000)">
            <g id="Product-Options" transform="translate(360.000000, 288.000000)">
              <g id="settings-4-line" transform="translate(264.000000, 55.000000)">
                <polygon id="Path" points="0 0 24 0 24 24 0 24" />
                <path
                  d="M12.5 40Q8.2 40 5.1 36.9Q2 33.8 2 29.5Q2 25.6 4.475 22.625Q6.95 19.65 10.85 19.05Q11.85 14.2 15.55 11.125Q19.25 8.05 24.1 8.05Q29.75 8.05 33.575 12.125Q37.4 16.2 37.4 21.9V23.1Q41 23 43.5 25.425Q46 27.85 46 31.55Q46 35 43.5 37.5Q41 40 37.55 40H25.5Q24.3 40 23.4 39.1Q22.5 38.2 22.5 37V24.1L18.35 28.25L16.2 26.1L24 18.3L31.8 26.1L29.65 28.25L25.5 24.1V37Q25.5 37 25.5 37Q25.5 37 25.5 37H37.55Q39.8 37 41.4 35.4Q43 33.8 43 31.55Q43 29.3 41.4 27.7Q39.8 26.1 37.55 26.1H34.4V21.9Q34.4 17.45 31.375 14.25Q28.35 11.05 23.9 11.05Q19.45 11.05 16.4 14.25Q13.35 17.45 13.35 21.9H12.4Q9.3 21.9 7.15 24.075Q5 26.25 5 29.45Q5 32.55 7.2 34.775Q9.4 37 12.5 37H19.5V40ZM24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Q24 25.5 24 25.5Z"
                  id="Shape"
                  fill={'#000000'}
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ToolTipIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon viewBox={props.viewBox} style={props.style}>
      <svg
        focusable="false"
        aria-hidden="true"
        viewBox="0 0 24 24"
        data-testid="TipsAndUpdatesOutlinedIcon"
      >
        <path d="M7 20h4c0 1.1-.9 2-2 2s-2-.9-2-2zm-2-1h8v-2H5v2zm11.5-9.5c0 3.82-2.66 5.86-3.77 6.5H5.27c-1.11-.64-3.77-2.68-3.77-6.5C1.5 5.36 4.86 2 9 2s7.5 3.36 7.5 7.5zm-2 0C14.5 6.47 12.03 4 9 4S3.5 6.47 3.5 9.5c0 2.47 1.49 3.89 2.35 4.5h6.3c.86-.61 2.35-2.03 2.35-4.5zm6.87-2.13L20 8l1.37.63L22 10l.63-1.37L24 8l-1.37-.63L22 6l-.63 1.37zM19 6l.94-2.06L22 3l-2.06-.94L19 0l-.94 2.06L16 3l2.06.94L19 6z" />
      </svg>
    </SvgIcon>
  );
};

export const UploadIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon style={props.style}>
      <svg
        width={props.width ? props.width : '120px'}
        height={props.height ? props.height : '120px'}
        viewBox="0 0 650 650"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="Upload-Icon" stroke="none" strokeWidth="1" fill="none" fillRule="nonzero">
          <g id="Import">
            <g id="Upload">
              <circle id="Oval" fill="#FFFFFF" cx="60" cy="60" r="60" />
              <g id="upload-line" transform="translate(48.000000, 48.000000)">
                <polygon id="Path" points="0 0 24 0 24 24 0 24" />
                <path
                  d="M3,19 L21,19 L21,21 L3,21 L3,19 Z M13,5.828 L13,17 L11,17 L11,5.828 L4.929,11.9 L3.515,10.486 L12,2 L20.485,10.485 L19.071,11.899 L13,5.83 L13,5.828 Z"
                  id="Shape"
                  fill="#6239EB"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const UpgradeIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon style={props.style} viewBox={'0 0 20 20'}>
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g opacity="0.3">
          <mask id="mask0_305_10221" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect width="20" height="20" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_305_10221)">
            <path
              d="M6 16V14.5H14V16H6ZM9.25 13V5.875L7.0625 8.0625L6 7L10 3L14 7L12.9375 8.0625L10.75 5.875V13H9.25Z"
              fill="#1C1B1F"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SuccessUploadIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon style={props.style}>
      <svg
        width={props.width ? props.width : '120px'}
        height={props.height ? props.height : '120px'}
        viewBox="0 0 320 320"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="Success-Upload-Icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Import-Success">
            <g id="Success-Upload">
              <circle id="Oval" fill="#6239EB" cx="30" cy="30" r="30" />
              <g id="check-line" transform="translate(18.000000, 18.000000)">
                <polygon id="Path" points="0 0 24 0 24 24 0 24" />
                <polygon
                  id="Path"
                  fill="#FFFFFF"
                  fillRule="nonzero"
                  points="10 15.172 19.192 5.979 20.607 7.393 10 18 3.636 11.636 5.05 10.222"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FailureUploadIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon style={props.style}>
      <svg
        width={props.width ? props.width : '120px'}
        height={props.height ? props.height : '120px'}
        viewBox="0 0 320 320"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="Failed-Upload-Icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Import-Failed">
            <g id="Failed-Upload">
              <circle id="Oval" fill="#FC5455" cx="30" cy="30" r="30" />
              <g
                id="failed"
                transform="translate(22.000000, 22.000000)"
                fill="#FFFFFF"
                fillRule="nonzero"
              >
                <path
                  d="M13.5182754,-0.10473272 L7.666,5.718 L1.86632011,-0.0841258463 C1.32705387,-0.62363088 0.452638817,-0.62363088 -0.0866274244,-0.0841258463 L-0.187267641,0.0280193348 C-0.623375246,0.570357263 -0.589828508,1.36563634 -0.0866274244,1.86906025 L5.713,7.671 L-0.0955864698,13.4822526 C-0.629206982,14.0161095 -0.635552042,14.8795928 -0.109836801,15.4212384 C0.377739518,15.9235894 1.15915451,15.9680962 1.69858983,15.5468728 L1.82194796,15.438011 L7.664,9.623 L13.4647874,15.4264757 C14.0040536,15.9659808 14.8784687,15.9659808 15.4177349,15.4264757 L15.5183751,15.3143306 C15.9544827,14.7719926 15.920936,13.9767135 15.4177349,13.4732896 L9.616,7.67 L15.4383327,1.81554855 C15.9659926,1.2848423 15.9648536,0.427117565 15.4357857,-0.10218463 C14.9065958,-0.631608849 14.0488699,-0.632748826 13.5182754,-0.10473272 Z"
                  id="Shape"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const UploadInProgressIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon style={props.style}>
      <svg
        width={props.width ? props.width : '120px'}
        height={props.height ? props.height : '120px'}
        viewBox="0 0 320 320"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="Inprogress-Upload-Icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Import-Inprogress">
            <g id="Inprogress-Upload">
              <circle id="Oval" fill="#12BB99" cx="30" cy="30" r="30" />
              <g id="Inprogress" transform="translate(22.000000, 28.000000)" fill="#FFFFFF">
                <path
                  d="M1,0 C2.1,0 3,0.9 3,2 C3,3.1 2.1,4 1,4 C-0.1,4 -1,3.1 -1,2 C-1,0.9 -0.1,0 1,0 Z M15,0 C16.1,0 17,0.9 17,2 C17,3.1 16.1,4 15,4 C13.9,4 13,3.1 13,2 C13,0.9 13.9,0 15,0 Z M8,0 C9.1,0 10,0.9 10,2 C10,3.1 9.1,4 8,4 C6.9,4 6,3.1 6,2 C6,0.9 6.9,0 8,0 Z"
                  id="Shape"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DiamondIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon style={props.style} viewBox={props.viewBox}>
      <svg
        width="31px"
        height="31px"
        viewBox="0 -5 31 31"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="new" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="1.2-Release-Notes"
            transform="translate(-1517.000000, -31.000000)"
            fill={props.fill}
            fillRule="nonzero"
          >
            <path
              d="M1537.15766,42.5852597 L1525.49815,30.9999998 L1541.8016,30.9999998 C1542.57572,30.9998156 1543.32135,31.2947642 1543.88928,31.825821 L1547.04066,34.9724072 C1547.28294,35.214778 1547.41864,35.5452348 1547.4173,35.8895736 L1547.4173,35.8895736 C1547.41513,36.2443948 1547.2673,36.5824191 1547.00902,36.8230782 L1540.83574,43.0308466 C1539.6514,44.1165934 1538.25888,43.7564106 1537.15766,42.5852597 Z M1539.14382,44.6646653 C1538.84957,44.9617233 1533.16032,50.6897431 1533.16032,50.6897431 C1532.64539,51.1732049 1531.78031,51.0818595 1531.22565,50.4840304 L1517.51672,36.8668943 C1517.00179,36.1985139 1517.00179,35.752927 1517.44316,35.1588111 L1520.82699,31.7426447 C1521.85685,30.7772064 1523.42298,30.8581547 1524.53229,32.0530703 L1536.20136,43.550698 L1536.93697,44.2933429 C1536.93697,44.2933429 1538.0404,45.3330457 1539.14382,44.6646653 Z"
              id="DiamondIcon"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CloseMenuIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 20 20'}
    >
      <svg
        width="20px"
        height="20px"
        viewBox="0 0 20 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="ask-nue" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.3-Ask-Nuebackup" transform="translate(-1917.000000, -114.000000)" fill="#A1A5AF">
            <path
              d="M1924.57579,120.270435 L1927,122.694716 L1929.42423,120.270435 C1929.78473,119.909855 1930.36917,119.909855 1930.72963,120.270435 C1931.09012,120.630707 1931.09012,121.215448 1930.72963,121.575721 L1928.30546,124.000014 L1930.72963,126.424307 C1931.09012,126.784579 1931.09012,127.36932 1930.72963,127.72963 C1930.36917,128.090123 1929.78473,128.090123 1929.42423,127.72963 L1927,125.305312 L1924.57579,127.72963 C1924.21529,128.090123 1923.63085,128.090123 1923.27035,127.72963 C1922.90988,127.36932 1922.90988,126.784579 1923.27035,126.424307 L1925.69456,124.000014 L1923.27035,121.575721 C1922.90988,121.215448 1922.90988,120.630707 1923.27035,120.270435 C1923.63085,119.909855 1924.21529,119.909855 1924.57579,120.270435 Z M1927,134 C1921.4875,134 1917,129.5125 1917,124 C1917,118.4875 1921.4875,114 1927,114 C1932.5125,114 1937,118.4875 1937,124 C1937,129.5125 1932.5125,134 1927,134 Z M1927,132 C1931.40809,132 1935,128.40809 1935,124 C1935,119.59191 1931.40809,116 1927,116 C1922.59191,116 1919,119.59191 1919,124 C1919,128.40809 1922.59191,132 1927,132 Z"
              id="Shape"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RocketIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon style={props.style}>
      <svg
        width="20px"
        height="20px"
        viewBox="0 0 20 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="rocket-icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="Release-3"
            transform="translate(-1360.000000, -115.000000)"
            fill="#6239EB"
            fillRule="nonzero"
          >
            <g transform="translate(1330.000000, 79.000000)">
              <path d="M36.0773295,50.1403713 C37.3684382,51.452591 37.5178823,52.8631438 36.086774,54.3175855 C35.200109,55.2181393 33.7645563,55.7592493 31.7890045,55.9909156 C31.6867825,56.0031377 31.5834494,56.0025822 31.4806718,55.99036 C30.7862287,55.9059157 30.2790074,55.2886947 30.3123407,54.583696 L30.3195629,54.491474 L30.3478962,54.2581411 C30.590118,52.3748115 31.1145614,50.9959252 31.9662264,50.1303714 C33.3973348,48.6764853 34.7856654,48.8287072 36.0773295,50.1403713 L36.0773295,50.1403713 Z M47.9056399,36.3553981 L48.1289728,36.4237313 L48.3473057,36.4965089 C48.8918416,36.687436 49.3182244,37.1183264 49.5034146,37.66484 C50.3739685,40.2081684 50.0973023,42.78483 48.6939717,45.3392695 C48.1156395,46.3914897 47.375641,47.3875989 46.4750872,48.3270415 L46.2256432,48.582041 L45.9895326,48.8131517 L45.9828659,48.8848182 C45.8189773,50.4148153 44.658424,52.4381447 42.5200948,55.0542507 L42.3345396,55.2803614 L41.9928736,55.6886939 C41.6062077,56.1475819 40.8862091,56.0198044 40.660654,55.4775832 L40.6339874,55.4048056 L39.4384341,51.5892574 L39.330101,51.5142576 C38.6583221,51.0356869 38.0184486,50.5138411 37.4145492,49.9520384 L37.1173275,49.6687056 L36.826217,49.3798173 C35.9663568,48.5048188 35.1924413,47.5493309 34.5151104,46.5264895 L34.4489994,46.4237119 L30.5556736,45.1648254 C30.0078969,44.987048 29.8312305,44.3098271 30.1790076,43.8842724 L30.2251187,43.8320502 L30.2751186,43.7853837 C33.4773346,40.9876113 35.8401078,39.6003918 37.5323267,39.7159471 L37.6412154,39.7253915 L37.7045486,39.7331693 L37.8473261,39.5887251 C38.6151024,38.8253933 39.4189897,38.1759501 40.2595437,37.6420622 L40.5406542,37.4681737 L40.798987,37.3170629 C43.144538,35.9870654 45.5256445,35.6598439 47.9056399,36.3559536 L47.9056399,36.3553981 Z M40.7284316,41.7281654 C39.7545446,42.702608 39.757878,44.2853827 40.7362094,45.2637141 C41.7145408,46.2420456 43.2973156,46.2453789 44.2717581,45.2714919 C45.2456451,44.2970493 45.2423118,42.7142746 44.2639803,41.7359432 C43.2856489,40.7576118 41.7028742,40.7542784 40.7284316,41.7281654 Z" />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const BackIcon: React.FC<Props> = (props) => {
  return (
    <SvgIcon style={props.style}>
      <svg
        width="28px"
        height="28px"
        viewBox="0 0 34 34"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <g id="back-icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Release-2" transform="translate(-1353.000000, -112.000000)">
            <g transform="translate(1329.000000, 80.000000)">
              <g transform="translate(24.000000, 32.000000)">
                <g>
                  <circle fill="#FFFFFF" cx="14.4242424" cy="14.4242424" r="13.5757576" />
                  <circle fill="#6239EB" cx="14" cy="14" r="14" />
                </g>
                <g transform="translate(2.000000, 2.000000)" fill="#FFFFFF">
                  <path
                    d="M17.5859415,8.28632034 C18.0252814,8.72566017 18.0252814,9.43797077 17.5859415,9.8773106 L11.7523106,15.7109415 L11.7523106,15.7109415 C11.5640221,15.89923 11.3255961,16.0068235 11.0800076,16.0337218 L10.9568155,16.0404464 C10.6689029,16.0404464 10.3809903,15.9306115 10.1613203,15.7109415 L4.3276894,9.8773106 C3.88834957,9.43797077 3.88834957,8.72566017 4.3276894,8.28632034 C4.76702923,7.84698052 5.47933983,7.84698052 5.91867966,8.28632034 L10.9561845,13.3238155 L15.9949513,8.28632034 C16.4342911,7.84698052 17.1466017,7.84698052 17.5859415,8.28632034 Z"
                    transform="translate(10.956815, 11.998631) scale(1, -1) rotate(90.000000) translate(-10.956815, -11.998631) "
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const LayersIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon style={props.style} className={props.className || ''}>
      <svg
        width="20px"
        height="20px"
        viewBox="0 0 20 20"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>obj-layers</title>
        <g id="chart" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1" transform="translate(-1371.000000, -610.000000)" fillRule="nonzero">
            <g id="layers-2" transform="translate(1371.000000, 608.000000)">
              <g id="obj-layers" transform="translate(0.000000, 2.000000)">
                <polygon
                  id="layers-polygon"
                  fillOpacity="0.01"
                  fill="#FFFFFF"
                  points="0 0 20 0 20 20 0 20"
                />
                <path
                  d="M17.5470833,11.6558333 C18.1537911,11.3264185 18.9126685,11.5512089 19.2420833,12.1579167 C19.5714981,12.7646245 19.3467078,13.5235018 18.74,13.8529167 L12.2775,17.36875 C11.6134951,17.7305179 10.8694935,17.9203142 10.1133333,17.9208333 C9.37041667,17.9208333 8.6275,17.7366667 7.95,17.3691667 L1.48666667,13.8533333 C0.879958857,13.5239185 0.655168492,12.7650411 0.984583333,12.1583333 C1.31399817,11.5516255 2.07287552,11.3268352 2.67958333,11.65625 L9.14291667,15.1720833 C9.74803942,15.502221 10.4794606,15.502221 11.0845833,15.1720833 L17.5470833,11.6558333 Z M9.35416667,1.85166667 C9.83055244,1.60454107 10.3973642,1.60454107 10.87375,1.85166667 L18.5045833,5.81125 C19.6745833,6.41833333 19.6954167,8.08375 18.5408333,8.71958333 L10.9095833,12.9241667 C10.4139898,13.1971343 9.81309358,13.1971343 9.3175,12.9241667 L1.68666667,8.72 C0.5325,8.08375 0.553333333,6.41791667 1.72291667,5.81166667 L9.35416667,1.85166667 Z M13.8529167,8.275 C13.6881402,7.97171782 13.308715,7.85941988 13.0054167,8.02416667 L10.4858333,9.3925 C10.2538455,9.51876449 9.97365453,9.51876449 9.74166667,9.3925 L7.22208333,8.02375 C6.9186719,7.85910012 6.53923324,7.97158858 6.37458335,8.27500001 C6.20993347,8.57841144 6.32242191,8.9578501 6.62583333,9.1225 L9.145,10.4908333 C9.44245671,10.6525234 9.77560482,10.7373143 10.1141667,10.7375 C10.4466667,10.7375 10.7791667,10.6554167 11.0825,10.4904167 L13.6020833,9.12291667 C13.7478366,9.04378747 13.8561632,8.90997213 13.9032094,8.75093724 C13.9502556,8.59190236 13.9321634,8.42068938 13.8529167,8.275 L13.8529167,8.275 Z"
                  id="layers-path"
                  fill="currentColor"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CashOutIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 44 44'}
    >
      <title>{props.title || ''}</title>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        height="44px"
        viewBox="0 -960 960 960"
        width="44px"
        fill="#e8eaed"
      >
        <path d="M440-280h80v-40h40q17 0 28.5-11.5T600-360v-120q0-17-11.5-28.5T560-520H440v-40h160v-80h-80v-40h-80v40h-40q-17 0-28.5 11.5T360-600v120q0 17 11.5 28.5T400-440h120v40H360v80h80v40ZM160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h640v-480H160v480Zm0 0v-480 480Z" />
      </svg>
    </SvgIcon>
  );
};

export const BalanceIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon style={props.style} className={props.className || ''} viewBox="0 0 32 32">
      <svg
        width="32px"
        height="32px"
        viewBox="0 0 32 32"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Balance</title>
        <g id="Balance" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="balance1" transform="translate(-1419.000000, -332.000000)">
            <g id="balance-6" transform="translate(1419.000000, 332.000000)">
              <path
                d="M16.4860918,0.639720885 C17.2111557,0.337610934 17.9533132,0.376347814 18.7037133,0.637598867 L18.841,0.689 L18.9679202,0.731964271 C19.191404,0.815429224 19.402739,0.920380229 19.5994121,1.0490689 L19.7911047,1.18575352 L19.958,1.327 L20.1661443,1.25588722 L20.2963334,1.21850436 L20.42751,1.18587401 L20.6926366,1.13507372 C21.0968539,1.07288644 21.4896275,1.107232 21.8769667,1.21276999 L22.0702113,1.27135438 L22.1805371,1.31176759 C22.8131197,1.56480062 23.3353179,2.00963611 23.6342231,2.6501472 C23.8766004,3.16952708 23.9503429,3.74103157 23.844505,4.30550006 C23.7621188,4.7448935 23.5835648,5.15301694 23.327681,5.52521147 C23.1122201,5.83860915 22.8488121,6.10991408 22.5482162,6.32836482 L22.3634821,6.45300668 C22.1782091,6.57002121 22.0692886,6.66204023 22.0321279,6.70849117 C21.8655669,6.91669246 21.6977483,7.13905201 21.5288187,7.37555352 L21.3515446,7.63050306 C21.3196677,7.67739466 21.2866395,7.72647082 21.2524811,7.77770835 L21.0968859,8.02416915 L21.0312005,8.1381469 C20.9576686,8.27295536 20.8809121,8.40507138 20.800934,8.53445342 C20.8677859,8.61953279 20.9423548,8.71009688 21.0264953,8.80801055 L21.3094867,9.1256652 C21.362056,9.18290407 21.417482,9.24244655 21.4759964,9.30452576 C21.6254675,9.47392637 21.7995735,9.66063862 21.9979396,9.86401479 L22.4269078,10.2927938 C22.8026653,10.6600113 23.2456702,11.0552412 23.7970994,11.5125775 C24.3166055,11.9671453 24.8432008,12.4937406 25.3789432,13.0925115 C25.9844721,13.7692791 26.5055357,14.5479472 26.9434257,15.4237272 C27.4016084,16.3400926 27.7135587,17.370117 27.8859784,18.5080872 C28.061381,19.6657444 28.0329673,20.9216301 27.8057842,22.3059419 C27.592346,23.4917097 27.1705988,24.6143888 26.5457579,25.6636501 C25.9074665,26.7354978 25.0617397,27.6651975 24.0215133,28.4453673 C23.0026313,29.2095288 21.8131761,29.807102 20.4618329,30.2426589 C19.1055355,30.6798126 17.5959024,30.893906 15.9361471,30.893906 C14.2119598,30.893906 12.6513873,30.6430997 11.2607539,30.1301611 C9.89253157,29.625489 8.70729368,28.9555719 7.71541376,28.1153913 C6.73149228,27.2819519 5.93744034,26.3349714 5.34392379,25.278512 C4.76446837,24.2470813 4.37379256,23.1993599 4.18158993,22.1422274 C3.97250901,21.0038979 3.94299812,19.8942882 4.09809773,18.8205217 C4.2461442,17.7955846 4.51622448,16.8244449 4.90908961,15.9116112 C5.29739996,15.0093606 5.78567823,14.1764154 6.37281161,13.4165957 C6.84265539,12.8085626 7.34972708,12.2514201 7.88490746,11.7540282 L8.20931578,11.4628339 C8.6788973,11.0474349 9.10078564,10.6569637 9.47463065,10.2920198 C9.82425941,9.95071554 10.1078099,9.64691143 10.3770389,9.32263124 C10.5942032,9.07991822 10.7901712,8.84922766 10.9648224,8.63104574 L11.096925,8.46473349 L10.7622906,7.92798679 L10.3635162,7.34071821 L10.2422597,7.15176597 C10.1397911,6.998063 10.0299436,6.868687 9.91207892,6.76025154 L9.69275163,6.56487307 L9.50513282,6.40810533 C9.47783618,6.38613875 9.45285883,6.36648879 9.43069982,6.3495094 C8.81991701,5.92665976 8.38223466,5.33092546 8.15356786,4.62205837 C7.79375922,3.50665159 8.17205073,2.39279327 9.15057605,1.62660981 C9.69016605,1.24118838 10.3354465,1.07986827 10.9891463,1.15250158 C11.1859178,1.17436508 11.3838937,1.20952352 11.5833163,1.25732386 L11.8835656,1.33842418 L11.968,1.364 L12.0420335,1.30692482 L12.210294,1.18358818 L12.3937879,1.05664645 L12.593733,0.924817732 C13.2525335,0.519402031 14.0093472,0.375868404 14.7757875,0.4956247 C15.0828097,0.543596923 15.3744905,0.623235036 15.646344,0.739439342 L15.8464465,0.833525301 L15.97,0.903 L16.0424475,0.85899836 L16.1512063,0.798698024 L16.2613861,0.742023209 L16.4860918,0.639720885 Z M19.1227629,9.6319516 L12.7210793,9.6319516 L12.6088142,9.7767362 C12.3892303,10.056141 12.1421327,10.3493025 11.8675215,10.656221 C11.6019702,10.9786761 11.2700311,11.3343252 10.8717041,11.7231682 C10.4733771,12.1120112 10.0276303,12.5245641 9.53446354,12.960827 C8.96542501,13.4539938 8.43906436,14.0135483 7.95538161,14.6394907 C7.47169886,15.2654331 7.06862989,15.9530213 6.74617472,16.7022554 C6.42371955,17.4514895 6.20084613,18.2528854 6.07755445,19.1064432 C5.95426276,19.960001 5.9779727,20.8514947 6.14868426,21.7809243 C6.30042787,22.6155142 6.61339907,23.454846 7.08759784,24.2989198 C7.56179662,25.1429937 8.20196497,25.9064537 9.0081029,26.5892999 C9.81424082,27.2721462 10.7958323,27.8269587 11.9528773,28.2537376 C12.9942178,28.6378387 14.1738347,28.8490942 15.491728,28.8875043 L15.9361471,28.893906 L16.4171954,28.8870565 C17.6845355,28.8505256 18.8282327,28.6678712 19.848287,28.3390934 C20.995848,27.9692184 21.9869235,27.4713097 22.8215133,26.8453673 C23.6561032,26.2194249 24.3247235,25.4844168 24.8273742,24.640343 C25.3300249,23.7962691 25.666706,22.9000334 25.8374176,21.9516359 C26.0270971,20.7945909 26.050807,19.7466116 25.9085474,18.807698 C25.7662877,17.8687844 25.5149624,17.0389365 25.1545713,16.3181544 C24.7941802,15.5973722 24.3721433,14.9666879 23.8884606,14.4261013 C23.4047778,13.8855146 22.935321,13.4160579 22.4800902,13.0177309 C21.9300196,12.5625001 21.4463369,12.1309792 21.0290419,11.7231682 C20.611747,11.3153573 20.2608399,10.9502242 19.9763207,10.627769 C19.6348975,10.267378 19.3503783,9.93543882 19.1227629,9.6319516 Z M10.7682833,3.14026905 C10.5975717,3.12130109 10.4458281,3.159237 10.3130524,3.25407675 C10.0475011,3.46272422 9.96214534,3.71404957 10.0569851,4.00805281 C10.1360182,4.25305551 10.2677401,4.4552486 10.4521507,4.61463208 L10.5691198,4.70512502 C10.7967352,4.87583658 11.0290926,5.07025808 11.266192,5.28838951 C11.5032914,5.50652095 11.7166808,5.75784631 11.9063603,6.04236557 L12.0075227,6.20043183 L12.439834,6.83901952 L12.816822,7.44362296 L13.0159855,7.7779331 L18.9055343,7.7779331 C18.9940514,7.6514801 19.0783534,7.52081199 19.1584403,7.38592878 L19.2754094,7.18044264 C19.3702491,7.00973108 19.4745728,6.83901952 19.5883806,6.66830796 L19.8033507,6.35217544 C19.8370715,6.30370179 19.8697385,6.25733569 19.9013517,6.21307713 C20.0910313,5.94752582 20.2807108,5.69620046 20.4703903,5.45910107 C20.6600698,5.22200169 20.9351051,4.98964428 21.2954962,4.76202887 C21.4472398,4.66718911 21.5752734,4.54389743 21.6795972,4.39215382 C21.7839209,4.24041021 21.8503087,4.0886666 21.8787607,3.93692299 C21.9072126,3.78517939 21.8882446,3.63817776 21.8218568,3.49591813 C21.755469,3.3536585 21.6274353,3.24459278 21.4377558,3.16872097 C21.3524,3.14026905 21.2729717,3.12130109 21.1994709,3.11181712 L21.0936653,3.10470414 L20.9967509,3.11181712 C20.8734592,3.13078507 20.7501676,3.16397899 20.6268759,3.21139886 L20.2427749,3.36788446 C20.1099992,3.42478831 19.9487716,3.45324024 19.7590921,3.45324024 C19.5694126,3.45324024 19.412927,3.42004633 19.2896353,3.3536585 C19.1663436,3.28727067 19.0525359,3.20665688 18.9482122,3.11181712 L18.621015,2.81307189 C18.5356593,2.73482909 18.4289645,2.66992313 18.3009309,2.61835402 L18.1657842,2.57123051 C18.0032018,2.50619754 17.854555,2.46380997 17.7198438,2.44406782 L17.5897779,2.43187414 C17.4643571,2.42722892 17.352872,2.44522912 17.2553226,2.48587473 C17.0277071,2.58071449 16.8000917,2.7324581 16.5724763,2.94110556 C16.3827968,3.13078507 16.2310532,3.26356073 16.1172455,3.33943253 C16.0603416,3.37736844 16.0129218,3.39633639 15.9749859,3.39633639 C15.8611781,3.33943253 15.7568544,3.2730447 15.6620147,3.1971729 C15.5671749,3.14026905 15.4770771,3.0786232 15.3917214,3.01223538 C15.3063656,2.94584755 15.2257518,2.86523375 15.14988,2.770394 C15.023427,2.64394099 14.8442852,2.5537115 14.6124547,2.49970553 L14.4670337,2.47164877 L14.3176611,2.45623731 C14.0726584,2.44438234 13.847414,2.50168136 13.6419279,2.62813436 C13.3194727,2.83678183 13.0918573,3.01223538 12.9590816,3.15449501 C12.826306,3.29675464 12.6271425,3.39633639 12.3615912,3.45324024 C12.3236553,3.46272422 12.2848302,3.46924445 12.2451161,3.47280094 L12.1233063,3.47457919 C12.0403215,3.4698372 11.9537802,3.45324024 11.8636824,3.42478831 L11.3088699,3.25407675 C11.1191904,3.1971729 10.9389948,3.159237 10.7682833,3.14026905 Z"
                id="shape-1backup"
                fill="#FF7D00"
                fillRule="nonzero"
              />
              <path
                d="M15.2397437,14.3084457 L15.2362711,14.3825269 L15.2362711,15.1540128 L15.1766589,15.1540128 L15.0846362,15.1557491 C13.6799877,15.2020498 12.5352021,16.2935896 12.5352021,17.662355 C12.5352021,19.0207027 13.6632037,20.1064549 15.0533832,20.1678034 L15.1766589,20.1701184 L16.8388552,20.1701184 L16.9094638,20.1724335 C17.4940106,20.204844 17.9373401,20.6354408 17.9373401,21.1349099 C17.9373401,21.6285915 17.5044282,22.055137 16.9302991,22.0962289 L16.8388552,22.0997014 L13.7228159,22.0997014 L13.66494,22.1014377 C13.2642018,22.1297574 12.9522688,22.4608708 12.9478843,22.8625845 C12.9434997,23.2642982 13.2481313,23.6021412 13.648156,23.6392008 L13.7228159,23.6426733 L15.2356923,23.6426733 L15.2362711,24.4147381 L15.2385861,24.472614 C15.2670754,24.8733255 15.5982993,25.185112 16,25.1893452 C16.4017007,25.1935784 16.7394217,24.8888414 16.7763492,24.4888192 L16.7798218,24.4147381 L16.7798218,23.6426733 L16.8388552,23.6426733 L16.9314567,23.6415158 C18.3361051,23.5952151 19.480312,22.5036753 19.480312,21.1349099 C19.480312,19.7765622 18.3528891,18.69081 16.9627097,18.6294616 L16.8388552,18.6271465 L15.1766589,18.6271465 L15.1060503,18.6248315 C14.5215035,18.592421 14.0787528,18.1618242 14.0787528,17.662355 C14.0787528,17.1686734 14.5116646,16.7421279 15.085215,16.701036 L15.1766589,16.6975635 L18.2932769,16.6975635 L18.3511529,16.6958272 C18.7518644,16.667338 19.0636509,16.336114 19.0678841,15.9344134 C19.0721173,15.5327127 18.7673803,15.1949917 18.3673581,15.1580641 L18.2932769,15.1545916 L16.779243,15.1540128 L16.779243,14.3825269 L16.7775067,14.324651 C16.7472144,13.9219917 16.4115541,13.6108119 16.007757,13.6110405 C15.606677,13.6110405 15.277363,13.9172045 15.2397437,14.3084457 Z"
                fill="#FF7D00"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const TrolleyIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon style={props.style} className={props.className || ''}>
      <svg
        width="20px"
        height="20px"
        viewBox="0 0 32 32"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>trollery</title>
        <g id="trollery" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="2" transform="translate(-1772.000000, -216.000000)">
            <g id="trollery-1" transform="translate(1743.000000, 185.000000)">
              <g id="trollery-2" transform="translate(29.000000, 31.000000)">
                <polygon id="trollery-3" points="0 0 32 0 32 32 0 32" />
                <polygon
                  id="trollery-path"
                  points="26.7654321 23.1111111 8.79012346 23.1111111 5.******** 9.48148148 30.2222222 9.48148148"
                />
                <path
                  d="M10.3703704,24.8888889 C12.1704096,24.8888889 13.6296296,26.348109 13.6296296,28.1481481 C13.6296296,29.9481873 12.1704096,31.4074074 10.3703704,31.4074074 C8.57033119,31.4074074 7.11111111,29.9481873 7.11111111,28.1481481 C7.11111111,26.348109 8.57033119,24.8888889 10.3703704,24.8888889 Z M25.1851852,24.8888889 C26.9852244,24.8888889 28.4444444,26.348109 28.4444444,28.1481481 C28.4444444,29.9481873 26.9852244,31.4074074 25.1851852,31.4074074 C23.385146,31.4074074 21.9259259,29.9481873 21.9259259,28.1481481 C21.9259259,26.348109 23.385146,24.8888889 25.1851852,24.8888889 Z M4.20596206,4.14814815 C4.71638413,4.14814815 5.16552019,4.47422142 5.32776169,4.95084197 L5.35476485,5.0419297 L5.95590284,7.41215897 C5.98691819,7.40736159 6.01833074,7.40376672 6.05009136,7.40142351 L6.13854312,7.39817271 L29.3428102,7.39817271 C29.5790208,7.39817271 29.8128632,7.44524557 30.0306757,7.53664122 C30.9037067,7.90297107 31.3309788,8.88487263 31.019754,9.76632642 L30.9821195,9.86381599 L27.8700451,17.2804581 C27.6083721,17.9040728 27.0174652,18.3219235 26.3493826,18.3664266 L26.2307359,18.3703704 L8.73459259,18.3701481 L9.63674074,21.9253333 L27.4044503,21.9259259 C28.0292573,21.9259259 28.5411407,22.4094091 28.5863847,23.0226594 L28.5896355,23.1111111 C28.5896355,23.7359181 28.1061523,24.2478016 27.4929021,24.2930455 L27.4044503,24.2962963 L8.71544715,24.2962963 C8.20502508,24.2962963 7.75588903,23.970223 7.59364752,23.4936025 L7.56664437,23.4025147 L3.28355556,6.51851852 L1.77777778,6.51851852 C1.15297079,6.51851852 0.641087335,6.03503538 0.595843399,5.42178509 L0.592592593,5.******** C0.592592593,4.70852634 1.07607573,4.19664289 1.68932602,4.15139895 L1.77777778,4.14814815 L4.20596206,4.14814815 Z M8.13359259,15.9991481 L25.837037,15.9994074 L28.450963,9.7682963 L6.55359259,9.76814815 L8.13359259,15.9991481 Z M5.92592593,9.48148148 L28.5925926,9.48148148 L25.7843302,17.0877447 L6.31416921,17.6638897 L5.92592593,9.48148148 Z"
                  id="trollery-5"
                  fill="currentColor"
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const BellIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon style={props.style} className={props.className || ''}>
      <svg
        width="15px"
        height="18px"
        viewBox="0 0 15 18"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Bell</title>
        <g id="bell3" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1" transform="translate(-1282.000000, -129.000000)" fill="currentColor">
            <path
              d="M1290.65651,131.165419 C1292.96196,131.753935 1294.66667,133.844557 1294.66667,136.333333 L1294.66667,142.333333 L1296.66667,143 L1296.66667,144.333333 L1282,144.333333 L1282,143 L1284,142.333333 L1284,136.333333 C1284,133.844557 1285.7047,131.753935 1288.01016,131.165419 C1288.00345,131.11122 1288,131.056013 1288,131 L1288,130.333333 C1288,129.596954 1288.59695,129 1289.33333,129 C1290.06971,129 1290.66667,129.596954 1290.66667,130.333333 L1290.66667,131 C1290.66667,131.056013 1290.66321,131.11122 1290.65651,131.165419 L1290.65651,131.165419 Z M1291.33333,145 C1291.33333,146.1 1290.43333,147 1289.33333,147 C1288.23333,147 1287.33333,146.1 1287.33333,145 L1291.33333,145 Z"
              id="bell"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ViewIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon style={props.style} className={props.className || ''}>
      <svg viewBox="0 0 18 16" version="1.1">
        <title>View Icon</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-1-1-Subscribers" transform="translate(-429.000000, -666.000000)">
            <g id="Product-Options" transform="translate(360.000000, 288.000000)">
              <g id="Group-10" transform="translate(45.000000, 246.000000)">
                <g id="row" transform="translate(0.500000, 116.000000)">
                  <g id="eye-line" transform="translate(24.500000, 16.000000)">
                    <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                    <path
                      d="M8,2 C11.5946667,2 14.5853333,4.58666667 15.2126667,8 C14.586,11.4133333 11.5946667,14 8,14 C4.40533333,14 1.41466667,11.4133333 0.787333333,8 C1.414,4.58666667 4.40533333,2 8,2 Z M8,12.6666667 C10.8000826,12.6660597 13.2278836,10.7297936 13.8513333,8 C13.2256107,5.27246562 10.7983875,3.33901883 8,3.33901883 C5.2016125,3.33901883 2.7743893,5.27246562 2.14866667,8 C2.77211643,10.7297936 5.19991743,12.6660597 8,12.6666667 Z M8,11 C6.34314575,11 5,9.65685425 5,8 C5,6.34314575 6.34314575,5 8,5 C9.65685425,5 11,6.34314575 11,8 C11,9.65685425 9.65685425,11 8,11 Z M8,9.******** C8.92047458,9.******** 9.********,8.92047458 9.********,8 C9.********,7.07952542 8.92047458,6.******** 8,6.******** C7.07952542,6.******** 6.********,7.07952542 6.********,8 C6.********,8.92047458 7.07952542,9.******** 8,9.******** L8,9.******** Z"
                      id="Shape"
                      fill="#6239EB"
                      fillRule="nonzero"
                    />
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ActivateIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon {...props} viewBox="-5 -4 23 23">
      <g id="Data-Flow-Manager" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="1.2-Run-Now" transform="translate(-623.000000, -1037.000000)" fill="#6239EB">
          <path
            d="M630,1051 C626.14125,1051 623,1047.85875 623,1044 C623,1040.14125 626.14125,1037 630,1037 C633.85875,1037 637,1040.14125 637,1044 C637,1047.85875 633.85875,1051 630,1051 Z M630,1049.6 C633.085663,1049.6 635.6,1047.08566 635.6,1044 C635.6,1040.91434 633.085663,1038.4 630,1038.4 C626.914337,1038.4 624.4,1040.91434 624.4,1044 C624.4,1047.08566 626.914337,1049.6 630,1049.6 Z M628.984863,1045.26606 L632.391768,1041.41595 L632.394817,1041.41272 C632.655362,1041.1387 633.088715,1041.12777 633.363983,1041.3895 C633.638745,1041.65206 633.648639,1042.08764 633.386082,1042.3624 L629.481327,1046.61137 C629.195774,1046.90888 628.723111,1046.91857 628.425602,1046.63302 L626.724765,1045.0568 C626.441997,1044.78539 626.423664,1044.33925 626.683211,1044.04556 C626.693,1044.03465 626.693,1044.03465 626.70311,1044.02403 C626.954668,1043.76383 627.369535,1043.75682 627.629741,1044.00837 L628.984863,1045.26606 Z"
            id="Shape-2"
          />
        </g>
      </g>
    </SvgIcon>
  );
};

export const Cancel2Icon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon {...props} viewBox="-5 -4 23 23">
      <g id="Data-Flow-Manager" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="1.2-Run-Now"
          transform="translate(-622.000000, -993.000000)"
          fill="#6239EB"
          fillRule="nonzero"
        >
          <path
            d="M626.310641,993.8982 C626.525064,994.026889 626.654481,994.260091 626.650141,994.50996 C626.645801,994.759829 626.508365,994.988404 626.289602,995.109584 C624.505949,996.094611 623.399105,997.97033 623.400002,1000.00646 C623.400002,1003.09656 625.907052,1005.60129 629.000001,1005.60129 C632.092952,1005.60129 634.600001,1003.09656 634.600001,1000.00646 C634.600001,998.014351 633.550001,996.204772 631.869302,995.20085 C631.537503,995.002611 631.429379,994.573179 631.627802,994.241686 C631.826224,993.910193 632.256053,993.80217 632.587851,994.000409 C634.704938,995.261997 636.001193,997.543659 636.000001,1000.00646 C636.000001,1003.86899 632.866101,1007 629.000001,1007 C625.133902,1007 622.000001,1003.86899 622.000001,1000.00646 C621.998911,997.461859 623.381836,995.117635 625.610602,993.886064 C625.829365,993.764885 626.096218,993.769511 626.310641,993.8982 Z M629.050003,993 C629.550003,993 629.800003,993.236953 629.800003,993.710859 L629.800003,1000.81945 C629.800003,1001.25387 629.589933,1001.48917 629.169794,1001.52538 L629.050003,1001.53031 C628.550003,1001.53031 628.300003,1001.29336 628.300003,1000.81945 L628.300003,993.710859 C628.300003,993.276445 628.510072,993.041138 628.930211,993.004937 L629.050003,993 Z"
            id="abadsfds"
          />
        </g>
      </g>
    </SvgIcon>
  );
};

export const SalesforceSettingIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 120 80'}
    >
      <svg
        width="120px"
        height="80px"
        viewBox="0 0 120 80"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>salesforca-seeklogo.com</title>
        <g id="overview" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.1-new1" transform="translate(-1176.000000, -416.000000)" fillRule="nonzero">
            <g id="salesforca-seeklogo.com" transform="translate(1176.000000, 416.000000)">
              <path
                d="M46.1914956,69.4929583 C49.7713246,73.2377074 54.7552561,75.5601185 60.2673074,75.5601185 C67.5945727,75.5601185 73.9873501,71.4581976 77.3916666,65.368813 C80.3500855,66.695905 83.6247435,67.4340604 87.0701707,67.4340604 C100.285812,67.4340604 111,56.5839711 111,43.2003418 C111,29.8151253 100.285812,18.9650358 87.0701707,18.9650358 C85.4573503,18.9650358 83.8808972,19.1269537 82.3566237,19.4365027 C79.3586749,14.0678118 73.6458119,10.4405324 67.0885896,10.4405324 C64.3436326,10.4405324 61.7473074,11.0770921 59.4355982,12.2089303 C56.3965383,5.03215564 49.3159401,0 41.0636751,0 C32.4698718,0 25.1457692,5.45917454 22.3344017,13.1153533 C21.105812,12.8534272 19.8329487,12.7169081 18.5268803,12.7169081 C8.29495725,12.7169081 0,21.1302914 0,31.5105014 C0,38.4666231 3.72688034,44.5401332 9.26423075,47.7896044 C8.12418802,50.4231518 7.4901282,53.3297377 7.4901282,56.385542 C7.4901282,68.3230217 17.1433333,78 29.0497435,78 C36.0402136,78 42.252735,74.6632203 46.1914956,69.4929583"
                id="path"
                fill="#00A1E0"
                transform="translate(55.500000, 39.000000) scale(-1, 1) rotate(-180.000000) translate(-55.500000, -39.000000) "
              />
              <path
                d="M16.0260093,32.4817275 C15.9538762,32.2906977 16.0522395,32.2508305 16.075191,32.217608 C16.2915904,32.0581395 16.5112686,31.9435216 16.7325862,31.8156146 C17.9063891,31.1843854 19.0146164,31 20.1736648,31 C22.5343858,31 24,32.2724252 24,34.320598 L24,34.3604651 C24,36.2541528 22.345856,36.9418604 20.7933541,37.4385382 L20.5917092,37.5049834 C19.4211851,37.8903654 18.4113211,38.2225914 18.4113211,39.0033222 L18.4113211,39.0448505 C18.4113211,39.7126246 19.0015013,40.2043189 19.9162807,40.2043189 C20.9327022,40.2043189 22.1392929,39.8621262 22.9163635,39.4269103 C22.9163635,39.4269103 23.1442387,39.2774086 23.2278475,39.5016611 C23.2737505,39.6212624 23.6672039,40.6943521 23.7081887,40.8106312 C23.7524522,40.9368771 23.6737615,41.0299003 23.5934314,41.0797342 C22.7065217,41.6262458 21.4802583,42 20.2113708,42 L19.9752987,41.9983389 C17.8145833,41.9983389 16.3063449,40.6760797 16.3063449,38.7807309 L16.3063449,38.7408638 C16.3063449,36.7425249 17.9703253,36.0946844 19.5293847,35.6428571 L19.7802114,35.564784 C20.9163083,35.2109634 21.8950238,34.9069768 21.8950238,34.0963455 L21.8950238,34.0564784 C21.8950238,33.3156146 21.2589407,32.7641196 20.2326829,32.7641196 C19.8343112,32.7641196 18.5637843,32.7724252 17.1916153,33.6511628 C17.0260369,33.7491694 16.9293129,33.820598 16.8014405,33.8986711 C16.7342256,33.9418605 16.5653685,34.0166113 16.4915959,33.7906977 L16.0260093,32.4817275 L16.0260093,32.4817275 Z"
                id="path"
                fill="#FFFFFF"
                transform="translate(20.000000, 36.500000) scale(-1, 1) rotate(-180.000000) translate(-20.000000, -36.500000) "
              />
              <path
                d="M49.0260092,32.4817275 C48.9538763,32.2906977 49.0522394,32.2508305 49.0751912,32.217608 C49.2915906,32.0581395 49.5112687,31.9435216 49.7325861,31.8156146 C50.906389,31.1843854 52.0146163,31 53.173665,31 C55.5343856,31 57,32.2724252 57,34.320598 L57,34.3604651 C57,36.2541528 55.3458562,36.9418604 53.7933542,37.4385382 L53.5917091,37.5049834 C52.4211849,37.8903654 51.4113211,38.2225914 51.4113211,39.0033222 L51.4113211,39.0448505 C51.4113211,39.7126246 52.0015015,40.2043189 52.9162809,40.2043189 C53.9327021,40.2043189 55.139293,39.8621262 55.9163634,39.4269103 C55.9163634,39.4269103 56.1442387,39.2774086 56.2278476,39.5016611 C56.2737505,39.6212624 56.6672037,40.6943521 56.7081885,40.8106312 C56.752452,40.9368771 56.6737616,41.0299003 56.5934314,41.0797342 C55.7065215,41.6262458 54.4802583,42 53.2113706,42 L52.9752986,41.9983389 C50.8145833,41.9983389 49.3063448,40.6760797 49.3063448,38.7807309 L49.3063448,38.7408638 C49.3063448,36.7425249 50.9703252,36.0946844 52.5293846,35.6428571 L52.7802113,35.564784 C53.9163081,35.2109634 54.8966631,34.9069768 54.8966631,34.0963455 L54.8966631,34.0564784 C54.8966631,33.3156146 54.2589409,32.7641196 53.2326827,32.7641196 C52.8343113,32.7641196 51.5637843,32.7724252 50.1916154,33.6511628 C50.0260369,33.7491694 49.9276734,33.8172757 49.8030801,33.8986711 C49.760456,33.9269103 49.5604502,34.0049834 49.491596,33.7906977 L49.0260092,32.4817275 L49.0260092,32.4817275 Z"
                id="path"
                fill="#FFFFFF"
                transform="translate(53.000000, 36.500000) scale(-1, 1) rotate(-180.000000) translate(-53.000000, -36.500000) "
              />
              <path
                d="M71.8860501,36.9908814 C71.8860501,35.931611 71.6867655,35.0972645 71.294328,34.5075988 C70.9064897,33.9240122 70.3193663,33.6398177 69.5007665,33.6398177 C68.6806337,33.6398177 68.0965766,33.9224924 67.7148696,34.5075988 C67.3285642,35.0957447 67.1323454,35.931611 67.1323454,36.9908814 C67.1323454,38.0486322 67.3285642,38.881459 67.7148696,39.4650456 C68.0965766,40.0425532 68.6806337,40.3237082 69.5007665,40.3237082 C70.3193663,40.3237082 70.9064897,40.0425532 71.2958609,39.4650456 C71.6867655,38.881459 71.8860501,38.0486322 71.8860501,36.9908814 M73.7286662,38.9544073 C73.5477771,39.5607903 73.2657128,40.0957447 72.8901379,40.5410334 C72.5145629,40.9878419 72.0393457,41.3465045 71.4752171,41.6079027 C70.9126214,41.8677811 70.2473171,42 69.5007665,42 C68.7526829,42 68.0873786,41.8677811 67.5247829,41.6079027 C66.9606543,41.3465045 66.4854371,40.9878419 66.1083292,40.5410334 C65.7342872,40.0942249 65.4522229,39.5592705 65.2698009,38.9544073 C65.0904448,38.3510638 65,37.6914894 65,36.9908814 C65,36.2902736 65.0904448,35.6291793 65.2698009,35.0273556 C65.4522229,34.4224924 65.7327543,33.887538 66.1098621,33.4407295 C66.4854371,32.993921 66.9621872,32.6367781 67.5247829,32.3829787 C68.0889115,32.1291793 68.7526829,32 69.5007665,32 C70.2473171,32 70.9110885,32.1291793 71.4752171,32.3829787 C72.0378128,32.6367781 72.5145629,32.993921 72.8901379,33.4407295 C73.2657128,33.8860182 73.5477771,34.4209727 73.7286662,35.0273556 C73.9095552,35.6306991 74,36.2917933 74,36.9908814 C74,37.6899696 73.9095552,38.3510638 73.7286662,38.9544073"
                id="shape-1"
                fill="#FFFFFF"
                transform="translate(69.500000, 37.000000) scale(-1, 1) rotate(-180.000000) translate(-69.500000, -37.000000) "
              />
              <path
                d="M90.5060707,33.9170591 C90.4412094,34.0947896 90.2579762,34.0279508 90.2579762,34.0279508 C89.9742084,33.9261735 89.6726036,33.8319915 89.3515404,33.7849005 C89.0256122,33.7378095 88.6672537,33.7135045 88.2829508,33.7135045 C87.3392192,33.7135045 86.5900717,33.9763026 86.0533445,34.4958226 C85.5149961,35.0153426 85.2133909,35.8553851 85.2166344,36.9916452 C85.2198773,38.0261279 85.4858083,38.8038888 85.9641603,39.3963238 C86.4392689,39.9857208 87.1624724,40.2880146 88.1272835,40.2880146 C88.9315636,40.2880146 89.5445026,40.2014279 90.186629,40.0115449 C90.186629,40.0115449 90.3406744,39.9492632 90.4136435,40.1376272 C90.5839043,40.581194 90.7103838,40.8986784 90.8919956,41.386298 C90.9438844,41.5245329 90.8174049,41.5837764 90.7720021,41.6004861 C90.5190431,41.693149 89.9223195,41.8435364 89.4715334,41.9073371 C89.0499352,41.9680997 88.5569897,42 88.0089119,42 C87.1900383,42 86.4603488,41.8693605 85.8360593,41.6080814 C85.2133909,41.3483214 84.6847716,40.9898223 84.2664163,40.5432174 C83.8480615,40.0966125 83.5302413,39.5619019 83.3178206,38.9573143 C83.1070215,38.3542458 83,37.6919338 83,36.9916452 C83,35.4771381 83.4361922,34.2527723 84.2972256,33.3565244 C85.1598806,32.4572383 86.4554843,32 88.1451204,32 C89.1439842,32 90.1687921,32.189883 90.9049675,32.4617955 C90.9049675,32.4617955 91.0460409,32.5255962 90.9844227,32.6790217 L90.5060707,33.9170591 L90.5060707,33.9170591 Z"
                id="path"
                fill="#FFFFFF"
                transform="translate(87.000000, 37.000000) scale(-1, 1) rotate(-180.000000) translate(-87.000000, -37.000000) "
              />
              <path
                d="M93.273602,38.0066968 C93.3647378,38.5947517 93.5358175,39.0840377 93.7996322,39.4654377 C94.1977524,40.0443755 94.8053253,40.3619556 95.6591257,40.3619556 C96.5129257,40.3619556 97.077329,40.042856 97.4818445,39.4654377 C97.7504558,39.0840377 97.8671739,38.5734784 97.9135412,38.0066968 L93.273602,38.0066968 L93.273602,38.0066968 Z M99.7442546,39.2998098 C99.5811692,39.8848257 99.1766537,40.4759197 98.9112401,40.7463945 C98.4923345,41.1748997 98.083022,41.4742455 97.6769077,41.6413929 C97.1460805,41.857165 96.5097275,42 95.8126177,42 C95.0003885,42 94.2633063,41.8708407 93.6653268,41.6034048 C93.0657481,41.335969 92.5621022,40.9712838 92.1671797,40.5169468 C91.7722572,40.0641293 91.4748659,39.524699 91.2861985,38.9123318 C91.0959323,38.3030035 91,37.6389725 91,36.938473 C91,36.2258173 91.09913,35.5617863 91.295792,34.9646142 C91.4940525,34.3628836 91.8106303,33.8325705 92.2391289,33.3934287 C92.6660291,32.9512478 93.2160424,32.6047969 93.8747796,32.3631929 C94.5287198,32.1231085 95.3233613,31.9985077 96.2363198,32.0000135 C98.1149998,32.0061053 99.104704,32.40422 99.5124176,32.6184726 C99.5843669,32.6564606 99.6531189,32.7233196 99.5667792,32.9147793 L99.1414782,34.046823 C99.0775232,34.21549 98.8968501,34.1531896 98.8968501,34.1531896 C98.4315772,33.9890812 97.7696423,33.694294 96.2267267,33.6973331 C95.2178355,33.6988526 94.4695613,33.9814836 94.0010908,34.4236645 C93.5198291,34.8764819 93.2847939,35.5420325 93.2432232,36.4810969 L99.7490512,36.4750189 C99.7490512,36.4750189 99.9201308,36.4780579 99.9377185,36.6360881 C99.9441144,36.7029471 100.161561,37.9064084 99.7442546,39.2998098 L99.7442546,39.2998098 Z"
                id="shape-1"
                fill="#FFFFFF"
                transform="translate(95.500000, 37.000000) scale(-1, 1) rotate(-180.000000) translate(-95.500000, -37.000000) "
              />
              <path
                d="M41.2736568,38.0066968 C41.3663938,38.5947517 41.5358788,39.0840377 41.7996997,39.4654377 C42.1978295,40.0443755 42.8054171,40.3619556 43.6592377,40.3619556 C44.5130582,40.3619556 45.077475,40.042856 45.4835991,39.4654377 C45.750618,39.0840377 45.8673389,38.5734784 45.9137073,38.0066968 L41.2736568,38.0066968 L41.2736568,38.0066968 Z M47.7428659,39.2998098 C47.5797765,39.8848257 47.1768501,40.4759197 46.9114302,40.7463945 C46.4925146,41.1748997 46.0831922,41.4742455 45.6770681,41.6413929 C45.1462282,41.857165 44.5098604,42 43.8127333,42 C43.0020836,42 42.263385,41.8708407 41.6653909,41.6034048 C41.0657979,41.335969 40.5621397,40.9712838 40.1672078,40.5169468 C39.7722758,40.0641293 39.4748777,39.524699 39.2862057,38.9123318 C39.0975338,38.3030035 39,37.6389725 39,36.938473 C39,36.2258173 39.0991327,35.5617863 39.2957992,34.9646142 C39.4940646,34.3628836 39.8106498,33.8325705 40.2391589,33.3934287 C40.6660692,32.9512478 41.2160958,32.6047969 41.8748487,32.3631929 C42.5288049,32.1231085 43.3234657,31.9985077 44.236446,32.0000135 C46.1151707,32.0061053 47.1048987,32.40422 47.5126222,32.6184726 C47.5845731,32.6564606 47.6533268,32.7233196 47.5669854,32.9147793 L47.1432727,34.046823 C47.0777173,34.21549 46.8970398,34.1531896 46.8970398,34.1531896 C46.4317558,33.9890812 45.7714039,33.694294 44.2252534,33.6973331 C43.2179373,33.6988526 42.469645,33.9814836 42.001163,34.4236645 C41.5198896,34.8764819 41.2848492,35.5420325 41.2432774,36.4810969 L47.7492614,36.4750189 C47.7492614,36.4750189 47.9203456,36.4780579 47.9379333,36.6360881 C47.9443292,36.7029471 48.1617815,37.9064084 47.7428659,39.2998098 L47.7428659,39.2998098 Z"
                id="shape-1"
                fill="#FFFFFF"
                transform="translate(43.500000, 37.000000) scale(-1, 1) rotate(-180.000000) translate(-43.500000, -37.000000) "
              />
              <path
                d="M27.5464878,33.9553666 C27.3047615,34.1481707 27.2713151,34.1967512 27.1892194,34.3212388 C27.0675961,34.5110065 27.0052642,34.7812358 27.0052642,35.1243358 C27.0052642,35.6678306 27.1846585,36.057993 27.5571299,36.3206316 C27.552569,36.3191134 28.0892318,36.7836648 29.3510736,36.7669653 C30.2374033,36.7548201 31.0294751,36.6242599 31.0294751,36.6242599 L31.0294751,33.8156976 L31.0309954,33.8156976 C31.0309954,33.8156976 30.2450048,33.6471838 29.3601953,33.5940489 C28.1013941,33.5181418 27.541927,33.9568848 27.5464878,33.9553666 M30.0078394,38.295734 C29.7569913,38.3139517 29.431649,38.3245787 29.0424544,38.3245787 C28.5118728,38.3245787 27.9995346,38.2577805 27.5191226,38.1287384 C27.03567,37.9996964 26.6008667,37.7977835 26.226875,37.5305906 C25.8513631,37.2618795 25.5488251,36.9187794 25.3299032,36.5119174 C25.1109813,36.1050554 25,35.6253226 25,35.0879004 C25,34.5413694 25.0942581,34.066191 25.2827742,33.6775467 C25.4712903,33.2873842 25.7434224,32.9625019 26.0900488,32.7120085 C26.4336347,32.4615151 26.8577959,32.27782 27.3503703,32.1669956 C27.8353432,32.0561712 28.3856886,32 28.9877239,32 C29.6216854,32 30.2541265,32.0516168 30.8668039,32.1563686 C31.4734001,32.2596022 32.2183428,32.4098983 32.4251024,32.4569607 C32.6303418,32.5055412 32.8583854,32.567785 32.8583854,32.567785 C33.0119349,32.6057386 32.9997726,32.7696979 32.9997726,32.7696979 L32.9967319,38.4187035 C32.9967319,39.6575072 32.6653084,40.575983 32.0131035,41.1452862 C31.3639391,41.7130712 30.407676,42 29.1716792,42 C28.7079904,42 27.9615273,41.9362381 27.5145617,41.8466677 C27.5145617,41.8466677 26.1630228,41.5855473 25.6065962,41.1513587 C25.6065962,41.1513587 25.4849729,41.0754517 25.5518657,40.9054198 L25.9897096,39.730378 C26.0444401,39.5785638 26.1919084,39.6301807 26.1919084,39.6301807 C26.1919084,39.6301807 26.2390373,39.6483983 26.2937678,39.6802794 C27.4841559,40.3270077 28.9892442,40.3072719 28.9892442,40.3072719 C29.6581724,40.3072719 30.1720308,40.1736754 30.5186572,39.9080006 C30.8561619,39.6499165 31.0279548,39.2597541 31.0279548,38.4369212 L31.0279548,38.1758008 C30.4958529,38.2517079 30.0078394,38.295734 30.0078394,38.295734"
                id="shape-1"
                fill="#FFFFFF"
                transform="translate(29.000000, 37.000000) scale(-1, 1) rotate(-180.000000) translate(-29.000000, -37.000000) "
              />
              <path
                d="M81.9869381,41.5777925 C82.0363382,41.7219845 81.9327579,41.7909458 81.8897323,41.8066188 C81.7797776,41.848936 81.2284122,41.9633492 80.8029362,41.9899934 C79.9886362,42.0385798 79.5360701,41.9037917 79.1313106,41.725119 C78.7297377,41.5464465 78.2835461,41.2580626 78.0349535,40.9304961 L78.0349535,41.7063114 C78.0349535,41.8144554 77.9568698,41.900657 77.848509,41.900657 L76.1864445,41.900657 C76.0780832,41.900657 76,41.8144554 76,41.7063114 L76,32.1943456 C76,32.087769 76.0892382,32 76.197599,32 L77.9010957,32 C78.0094569,32 78.0971016,32.087769 78.0971016,32.1943456 L78.0971016,36.9464103 C78.0971016,37.5843028 78.1688108,38.2206281 78.3122297,38.6202905 C78.4524615,39.0152511 78.6436865,39.3318464 78.879531,39.5591054 C79.1169686,39.7847972 79.3862773,39.9430948 79.6810825,40.0324311 C79.9822622,40.1233348 80.3153125,40.1531135 80.5511566,40.1531135 C80.8905814,40.1531135 81.2634703,40.0669118 81.2634703,40.0669118 C81.3877666,40.0528061 81.4578823,40.1280366 81.4993144,40.2393152 C81.6108622,40.5308337 81.9263839,41.4038218 81.9869381,41.5777925"
                id="path"
                fill="#FFFFFF"
                transform="translate(79.000000, 37.000000) scale(-1, 1) rotate(-180.000000) translate(-79.000000, -37.000000) "
              />
              <path
                d="M65.8722467,45.7810985 C65.6709438,45.8445481 65.4882228,45.8873766 65.2497563,45.9333775 C65.0081929,45.9777927 64.7201751,46 64.3934452,46 C63.2537613,46 62.3556408,45.6700618 61.7254082,45.0197026 C61.0982725,44.3725162 60.6724396,43.3874602 60.4587488,42.091501 L60.3813245,41.6552846 L58.9505259,41.6552846 C58.9505259,41.6552846 58.7770961,41.6616296 58.7399324,41.4681081 L58.5061112,40.1245616 C58.4890783,39.9976623 58.5432749,39.916764 58.7105114,39.916764 L60.1025979,39.916764 L58.6903811,31.8396226 C58.5804386,31.1892636 58.4534631,30.6547003 58.3125511,30.2486225 C58.174736,29.8488896 58.0400178,29.54909 57.8727817,29.3301887 C57.7117395,29.1208048 57.5599879,28.9653532 57.2967458,28.8749374 C57.0799584,28.800384 56.8291039,28.7654867 56.5550226,28.7654867 C56.403271,28.7654867 56.2004197,28.7908666 56.0502166,28.8225914 C55.9015624,28.85273 55.8225897,28.8860411 55.7095507,28.9352146 C55.7095507,28.9352146 55.5469599,28.9986642 55.4819233,28.8321089 C55.4308236,28.6941059 55.0591874,27.6487728 55.0142814,27.5202872 C54.970924,27.3918016 55.0328633,27.2918684 55.111836,27.2617298 C55.2976539,27.1951077 55.4354689,27.1506929 55.6878719,27.0888295 C56.037829,27.0047588 56.3335895,27 56.6107677,27 C57.1899005,27 57.7194817,27.0840708 58.1577026,27.2458674 C58.597472,27.4092503 58.9814958,27.6931875 59.3221622,28.0770579 C59.6891526,28.4926532 59.9198769,28.9272833 60.1397611,29.5221239 C60.3580973,30.1090332 60.5454641,30.8387043 60.6941184,31.6889297 L62.1140774,39.916764 L64.1890451,39.916764 C64.1890451,39.916764 64.3640238,39.910419 64.399639,40.1055267 L64.6350086,41.447487 C64.6504931,41.5759725 64.597845,41.6552846 64.42906,41.6552846 L62.4144832,41.6552846 C62.4253228,41.7012856 62.5166831,42.4277842 62.7474073,43.1114542 C62.8465103,43.4017365 63.0323282,43.6380863 63.1887251,43.799883 C63.3435736,43.9585072 63.5216488,44.0711302 63.7167578,44.136166 C63.9165122,44.2027885 64.1441391,44.2345131 64.3934452,44.2345131 C64.58236,44.2345131 64.7697264,44.2123057 64.9106384,44.1821674 C65.1057474,44.1393389 65.1816232,44.1171311 65.2327229,44.101269 C65.4386715,44.0378194 65.4665441,44.0996828 65.5068046,44.2012023 L65.988383,45.5558522 C66.0379343,45.7017864 65.9156041,45.7636498 65.8722467,45.7810985"
                id="path"
                fill="#FFFFFF"
                transform="translate(60.500000, 36.500000) scale(-1, 1) rotate(-180.000000) translate(-60.500000, -36.500000) "
              />
              <path
                d="M38,28.1928332 C38,28.0862262 37.9253049,28 37.8216463,28 L36.1768293,28 C36.0731707,28 36,28.0862262 36,28.1928332 L36,41.8071669 C36,41.9137738 36.0731707,42 36.1768293,42 L37.8216463,42 C37.9253049,42 38,41.9137738 38,41.8071669 L38,28.1928332 L38,28.1928332 Z"
                id="path"
                fill="#FFFFFF"
                transform="translate(37.000000, 35.000000) scale(-1, 1) rotate(-180.000000) translate(-37.000000, -35.000000) "
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AvalaraSettingIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 110 60'}
    >
      <svg
        width="120px"
        height="80px"
        viewBox="0 0 170 60"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Avalara</title>
        <defs>
          <polygon id="path-1" points="0 0 7.77730613 0 7.77730613 32.5412073 0 32.5412073" />
          <polygon
            id="path-3"
            points="1.18531614 0 3.69210933 0 3.69210933 21.7825822 1.18531614 21.7825822"
          />
          <polygon
            id="path-5"
            points="0 0.18252819 4.87742547 0.18252819 4.87742547 4.69489728 0 4.69489728"
          />
          <polygon
            id="path-7"
            points="0 0.902444506 4.87742547 0.902444506 4.87742547 3.97498097 0 3.97498097"
          />
          <polygon
            id="path-9"
            points="0 0.317396729 4.87742547 0.317396729 4.87742547 4.56002874 0 4.56002874"
          />
          <polygon
            id="path-11"
            points="0 1.09048212 4.87742547 1.09048212 4.87742547 3.78694335 0 3.78694335"
          />
          <polygon
            id="path-13"
            points="0 1.13808627 4.87742547 1.13808627 4.87742547 3.73933921 0 3.73933921"
          />
          <polygon
            id="path-15"
            points="0 0.934134202 4.87742547 0.934134202 4.87742547 3.94329127 0 3.94329127"
          />
          <polygon
            id="path-17"
            points="0 1.08519879 4.87742547 1.08519879 4.87742547 3.79222668 0 3.79222668"
          />
          <polygon
            id="path-19"
            points="0 1.01870454 4.87742547 1.01870454 4.87742547 3.85872094 0 3.85872094"
          />
          <polygon
            id="path-21"
            points="0 0.298986181 4.87742547 0.298986181 4.87742547 4.57843929 0 4.57843929"
          />
        </defs>
        <g id="overview" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.1-new1" transform="translate(-877.000000, -915.000000)">
            <g id="Avalara" transform="translate(877.000000, 915.000000)">
              <path
                d="M113.13778,20.8851359 C113.13778,17.3685121 113.142657,13.8518883 113.13778,10.3303872 C113.132902,10.076761 113.230451,9.95970281 113.484077,9.96458024 C114.89853,9.96458024 116.308106,9.96945767 117.72256,9.96945767 C117.985941,9.96945767 118.059102,10.076761 118.04447,10.340142 C118.02496,10.9498202 118.04447,11.5643758 118.054225,12.174054 C118.054225,12.2764799 118.039592,12.403293 118.190793,12.4471898 C118.332238,12.4813318 118.405399,12.3984156 118.473683,12.3154993 C118.971181,11.7253308 119.556472,11.2327109 120.205169,10.8230071 C121.565971,9.95482539 123.038953,9.52073452 124.648504,9.5353668 C125.228918,9.54024422 125.233795,9.5353668 125.233795,10.1304127 C125.238672,11.2863625 125.228918,12.4471898 125.24355,13.6031396 C125.248427,13.8909077 125.141124,13.9787014 124.872865,13.9689466 C123.902258,13.9104174 122.936528,14.0030885 121.995184,14.2518372 C120.536834,14.6371538 119.244316,15.3395031 118.181038,16.4418013 C118.102999,16.5247175 118.068857,16.6271434 118.063979,16.7393242 C118.054225,16.9636858 118.029838,17.1880474 118.029838,17.4075315 L118.029838,31.4008652 C118.029838,31.8593432 118.029838,31.8593432 117.566482,31.8593432 C116.230068,31.8593432 114.893653,31.8544658 113.562116,31.8643664 C113.230451,31.869098 113.13778,31.7520398 113.13778,31.4398846 C113.142657,27.9183834 113.142657,24.4017596 113.142657,20.8851359 L113.13778,20.8851359 Z"
                id="Fill-1"
                fill={props?.style?.color || '#FF4D00'}
              />
              <g id="overview" transform="translate(77.170905, 0.000000)">
                <mask id="mask-2" fill="white">
                  <use xlinkHref="#path-1" />
                </mask>
                <g id="Clip-4" />
                <path
                  d="M0.0173222053,13.1592939 C0.0173222053,8.95983059 0.0173222053,4.76036726 0.0173222053,0.560903929 C0.0173222053,0.0438968293 0.06609646,0 0.592858411,0 C1.92927299,0 3.26568757,0 4.60210215,0 C5.08008985,0 5.15812865,0.082916233 5.15812865,0.565781355 L5.15812865,24.6212438 C5.15812865,25.0748444 5.11423182,25.5430772 5.24104489,25.9820455 C5.4166322,26.6014785 5.76780684,27.1136082 6.33358819,27.4599054 C6.6798854,27.6647573 7.05056974,27.7915703 7.4456412,27.8647317 C7.67975762,27.9037511 7.77730613,28.0208093 7.77730613,28.2744355 C7.76755128,29.5620758 7.76755128,30.8545935 7.77242871,32.1471113 C7.77730613,32.4202471 7.68463505,32.5714473 7.38223467,32.5373053 C6.74816936,32.4641439 6.11410405,32.3665954 5.48491616,32.2592921 C4.8996251,32.1568661 4.35823088,31.971524 3.81683665,31.776427 C2.53407375,31.317949 1.62687261,30.4400124 0.929400769,29.3035722 C0.587980986,28.7377909 0.446535647,28.108603 0.280703181,27.48917 C-0.119245708,26.01131 0.0270770563,24.509063 0.0221996308,23.0165708 C0.0026899289,19.729186 0.0173222053,16.4418013 0.0173222053,13.1592939 Z"
                  id="Fill-3"
                  fill={props?.style?.color || '#F6CEA8'}
                  mask="url(#mask-2)"
                />
              </g>
              <path
                d="M101.187112,10.0157932 C101.694364,10.0499352 102.094313,10.3474581 102.523526,10.5474326 C102.97225,10.7522845 103.372198,11.0302977 103.757515,11.3229432 C103.962367,11.4790208 104.055038,11.4448789 104.055038,11.1814979 C104.050161,10.8839749 104.059915,10.5815746 104.050161,10.2840516 C104.045283,10.0206706 104.142832,9.88898015 104.420845,9.88898015 C105.864563,9.89385757 107.313158,9.89385757 108.761754,9.88898015 C109.010502,9.88898015 109.132438,10.0011609 109.122683,10.2499096 C109.117806,10.4401292 109.122683,10.6254714 109.122683,10.8205684 L109.122683,31.3545296 C109.122683,31.8715367 109.122683,31.8715367 108.610553,31.8715367 C107.220487,31.8764142 105.830421,31.8666593 104.440355,31.8812916 C104.152587,31.8812916 104.040406,31.8032528 104.050161,31.5008524 C104.06967,30.9887227 104.059915,30.4765931 104.055038,29.9644634 C104.050161,29.8717923 104.098935,29.7498567 103.986754,29.6913276 C103.859941,29.627921 103.777025,29.7449792 103.708741,29.8181406 C103.157592,30.3400252 102.582056,30.8180128 101.904093,31.1643101 C101.182234,31.5349944 100.450621,31.8471496 99.6556003,32.0276144 C98.792296,32.2227114 97.9192368,32.344647 97.0510551,32.310505 C96.1487313,32.2812405 95.2512851,32.1885694 94.3635936,31.9300659 C93.0515662,31.5545041 91.8907389,30.9301936 90.8079505,30.1302959 C90.7152794,30.0571345 90.6372406,29.959586 90.5445695,29.8766697 C90.4957952,29.7742438 90.2665562,29.7596115 90.3884919,29.5693919 C90.5055501,29.5498822 90.5787115,29.6132888 90.6567503,29.6815727 C92.1297328,30.9789679 93.851464,31.759356 95.7829244,31.988595 C96.9437517,32.1300403 98.1338435,32.1446726 99.2995482,31.8666593 C100.265278,31.6374203 101.187112,31.3398974 102.035784,30.8277677 C102.255268,30.774116 102.44061,30.6619352 102.596688,30.5009802 C102.738133,30.3595349 102.889333,30.2180895 103.069798,30.1302959 C103.347811,29.9400763 103.533154,29.6523082 103.806289,29.4523337 C104.040406,29.3596626 104.172096,29.4718434 104.255012,29.6815727 C104.298909,30.2132121 104.264767,30.7448515 104.2794,31.2813683 C104.284277,31.6764397 104.308664,31.7057043 104.693981,31.7057043 C105.942602,31.7105817 107.181468,31.7105817 108.430089,31.7057043 C108.84467,31.7057043 108.893444,31.65693 108.898322,31.2472263 L108.898322,25.1650767 L108.898322,10.6693682 C108.898322,10.1182191 108.893444,10.1182191 108.33254,10.1182191 L104.825671,10.1182191 C104.2794,10.1182191 104.2794,10.1182191 104.2794,10.6840005 C104.274522,10.8595878 104.284277,11.0400526 104.269645,11.2156399 C104.245258,11.5180403 104.142832,11.6009565 103.811167,11.6058339 C103.694109,11.5765694 103.581928,11.5229177 103.494134,11.4448789 C103.025901,11.04493 102.469875,10.8108136 101.94799,10.5035358 C101.821177,10.3864775 101.674854,10.3133162 101.49439,10.2986839 C101.348067,10.2694193 101.216376,10.2206451 101.104196,10.1133417 C101.084686,10.0840772 101.084686,10.0499352 101.094441,10.0157932 C101.123705,9.99628351 101.157847,9.99628351 101.187112,10.0157932"
                id="Fill-5"
                fill={props?.style?.color || '#E9B390'}
              />
              <path
                d="M43.115021,28.4012485 C42.6419107,29.4889144 42.1688004,30.5717029 41.7005676,31.6544913 C41.6322836,31.8154464 41.5201028,31.869098 41.3591478,31.869098 C39.837391,31.8642206 38.3107569,31.8593432 36.7890001,31.869098 C36.4768449,31.869098 36.4134384,31.6301042 36.340277,31.4496394 C35.7988828,30.1424894 35.1892046,28.8694814 34.6136684,27.581841 C33.979603,26.1381231 33.3016409,24.7139149 32.6822079,23.2653195 C32.2432396,22.2313053 31.7750067,21.2119234 31.3262836,20.187664 C30.940967,19.3146049 30.5556504,18.4366683 30.1410692,17.573364 C29.6533267,16.5637369 29.2338681,15.5199679 28.7753901,14.4957085 C28.2730152,13.3739006 27.7511307,12.2569702 27.2487559,11.1351624 C27.1414525,10.8961685 27.0292718,10.6522972 26.917091,10.4133034 C26.7561359,10.076761 26.8878264,9.89629628 27.2682656,9.89629628 C28.7461255,9.89629628 30.2239854,9.90117371 31.7018454,9.8912459 C32.0091232,9.88654143 32.1993428,9.98408994 32.3212784,10.2767355 C32.706595,11.222956 33.1114213,12.1642991 33.5016154,13.1105197 C33.5747767,13.2812296 33.7308544,13.4226749 33.6967124,13.6324042 C33.6625704,13.6519139 33.6284284,13.6567913 33.5942864,13.6372816 C33.428454,13.5007137 33.326028,13.3104941 33.2528667,13.1153971 C32.9212017,12.2520928 32.5310077,11.418053 32.1895879,10.5547487 C32.0822845,10.2767355 31.9213295,10.1694321 31.6140517,10.1743095 C30.2337403,10.1889418 28.8534289,10.179187 27.4731175,10.179187 C27.1024331,10.179187 27.0829234,10.2084515 27.239001,10.5547487 C27.8242921,11.876531 28.4242154,13.1934359 29.0192613,14.5103408 C30.0679078,16.8222405 31.0970446,19.143895 32.1213039,21.4606721 C32.7797564,22.9482868 33.4577185,24.4261468 34.116171,25.9040067 C34.9111913,27.684267 35.7013342,29.4596499 36.4963546,31.2399102 C36.6085354,31.4837814 36.764613,31.5715751 37.0328714,31.5862074 C38.3448988,31.6447365 39.6569263,31.5374331 40.9689538,31.5862074 C41.3542704,31.6008396 41.5688771,31.4545169 41.7005676,31.1082197 C42.0273551,30.2449154 42.4565685,29.4206304 42.8223754,28.5768358 C42.8809045,28.4402679 42.9150465,28.2744355 43.115021,28.2549258 C43.1540404,28.2988226 43.1491629,28.3524743 43.115021,28.4012485"
                id="Fill-7"
                fill={props?.style?.color || '#E9B490'}
              />
              <path
                d="M131.232541,30.7448515 C130.486294,30.3936768 129.96441,29.7644889 129.354732,29.2426044 C128.754808,28.7353522 128.325595,28.1061643 127.906136,27.4769764 C127.52082,26.8916853 127.203787,26.2624975 126.959916,25.5894127 C126.740432,24.9748571 126.481928,24.3651789 126.34536,23.7262362 C126.228302,23.1799646 126.150263,22.6190606 126.106366,22.0630341 C126.06247,21.5557819 125.989308,21.0485296 126.033205,20.5364 C126.077102,19.9072121 126.072224,19.2585145 126.199037,18.6537137 C126.350238,17.8928353 126.584354,17.131957 126.906264,16.4003432 C127.189155,15.756523 127.52082,15.1419674 127.828098,14.5127795 C128.052459,14.0445466 128.428021,13.6348429 128.779196,13.2397714 C129.159635,12.810558 129.579093,12.4106091 130.013184,12.0252925 C130.925263,11.2156399 131.944645,10.586452 133.085962,10.1474837 C133.939512,9.81581876 134.827203,9.65486372 135.714895,9.46464413 C136.548934,9.28905681 137.378097,9.33783107 138.202382,9.38660532 C138.675492,9.41099245 139.182744,9.47439898 139.660732,9.64023145 C139.92899,9.73290253 140.236268,9.73777996 140.519159,9.82069619 C141.367831,10.0743223 142.172606,10.4011098 142.874955,10.9522589 C142.835936,10.9961557 142.782284,11.0156654 142.723755,11.0156654 C142.304296,10.8790975 141.923857,10.6303488 141.519031,10.4547615 C140.758152,10.1230966 139.96801,9.92799955 139.153479,9.78167679 C138.207259,9.61096689 137.241529,9.59633462 136.290431,9.65974115 C135.271049,9.7280251 134.271177,9.96214153 133.315201,10.3377033 C132.403123,10.6986328 131.554451,11.1424785 130.774063,11.732647 C129.515687,12.6691127 128.501182,13.8250625 127.740304,15.1956191 C126.959916,16.6003176 126.506315,18.1220743 126.340483,19.7218699 C126.169773,21.2972783 126.34536,22.8434222 126.794083,24.3651789 C127.189155,25.7162258 127.798833,26.9599693 128.652382,28.0768997 C129.305957,28.940204 130.0961,29.6766953 130.959405,30.3253929 C131.052076,30.3936768 131.144747,30.4522059 131.213031,30.5399996 C131.25205,30.6082836 131.286192,30.6716901 131.232541,30.7448515"
                id="Fill-9"
                fill={props?.style?.color || '#FAE4D2'}
              />
              <path
                d="M142.721316,10.963477 C142.77009,10.963477 142.823742,10.9585996 142.872516,10.9537221 C143.033471,10.9732318 143.11151,11.1000449 143.204181,11.2122257 C143.287098,11.319529 143.404156,11.3000193 143.506582,11.2756322 C143.628517,11.2512451 143.60413,11.1390643 143.60413,11.0561481 L143.60413,10.2513729 C143.60413,9.96848218 143.745576,9.82215942 144.033344,9.82215942 C145.462429,9.82215942 146.896392,9.82703684 148.325478,9.82215942 C148.491311,9.82215942 148.608369,9.8758111 148.686408,10.0221339 C148.759569,10.1489469 148.744937,10.3001471 148.744937,10.4415925 L148.744937,31.38038 C148.744937,31.5267028 148.764446,31.6730255 148.68153,31.7998386 C148.657143,31.8242257 148.632756,31.8486129 148.613246,31.873 C148.54984,31.9120194 148.486433,31.9022645 148.418149,31.9022645 C146.940289,31.9022645 145.467307,31.9022645 143.994324,31.9022645 C143.92604,31.9022645 143.857756,31.9120194 143.799227,31.873 C143.745576,31.873 143.691924,31.8583677 143.667537,31.804716 C143.599253,31.2877089 143.657782,30.7755792 143.633395,30.2585721 C143.628517,30.1220042 143.701679,29.9464169 143.535846,29.8635007 C143.384646,29.7903393 143.296852,29.9415395 143.204181,30.0195783 C143.140775,30.0683526 143.057859,30.0829848 143.004207,30.1366365 C142.16529,30.8487406 141.253211,31.4340317 140.21432,31.7998386 C140.126526,31.8291031 140.0241,31.8486129 139.931429,31.8778774 C139.70219,31.9217742 139.487583,32.0095679 139.258344,32.068097 C138.931557,32.1266261 138.604769,32.2436843 138.263349,32.2144198 C138.004846,32.3363554 137.721955,32.263194 137.453697,32.3363554 C137.365903,32.3363554 137.278109,32.3412328 137.190316,32.3412328 C136.878161,32.3900071 136.561128,32.3900071 136.248973,32.3412328 C136.117282,32.3412328 135.985592,32.3363554 135.853901,32.3363554 C135.653927,32.2875811 135.444197,32.263194 135.244223,32.2095423 C135.093023,32.2095423 134.936945,32.2046649 134.780868,32.2046649 C134.634545,32.1315035 134.45408,32.1705229 134.312635,32.0729744 C133.883421,32.0144453 133.478595,31.8778774 133.083523,31.7266772 C132.434826,31.4828059 131.795883,31.1804056 131.234979,30.7463147 C131.21547,30.692663 131.200837,30.6438888 131.181328,30.5902371 C131.342283,30.5170757 131.454463,30.6292565 131.576399,30.6975404 C132.600658,31.2828315 133.678569,31.7120449 134.839397,31.9364065 C135.702701,32.102239 136.561128,32.136381 137.42931,32.0876067 C138.458446,32.0290776 139.458319,31.8339806 140.419171,31.4437865 C141.399534,31.0487151 142.301858,30.5365854 143.048104,29.7708296 C143.067613,29.7513199 143.092001,29.7269328 143.121265,29.7025456 C143.267588,29.5659777 143.418788,29.4050227 143.64315,29.4928163 C143.882143,29.5854874 143.896776,29.809849 143.901653,30.0244557 C143.911408,30.4585466 143.906531,30.8926375 143.901653,31.3267283 C143.901653,31.5998642 144.023589,31.7022901 144.28697,31.7022901 C145.564855,31.6974127 146.847618,31.6974127 148.125504,31.7022901 C148.388885,31.7022901 148.525453,31.6047416 148.51082,31.3316058 C148.505943,31.2438121 148.51082,31.1560184 148.51082,31.0682248 L148.51082,10.7586251 C148.51082,10.1245598 148.51082,10.1245598 147.862123,10.1245598 C146.691541,10.1245598 145.520958,10.1245598 144.355254,10.1294372 C143.94555,10.1294372 143.916285,10.1587018 143.906531,10.5586507 C143.901653,10.7830122 143.921163,11.0073738 143.891898,11.226858 C143.843124,11.5731552 143.633395,11.6999682 143.326117,11.5341358 C143.077368,11.4024453 142.7896,11.2902645 142.721316,10.963477"
                id="Fill-11"
                fill={props?.style?.color || '#F8D7C3'}
              />
              <path
                d="M21.4641293,17.8391837 L21.220258,17.8391837 C21.5470455,17.4831316 21.9323621,17.2343829 22.1323366,16.8246792 C22.6542211,16.3174269 23.1858605,15.8150521 23.7711515,15.3712064 C24.2979135,14.9810123 24.7759012,14.5322892 25.312418,14.15185 C25.5709215,13.9713853 25.6879797,14.0738112 25.770896,14.2689082 C25.9367284,14.6542248 26.0684189,15.0492963 26.2537611,15.4297355 C26.3464322,15.6248325 26.2879031,15.8101747 26.1074383,15.9662523 C25.2929083,16.673479 24.556417,17.4587445 23.8248032,18.2537648 C22.5225306,19.6584634 21.3129291,21.1314459 20.1813664,22.6727123 C20.0204113,22.8970739 19.8399466,23.1068032 19.6692367,23.3214099 C19.6009528,23.2970228 19.5326688,23.3945713 19.4595074,23.3214099 C19.4009783,23.0141321 19.5765656,22.7800157 19.7521529,22.5849187 C20.4837668,21.7801434 21.1178321,20.892452 21.7421425,20.0047606 C21.9372396,19.7316247 22.2396399,19.5365277 22.4054724,19.2731468 C22.6444663,18.897585 22.9614989,18.6146943 23.2590219,18.3025391 C23.5565448,17.9952613 23.936984,17.7611449 24.1125713,17.3368088 C24.166223,17.2099958 24.2881586,17.1026924 24.4100943,17.0246536 C24.9368562,16.7027435 25.2343792,16.1320848 25.7416314,15.7857875 C25.8538122,15.7077487 25.8928316,15.5955679 25.8684445,15.459 C25.8586896,15.4151032 25.8684445,15.4736323 25.8586896,15.4297355 C25.7123669,14.6298377 25.4636182,14.5469215 24.819798,15.0249092 C24.3759523,15.3516967 23.902842,15.6443422 23.512648,16.0491685 C23.3712026,16.1906139 23.2882864,16.3808334 23.1273314,16.5027691 C22.9029698,16.6783564 22.7029954,16.8832083 22.4201047,16.9905116 C22.1567237,17.0880601 21.9811364,17.3221766 21.8299362,17.556293 C21.7421425,17.6928609 21.678736,17.8684482 21.4641293,17.8391837"
                id="Fill-13"
                fill={props?.style?.color || '#2CA6C4'}
              />
              <path
                d="M27.4253187,31.8734877 C26.6595629,31.8734877 25.8889297,31.8637329 25.1231739,31.8783651 C24.8451606,31.88812 24.7037153,31.7417972 24.6207991,31.5320679 C24.333031,30.810209 24.0111209,30.0981048 23.7184753,29.3811233 C23.1965908,28.0983604 22.6454417,26.8302298 22.0991701,25.5620992 C22.0308861,25.4011441 21.972357,25.2401891 21.9040731,25.079234 C21.8211568,24.8938919 21.8309117,24.6792851 21.9918667,24.4890656 C22.6503192,23.7281872 23.2648748,22.9282894 23.9135724,22.1527788 C24.3379084,21.6455265 24.8305283,21.2016808 25.2255998,20.6700414 C25.3719226,20.4749444 25.5523873,20.5237186 25.6694455,20.7968545 C25.86942,21.2602099 26.0596396,21.7284427 26.2547366,22.1966756 C26.5473821,22.8843926 26.8497825,23.5721096 27.1375506,24.2598266 C27.5570092,25.2645762 27.9667129,26.2742033 28.3910489,27.2740755 C28.8836689,28.449535 29.3762889,29.6249946 29.8835411,30.7955767 C29.9762122,31.0101834 30.0201091,31.2394224 30.1274124,31.4491517 C30.2591029,31.7076553 30.1274124,31.8686103 29.8298895,31.8734877 C29.0299917,31.8734877 28.2300939,31.8734877 27.4253187,31.8734877"
                id="Fill-15"
                fill={props?.style?.color || '#F1C9AB'}
              />
              <path
                d="M90.3509357,29.5967055 C90.4192196,29.6893766 90.4826262,29.7820477 90.5460327,29.8747188 C90.0631676,29.5820732 89.711993,29.1479824 89.3413086,28.7382786 C88.6682239,27.9871551 88.1219522,27.1336056 87.6976162,26.2264045 C87.2440157,25.2655517 86.8586991,24.2656795 86.7221311,23.1877684 C86.6245826,22.4122578 86.4587502,21.6562568 86.4782599,20.8709913 C86.4977696,20.1442549 86.6197052,19.4272734 86.7123763,18.700537 C86.8489442,17.6275034 87.2293834,16.6422635 87.682984,15.6862881 C88.3268041,14.3108541 89.2340053,13.1207623 90.3997099,12.12089 C90.985001,11.6087604 91.6239437,11.1746695 92.2823962,10.7893529 C93.0042551,10.3698943 93.7651335,10.0431068 94.5699087,9.84313235 C95.7356134,9.54560939 96.9354601,9.34563495 98.1450616,9.42855118 C98.8815528,9.48220286 99.6180441,9.58950622 100.349658,9.71144186 C100.637426,9.76021611 100.910562,9.9114163 101.188575,10.0138422 C101.159311,10.0187197 101.125169,10.0284745 101.095904,10.0333519 C100.959336,10.1455327 100.822768,10.0674939 100.6862,10.0333519 C100.393555,9.96506798 100.105787,9.82362264 99.7936314,9.84313235 C99.7399797,9.83825492 99.6814506,9.82850007 99.6277989,9.81874522 C99.3351534,9.75046126 99.0376305,9.70656443 98.7352301,9.69193216 C98.6962107,9.68705473 98.6620687,9.68217731 98.6230493,9.67242245 C97.6621965,9.61877077 96.7013437,9.61389335 95.7502457,9.80899037 C94.2138567,10.0626165 92.8091581,10.6479075 91.5263952,11.5307216 C90.2826517,12.3891484 89.2681472,13.4524272 88.463372,14.7351901 C87.6976162,15.949669 87.2098737,17.2616965 86.926983,18.6517627 C86.7465183,19.5248219 86.7513957,20.4173908 86.766028,21.3050822 C86.7952925,23.3389686 87.3318093,25.2265323 88.3707009,26.977528 C88.8828306,27.8457098 89.4876314,28.6358527 90.1948581,29.3479568 C90.2582646,29.4211182 90.3363034,29.4894021 90.3509357,29.5967055"
                id="Fill-17"
                fill={props?.style?.color || '#EEBC9C'}
              />
              <path
                d="M1.88077827,27.1248263 C1.98808163,26.6273289 2.23195291,26.1786057 2.41729508,25.7103729 C2.87089564,24.5641779 3.34888334,23.4326152 3.82687104,22.2961751 C4.21218765,21.3840965 4.62676881,20.4866502 4.99257572,19.5648168 C5.44129887,18.4283767 5.91928656,17.3065688 6.39239684,16.1798835 L7.55810152,13.4095059 C8.02633437,12.2925754 8.48481236,11.1707676 8.95304521,10.0538371 C9.40664578,8.97104867 9.8895109,7.89801507 10.3236018,6.80547176 C10.5138214,6.33723892 10.6796538,5.85437379 10.8893831,5.40077323 C11.1771512,4.77158534 11.4210225,4.12776518 11.6990357,3.49369987 C11.9868039,2.82549258 12.2453074,2.14753044 12.5184432,1.47932315 C12.5867272,1.31349068 12.698908,1.20618732 12.8988824,1.20618732 L17.2739331,1.20618732 C17.410501,1.20618732 17.5129269,1.24520672 17.5275592,1.40616176 C16.0643315,1.31836811 14.6011039,1.38665206 13.1378763,1.36714236 C13.0403278,1.36226493 12.9379018,1.35738751 12.8501082,1.42079404 C12.5672175,1.96706569 12.3331011,2.53284705 12.1087395,3.0986284 C11.4941839,4.62526258 10.8259766,6.13238705 10.2016661,7.65414379 C9.40664578,9.58560428 8.5872383,11.5121873 7.78734052,13.4436478 C7.10450095,15.0970951 6.38751941,16.7407874 5.71443469,18.3942347 C5.07549196,19.9647657 4.41216209,21.5206644 3.75858708,23.0765631 C3.2269477,24.3495712 2.73920516,25.6420889 2.13928182,26.8858324 C2.09538499,26.9736261 2.05636559,27.0662972 1.97832678,27.1345811 C1.9441848,27.1540908 1.91004283,27.1492134 1.88077827,27.1248263"
                id="Fill-19"
                fill={props?.style?.color || '#EDBC9A'}
              />
              <path
                d="M64.1011195,9.67973859 C64.1889132,9.67973859 64.2718294,9.68461602 64.3596231,9.68949344 C64.530333,9.76265483 64.7303074,9.70900315 64.8961399,9.82118393 C65.115624,9.88946789 65.3399856,9.9675067 65.5643472,10.016281 C66.3935095,10.2552748 67.1251233,10.6893657 67.8225952,11.1819856 C67.8469823,11.2014953 67.866492,11.2307599 67.8957566,11.2453922 C68.0079373,11.2844116 68.0810987,11.4453666 68.2127892,11.3819601 C68.3493571,11.3185536 68.3005829,11.1673534 68.3054603,11.0454177 C68.3103377,10.7917916 68.3103377,10.5332881 68.3005829,10.2796619 C68.290828,10.016281 68.3981314,9.88459046 68.6712672,9.88459046 C70.1198626,9.88459046 71.5635805,9.88459046 73.0121759,9.88459046 C73.2609246,9.88459046 73.3877376,9.99677125 73.3779828,10.2503974 C73.3779828,10.338191 73.3779828,10.4259847 73.3779828,10.5137783 L73.3779828,31.3940368 C73.3779828,31.8769019 73.3779828,31.8769019 72.8999951,31.8769019 L68.7590609,31.8769019 C68.3005829,31.8769019 68.2957054,31.8720245 68.3005829,31.4037916 C68.3054603,30.9160491 68.3054603,30.4234291 68.3005829,29.9356866 C68.3005829,29.8527703 68.3396023,29.7405896 68.2371763,29.6966927 C68.1201181,29.6381636 68.061589,29.7552218 67.9835502,29.8137509 C67.8225952,29.940564 67.7250467,30.1502933 67.5006851,30.203945 C67.5640916,29.8527703 67.8225952,29.6674282 68.1152407,29.5406151 C68.2810732,29.4723312 68.4225185,29.5650022 68.4517831,29.7600993 C68.4664153,29.8576478 68.4908025,29.9551963 68.5103122,30.0527448 C68.5493316,30.3795323 68.554209,30.7014424 68.5054347,31.0282299 C68.485925,31.1160235 68.4664153,31.1989398 68.4664153,31.2916109 C68.4566605,31.6379081 68.5103122,31.706192 68.8614868,31.706192 C70.1491271,31.7110695 71.4416449,31.706192 72.7341626,31.7110695 C73.002421,31.7159469 73.1389889,31.6135209 73.1731309,31.3355077 C73.1926406,31.1599204 73.2170277,30.9843331 73.2170277,30.8038683 C73.2170277,24.1705697 73.2170277,17.537271 73.2219052,10.9039724 C73.2219052,10.6844882 73.2170277,10.4601267 73.1243567,10.2503974 C73.0999695,10.2211328 73.070705,10.1918683 73.0414404,10.1723586 C72.9390145,10.1040746 72.8317111,10.1187069 72.7195304,10.1187069 C71.4513997,10.1138295 70.1881465,10.1138295 68.9200159,10.1187069 C68.8127125,10.1187069 68.6956543,10.0991972 68.6078607,10.1821134 C68.4615379,10.4211073 68.554209,10.6844882 68.5298219,10.9332369 C68.520067,11.0551726 68.5346993,11.1771082 68.520067,11.2990438 C68.4712928,11.6550959 68.2225441,11.7721541 67.9298985,11.5477925 C67.247059,11.025908 66.5105677,10.5918172 65.6960377,10.3089265 C65.0668498,10.0894423 64.4376619,9.87971304 63.7645772,9.83581621 C63.3646283,9.81630651 62.9793117,9.69437087 62.5744854,9.69924829 C62.4866917,9.70412572 62.3208592,9.72363542 62.3696335,9.53341583 C62.5159563,9.45537702 62.6720339,9.49439642 62.8281115,9.49439642 C63.2573249,9.5285384 63.6865384,9.56755781 64.1011195,9.67973859"
                id="Fill-21"
                fill={props?.style?.color || '#E9B99A'}
              />
              <g id="overview-2" transform="translate(145.122575, 10.019207)">
                <mask id="mask-4" fill="white">
                  <use xlinkHref="#path-3" />
                </mask>
                <g id="Clip-24" />
                <path
                  d="M3.55749239,21.7825822 C3.55749239,14.5200956 3.55749239,7.26248653 3.56236981,0 C3.70869258,0.0780388076 3.68918288,0.219484146 3.68918288,0.351174634 L3.68918288,21.436285 C3.68918288,21.5728529 3.71357,21.7142982 3.55749239,21.7825822"
                  id="Fill-23"
                  fill={props?.style?.color || '#FAE4D2'}
                  mask="url(#mask-4)"
                />
              </g>
              <path
                d="M33.6269652,13.630941 L33.6952491,13.630941 C34.0708109,14.2796386 34.3000499,14.9966201 34.6122051,15.6745822 C35.012154,16.542764 35.3633287,17.4353328 35.7388904,18.3132694 C36.1144522,19.1960834 36.4997688,20.07402 36.8753306,20.9519566 C37.1679761,21.6299187 37.4557442,22.3078809 37.7386349,22.985843 C38.0946869,23.8150054 38.4409841,24.6490451 38.7921588,25.4830849 C38.8019136,25.5025946 38.806791,25.5221043 38.8165459,25.541614 C38.8799524,25.6586722 38.9336041,25.8293821 39.0555397,25.8342595 C39.2213722,25.8342595 39.299411,25.6489173 39.3481853,25.5172268 C39.7091148,24.5319869 40.1578379,23.5906438 40.5626642,22.6249135 C40.9235937,21.7713641 41.2698909,20.922692 41.6308204,20.0691426 C41.7088592,19.878923 41.8161626,19.6984583 41.9137111,19.5131161 C41.947853,19.488729 41.9771176,19.4936064 42.0015047,19.5228709 C42.0795435,19.6594389 42.016137,19.7764971 41.9624853,19.9033101 C41.5674139,20.8544081 41.1479553,21.8006286 40.7382515,22.7517266 C40.3285478,23.7125794 39.9090892,24.6734322 39.5091403,25.6391625 C39.4847532,25.6928142 39.4701209,25.7415884 39.4457338,25.7952401 C39.367695,25.9464403 39.2750239,26.0976405 39.0848043,26.1025179 C38.875075,26.1122728 38.7628942,25.9561951 38.6994877,25.7854852 C38.4995132,25.2538459 38.2458871,24.7465936 38.0166481,24.2247091 C37.1826084,22.3078809 36.3924654,20.371543 35.5584257,18.4547148 C34.9097281,16.9768548 34.2756628,15.4941175 33.6611072,14.0016253 C33.6123329,13.8845671 33.5147844,13.7626315 33.6269652,13.630941"
                id="Fill-25"
                fill={props?.style?.color || '#EAB18D'}
              />
              <path
                d="M1.88077827,27.1248263 L1.94906223,27.1248263 C2.06124302,27.2370071 1.99295906,27.3589427 1.9441848,27.4662461 C1.48570681,28.4905054 1.1198999,29.5537842 0.646789629,30.5731661 C0.539486268,30.8072825 0.461447461,31.0511538 0.363898951,31.2901476 C0.280982718,31.4949995 0.349266675,31.5779157 0.563873396,31.5779157 C1.06624822,31.5779157 1.56862304,31.5779157 2.06612044,31.5779157 C2.96844415,31.5779157 3.87076787,31.5681609 4.76821415,31.5827932 C5.08524681,31.5876706 5.25107927,31.465735 5.37301491,31.1779668 C6.0022028,29.6074358 6.68504236,28.061292 7.33861738,26.5005158 C7.41665618,26.3102962 7.44104331,26.0566701 7.69954686,25.9835087 C7.74344369,26.032283 7.73856627,26.0810572 7.70930171,26.1347089 C7.63126291,26.5883095 7.3630045,26.9687487 7.19717204,27.3833298 C6.73381662,28.5636668 6.23144179,29.7293715 5.74369925,30.8950762 C5.64615074,31.1194377 5.5437248,31.3389219 5.44129887,31.558406 C5.34375036,31.7632579 5.20718245,31.8705613 4.9486789,31.8705613 C3.43179957,31.855929 1.91979768,31.8656838 0.40779578,31.8608064 C0.0078468917,31.8608064 -0.0799467668,31.7291159 0.0663759974,31.3779413 C0.632157352,30.0268944 1.19793871,28.6758476 1.76372006,27.3248007 C1.79298461,27.2516393 1.84175887,27.1931102 1.88077827,27.1248263"
                id="Fill-27"
                fill={props?.style?.color || '#EFA980'}
              />
              <path
                d="M12.7145157,1.40616176 C12.7827997,1.24520672 12.924245,1.26959385 13.0559355,1.26959385 C14.4021049,1.26959385 15.7482744,1.27447128 17.0944438,1.26471643 C17.2602763,1.26471643 17.3968442,1.30861325 17.5285347,1.40616176 C17.7236317,1.85488491 17.9236061,2.30360805 18.1138257,2.75720862 C18.4650004,3.59124838 18.8112976,4.42041071 19.1575948,5.25445046 C19.4599952,5.98606428 19.7526407,6.7176781 20.0599185,7.4444145 C20.2989123,8.00531843 20.5720482,8.54183523 20.7769,9.10761658 C21.1036875,10.0196951 21.5475333,10.878122 21.8743208,11.7853232 C21.9767467,12.0779687 22.1035598,12.3608594 22.2498825,12.6291178 C22.3864504,12.8924988 22.3084116,13.0827183 22.0889275,13.2290411 C21.4938816,13.6338674 20.9719971,14.1264874 20.4403577,14.6142299 C20.3574414,14.6873913 20.2745252,14.7556753 20.191609,14.8288367 C20.1428347,14.8727335 20.0989379,14.8727335 20.0501636,14.8288367 C20.0355314,14.7020236 20.1184476,14.6191074 20.2013638,14.545946 C20.6939838,14.1167325 21.1524618,13.6436223 21.6743463,13.2485508 C22.0645404,12.9559053 22.1084372,12.8047051 21.9084627,12.3657368 C20.9427325,10.2391793 20.1086927,8.05409268 19.1819819,5.9129029 C18.6113231,4.59599802 18.0504192,3.27421572 17.5236572,1.94267857 C17.4017216,1.63540076 17.245644,1.50371027 16.8944694,1.47932315 C15.5726871,1.39640691 14.2509048,1.41103919 12.9291225,1.46469087 C12.8510836,1.46956829 12.7730448,1.46956829 12.7145157,1.40616176"
                id="Fill-29"
                fill={props?.style?.color || '#E7B08B'}
              />
              <path
                d="M65.299503,31.5398718 C64.5142375,31.9446981 63.6655654,32.1349177 62.7925063,32.2032017 C62.3437831,32.2373437 61.8999374,32.3641567 61.436582,32.3348922 C60.4562195,32.2714856 59.4856118,32.1934468 58.524759,31.9105562 C56.7103567,31.3886716 55.2178645,30.4034317 53.9155919,29.0621397 C53.2766492,28.4036872 52.7742744,27.6623186 52.2816544,26.9063176 C52.2280027,26.8282788 52.1889833,26.7551174 52.1938607,26.6624463 C52.2231253,26.6331818 52.2572673,26.6234269 52.2914092,26.6234269 C52.5352805,26.7404852 52.6084419,26.9941113 52.7303775,27.1940857 C53.5692947,28.5402552 54.6569606,29.6230436 55.9738655,30.5009802 C57.2273638,31.3350199 58.6027978,31.8569045 60.0757803,32.0081047 C61.2268527,32.1251629 62.4071897,32.1544274 63.5631395,31.8617819 C64.0557595,31.7398463 64.5630117,31.6520526 65.0409994,31.4667104 C65.1385479,31.4520782 65.231219,31.4569556 65.299503,31.5398718"
                id="Fill-31"
                fill={props?.style?.color || '#EBBA96'}
              />
              <path
                d="M41.9741911,19.5082387 C41.9546814,19.5082387 41.9302943,19.5082387 41.9107846,19.5082387 C41.8717652,19.3375288 41.983946,19.2155931 42.0424751,19.0741478 C42.5253402,17.9718496 43.0033279,16.8646741 43.4813156,15.7574985 C44.012955,14.513755 44.5494718,13.2748889 45.0713563,12.026268 C45.3444921,11.3824478 45.6029957,10.7337502 45.8956412,10.0948075 C45.97368,9.93385246 46.0663511,9.88507821 46.2273062,9.88507821 C47.0808556,9.89483306 47.9392825,9.89483306 48.792832,9.89483306 C49.4805489,9.95823959 50.1633885,9.90458791 50.8462281,9.92409761 C50.9779186,9.92897503 51.1193639,9.88020078 51.2071576,10.031401 C51.177893,10.2069883 51.0413251,10.1387043 50.9437766,10.1387043 C49.4659167,10.0850526 47.9880567,10.1289495 46.5101968,10.1143172 C46.2273062,10.1143172 46.0565963,10.2411303 45.9492929,10.4947564 C45.3981438,11.8214161 44.8567496,13.1480758 44.2812134,14.4601033 C43.6178835,15.9672278 42.9886956,17.4889845 42.3302432,18.996109 C42.247327,19.1863286 42.1595333,19.386303 41.9741911,19.5082387"
                id="Fill-33"
                fill={props?.style?.color || '#EFC0A1'}
              />
              <path
                d="M62.827136,9.54853585 C62.666181,9.55341327 62.5101034,9.55341327 62.3491483,9.5582907 C62.2369675,9.66559406 62.1003996,9.63632951 61.9687091,9.64120693 C60.7786173,9.67534891 59.5982803,9.82167167 58.4569628,10.1874786 C57.0425094,10.6459566 55.7890111,11.3922027 54.676958,12.3774426 C53.1308142,13.7479992 52.1114322,15.4550981 51.4773669,17.4060683 C51.443225,17.5231265 51.4042056,17.6401847 51.2920248,17.7133461 C51.2578828,17.7328558 51.2286182,17.7279784 51.1993537,17.7035912 C51.2091085,17.2402358 51.4383475,16.8402869 51.5944251,16.4208283 C51.9797418,15.3721819 52.5601554,14.4162065 53.2381175,13.5382699 C53.7161052,12.9237143 54.3062737,12.3871975 54.9208293,11.8848226 C55.4378364,11.4702415 55.9792306,11.0849249 56.545012,10.7581374 C57.0717739,10.4508596 57.6424327,10.192356 58.2374786,10.0265235 C58.7105889,9.89483306 59.1593121,9.64120693 59.6763192,9.68022634 C59.8519065,9.60706495 60.0421261,9.61194238 60.2177134,9.55341327 C60.2372231,9.55341327 60.2616102,9.54853585 60.2859973,9.54853585 C61.0810177,9.43147764 61.8809155,9.37782596 62.6856907,9.45586476 C62.7490972,9.46074219 62.827136,9.45098734 62.827136,9.54853585"
                id="Fill-35"
                fill={props?.style?.color || '#EEBF9F'}
              />
              <path
                d="M51.1910621,10.0967585 C51.132533,9.92604858 50.9862102,9.96019056 50.8593971,9.95531313 C50.2741061,9.95531313 49.688815,9.96019056 49.1084014,9.95531313 C49.001098,9.95531313 48.8840398,9.98945511 48.7962462,9.89678403 C49.4839631,9.8919066 50.1765576,9.88702918 50.8642746,9.88702918 C51.3129977,9.88702918 51.4300559,10.0626165 51.2349589,10.4674428 C50.5521193,11.8770188 49.918054,13.3158593 49.2937435,14.7546998 C48.9425689,15.5643524 48.5279877,16.3447405 48.1914454,17.1592706 C47.9134321,17.8226004 47.6354189,18.4908077 47.3135088,19.1346279 C47.1330441,19.4809251 47.0501278,19.880874 46.7965017,20.1881518 C46.7282177,20.1442549 46.7184629,20.0710936 46.7135855,19.9979322 C46.8794179,19.456538 47.1476763,18.9541631 47.3769153,18.4420335 C48.1085292,16.8227282 48.806001,15.1887907 49.5522471,13.5792403 C50.0302348,12.5305938 50.5130999,11.4819473 50.9618231,10.4137911 C51.0105973,10.2918555 51.0788813,10.1796747 51.1910621,10.0967585"
                id="Fill-37"
                fill={props?.style?.color || '#F3CBAF'}
              />
              <path
                d="M7.70832623,26.1337334 C7.7034488,26.0849592 7.69857138,26.0313075 7.69857138,25.9825332 C7.60102287,25.8605976 7.67418425,25.7484168 7.7229585,25.6411135 C8.27898501,24.3534731 8.82525666,23.0609554 9.36177346,21.7684376 C9.99096135,20.2466809 10.6689235,18.7493113 11.2932339,17.2226771 C11.5566149,16.5788569 11.8395056,15.9350368 12.1126414,15.2863392 C12.1662931,15.1595261 12.1809254,14.9693065 12.3906547,14.9644291 C12.4199192,15.0180808 12.4150418,15.066855 12.3857773,15.1156293 C12.1614157,15.6862881 11.9419315,16.2618243 11.7029377,16.8276056 C11.3176211,17.7640713 10.9176722,18.6956596 10.5226007,19.6272478 C10.1616712,20.4710425 9.79586433,21.3099596 9.43981227,22.1537542 C9.03010853,23.1146071 8.65454677,24.0852147 8.2204559,25.0314353 C8.04974601,25.4021196 7.97658463,25.8167008 7.70832623,26.1337334"
                id="Fill-39"
                fill={props?.style?.color || '#EAB994'}
              />
              <path
                d="M51.1978905,17.7055422 L51.2710518,17.7055422 C51.3929875,17.8421101 51.3490906,17.9981877 51.3149487,18.1493879 C50.9149998,19.846732 50.9101224,21.5587083 51.227155,23.2706847 C51.4173746,24.2998214 51.7441621,25.2850614 52.212395,26.2264045 C52.2806789,26.3629724 52.3879823,26.4946629 52.2709241,26.6556179 C52.2465369,26.6556179 52.2172724,26.6556179 52.1977627,26.6604954 C51.5100457,25.4557713 51.0905871,24.1534987 50.8954901,22.792697 C50.7394125,21.7147859 50.6711285,20.6222426 50.8418384,19.5248219 C50.9345095,18.9102663 51.0174257,18.3005881 51.1978905,17.7055422"
                id="Fill-41"
                fill={props?.style?.color || '#E7B190'}
              />
              <path
                d="M46.7872346,19.9798857 C46.7872346,20.0481697 46.792112,20.1164536 46.792112,20.189615 C46.5823827,20.8285577 46.2799823,21.4236036 46.0068465,22.0332818 C45.484962,23.1892317 44.9679549,24.3500589 44.4509478,25.5108862 C44.0558763,26.4034551 43.6656823,27.3009013 43.2754883,28.1983476 C43.2413463,28.2763864 43.2023269,28.3544252 43.1145332,28.4031995 C43.1145332,28.3495478 43.1096558,28.3007736 43.1047784,28.2519993 C43.0267396,28.0569023 43.1486752,27.9057021 43.2169592,27.7496245 C43.7290888,26.5692875 44.2558508,25.3889506 44.7874902,24.2183684 C45.3874135,22.8917087 45.9727045,21.5552941 46.5726279,20.2286344 C46.6165247,20.1310859 46.6165247,19.9798857 46.7872346,19.9798857"
                id="Fill-43"
                fill={props?.style?.color || '#F0C3A2'}
              />
              <path
                d="M20.0594308,14.8312754 C20.1033276,14.8312754 20.1472244,14.8312754 20.1911212,14.8312754 C19.7375207,15.3434051 19.2058813,15.7823733 18.7278936,16.2652385 C18.3864738,16.6115357 18.2840479,16.5969034 18.0987057,16.1579351 C17.7719182,15.3824245 17.4548855,14.6020364 17.128098,13.8216483 C16.7818008,12.992486 16.4208713,12.1633236 16.0599419,11.3390387 C16.0111676,11.2317354 15.9770256,11.1390643 15.9916579,11.0220061 C16.016045,11.0024964 16.050187,10.997619 16.084329,11.0122512 C16.2208969,11.0854126 16.2647937,11.2317354 16.3184454,11.3634259 C16.9037365,12.6900856 17.4548855,14.0216227 18.0206669,15.3580373 C18.1035831,15.5628892 18.2157639,15.7579862 18.2938027,15.9628381 C18.3913512,16.2067094 18.503532,16.1725674 18.6547322,16.0213672 C19.0254166,15.6604377 19.3619589,15.2751211 19.7716626,14.9678433 C19.8594563,14.9044368 19.9277403,14.7971334 20.0594308,14.8312754"
                id="Fill-45"
                fill={props?.style?.color || '#F2CBAD'}
              />
              <path
                d="M12.3857773,15.1146538 C12.3808998,15.0610021 12.3760224,15.0073504 12.371145,14.9536988 C12.2931062,14.8268857 12.371145,14.7147049 12.4150418,14.6074015 C12.9954554,13.2319676 13.5514819,11.8467787 14.1757924,10.4957319 C14.2733409,10.2811252 14.2391989,9.99335705 14.4977025,9.85678914 C14.5318445,9.85191171 14.5659864,9.86166656 14.595251,9.88117626 C14.6440252,10.0665184 14.4635605,10.1884541 14.4538057,10.359164 C14.0343471,11.2907522 13.6343982,12.2320953 13.2442041,13.1734385 C13.0198426,13.729465 12.7418293,14.2659818 12.566242,14.841518 C12.5321,14.9488213 12.4833258,15.0463698 12.3857773,15.1146538"
                id="Fill-47"
                fill={props?.style?.color || '#EEBD9B'}
              />
              <g id="group" transform="translate(64.336811, 27.846310)">
                <mask id="mask-6" fill="white">
                  <use xlinkHref="#path-5" />
                </mask>
                <g id="Clip-50" />
                <path
                  d="M0.962691732,3.69356183 C0.870020648,3.68868441 0.777349564,3.68868441 0.68467848,3.68380698 C0.689555906,3.56674877 0.791981841,3.55211649 0.865143223,3.51797452 C1.83087347,3.07900622 2.74295203,2.56199912 3.464811,1.76697877 C3.48919813,1.74259164 3.51358525,1.72308194 3.54284981,1.70357224 C3.69892742,1.58651403 3.87939216,1.46945582 4.05497948,1.62065601 C4.2305668,1.76210135 4.2305668,1.96207579 4.09887631,2.15229538 C3.90865672,1.60602373 3.88426959,1.6011463 3.4453013,2.0401146 C3.34775279,2.14254053 3.22093973,2.22057934 3.16728805,2.36202468 C2.51371303,2.93756088 1.78697664,3.4009163 0.962691732,3.69356183"
                  id="Fill-49"
                  fill={props?.style?.color || '#EFA67A'}
                  mask="url(#mask-6)"
                />
              </g>
              <path
                d="M14.5840329,9.88946789 C14.5596458,9.88946789 14.5352587,9.88946789 14.5108715,9.88459046 C14.4523424,9.47976415 14.5937878,9.12371209 14.8425365,8.82618913 C15.0278786,8.60182756 15.3107693,8.67011152 15.4229501,8.96763447 C15.6375568,9.5480481 15.8472861,10.1382166 16.1009122,10.7039979 C16.1448091,10.8064239 16.1496865,10.9234821 16.0618928,11.0161532 C16.0375057,11.0210306 16.0131186,11.0210306 15.9887315,11.0210306 C15.8765507,10.7576496 15.7692473,10.4893912 15.6570665,10.2211328 C15.5107438,9.93824214 15.4083178,9.63584176 15.2815048,9.34319624 C15.2376079,9.23589287 15.1985885,9.07981526 15.0522658,9.09444754 C14.9156978,9.10907981 14.8815559,9.25540258 14.8425365,9.37246079 C14.8279042,9.41148019 14.8132719,9.45537702 14.7791299,9.48464157 C14.7401105,9.63096434 14.7303557,9.79191938 14.5840329,9.88946789"
                id="Fill-51"
                fill={props?.style?.color || '#E9A57B'}
              />
              <path
                d="M62.827136,9.54853585 C62.7247101,9.45586476 62.597897,9.49488417 62.4759614,9.49000674 C61.7443476,9.47049704 61.0127337,9.49976159 60.2859973,9.54853585 C60.9249401,9.39245823 61.5785151,9.309542 62.241845,9.36319368 C62.7881166,9.41196793 63.3343883,9.49488417 63.8806599,9.55341327 C63.973331,9.56316812 64.0464924,9.5973101 64.1001441,9.68022634 C63.6709306,9.69485861 63.2612269,9.49976159 62.827136,9.54853585"
                id="Fill-53"
                fill={props?.style?.color || '#F7D4BA'}
              />
              <g id="group" transform="translate(143.766061, 28.566226)">
                <mask id="mask-8" fill="white">
                  <use xlinkHref="#path-7" />
                </mask>
                <g id="Clip-56" />
                <path
                  d="M0.0317032656,3.30726142 C1.63637625,3.30726142 3.24104923,3.30726142 4.84572221,3.30726142 C4.77256082,3.3999325 4.66525746,3.37066795 4.56770895,3.37066795 C3.15325557,3.37066795 1.73392476,3.37066795 0.314593943,3.37066795 C0.217045434,3.37066795 0.114619499,3.39505508 0.0317032656,3.30726142"
                  id="Fill-55"
                  fill={props?.style?.color || '#FAE4D2'}
                  mask="url(#mask-8)"
                />
              </g>
              <g id="group" transform="translate(140.897647, 27.981179)">
                <mask id="mask-10" fill="white">
                  <use xlinkHref="#path-9" />
                </mask>
                <g id="Clip-58" />
                <path
                  d="M2.10460909,2.15399476 C2.18752532,1.95889774 2.41676432,1.92475576 2.52406768,1.74429102 C2.57284194,1.65649736 2.6606356,1.68088449 2.699655,1.778433 C2.75330668,1.90036863 2.77281638,2.0271817 2.77281638,2.16374961 C2.76793896,2.71977611 2.77281638,3.27092519 2.77281638,3.82207427 C2.63137104,3.578203 2.72404213,3.30506717 2.70940985,3.04656362 C2.68990015,2.74904067 2.70453242,2.45151771 2.699655,2.14911733 C2.699655,2.07107852 2.70453242,1.98816229 2.60698392,1.96377516 C2.52406768,1.94426546 2.42164175,1.95889774 2.40700947,2.04181397 C2.36311264,2.25154327 2.2216673,2.17350446 2.10460909,2.15399476"
                  id="Fill-57"
                  fill={props?.style?.color || '#FAE4D2'}
                  mask="url(#mask-10)"
                />
              </g>
              <path
                d="M14.7791299,9.48464157 C14.7888848,9.40660277 14.7791299,9.32368653 14.8083945,9.26028 C14.8669236,9.13834437 14.8766784,8.95787962 15.066898,8.9530022 C15.2522402,8.9530022 15.2473628,9.12371209 15.2912596,9.23589287 C15.4180727,9.56268038 15.59366,9.87483561 15.6570665,10.2211328 C15.4619695,10.0894423 15.4083178,9.86020334 15.3351564,9.66998374 C15.1400594,9.13834437 15.1546917,9.12858951 14.7791299,9.48464157"
                id="Fill-59"
                fill={props?.style?.color || '#FAE2CA'}
              />
              <path
                d="M14.5840329,9.88946789 C14.6474394,9.75289997 14.7157234,9.62120949 14.7791299,9.48464157 C14.8132719,9.83093878 14.6132975,10.0894423 14.4523424,10.3577007 C14.3986907,10.177236 14.5645232,10.0504229 14.5840329,9.88946789"
                id="Fill-61"
                fill={props?.style?.color || '#FAE2CA'}
              />
              <g id="group" transform="translate(135.419323, 28.754264)">
                <mask id="mask-12" fill="white">
                  <use xlinkHref="#path-11" />
                </mask>
                <g id="Clip-64" />
                <path
                  d="M2.03388642,3.58355471 C2.2972674,3.49576105 2.5655258,3.46161907 2.84353905,3.46161907 C2.6045452,3.70549035 2.30702225,3.53478045 2.03388642,3.58355471"
                  id="Fill-63"
                  fill={props?.style?.color || '#FAE4D2'}
                  mask="url(#mask-12)"
                />
              </g>
              <g id="group" transform="translate(134.279956, 28.801868)">
                <mask id="mask-14" fill="white">
                  <use xlinkHref="#path-13" />
                </mask>
                <g id="Clip-66" />
                <path
                  d="M1.96804118,3.53838928 L2.90938429,3.53838928 C2.59722906,3.64081521 2.28019641,3.64081521 1.96804118,3.53838928"
                  id="Fill-65"
                  fill={props?.style?.color || '#FAE4D2'}
                  mask="url(#mask-14)"
                />
              </g>
              <g id="group" transform="translate(137.156174, 28.597916)">
                <mask id="mask-16" fill="white">
                  <use xlinkHref="#path-15" />
                </mask>
                <g id="Clip-68" />
                <path
                  d="M2.10217038,3.47066874 C2.31189967,3.35361053 2.53138382,3.26581687 2.77525509,3.27557172 C2.5655258,3.38775251 2.34604165,3.47554617 2.10217038,3.47066874"
                  id="Fill-67"
                  fill={props?.style?.color || '#FAE4D2'}
                  mask="url(#mask-16)"
                />
              </g>
              <path
                d="M65.5658104,10.0177442 C65.3219391,10.027499 65.102455,9.93970537 64.8976031,9.81776973 C65.1414744,9.81289231 65.3560811,9.91044082 65.5658104,10.0177442"
                id="Fill-69"
                fill={props?.style?.color || '#F7D4BA'}
              />
              <g id="group" transform="translate(133.109374, 28.748981)">
                <mask id="mask-18" fill="white">
                  <use xlinkHref="#path-17" />
                </mask>
                <g id="Clip-72" />
                <path
                  d="M2.13387364,3.46251272 C2.34848036,3.44300302 2.55333223,3.477145 2.74355183,3.58932578 C2.52894511,3.59420321 2.31921581,3.58932578 2.13387364,3.46251272"
                  id="Fill-71"
                  fill={props?.style?.color || '#FAE4D2'}
                  mask="url(#mask-18)"
                />
              </g>
              <path
                d="M64.8966276,9.82020845 C64.7015306,9.8299633 64.5259433,9.77631162 64.3601108,9.68851796 C64.5405756,9.71778251 64.7600597,9.6007243 64.8966276,9.82020845"
                id="Fill-73"
                fill={props?.style?.color || '#F7D4BA'}
              />
              <path
                d="M60.2172256,9.55585199 C60.0513932,9.65827792 59.8709284,9.69729732 59.6758314,9.68266505 C59.831909,9.52658743 60.027006,9.55585199 60.2172256,9.55585199"
                id="Fill-75"
                fill={props?.style?.color || '#F7D4BA'}
              />
              <g id="group" transform="translate(132.109989, 28.682486)">
                <mask id="mask-20" fill="white">
                  <use xlinkHref="#path-19" />
                </mask>
                <g id="Clip-78" />
                <path
                  d="M2.20459631,3.39097583 C2.37530621,3.38122098 2.53138382,3.41048553 2.67282916,3.52266632 C2.50699669,3.50315662 2.31189967,3.59095028 2.20459631,3.39097583"
                  id="Fill-77"
                  fill={props?.style?.color || '#FAE4D2'}
                  mask="url(#mask-20)"
                />
              </g>
              <path
                d="M82.1441796,12.7281295 C82.1441796,16.6837216 82.1441796,20.6344362 82.1393022,24.5900283 C82.1393022,25.0777708 82.149057,25.5752682 82.2758701,26.0435011 C82.5051091,26.8629085 83.0172388,27.4725867 83.8268914,27.7847419 C84.0610078,27.8725356 84.2804919,28.0042261 84.5487503,28.0188584 C84.7633571,28.0286132 84.8706604,28.1700586 84.865783,28.404175 C84.8560282,29.6186539 84.8560282,30.8331329 84.8609056,32.0427344 C84.865783,32.2622185 84.7828668,32.3451348 84.5682601,32.3404595 C82.7879998,32.2817282 81.071146,31.9646956 79.627428,30.8331329 C78.7933883,30.1893127 78.1983424,29.3601504 77.827658,28.3407685 C77.437464,27.2531026 77.3447929,26.1312947 77.3447929,24.999732 C77.3301606,16.9373477 77.3350381,8.87496339 77.3350381,0.812579084 C77.3350381,0.129549491 77.3350381,0.129549491 77.9934905,0.129549491 C79.2421114,0.134616943 80.4907324,0.144371794 81.7344759,0.129549491 C82.0515085,0.124862092 82.149057,0.212655751 82.149057,0.534565832 C82.1393022,4.59746125 82.1393022,8.66523409 82.1393022,12.7281295 L82.1441796,12.7281295 Z"
                id="Fill-79"
                fill={props?.style?.color || '#FF4D00'}
              />
              <path
                d="M21.0641804,18.1698731 C20.6642315,18.4820283 20.3764634,18.9014869 20.0204113,19.2526616 C19.844824,19.4233715 19.6692367,19.5892039 19.4595074,19.7111396 C19.4692623,19.4819006 19.6643593,19.3697198 19.7960498,19.2282744 C20.1277147,18.8575901 20.4886442,18.5161703 20.8446963,18.1649957 C20.9081028,18.1015892 20.9958964,17.9455115 21.0641804,18.1698731"
                id="Fill-81"
                fill={props?.style?.color || '#2CA6C4'}
              />
              <path
                d="M19.6711877,23.3214099 C19.2175871,24.0042495 18.7591091,24.6822116 18.3152634,25.3699286 C17.5251205,26.6087947 16.7593647,27.862293 16.052138,29.1499333 C15.7936344,29.6230436 15.5107438,30.086399 15.2571176,30.5595093 C15.1156723,30.8228903 14.9986141,30.7692386 14.8815559,30.5692641 C14.5596458,30.0132376 14.2328583,29.4572111 13.9353353,28.8963072 C13.4719799,28.0134932 12.9257083,27.1794534 12.418456,26.325904 C12.0965459,25.7942646 11.764881,25.2723801 11.4283386,24.755373 C11.3210353,24.5846631 11.3161578,24.4285855 11.3893192,24.2481207 C11.7453713,23.4287133 12.1063008,22.6093058 12.4477206,21.7801434 C12.5599013,21.511885 12.6623273,21.5411496 12.8184049,21.7167369 C13.3500443,22.3459248 13.8816836,22.9702353 14.413323,23.5994231 C14.5742781,23.7847653 14.744988,23.9603526 14.8913107,24.1554497 C15.0230012,24.3456692 15.1059174,24.3505467 15.2571176,24.1652045 C15.8863055,23.3750616 16.5301257,22.5946735 17.2080878,21.84355 C17.7153401,21.2875235 18.20796,20.7071098 18.7249671,20.1559608 C19.7345942,19.0780497 20.7881181,18.0391581 21.8660291,17.0246536 C21.944068,16.9514922 22.0416165,16.8929631 22.1294101,16.8246792 C22.2318361,16.9905116 22.0903907,17.0831827 22.0074745,17.1563441 C21.8026226,17.3514411 21.646545,17.5953124 21.4026737,17.7562674 C21.3295123,17.8099191 21.4709577,17.7806546 21.4709577,17.8294288 C21.4563254,18.0684227 21.2661058,18.1269518 21.0953959,18.2001132 C21.0173571,18.1854809 20.9441957,18.175726 20.8807892,18.2391326 C20.4174338,18.7122428 19.9199364,19.1463337 19.5248649,19.6779731 C19.5053552,19.7072376 19.5004778,19.7413796 19.4858455,19.7706442 C19.2468517,20.1510833 18.9493287,20.4778708 18.6127864,20.7705164 C18.2177149,21.1168136 17.9006822,21.5216399 17.622669,21.970363 C17.4763462,22.2093569 17.2275975,22.3849442 17.0325005,22.5995509 C16.5301257,23.1604549 16.1204219,23.804275 15.5643954,24.3212821 C15.4278275,24.4432178 15.3058919,24.5553985 15.1254271,24.5992954 C14.905943,24.6529471 14.7547428,24.6139276 14.6181749,24.4090758 C14.28651,23.9213332 13.9109482,23.4628552 13.4329605,23.1068032 C13.2720055,22.9848675 13.1598247,22.8190351 13.0817859,22.6288155 C12.9500954,22.3069054 12.7306112,22.2825183 12.5111271,22.5507767 C12.4721077,22.6044284 12.4233334,22.65808 12.4135786,22.7214866 C12.3355398,23.184842 12.0672814,23.5604037 11.8819392,23.9701075 C11.6527002,24.4724823 11.730739,24.8334118 12.155075,25.1894639 C12.3599269,25.3650512 12.48674,25.5601482 12.5647788,25.8186517 C12.6915918,26.2234781 12.9354631,26.5795301 13.1647021,26.9258273 C13.7890126,27.8574156 14.2962648,28.8524104 14.8961881,29.7937535 C15.0230012,29.9888505 15.1303046,29.9742182 15.2571176,29.8181406 C15.3595436,29.696205 15.4278275,29.5547597 15.4814792,29.4133143 C15.6034148,29.1011591 15.822899,28.8329007 15.9448346,28.5451326 C16.1301768,28.0964094 16.5057386,27.7891316 16.6813259,27.3599182 C16.8910552,26.8477885 17.2129652,26.4088202 17.5202431,25.9649745 C17.5592625,25.9064454 17.6031593,25.8625486 17.6665658,25.833284 C18.0518824,25.6625741 18.0957793,25.2333607 18.3347731,24.9553474 C18.4030571,24.8724312 18.5445024,24.789515 18.437199,24.6431922 C18.4420765,24.4627275 18.5249927,24.3456692 18.6908252,24.2920176 C18.998103,24.1164302 19.1444257,23.8286621 19.2517291,23.5116295 C19.2712388,23.414081 19.3151356,23.3311647 19.4126841,23.2970228 C19.5004778,23.2823905 19.5882715,23.2482485 19.6711877,23.3214099"
                id="Fill-83"
                fill={props?.style?.color || '#2DA6C7'}
              />
              <path
                d="M21.4641293,17.8391837 C21.6982457,17.7367577 21.7567748,17.488009 21.8933427,17.3026669 C22.1128269,17.0197762 22.4444918,16.9124728 22.7322599,16.7368855 C23.0980668,16.5076465 23.3370607,16.1320848 23.6297062,15.8248069 C24.0881842,15.3419418 24.7027398,15.0785608 25.1953598,14.6493474 C25.4197213,14.4591278 25.6245732,14.6249603 25.7318766,14.839567 C25.8391799,15.0444189 25.9025865,15.2736579 25.9708704,15.4980194 C26.0245221,15.6784842 25.9123413,15.8052972 25.7757734,15.8784586 C25.575799,15.9760071 25.4294762,16.112575 25.3172954,16.2930398 C25.1075661,16.6198273 24.7320044,16.7759049 24.4978879,17.0587956 C23.9516163,17.7270029 23.3029187,18.2976617 22.756647,18.9512367 C22.400595,19.3658178 21.9713815,19.7316247 21.6933683,20.2242447 C21.415355,20.7119873 20.991019,21.1119362 20.6691089,21.5752916 C20.405728,21.9606082 20.0935727,22.2922731 19.7960498,22.6434478 C19.6399722,22.8287899 19.5277914,23.0726612 19.4595074,23.3214099 C19.3717138,23.3555519 19.3278169,23.4238358 19.3278169,23.5165069 C19.1229651,23.7798879 19.030294,24.1310625 18.7278936,24.3261595 C18.5766934,24.3846886 18.5181643,24.5212566 18.4596352,24.6529471 C18.5279192,24.8187795 18.3864738,24.9016957 18.3181899,24.9992443 C18.1279703,25.2918898 18.0060346,25.6284321 17.723144,25.8625486 C17.4304984,26.25762 17.0988335,26.6234269 16.9037365,27.0867824 C16.7525363,27.4330796 16.6354781,27.8183962 16.2599163,28.0134932 C16.2306518,28.0281255 16.2013872,28.0622675 16.1965098,28.0866546 C16.1379807,28.7255973 15.557567,29.1157914 15.4161217,29.7205921 C15.3771023,29.8864246 15.2551667,30.052257 15.0503148,30.0571345 C14.8259532,30.0668893 14.8503403,29.8522826 14.7820564,29.7254695 C14.5869594,29.3499078 14.3333332,29.0182428 14.0943394,28.6719456 C13.8797327,28.3646678 13.7724293,28.0037384 13.5529452,27.6964605 C13.2310351,27.24286 12.9286347,26.7648723 12.6945183,26.25762 C12.450647,25.7259807 12.0750853,25.2967672 11.7531752,24.8187795 C11.4654071,24.3895661 11.7580526,23.9310881 12.0019239,23.5555263 C12.1384918,23.345797 12.2018983,23.1263129 12.2604274,22.9019513 C12.2994468,22.760506 12.3433437,22.6239381 12.4262599,22.5166347 C12.5238084,22.3898216 12.6213569,22.2191117 12.8262088,22.2581311 C13.0115509,22.2971505 13.1188543,22.4044539 13.1578737,22.6141832 C13.1920157,22.7946479 13.3383385,22.9507256 13.4944161,23.0580289 C13.9919135,23.3994487 14.3284558,23.8823138 14.6893853,24.3407918 C14.8600952,24.5651534 15.2161473,24.5895405 15.4209991,24.3700564 C16.0209225,23.7359911 16.5232973,23.0190095 17.1183432,22.3800668 C17.4792727,21.9898727 17.7572859,21.5509045 18.0840734,21.1460781 C18.2791705,20.9022069 18.5669386,20.7217421 18.7864227,20.4827483 C19.0156617,20.2339996 19.2351458,19.9706186 19.4595074,19.712115 C19.8935983,19.2438822 20.3471989,18.7951591 20.7812897,18.3269262 C20.8593285,18.24401 20.9373673,18.1610937 21.0641804,18.1708486 C21.1958709,18.0586678 21.3617034,17.9903839 21.4641293,17.8391837"
                id="Fill-85"
                fill={props?.style?.color || '#01A9CE'}
              />
              <path
                d="M17.7250949,25.8601099 C17.9689662,25.4552835 18.2177149,25.0553347 18.4615862,24.6553858 C18.5103604,24.6748955 18.6518058,24.6505083 18.5981541,24.7187923 C18.2957537,25.0845992 18.1640632,25.5772192 17.7787466,25.8796196 C17.7738692,25.8893744 17.7446046,25.8649873 17.7250949,25.8601099"
                id="Fill-87"
                fill={props?.style?.color || '#2CA6C4'}
              />
              <path
                d="M18.7278936,24.3237208 C18.9766423,24.0896044 19.0254166,23.7091652 19.3278169,23.5140682 C19.2887975,23.88963 18.9571326,24.0993593 18.8156873,24.4163919 C18.7913001,24.4602887 18.7376484,24.3773725 18.7278936,24.3237208"
                id="Fill-89"
                fill={props?.style?.color || '#2CA6C4'}
              />
              <path
                d="M98.7074288,9.61974626 C99.0049517,9.75143675 99.3366166,9.66852051 99.6438944,9.75143675 C99.6877913,9.75143675 99.7316881,9.75631417 99.7658301,9.75631417 C100.229185,9.81484328 100.653522,10.02945 101.116877,10.0831017 C101.24369,10.1318759 101.37538,10.1757728 101.507071,10.224547 C101.663149,10.292831 101.814349,10.3708698 101.975304,10.4293989 C102.580105,10.7415541 103.184905,11.0634642 103.716545,11.4926776 C104.053087,11.668265 104.214042,11.5755939 104.214042,11.1951547 C104.21892,10.9415286 104.21892,10.683025 104.214042,10.4293989 C104.209165,10.1708953 104.287203,10.0489597 104.579849,10.0538371 C105.911386,10.063592 107.247801,10.063592 108.584215,10.0489597 C108.876861,10.0489597 108.945145,10.1708953 108.959777,10.419644 C108.969532,10.6781476 108.925635,10.9317737 108.925635,11.1853998 C108.925635,14.4045006 108.911003,17.618724 108.930513,20.8329474 L108.952913,23.2326408 C108.955442,23.6325896 108.956525,24.0325385 108.9549,24.4324874 C108.945145,25.7054955 108.891493,26.9882584 108.930513,28.2612664 C108.959777,29.2855258 108.945145,30.3049077 108.925635,31.3242896 C108.91588,31.7144836 108.901248,31.7339934 108.506177,31.7339934 C107.218536,31.7388708 105.926018,31.7388708 104.633501,31.7339934 C104.243307,31.7339934 104.19941,31.7047288 104.21892,31.3194122 C104.253062,30.8511793 104.194532,30.3878239 104.243307,29.9244685 C104.257939,29.8122877 104.253062,29.7001069 104.1799,29.6025584 C104.057964,29.5050099 103.940906,29.3977066 103.784829,29.5342745 C103.526325,29.7196166 103.316596,29.9586105 103.106867,30.1927269 C102.926402,30.2756431 102.799589,30.4219659 102.648389,30.5341467 L102.388666,30.7164405 C102.300872,30.7755792 102.20942,30.8292309 102.106994,30.8658116 C101.204671,31.4901221 100.180411,31.7973999 99.1317648,31.9924969 C97.5075821,32.2900199 95.878522,32.2558779 94.2836038,31.7827676 C92.9081699,31.3730639 91.6497941,30.7048566 90.5767605,29.7293715 C90.5084765,29.6708424 90.4499474,29.5928036 90.3523989,29.597681 C89.3427718,28.6953573 88.5672612,27.6125688 87.9527056,26.4175996 C87.3479048,25.2518949 86.9723431,24.0081514 86.7821235,22.696124 C86.6309233,21.6864969 86.6260459,20.6768698 86.7040847,19.6721202 C86.7723686,18.7063899 86.9918528,17.7552919 87.33815,16.8334585 C87.7819957,15.6189796 88.4063062,14.5020492 89.2208362,13.5070544 C89.9768372,12.5998532 90.8547737,11.8243426 91.8644008,11.1951547 C92.932557,10.5318248 94.078752,10.0831017 95.2932309,9.79533358 C95.4249214,9.7611916 95.5566119,9.76606902 95.6883024,9.75143675 C96.693052,9.61974626 97.6929243,9.51732032 98.7074288,9.61974626 Z M98.4730123,13.7487926 L98.0992138,13.7509256 C97.2602966,13.7606805 96.4652763,13.9021258 95.6897656,14.2337908 C94.7484225,14.6386171 94.0021764,15.2775598 93.3242143,16.0189285 C92.9437751,16.4383871 92.6511295,16.9163748 92.3731163,17.4041173 C92.0219417,18.0381826 91.8463543,18.730777 91.7000316,19.4282489 C91.5585862,20.0915787 91.6073605,20.7792957 91.6219928,21.4572579 C91.6317476,21.7986777 91.6122379,22.1596071 91.7244187,22.4766398 C91.9097609,23.0034018 92.0121868,23.5545508 92.2609355,24.0666805 C92.6364973,24.8617009 93.0998527,25.5835598 93.7192857,26.1834832 C94.7532999,27.1882328 95.9726563,27.8613175 97.4456388,27.9783757 C97.8065683,28.0076403 98.1626203,28.1051888 98.5333047,28.0417823 C98.8308276,27.9881306 99.1283506,27.9491112 99.4258735,27.9344789 C100.230649,27.9003369 100.962263,27.6125688 101.64998,27.2516393 C102.57669,26.7687742 103.298549,26.0225281 103.854576,25.1348367 C103.976511,24.9348622 104.05455,24.7300104 104.05455,24.4763842 C104.039918,23.052176 104.049673,21.6279678 104.049673,20.2037595 C104.049673,18.8819772 104.035041,17.5553175 104.05455,16.2286578 C104.064305,15.7409152 103.947247,15.4336374 103.498524,15.1507467 C102.61571,14.5898428 101.669489,14.2240359 100.679372,13.9850421 C99.8404547,13.7850676 98.972273,13.7362934 98.0992138,13.7509256 Z"
                id="shape-1merge"
                fill={props?.style?.color || '#FF4D00'}
              />
              <path
                d="M104.177949,29.6040216 C104.348659,29.6674282 104.329149,29.8186284 104.304762,29.9454414 C104.207214,30.4039194 104.353536,30.8672748 104.255988,31.3257528 C104.202336,31.579379 104.324272,31.6769275 104.59253,31.6769275 C105.904558,31.6671726 107.221463,31.6671726 108.53349,31.6769275 C108.767607,31.6769275 108.865155,31.6281532 108.865155,31.3647722 C108.870032,30.559997 108.952949,29.7503444 108.909052,28.9504466 C108.8554,28.07251 108.845645,27.1945735 108.870032,26.3166369 C108.909052,25.1119128 108.952949,23.9071887 108.918807,22.6975872 C108.821258,18.912705 108.879787,15.1229454 108.865155,11.3331858 C108.865155,11.0454177 108.913929,10.7576496 108.923684,10.4747589 C108.928562,10.2162554 108.831013,10.0796875 108.528613,10.0845649 C107.236095,10.0991972 105.9387,10.0991972 104.651059,10.0845649 C104.334027,10.0796875 104.236478,10.2016231 104.251111,10.4942686 C104.260865,10.762527 104.251111,11.025908 104.251111,11.2941664 C104.255988,11.4453666 104.197459,11.5624248 104.056014,11.6111991 C103.919446,11.6550959 103.787755,11.6453411 103.714594,11.4941409 C104.085278,11.5965668 104.177949,11.5282828 104.182827,11.1478437 C104.187704,10.8698304 104.187704,10.5918172 104.182827,10.3138039 C104.177949,10.1235843 104.241356,10.0211584 104.446208,10.0211584 C105.870416,10.0260358 107.299501,10.0211584 108.72371,10.0211584 C108.923684,10.0211584 108.996846,10.1138295 108.996846,10.3040491 C108.991968,10.5723075 108.991968,10.8405659 108.991968,11.1088243 L108.991968,30.9160491 L108.991968,31.315998 C108.987091,31.7598437 108.952949,31.7988631 108.523735,31.7988631 C107.309256,31.7988631 106.094777,31.8037405 104.880298,31.7988631 C104.124297,31.7939857 104.182827,31.9159213 104.182827,31.1062687 C104.182827,30.6087713 104.177949,30.1063965 104.177949,29.6040216"
                id="Fill-93"
                fill={props?.style?.color || '#D58D5D'}
              />
              <path
                d="M101.115414,10.0821262 C100.627671,10.126023 100.21309,9.86751947 99.7692443,9.75533869 C100.115541,9.72119671 100.413064,9.88702918 100.739852,9.95531313 C100.85691,9.97970026 100.978846,10.0040874 101.095904,10.0333519 C101.105659,10.0479842 101.110536,10.0674939 101.115414,10.0821262"
                id="Fill-95"
                fill={props?.style?.color || '#D58D5D'}
              />
              <path
                d="M103.106379,30.1946779 C103.262456,29.9020323 103.472186,29.6679159 103.784341,29.5362254 C103.579489,29.7800967 103.36976,30.0142131 103.106379,30.1946779"
                id="Fill-97"
                fill={props?.style?.color || '#D58D5D'}
              />
              <g id="edit" transform="translate(99.938003, 27.962768)">
                <mask id="mask-22" fill="white">
                  <use xlinkHref="#path-21" />
                </mask>
                <g id="Clip-100" />
                <path
                  d="M2.16801562,2.90353138 C2.33872551,2.77671832 2.4899257,2.62551813 2.70940985,2.57186645 C2.57284194,2.75233119 2.40700947,2.88889911 2.16801562,2.90353138"
                  id="Fill-99"
                  fill={props?.style?.color || '#D58D5D'}
                  mask="url(#mask-22)"
                />
              </g>
              <path
                d="M101.972865,10.4274479 C101.782646,10.4469576 101.6412,10.3494091 101.504632,10.222596 C101.689974,10.222596 101.846052,10.3006349 101.972865,10.4274479"
                id="Fill-101"
                fill={props?.style?.color || '#D58D5D'}
              />
              <path
                d="M46.7872346,19.9798857 C46.3141243,20.9846354 45.8897883,22.0137721 45.4313103,23.0282766 C45.0459937,23.8818261 44.6557997,24.7353755 44.2753605,25.588925 C43.8851664,26.4766164 43.4461981,27.3447982 43.1047784,28.2519993 C42.8267651,28.7007225 42.6560552,29.2030973 42.4316937,29.6762075 C42.1536804,30.266376 41.8805446,30.8565445 41.622041,31.4564678 C41.5440022,31.6271777 41.4318214,31.675952 41.2611115,31.675952 C40.6026591,31.6710746 39.9442067,31.6564423 39.2906316,31.6857068 C38.5346307,31.7149714 37.7786298,31.6661971 37.0323837,31.6905843 C36.6665767,31.7003391 36.5153766,31.5393841 36.3934409,31.2564934 C35.832537,29.9834853 35.2716331,28.7153547 34.7058517,27.4423467 C34.2571286,26.4278422 33.803528,25.4133377 33.3499274,24.3988332 C32.8475526,23.2819028 32.3500552,22.1649723 31.8476804,21.0431645 C31.3892024,20.02866 30.9404792,19.009278 30.4771238,17.999651 C30.0576652,17.0778175 29.677226,16.131597 29.2236255,15.2243959 C28.9309799,14.6488597 28.7212506,14.0343041 28.4529922,13.4441356 C27.9750046,12.4003665 27.4921394,11.3614749 27.0385389,10.3030736 C26.9458678,10.0884668 27.0092743,10.0835894 27.1799842,10.0835894 C28.7358829,10.0835894 30.2966591,10.0835894 31.8525578,10.078712 C32.0183903,10.078712 32.1061839,10.1372411 32.1744679,10.2981961 C32.5500296,11.1273585 32.8670623,11.9711531 33.2426241,12.795438 C33.3694371,13.0734512 33.4767405,13.3612193 33.6279407,13.6294777 C33.735244,14.0879557 33.9693605,14.492782 34.1449478,14.9171181 C34.5546515,15.8828483 34.9594778,16.8485786 35.3691816,17.8143088 C35.6910916,18.5605549 36.0227566,19.3116784 36.3446667,20.0628019 C36.8811835,21.3211777 37.4274551,22.5795535 37.9493396,23.8428067 C38.2224755,24.4963817 38.5541404,25.1255696 38.7833794,25.7937769 C38.8272762,25.9157125 38.9248247,26.0425256 39.0662701,26.0425256 C39.2174703,26.0425256 39.3101413,25.9157125 39.3589156,25.7937769 C39.8173936,24.706111 40.2612393,23.6184451 40.7197173,22.5307792 C41.1050339,21.6235781 41.5049828,20.7261318 41.9049317,19.8238081 C41.953706,19.7213822 41.9878479,19.6238336 41.9732157,19.5067754 C42.3877968,18.5946969 42.8316425,17.6923732 43.2072043,16.7607849 C43.6803146,15.5804479 44.2070765,14.4196207 44.6996965,13.2490386 C45.1094002,12.268676 45.5337363,11.2980684 45.9336851,10.3177058 C46.0166014,10.1177314 46.1336596,10.0348152 46.3238792,10.0348152 C47.8358811,10.0348152 49.347883,10.0348152 50.8598849,10.0396926 C50.9720657,10.0396926 51.079369,10.0738346 51.1915498,10.0982217 C50.5135877,11.717527 49.7673416,13.3075677 49.0357277,14.9024858 C48.5333529,15.9950291 48.0699975,17.1022047 47.5871324,18.1996254 C47.3237514,18.7946713 47.055493,19.3848398 46.7872346,19.9798857"
                id="Fill-103"
                fill={props?.style?.color || '#FF4D00'}
              />
              <path
                d="M139.307118,9.71485606 C140.526475,9.9148305 141.658037,10.3391665 142.721316,10.963477 C142.911536,11.1683288 143.140775,11.3244065 143.379769,11.4707292 C143.633395,11.6219294 143.784595,11.5292583 143.79435,11.2366128 C143.804105,10.9244576 143.799227,10.6123023 143.79435,10.3001471 C143.789472,10.1050501 143.862634,10.0172564 144.067486,10.0221339 C145.491694,10.0221339 146.915902,10.0221339 148.34011,10.0172564 C148.544962,10.0172564 148.618124,10.1050501 148.613246,10.3001471 C148.603491,10.4659796 148.613246,10.6366895 148.613246,10.8025219 C148.613246,17.6553047 148.608369,24.5129649 148.613246,31.3657477 C148.618124,31.7022901 148.525453,31.804716 148.184033,31.7998386 C146.852496,31.7852063 145.511204,31.7900837 144.179666,31.804716 C143.887021,31.804716 143.784595,31.7217998 143.79435,31.4242768 C143.81386,30.9267794 143.799227,30.4244046 143.799227,29.9220298 C143.799227,29.775707 143.799227,29.6245068 143.633395,29.5513455 C143.467562,29.4733066 143.370014,29.5952423 143.267588,29.6976682 C142.711561,30.2780818 142.082373,30.7707018 141.365392,31.1316313 C140.107016,31.7754515 138.765724,32.1266261 137.351271,32.1949101 C136.014856,32.2583166 134.707706,32.1168713 133.444453,31.677903 C132.761613,31.4437865 132.112916,31.1365087 131.493483,30.760947 C131.395934,30.6975404 131.283753,30.6487662 131.181328,30.5902371 C129.961971,29.7123005 128.893815,28.6929186 128.079285,27.4199105 C127.191594,26.0298443 126.645322,24.5227198 126.362431,22.908292 C126.001502,20.879283 126.191721,18.8892934 126.855051,16.9578329 C127.59642,14.8020108 128.88406,13.026628 130.713095,11.6560714 C132.308013,10.4611022 134.11266,9.79289486 136.08314,9.58316557 C137.151296,9.47098478 138.234085,9.53439131 139.307118,9.71485606 Z M137.871692,13.7523889 C137.188853,13.6792275 136.515768,13.7962857 135.88658,14.0108924 C134.535533,14.4693704 133.428357,15.2887779 132.574808,16.4544826 C131.867581,17.4153354 131.443245,18.488369 131.257903,19.6443188 C131.145722,20.3466681 131.13109,21.0587722 131.218884,21.7708763 C131.262781,22.1122961 131.257903,22.3788203 131.370084,22.7853808 C131.399348,23.0877812 131.531039,23.3462848 131.652975,23.624298 C131.960252,24.3705441 132.330937,25.068016 132.862576,25.6923264 C134.135584,27.1945735 135.750012,28.0091035 137.710737,28.0481229 C138.613061,28.0627552 139.510507,27.9652067 140.373811,27.6432966 C141.13469,27.3604059 141.807774,26.960457 142.407698,26.4190628 C142.885685,25.9849719 143.23686,25.4484551 143.59779,24.9216932 C143.622177,24.6144154 143.661196,24.3022602 143.661196,23.9949824 C143.670951,22.8244002 143.666074,21.6586956 143.666074,20.4881134 C143.666074,19.0639052 143.670951,17.639697 143.661196,16.2154887 C143.656319,15.8691915 143.695338,15.4741201 143.314899,15.2692682 C142.817401,15.0010098 142.344291,14.6790997 141.807774,14.4742478 C141.007877,14.1718475 140.178714,13.9767504 139.33492,13.8255502 C138.847177,13.742634 138.359435,13.8011631 137.871692,13.7523889 Z"
                id="shape-1merge"
                fill={props?.style?.color || '#FF4D00'}
              />
              <path
                d="M27.3780077,31.7305792 C26.6317616,31.7305792 25.8855155,31.7257017 25.1441468,31.7354566 C24.9490498,31.7354566 24.8515013,31.6769275 24.7734625,31.4867079 C24.2223134,30.1502933 23.6516546,28.8187561 23.0858733,27.487219 C22.7444535,26.6824438 22.4127885,25.8776686 22.0616139,25.0826482 C21.9738202,24.8924286 22.0030848,24.7558607 22.120143,24.5997831 C22.80786,23.6730723 23.5541061,22.7902583 24.3052296,21.9171991 C24.6125074,21.5660245 24.9539272,21.2441144 25.2465727,20.8783075 C25.4514246,20.6198039 25.5294634,20.7661267 25.6026248,20.9514689 C25.8221089,21.468476 26.0269608,21.9952379 26.246445,22.5171224 C26.6610261,23.5023624 27.0902396,24.4827249 27.5048207,25.4728423 C28.0657247,26.8141343 28.6071189,28.1700586 29.1631454,29.5113506 C29.4021392,30.0966416 29.6508879,30.6770553 29.9045141,31.2525915 C30.075224,31.6379081 30.0313271,31.7305792 29.6118685,31.7305792 C28.8704999,31.7354566 28.1242538,31.7305792 27.3780077,31.7305792 Z"
                id="Fill-107"
                fill={props?.style?.color || '#FF4D00'}
              />
              <path
                d="M98.7054778,9.620234 C98.2860192,9.620234 97.8616832,9.64462113 97.447102,9.620234 C96.8520561,9.57633717 96.2862747,9.85922785 95.6912288,9.75192449 C96.2472553,9.59584687 96.8130367,9.54219519 97.3885729,9.55682747 C97.8275412,9.57145975 98.2713869,9.48854351 98.7054778,9.620234"
                id="Fill-109"
                fill={props?.style?.color || '#D58D5D'}
              />
              <path
                d="M99.6438944,9.75143675 C99.4195329,9.75143675 99.2000487,9.75143675 98.9805646,9.75631417 C98.8635064,9.75631417 98.756203,9.74655932 98.7074288,9.61974626 C99.0244614,9.60023656 99.3414941,9.64901081 99.6438944,9.75143675"
                id="Fill-111"
                fill={props?.style?.color || '#D58D5D'}
              />
              <path
                d="M16.0604296,11.0181041 C16.0555522,10.5840133 15.743397,10.2523483 15.6507259,9.84752203 C15.5873193,9.5938959 15.4166094,9.37441176 15.3873449,9.10127593 C15.3629578,8.93056604 15.2654093,8.79887555 15.1044542,8.77448842 C14.9288669,8.74522387 14.7825441,8.91593376 14.7679119,9.0378694 C14.7288925,9.34026978 14.5337954,9.58414105 14.5094083,9.88654143 C14.1874982,10.6522972 13.8558333,11.418053 13.5339232,12.1838088 C13.1437292,13.1056422 12.7584126,14.0323531 12.3730959,14.9541865 C11.8414566,16.1247686 11.353714,17.3197379 10.8610941,18.5098297 C10.4709,19.4511728 10.0416866,20.3778836 9.63686026,21.3192267 C9.08083376,22.6166219 8.53943953,23.9237719 7.9980453,25.2260445 C7.89074194,25.4747932 7.73466433,25.7089097 7.69564492,25.9820455 C7.35910256,26.5673365 7.15425069,27.2111567 6.88599229,27.8257123 C6.4080046,28.9133782 5.9495266,30.0156764 5.49104861,31.1130971 C5.45690663,31.181381 5.4520292,31.2594199 5.41788723,31.3277038 C5.32033872,31.5374331 5.21303536,31.6838608 4.92526725,31.6838608 C3.43765248,31.6642462 1.95003771,31.6642462 0.45754552,31.6838608 C0.116125737,31.6886333 0.155145141,31.5228008 0.233183949,31.3277038 C0.55509403,30.5326835 0.911146089,29.7571728 1.22330132,28.957275 C1.45741774,28.3427194 1.78420525,27.7671832 1.95003771,27.1233631 C2.56459332,25.816213 3.07184557,24.4602887 3.65713663,23.133629 C4.1302469,22.055718 4.54970549,20.9485424 5.00818349,19.8559991 C5.51055831,18.6805396 5.97391373,17.4855703 6.47141113,16.3052334 C6.96890853,15.1151415 7.5005479,13.9445594 7.99316788,12.7495902 C8.40774904,11.7399631 8.85159476,10.7400909 9.25642107,9.73046382 C9.98803489,7.91606154 10.7586681,6.11629154 11.5195465,4.31652154 C11.9292502,3.3507913 12.3145668,2.38018363 12.7145157,1.40469854 C14.2118854,1.28764032 15.709255,1.33641458 17.2066246,1.39494369 C17.4651281,1.40469854 17.4846378,1.60955041 17.543167,1.75099574 C18.0991935,3.02400379 18.6503425,4.30188927 19.1673496,5.59440702 C19.5672985,6.59427924 20.0160217,7.57464176 20.420848,8.57451398 C20.8598163,9.67193471 21.3134168,10.7596006 21.7718948,11.8472665 C21.8401788,12.0130989 21.9182176,12.1691766 21.9962564,12.3301316 C22.2254954,12.7983644 22.1718437,12.9739518 21.76214,13.3007393 C21.1622167,13.7738495 20.5866804,14.2762244 20.0599185,14.8322509 C19.5477888,15.2273223 19.1088205,15.6955552 18.6454651,16.1442783 C18.4698778,16.3198656 18.3674519,16.3881496 18.2406388,16.0662395 C17.865077,15.1346512 17.4358636,14.2225727 17.0407921,13.3007393 C16.753024,12.632532 16.4750108,11.9545698 16.1921201,11.2863625 C16.1579781,11.1936915 16.1043264,11.1058978 16.0604296,11.0181041"
                id="Fill-113"
                fill={props?.style?.color || '#FF4D00'}
              />
              <path
                d="M62.3462219,9.56024167 C62.7998224,9.65291275 63.2631778,9.68217731 63.7216558,9.73582899 C65.3019417,9.91629373 66.7115177,10.5015848 67.9698934,11.4721925 C68.0674419,11.5453538 68.1406033,11.6477798 68.2869261,11.569741 C68.4186166,11.501457 68.4381263,11.3941536 68.4381263,11.2673406 C68.4381263,10.8771465 68.4430037,10.4869525 68.4430037,10.1016359 C68.4966554,10.0431068 68.5649393,10.0577391 68.6332233,10.0577391 L73.0131514,10.0577391 C73.0716805,10.0577391 73.1253321,10.0577391 73.1789838,10.0821262 L73.1887387,10.091881 C73.2277581,10.3455072 73.2472678,10.5942559 73.2472678,10.8527594 L73.2472678,30.840449 C73.2472678,31.0745654 73.2082484,31.3086819 73.2082484,31.5379208 C73.2131258,31.7086307 73.1155773,31.7720373 72.9546223,31.7720373 C71.5450463,31.7671598 70.1305929,31.7671598 68.7161395,31.7671598 C68.5161651,31.7671598 68.3991069,31.6744888 68.4039843,31.4647595 C68.4088617,31.3135593 68.3698423,31.1574817 68.4332488,31.0062815 L68.4332488,29.9966544 C68.4381263,29.9283704 68.4430037,29.8649639 68.4430037,29.79668 C68.4430037,29.6747443 68.457636,29.5381764 68.3113132,29.4894021 C68.1698679,29.4357505 68.0430548,29.499157 67.9455063,29.5918281 C67.7308996,29.7869251 67.5358026,30.0015318 67.3163184,30.1917514 C66.6432337,30.7867973 65.8433359,31.1818688 65.019051,31.528166 C64.5215536,31.8208115 63.9508948,31.8842181 63.3997458,31.9817666 C62.0145569,32.2402701 60.6196132,32.2549024 59.2344244,31.9573794 C57.9028872,31.6696113 56.6786535,31.1282171 55.561723,30.3331968 C54.5325862,29.6064604 53.6839142,28.7187689 52.9523004,27.7042644 C52.7084291,27.3628446 52.4986998,27.0067926 52.2743383,26.6556179 C52.0743638,26.1922625 51.8743894,25.7240297 51.6890472,25.2509194 C51.5134599,24.7924414 51.3525048,24.3242086 51.2549563,23.836466 C51.1622852,23.3926203 51.0305948,22.953652 50.9964528,22.5049289 C50.8940268,21.2416757 50.85013,19.9784225 51.0842464,18.7151693 C51.1525304,18.3835043 51.3037306,18.0615943 51.2695886,17.7055422 C51.4744405,17.1592706 51.6646601,16.6032441 51.8987765,16.0667272 C52.2840931,15.2034229 52.7572034,14.3937703 53.3424944,13.6524016 C54.093618,12.7110585 54.9910642,11.925793 55.9958139,11.2624632 C56.7957117,10.7308238 57.6687708,10.3601394 58.575972,10.0674939 C59.8002058,9.67242245 61.0683364,9.56024167 62.3462219,9.56024167 Z M59.0174314,14.562547 C58.8295981,14.708852 58.6540108,14.8551748 58.4686686,14.9917427 C57.2736994,15.8648018 56.5225758,17.0451388 56.0689753,18.4352051 L56.0689753,18.4352051 L56.0684875,18.4322786 C55.8587582,18.99806 55.7368226,19.5784736 55.6929258,20.1783969 C55.6392741,20.890501 55.6246418,21.6172374 55.7709646,22.3049544 C55.9758164,23.2804395 56.3123588,24.2169052 56.8732627,25.0606998 C57.8877672,26.5922114 59.2436915,27.665245 61.0580938,28.0261745 C63.2187933,28.4602654 65.23317,28.0944585 66.9792883,26.6653728 C67.9206314,25.8947396 68.5059225,24.9924159 68.4620257,23.6560013 C68.3693546,21.0417012 68.4376385,18.4225238 68.4327611,15.8082237 C68.4327611,15.6326364 68.4669031,15.4472942 68.2815609,15.3253586 C68.0523219,15.1741584 67.8328378,14.9985711 67.5938439,14.8620032 C66.4476489,14.2035507 65.1941506,13.8377438 63.8967554,13.6475242 C62.1761622,13.3989399 60.5188917,13.5887439 59.0174314,14.562547 Z"
                id="shape-1merge"
                fill={props?.style?.color || '#FF4D00'}
              />
              <path
                d="M68.4332488,31.0043305 C68.457636,31.7359443 68.3405778,31.7408217 69.1990046,31.7359443 C70.3744642,31.7261895 71.5548011,31.7310669 72.7351381,31.7310669 C73.1789838,31.7310669 73.1838613,31.7310669 73.1838613,31.2920986 C73.1838613,24.3710319 73.1838613,17.4499651 73.1838613,10.5240209 C73.1838613,10.3825756 73.1838613,10.2362528 73.1887387,10.0899301 C73.2814098,10.3045368 73.3106743,10.5337758 73.3106743,10.7678922 C73.3106743,17.4841071 73.3106743,24.200322 73.3106743,30.9214143 C73.3106743,31.1506533 73.2716549,31.3847697 73.2423904,31.6140087 C73.2277581,31.7749637 73.1155773,31.8042283 72.9838868,31.8042283 C71.5352914,31.8042283 70.0866961,31.8042283 68.6429781,31.8091057 C68.4381263,31.8091057 68.3503326,31.7164346 68.3747197,31.5213376 C68.3942294,31.3506277 68.3113132,31.1652855 68.4332488,31.0043305"
                id="Fill-117"
                fill={props?.style?.color || '#EFA67A'}
              />
              <path
                d="M73.1775206,10.0821262 C71.5972347,10.0870036 70.0218263,10.091881 68.4415405,10.1016359 C68.504947,9.98945511 68.6171278,10.0187197 68.7097989,10.0187197 C70.1193748,10.0187197 71.5240734,10.0187197 72.9287719,10.0187197 C73.0165656,10.0187197 73.1092366,9.99920996 73.1775206,10.0821262"
                id="Fill-119"
                fill={props?.style?.color || '#EFA67A'}
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const QuickbookSettingIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 120 80'}
    >
      <g id="logo" stroke="none" strokeWidth="1" fill="currentColor" fillRule="evenodd">
        <path
          d="M0.008,58.9507125 C0.008,56.6594207 0.008,54.3458833 0.008,52.0545915 C0.0747366557,51.8321359 0.163718863,51.631926 0.185964415,51.4094704 C0.36392883,47.6944633 0.986804284,44.0684383 2.09908188,40.5536411 C2.8776762,38.1511215 3.58953386,35.7041108 4.56833814,33.3683279 C6.21450898,29.4531107 8.26109976,25.7381036 10.9750571,22.4457619 C13.066139,19.909769 15.2462031,17.3960216 17.5597405,15.0602386 C19.2059113,13.3918222 21.0967832,11.9681069 22.9876551,10.5888827 C24.8117904,9.25414959 26.6804167,7.91941648 28.704762,6.91836664 C31.7746481,5.40566911 34.955762,4.1154271 38.136876,2.84743064 C39.9165201,2.15781853 41.7629009,1.57943419 43.6315273,1.22350535 C46.2120113,0.734103213 48.8147409,0.511647694 51.4174704,0.177964415 C51.639926,0.155718863 51.8401359,0.0667366557 52.0625915,-2.84217094e-14 C54.3538833,-2.84217094e-14 56.6674207,-2.84217094e-14 58.9587125,-2.84217094e-14 C59.4703602,0.0667366557 59.9820079,0.155718863 60.4714101,0.222455519 C63.1408763,0.622875453 65.8548336,0.845330972 68.4575632,1.46820643 C71.0825383,2.11332743 73.6185312,3.13662282 76.1990153,4.04869045 C80.9150723,5.69486129 85.1862182,8.1863631 89.0569443,11.2340037 C91.9043749,13.4808045 94.5071045,16.083534 97.0430974,18.7085092 C100.513403,22.290043 103.227361,26.4722068 105.340688,30.9880538 C106.475211,33.412819 107.409525,35.9488119 108.299347,38.4848048 C109.011204,40.4869045 109.63408,42.5557408 110.056745,44.6468227 C111.080041,49.7188085 111.080041,54.8797765 110.946567,60.018499 C110.946567,60.3076912 110.946567,60.6191289 110.879831,60.9083211 C110.323692,63.6890151 109.856535,66.4919546 109.166923,69.2281575 C108.521802,71.786396 107.765453,74.3446345 106.764404,76.7916452 C105.118233,80.8180901 102.960414,84.5553428 100.357685,88.0478944 C98.2443572,90.8953251 95.9530654,93.5203002 93.3725813,95.9228198 C89.7020653,99.3486348 85.6533748,102.196065 81.22651,104.554094 C76.4659619,107.067841 71.4162216,108.869731 66.1440258,109.804044 C60.9163211,110.738357 55.5996342,111.450215 50.2384562,110.538147 C48.3698298,110.22671 46.4567124,110.22671 44.6325771,109.781799 C41.7184098,109.047695 38.8264881,108.113382 35.9790574,107.179069 C31.0405449,105.555144 26.569189,102.97466 22.4315163,99.882528 C18.872228,97.2130618 15.6688685,94.1431757 12.7991923,90.6951151 C10.1074806,87.4472645 7.81618872,83.9769584 5.94756236,80.2174602 C4.79079366,77.8816772 3.87872603,75.412421 2.98890396,72.9654102 C2.29929185,71.0967839 1.65417084,69.205912 1.27599646,67.270549 C0.786594317,64.734556 0.564138798,62.154072 0.230455519,59.5958335 C0.163718863,59.373378 0.0747366557,59.1731681 0.008,58.9507125 Z M44.1876661,41.8883742 C44.1876661,42.7337052 44.1876661,43.2008618 44.1876661,43.6680184 C44.1876661,56.9708584 44.1654205,70.2736985 44.2099116,83.5987841 C44.2099116,84.844535 44.2321572,86.2237592 44.7660504,87.2693001 C46.1675202,90.0277486 48.458812,91.7851472 51.6621715,92.0965849 C52.5964847,92.1855671 52.8856769,91.8296383 52.8634313,90.9175706 C52.8411858,72.2090615 52.8411858,53.4783068 52.8634313,34.7697976 C52.8634313,33.7909933 52.529748,33.5018012 51.5731893,33.5240467 C46.278748,33.5685378 40.9620611,33.4795556 35.6676197,33.5907834 C33.9547122,33.6352745 32.1973136,33.9022211 30.5511428,34.4138688 C26.0130502,35.793093 22.2757975,38.3958226 19.3838757,42.199812 C17.7154593,44.379876 16.4029718,46.8046412 15.8023419,49.4518619 C15.1572208,52.3437836 14.6900643,55.2801965 15.2017119,58.3055915 C15.7133596,61.2864955 16.491954,64.1116806 18.0491426,66.6699191 C19.6730679,69.3616308 21.853132,71.5639405 24.4558615,73.4325668 C26.81389,75.1232288 29.3943741,76.2132608 32.1528225,76.7693996 C34.510851,77.2365562 36.9801073,77.1920651 39.4048724,77.3700295 C40.0944845,77.4145206 40.2724489,77.0363462 40.2724489,76.4134708 C40.2502034,74.1444245 40.2502034,71.8976238 40.2724489,69.6285775 C40.2724489,68.8944742 39.9832568,68.6497732 39.2713991,68.6720187 C38.2036126,68.7165098 37.1135806,68.7387554 36.0457941,68.6720187 C32.9759079,68.4718088 30.2841961,67.2260579 28.0373954,65.1794671 C25.7461036,63.0883852 24.3223882,60.5523923 23.8329861,57.3712784 C23.4548117,54.9242676 23.7662494,52.6997125 24.6560715,50.4751573 C25.523648,48.3173387 26.8583811,46.5599401 28.7270075,45.0917337 C30.1952139,43.934965 31.7968937,42.8894241 33.5765378,42.6669686 C37.0245984,42.199812 40.51715,42.1330753 44.1876661,41.8883742 Z M58.291346,47.9169188 C58.291346,57.2155595 58.3135915,66.5364457 58.2691004,75.8350864 C58.2691004,76.8361363 58.5582926,77.2143107 59.581588,77.1920651 C64.9205205,77.1253284 70.2594529,77.1698196 75.5983854,77.0363462 C82.0273499,76.8806274 87.032599,73.8774779 91.1257806,69.0724387 C93.3058447,66.5142002 94.8185422,63.5555418 95.374681,60.3299368 C96.220012,55.3691787 96.0865387,50.4751573 93.6840191,45.7591003 C91.8598838,42.199812 89.3906275,39.3301358 86.142777,37.1500717 C81.7826488,34.2136588 76.8663818,33.2793456 71.6831682,33.5018012 C71.0825383,33.5240467 70.7933462,33.7242567 70.7933462,34.3248866 C70.7933462,36.6161784 70.7933462,38.9297158 70.7933462,41.2210077 C70.7933462,41.9106198 71.1047839,42.1108298 71.7499049,42.0885842 C72.6842181,42.0440931 73.5962857,42.0663386 74.5305989,42.0885842 C77.9119228,42.1775664 80.8928267,43.4678084 83.2731008,45.7591003 C85.6978659,48.0948832 87.1438268,50.986805 87.3440368,54.4571111 C87.7222112,60.7303567 84.2519051,65.2684493 79.4468658,67.5597411 C77.8896772,68.3160899 76.1545242,68.7832465 74.2858978,68.6720187 C72.2615526,68.560791 70.2149618,68.560791 68.168371,68.6720187 C66.9893568,68.7387554 66.7669013,68.3160899 66.7891468,67.2260579 C66.8336379,53.7674989 66.7446557,40.30894 66.8558835,26.8503811 C66.878129,22.9796551 63.5857873,19.2424024 59.8707802,18.7974914 C58.3803282,18.6195269 58.2691004,18.664018 58.2691004,20.1767156 C58.291346,29.4531107 58.291346,38.6850148 58.291346,47.9169188 Z"
          fill="currentColor"
          fillRule="nonzero"
        />
      </g>
    </SvgIcon>
  );
};

export const QuickbookLogoIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 120 80'}
    >
      <svg
        width="120px"
        height="80px"
        viewBox="0 0 120 80"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Quickbook</title>
        <g id="logo" stroke="none" strokeWidth="1" fill="evenodd" fillRule="evenodd">
          <g
            id="Quickbook"
            // @ts-expect-error
            transformOrigin="top"
            transform="scale(2)"
            fill="currentColor"
            fillRule="nonzero"
          >
            <path
              d="M54.008,12.6 C54.008,12.188 54.008,11.772 54.008,11.36 C54.02,11.32 54.036,11.284 54.04,11.244 C54.072,10.576 54.184,9.924 54.384,9.292 C54.524,8.86 54.652,8.42 54.828,8 C55.124,7.296 55.492,6.628 55.98,6.036 C56.356,5.58 56.748,5.128 57.164,4.708 C57.46,4.408 57.8,4.152 58.14,3.904 C58.468,3.664 58.804,3.424 59.168,3.244 C59.72,2.972 60.292,2.74 60.864,2.512 C61.184,2.388 61.516,2.284 61.852,2.22 C62.316,2.132 62.784,2.092 63.252,2.032 C63.292,2.028 63.328,2.012 63.368,2 C63.78,2 64.196,2 64.608,2 C64.7,2.012 64.792,2.028 64.88,2.04 C65.36,2.112 65.848,2.152 66.316,2.264 C66.788,2.38 67.244,2.564 67.708,2.728 C68.556,3.024 69.324,3.472 70.02,4.02 C70.532,4.424 71,4.892 71.456,5.364 C72.08,6.008 72.568,6.76 72.948,7.572 C73.152,8.008 73.32,8.464 73.48,8.92 C73.608,9.28 73.72,9.652 73.796,10.028 C73.98,10.94 73.98,11.868 73.956,12.792 C73.956,12.844 73.956,12.9 73.944,12.952 C73.844,13.452 73.76,13.956 73.636,14.448 C73.52,14.908 73.384,15.368 73.204,15.808 C72.908,16.532 72.52,17.204 72.052,17.832 C71.672,18.344 71.26,18.816 70.796,19.248 C70.136,19.864 69.408,20.376 68.612,20.8 C67.756,21.252 66.848,21.576 65.9,21.744 C64.96,21.912 64.004,22.04 63.04,21.876 C62.704,21.82 62.36,21.82 62.032,21.74 C61.508,21.608 60.988,21.44 60.476,21.272 C59.588,20.98 58.784,20.516 58.04,19.96 C57.4,19.48 56.824,18.928 56.308,18.308 C55.824,17.724 55.412,17.1 55.076,16.424 C54.868,16.004 54.704,15.56 54.544,15.12 C54.42,14.784 54.304,14.444 54.236,14.096 C54.148,13.64 54.108,13.176 54.048,12.716 C54.036,12.676 54.02,12.64 54.008,12.6 Z M61.952,9.532 C61.952,9.684 61.952,9.768 61.952,9.852 C61.952,12.244 61.948,14.636 61.956,17.032 C61.956,17.256 61.96,17.504 62.056,17.692 C62.308,18.188 62.72,18.504 63.296,18.56 C63.464,18.576 63.516,18.512 63.512,18.348 C63.508,14.984 63.508,11.616 63.512,8.252 C63.512,8.076 63.452,8.024 63.28,8.028 C62.328,8.036 61.372,8.02 60.42,8.04 C60.112,8.048 59.796,8.096 59.5,8.188 C58.684,8.436 58.012,8.904 57.492,9.588 C57.192,9.98 56.956,10.416 56.848,10.892 C56.732,11.412 56.648,11.94 56.74,12.484 C56.832,13.02 56.972,13.528 57.252,13.988 C57.544,14.472 57.936,14.868 58.404,15.204 C58.828,15.508 59.292,15.704 59.788,15.804 C60.212,15.888 60.656,15.88 61.092,15.912 C61.216,15.92 61.248,15.852 61.248,15.74 C61.244,15.332 61.244,14.928 61.248,14.52 C61.248,14.388 61.196,14.344 61.068,14.348 C60.876,14.356 60.68,14.36 60.488,14.348 C59.936,14.312 59.452,14.088 59.048,13.72 C58.636,13.344 58.38,12.888 58.292,12.316 C58.224,11.876 58.28,11.476 58.44,11.076 C58.596,10.688 58.836,10.372 59.172,10.108 C59.436,9.9 59.724,9.712 60.044,9.672 C60.664,9.588 61.292,9.576 61.952,9.532 Z M64.488,10.616 C64.488,12.288 64.492,13.964 64.484,15.636 C64.484,15.816 64.536,15.884 64.72,15.88 C65.68,15.868 66.64,15.876 67.6,15.852 C68.756,15.824 69.656,15.284 70.392,14.42 C70.784,13.96 71.056,13.428 71.156,12.848 C71.308,11.956 71.284,11.076 70.852,10.228 C70.524,9.588 70.08,9.072 69.496,8.68 C68.712,8.152 67.828,7.984 66.896,8.024 C66.788,8.028 66.736,8.064 66.736,8.172 C66.736,8.584 66.736,9 66.736,9.412 C66.736,9.536 66.792,9.572 66.908,9.568 C67.076,9.56 67.24,9.564 67.408,9.568 C68.016,9.584 68.552,9.816 68.98,10.228 C69.416,10.648 69.676,11.168 69.712,11.792 C69.78,12.92 69.156,13.736 68.292,14.148 C68.012,14.284 67.7,14.368 67.364,14.348 C67,14.328 66.632,14.328 66.264,14.348 C66.052,14.36 66.012,14.284 66.016,14.088 C66.024,11.668 66.008,9.248 66.028,6.828 C66.032,6.132 65.44,5.46 64.772,5.38 C64.504,5.348 64.484,5.356 64.484,5.628 C64.488,7.296 64.488,8.956 64.488,10.616 Z"
              id="shape-1"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const QuickbookLogoIconForTrans: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 120 120'}
    >
      <g id="logo" stroke="none" strokeWidth="1" fill="currentColor" fillRule="evenodd">
        <path
          d="M0.008,58.9507125 C0.008,56.6594207 0.008,54.3458833 0.008,52.0545915 C0.0747366557,51.8321359 0.163718863,51.631926 0.185964415,51.4094704 C0.36392883,47.6944633 0.986804284,44.0684383 2.09908188,40.5536411 C2.8776762,38.1511215 3.58953386,35.7041108 4.56833814,33.3683279 C6.21450898,29.4531107 8.26109976,25.7381036 10.9750571,22.4457619 C13.066139,19.909769 15.2462031,17.3960216 17.5597405,15.0602386 C19.2059113,13.3918222 21.0967832,11.9681069 22.9876551,10.5888827 C24.8117904,9.25414959 26.6804167,7.91941648 28.704762,6.91836664 C31.7746481,5.40566911 34.955762,4.1154271 38.136876,2.84743064 C39.9165201,2.15781853 41.7629009,1.57943419 43.6315273,1.22350535 C46.2120113,0.734103213 48.8147409,0.511647694 51.4174704,0.177964415 C51.639926,0.155718863 51.8401359,0.0667366557 52.0625915,-2.84217094e-14 C54.3538833,-2.84217094e-14 56.6674207,-2.84217094e-14 58.9587125,-2.84217094e-14 C59.4703602,0.0667366557 59.9820079,0.155718863 60.4714101,0.222455519 C63.1408763,0.622875453 65.8548336,0.845330972 68.4575632,1.46820643 C71.0825383,2.11332743 73.6185312,3.13662282 76.1990153,4.04869045 C80.9150723,5.69486129 85.1862182,8.1863631 89.0569443,11.2340037 C91.9043749,13.4808045 94.5071045,16.083534 97.0430974,18.7085092 C100.513403,22.290043 103.227361,26.4722068 105.340688,30.9880538 C106.475211,33.412819 107.409525,35.9488119 108.299347,38.4848048 C109.011204,40.4869045 109.63408,42.5557408 110.056745,44.6468227 C111.080041,49.7188085 111.080041,54.8797765 110.946567,60.018499 C110.946567,60.3076912 110.946567,60.6191289 110.879831,60.9083211 C110.323692,63.6890151 109.856535,66.4919546 109.166923,69.2281575 C108.521802,71.786396 107.765453,74.3446345 106.764404,76.7916452 C105.118233,80.8180901 102.960414,84.5553428 100.357685,88.0478944 C98.2443572,90.8953251 95.9530654,93.5203002 93.3725813,95.9228198 C89.7020653,99.3486348 85.6533748,102.196065 81.22651,104.554094 C76.4659619,107.067841 71.4162216,108.869731 66.1440258,109.804044 C60.9163211,110.738357 55.5996342,111.450215 50.2384562,110.538147 C48.3698298,110.22671 46.4567124,110.22671 44.6325771,109.781799 C41.7184098,109.047695 38.8264881,108.113382 35.9790574,107.179069 C31.0405449,105.555144 26.569189,102.97466 22.4315163,99.882528 C18.872228,97.2130618 15.6688685,94.1431757 12.7991923,90.6951151 C10.1074806,87.4472645 7.81618872,83.9769584 5.94756236,80.2174602 C4.79079366,77.8816772 3.87872603,75.412421 2.98890396,72.9654102 C2.29929185,71.0967839 1.65417084,69.205912 1.27599646,67.270549 C0.786594317,64.734556 0.564138798,62.154072 0.230455519,59.5958335 C0.163718863,59.373378 0.0747366557,59.1731681 0.008,58.9507125 Z M44.1876661,41.8883742 C44.1876661,42.7337052 44.1876661,43.2008618 44.1876661,43.6680184 C44.1876661,56.9708584 44.1654205,70.2736985 44.2099116,83.5987841 C44.2099116,84.844535 44.2321572,86.2237592 44.7660504,87.2693001 C46.1675202,90.0277486 48.458812,91.7851472 51.6621715,92.0965849 C52.5964847,92.1855671 52.8856769,91.8296383 52.8634313,90.9175706 C52.8411858,72.2090615 52.8411858,53.4783068 52.8634313,34.7697976 C52.8634313,33.7909933 52.529748,33.5018012 51.5731893,33.5240467 C46.278748,33.5685378 40.9620611,33.4795556 35.6676197,33.5907834 C33.9547122,33.6352745 32.1973136,33.9022211 30.5511428,34.4138688 C26.0130502,35.793093 22.2757975,38.3958226 19.3838757,42.199812 C17.7154593,44.379876 16.4029718,46.8046412 15.8023419,49.4518619 C15.1572208,52.3437836 14.6900643,55.2801965 15.2017119,58.3055915 C15.7133596,61.2864955 16.491954,64.1116806 18.0491426,66.6699191 C19.6730679,69.3616308 21.853132,71.5639405 24.4558615,73.4325668 C26.81389,75.1232288 29.3943741,76.2132608 32.1528225,76.7693996 C34.510851,77.2365562 36.9801073,77.1920651 39.4048724,77.3700295 C40.0944845,77.4145206 40.2724489,77.0363462 40.2724489,76.4134708 C40.2502034,74.1444245 40.2502034,71.8976238 40.2724489,69.6285775 C40.2724489,68.8944742 39.9832568,68.6497732 39.2713991,68.6720187 C38.2036126,68.7165098 37.1135806,68.7387554 36.0457941,68.6720187 C32.9759079,68.4718088 30.2841961,67.2260579 28.0373954,65.1794671 C25.7461036,63.0883852 24.3223882,60.5523923 23.8329861,57.3712784 C23.4548117,54.9242676 23.7662494,52.6997125 24.6560715,50.4751573 C25.523648,48.3173387 26.8583811,46.5599401 28.7270075,45.0917337 C30.1952139,43.934965 31.7968937,42.8894241 33.5765378,42.6669686 C37.0245984,42.199812 40.51715,42.1330753 44.1876661,41.8883742 Z M58.291346,47.9169188 C58.291346,57.2155595 58.3135915,66.5364457 58.2691004,75.8350864 C58.2691004,76.8361363 58.5582926,77.2143107 59.581588,77.1920651 C64.9205205,77.1253284 70.2594529,77.1698196 75.5983854,77.0363462 C82.0273499,76.8806274 87.032599,73.8774779 91.1257806,69.0724387 C93.3058447,66.5142002 94.8185422,63.5555418 95.374681,60.3299368 C96.220012,55.3691787 96.0865387,50.4751573 93.6840191,45.7591003 C91.8598838,42.199812 89.3906275,39.3301358 86.142777,37.1500717 C81.7826488,34.2136588 76.8663818,33.2793456 71.6831682,33.5018012 C71.0825383,33.5240467 70.7933462,33.7242567 70.7933462,34.3248866 C70.7933462,36.6161784 70.7933462,38.9297158 70.7933462,41.2210077 C70.7933462,41.9106198 71.1047839,42.1108298 71.7499049,42.0885842 C72.6842181,42.0440931 73.5962857,42.0663386 74.5305989,42.0885842 C77.9119228,42.1775664 80.8928267,43.4678084 83.2731008,45.7591003 C85.6978659,48.0948832 87.1438268,50.986805 87.3440368,54.4571111 C87.7222112,60.7303567 84.2519051,65.2684493 79.4468658,67.5597411 C77.8896772,68.3160899 76.1545242,68.7832465 74.2858978,68.6720187 C72.2615526,68.560791 70.2149618,68.560791 68.168371,68.6720187 C66.9893568,68.7387554 66.7669013,68.3160899 66.7891468,67.2260579 C66.8336379,53.7674989 66.7446557,40.30894 66.8558835,26.8503811 C66.878129,22.9796551 63.5857873,19.2424024 59.8707802,18.7974914 C58.3803282,18.6195269 58.2691004,18.664018 58.2691004,20.1767156 C58.291346,29.4531107 58.291346,38.6850148 58.291346,47.9169188 Z"
          fill="currentColor"
          fillRule="nonzero"
        />
      </g>
    </SvgIcon>
  );
};

export const QuickbookStyleIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 120 80'}
    >
      <svg
        width="150px"
        height="80px"
        viewBox="50 0 150 80"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Quickbook</title>
        <g id="logo" stroke="none" strokeWidth="1" fill="evenodd" fillRule="evenodd">
          <g
            id="Quickbook"
            // @ts-expect-error
            transformOrigin="top"
            transform="scale(2)"
            fill="currentColor"
            fillRule="nonzero"
          >
            <path
              d="M54.008,12.6 C54.008,12.188 54.008,11.772 54.008,11.36 C54.02,11.32 54.036,11.284 54.04,11.244 C54.072,10.576 54.184,9.924 54.384,9.292 C54.524,8.86 54.652,8.42 54.828,8 C55.124,7.296 55.492,6.628 55.98,6.036 C56.356,5.58 56.748,5.128 57.164,4.708 C57.46,4.408 57.8,4.152 58.14,3.904 C58.468,3.664 58.804,3.424 59.168,3.244 C59.72,2.972 60.292,2.74 60.864,2.512 C61.184,2.388 61.516,2.284 61.852,2.22 C62.316,2.132 62.784,2.092 63.252,2.032 C63.292,2.028 63.328,2.012 63.368,2 C63.78,2 64.196,2 64.608,2 C64.7,2.012 64.792,2.028 64.88,2.04 C65.36,2.112 65.848,2.152 66.316,2.264 C66.788,2.38 67.244,2.564 67.708,2.728 C68.556,3.024 69.324,3.472 70.02,4.02 C70.532,4.424 71,4.892 71.456,5.364 C72.08,6.008 72.568,6.76 72.948,7.572 C73.152,8.008 73.32,8.464 73.48,8.92 C73.608,9.28 73.72,9.652 73.796,10.028 C73.98,10.94 73.98,11.868 73.956,12.792 C73.956,12.844 73.956,12.9 73.944,12.952 C73.844,13.452 73.76,13.956 73.636,14.448 C73.52,14.908 73.384,15.368 73.204,15.808 C72.908,16.532 72.52,17.204 72.052,17.832 C71.672,18.344 71.26,18.816 70.796,19.248 C70.136,19.864 69.408,20.376 68.612,20.8 C67.756,21.252 66.848,21.576 65.9,21.744 C64.96,21.912 64.004,22.04 63.04,21.876 C62.704,21.82 62.36,21.82 62.032,21.74 C61.508,21.608 60.988,21.44 60.476,21.272 C59.588,20.98 58.784,20.516 58.04,19.96 C57.4,19.48 56.824,18.928 56.308,18.308 C55.824,17.724 55.412,17.1 55.076,16.424 C54.868,16.004 54.704,15.56 54.544,15.12 C54.42,14.784 54.304,14.444 54.236,14.096 C54.148,13.64 54.108,13.176 54.048,12.716 C54.036,12.676 54.02,12.64 54.008,12.6 Z M61.952,9.532 C61.952,9.684 61.952,9.768 61.952,9.852 C61.952,12.244 61.948,14.636 61.956,17.032 C61.956,17.256 61.96,17.504 62.056,17.692 C62.308,18.188 62.72,18.504 63.296,18.56 C63.464,18.576 63.516,18.512 63.512,18.348 C63.508,14.984 63.508,11.616 63.512,8.252 C63.512,8.076 63.452,8.024 63.28,8.028 C62.328,8.036 61.372,8.02 60.42,8.04 C60.112,8.048 59.796,8.096 59.5,8.188 C58.684,8.436 58.012,8.904 57.492,9.588 C57.192,9.98 56.956,10.416 56.848,10.892 C56.732,11.412 56.648,11.94 56.74,12.484 C56.832,13.02 56.972,13.528 57.252,13.988 C57.544,14.472 57.936,14.868 58.404,15.204 C58.828,15.508 59.292,15.704 59.788,15.804 C60.212,15.888 60.656,15.88 61.092,15.912 C61.216,15.92 61.248,15.852 61.248,15.74 C61.244,15.332 61.244,14.928 61.248,14.52 C61.248,14.388 61.196,14.344 61.068,14.348 C60.876,14.356 60.68,14.36 60.488,14.348 C59.936,14.312 59.452,14.088 59.048,13.72 C58.636,13.344 58.38,12.888 58.292,12.316 C58.224,11.876 58.28,11.476 58.44,11.076 C58.596,10.688 58.836,10.372 59.172,10.108 C59.436,9.9 59.724,9.712 60.044,9.672 C60.664,9.588 61.292,9.576 61.952,9.532 Z M64.488,10.616 C64.488,12.288 64.492,13.964 64.484,15.636 C64.484,15.816 64.536,15.884 64.72,15.88 C65.68,15.868 66.64,15.876 67.6,15.852 C68.756,15.824 69.656,15.284 70.392,14.42 C70.784,13.96 71.056,13.428 71.156,12.848 C71.308,11.956 71.284,11.076 70.852,10.228 C70.524,9.588 70.08,9.072 69.496,8.68 C68.712,8.152 67.828,7.984 66.896,8.024 C66.788,8.028 66.736,8.064 66.736,8.172 C66.736,8.584 66.736,9 66.736,9.412 C66.736,9.536 66.792,9.572 66.908,9.568 C67.076,9.56 67.24,9.564 67.408,9.568 C68.016,9.584 68.552,9.816 68.98,10.228 C69.416,10.648 69.676,11.168 69.712,11.792 C69.78,12.92 69.156,13.736 68.292,14.148 C68.012,14.284 67.7,14.368 67.364,14.348 C67,14.328 66.632,14.328 66.264,14.348 C66.052,14.36 66.012,14.284 66.016,14.088 C66.024,11.668 66.008,9.248 66.028,6.828 C66.032,6.132 65.44,5.46 64.772,5.38 C64.504,5.348 64.484,5.356 64.484,5.628 C64.488,7.296 64.488,8.956 64.488,10.616 Z"
              id="shape-1"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const StripeSettingIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 120 80'}
    >
      <svg
        width="120px"
        height="80px"
        viewBox="0 0 120 80"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>Stripe_Logo,_revised_2016</title>
        <g id="overview" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.1-new1" transform="translate(-1354.000000, -485.000000)" fill="currentColor">
            <g id="Stripe_Logo,_revised_2016" transform="translate(1354.000000, 485.000000)">
              <path
                d="M115,24.6116612 C115,16.4444444 111.161507,10 103.825034,10 C96.4576043,10 92,16.4444444 92,24.5478548 C92,34.1507151 97.2624495,39 104.815612,39 C108.499327,39 111.28533,38.1386139 113.39031,36.9262926 L113.39031,30.5456546 C111.28533,31.630363 108.870794,32.30033 105.806191,32.30033 C102.803499,32.30033 100.141319,31.2156216 99.8008075,27.4510451 L114.938089,27.4510451 C114.938089,27.0363036 115,25.3773377 115,24.6116612 Z M99.7079408,21.5808581 C99.7079408,17.9757976 101.843876,16.4763476 103.794078,16.4763476 C105.682369,16.4763476 107.694482,17.9757976 107.694482,21.5808581 L99.7079408,21.5808581 Z"
                id="shape-1"
              />
              <path
                d="M79.9285714,10 C76.7467532,10 74.7012987,11.4788494 73.5649351,12.5076142 L73.1428571,10.5143824 L66,10.5143824 L66,48 L74.1168831,46.2961083 L74.1493506,37.1979695 C75.3181818,38.0338409 77.038961,39.2233503 79.8961039,39.2233503 C85.7077922,39.2233503 91,34.5939086 91,24.4027073 C90.9675325,15.0795262 85.6103896,10 79.9285714,10 Z M77.9805195,32.1505922 C76.0649351,32.1505922 74.9285714,31.4754653 74.1493506,30.6395939 L74.1168831,18.7123519 C74.961039,17.7800338 76.1298701,17.1370558 77.9805195,17.1370558 C80.9350649,17.1370558 82.9805195,20.4162437 82.9805195,24.6277496 C82.9805195,28.9357022 80.9675325,32.1505922 77.9805195,32.1505922 Z"
                id="shape-1"
              />
              <polygon id="path" points="55 8 63 6.31906615 63 0 55 1.64980545" />
              <rect id="rectangle" x="55" y="11" width="8" height="28" />
              <path
                d="M45.9090909,12.7625203 L45.3636364,10.3385834 L38,10.3385834 L38,39 L46.5227273,39 L46.5227273,19.5757485 C48.5340909,17.0535438 51.9431818,17.5121265 53,17.8724414 L53,10.3385834 C51.9090909,9.9455125 47.9204545,9.22488259 45.9090909,12.7625203 Z"
                id="path"
              />
              <path
                d="M29.4677419,3 L21.0345622,4.68648649 L21,30.6648649 C21,35.4648649 24.8364055,39 29.9516129,39 C32.7857143,39 34.859447,38.5135135 36,37.9297297 L36,31.3459459 C34.8940092,31.7675676 29.4331797,33.2594595 29.4331797,28.4594595 L29.4331797,16.9459459 L36,16.9459459 L36,10.0378378 L29.4331797,10.0378378 L29.4677419,3 Z"
                id="path"
              />
              <path
                d="M8.36692913,18.6362637 C8.36692913,17.3934066 9.42519685,16.9153846 11.1779528,16.9153846 C13.6913386,16.9153846 16.8661417,17.6483516 19.3795276,18.9549451 L19.3795276,11.4659341 C16.6346457,10.4142857 13.9228346,10 11.1779528,10 C4.46456693,10 0,13.378022 0,19.0186813 C0,27.8142857 12.5669291,26.4120879 12.5669291,30.2043956 C12.5669291,31.6703297 11.2440945,32.1483516 9.39212598,32.1483516 C6.64724409,32.1483516 3.14173228,31.0648352 0.363779528,29.5989011 L0.363779528,37.1835165 C3.43937008,38.4582418 6.5480315,39 9.39212598,39 C16.2708661,39 21,35.7175824 21,30.0131868 C20.9669291,20.5164835 8.36692913,22.2054945 8.36692913,18.6362637 Z"
                id="path"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RightRevSettingIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 90 80'}
    >
      <svg
        width="120px"
        height="80px"
        viewBox="0 0 110 80"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>RightRev</title>
        <g id="overview" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="1.1-new1" transform="translate(-1527.000000, -416.000000)">
            <g id="RightRev" transform="translate(1527.690602, 416.345292)">
              <path
                d="M16.0482685,16.5755772 L50.7521676,16.5755772 C57.8834677,16.5755772 63.6835208,22.3205413 63.7515756,29.4515167 L63.9517315,50.4244228 L63.9517315,50.4244228 C60.6142417,46.5852576 57.2437098,44.6279668 53.8401357,44.5525504 C50.4741414,44.4779666 33.8781613,44.4998079 4.0521953,44.6180742 C3.49993591,44.6202257 3.05046614,44.1743064 3.04827631,43.6220471 C3.04827107,43.6207255 3.04826845,43.6194038 3.04826845,43.6180821 L3.04826845,29.5755772 C3.04826845,22.3958755 8.8685667,16.5755772 16.0482685,16.5755772 Z"
                id="rectanglebackup-2"
                fill={props?.style?.color || '#71BF00'}
                transform="translate(33.500000, 33.500000) rotate(-45.000000) translate(-33.500000, -33.500000) "
              />
              <path
                d="M35.1198845,16.5755772 L69.8237837,16.5755772 C76.9550838,16.5755772 82.7551369,22.3205413 82.8231917,29.4515167 L83.0233476,50.4244228 L83.0233476,50.4244228 C79.6858578,46.5852576 76.3153259,44.6279668 72.9117518,44.5525504 C69.5457575,44.4779666 52.9497774,44.4998079 23.1238114,44.6180742 C22.571552,44.6202257 22.1220822,44.1743064 22.1198924,43.6220471 C22.1198872,43.6207255 22.1198845,43.6194038 22.1198845,43.6180821 L22.1198845,29.5755772 C22.1198845,22.3958755 27.9401828,16.5755772 35.1198845,16.5755772 Z"
                id="rectanglebackup-3"
                fill={props?.style?.color || '#71BF00'}
                transform="translate(52.571616, 33.500000) scale(-1, 1) rotate(-45.000000) translate(-52.571616, -33.500000) "
              />
              <path
                d="M52.3852328,9.14065759 L67,23.9347482 C62.1595337,23.5015587 58.5058285,24.4782601 56.0388845,26.8648523 L54.3644868,28.5199851 L48.7489833,34.1749156 L43.035808,39.6551983 L33.3432357,29.5467322 L31.1593685,27.3696464 L30.6432373,26.8648523 C28.2510492,24.5505811 24.6261093,23.527518 19.7684177,23.795663 L19.3723975,23.8198231 L33.8932009,9.13610098 C38.8613516,4.11194578 46.9119983,3.98751731 52.0322467,8.79651641 L52.3852328,9.14065759 Z"
                id="path"
                fill={props?.style?.color || '#558600'}
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const EvergreenIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        enableBackground="new 0 0 24 24"
        height="24"
        viewBox="0 0 24 24"
        width="24"
      >
        <g>
          <rect fill="none" height="24" width="24" />
        </g>
        <g>
          <g>
            <polygon points="16,12 9,2 2,12 3.86,12 0,18 7,18 7,22 11,22 11,18 18,18 14.14,12" />
            <polygon points="20.14,12 22,12 15,2 12.61,5.41 17.92,13 16,13 15.97,13 19.19,18 24,18" />
            <rect height="3" width="4" x="13" y="19" />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const StripeLogoIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <SvgIcon style={style} className={userProps.className || ''}>
      <svg xmlns="http://www.w3.org/2000/svg" width="28px" height="28px" viewBox="0 0 28 28">
        <g id="logo" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g fill="currentColor" fillRule="nonzero">
            <path d="M10.770296,7.95604393 C10.770296,7.0989011 11.5001358,6.76923076 12.708933,6.76923076 C14.4423025,6.76923076 16.6318219,7.27472524 18.3651914,8.17582421 L18.3651914,3.01098903 C16.4721694,2.28571428 14.6019549,2 12.708933,2 C8.07901168,2 5,4.32967034 5,8.21978021 C5,14.2857143 13.6668477,13.3186813 13.6668477,15.9340659 C13.6668477,16.945055 12.7545479,17.2747252 11.4773283,17.2747252 C9.58430627,17.2747252 7.16671192,16.5274726 5.25088243,15.5164835 L5.25088243,20.7472528 C7.37197937,21.6263737 9.51588379,22 11.4773283,22 C16.221287,22 19.4827586,19.7362637 19.4827586,15.8021978 C19.4599511,9.25274724 10.770296,10.4175824 10.770296,7.95604393 Z" />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const WebhookConnectionIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <SvgIcon style={style} className={userProps.className || ''}>
      <svg
        width="32px"
        height="32px"
        viewBox="2 2 36 36"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>WebhookConnectionIcon</title>
        <g id="integration" stroke="none" strokeWidth="1" fill="currentColor" fillRule="evenodd">
          <path
            d="M13.9302573,25.4089243 C12.0051441,28.1383657 8.2472175,28.8240225 5.5309618,26.9252806 C2.82789179,25.0265388 2.19497784,21.2422408 4.09371969,18.486428 C5.17022541,16.923506 6.91815192,15.9567757 8.81420292,15.8756579 L8.88013145,17.7612141 C7.68023207,17.853514 6.51988985,18.4732423 5.76830453,19.5544703 C4.44973379,21.4532121 4.85849073,24.0112393 6.66493262,25.2902529 C8.48456023,26.5560808 11.0294017,26.0682097 12.3479725,24.1826535 C12.7567294,23.5892967 12.9940721,22.943197 13.0863721,22.2839117 L13.0863721,20.9521552 L20.4439968,20.8994124 L20.5362967,20.7543696 C21.2351392,19.5412845 22.7514955,19.1193419 23.9382092,19.8049987 C24.5118132,20.1406389 24.928552,20.6904385 25.096711,21.3333992 C25.26487,21.9763599 25.1706682,22.6597902 24.8348373,23.2332826 C24.1359948,24.433182 22.6064528,24.8551246 21.4197391,24.1694678 C20.8791251,23.8661966 20.4967396,23.3783254 20.3253254,22.8245257 L14.9587425,22.8508971 C14.8015773,23.7669679 14.4509593,24.6390178 13.9302573,25.4089243 M23.5294523,15.9943293 C26.8654362,16.4030862 29.2388636,19.3962418 28.8301066,22.6794829 C28.4213497,25.9759097 25.388637,28.3097799 22.0526531,27.901023 C20.1752094,27.6823494 18.5050012,26.6062094 17.5299554,24.9869817 L19.1649832,24.0376107 C19.8497791,25.0979607 20.9708646,25.7986391 22.2240673,25.9495383 C24.531566,26.2264382 26.5753507,24.657339 26.8522505,22.4553259 C27.1291504,20.2533127 25.4941227,18.2358995 23.2129953,17.9589997 C22.5009671,17.8798854 21.8153103,17.9721854 21.1955821,18.1963424 L20.074797,18.7765135 L16.6728845,12.4869311 L16.3827989,12.4869311 C15.0008758,12.4466938 13.9109422,11.2978449 13.9434431,9.9157182 C13.9830002,8.54440465 15.1697138,7.47636235 16.5542131,7.52910519 C17.9387124,7.60821944 19.0331261,8.72900457 18.9935689,10.1003181 C18.9671975,10.6804892 18.7430405,11.2079175 18.3870264,11.6166744 L20.8923108,16.2448577 C21.7098246,15.9811436 22.6064528,15.8888436 23.5294523,15.9943293 M11.016216,12.4078169 C9.69764529,9.30917565 11.0953303,5.76222039 14.1412287,4.47002109 C17.2003128,3.17782178 20.7340823,4.64143529 22.0526531,7.74007649 C22.8306098,9.54651838 22.6723813,11.5243745 21.7889389,13.1066594 L20.1539112,12.1572885 C20.7077109,11.0892462 20.8000109,9.78386114 20.2725826,8.55759036 C19.3759545,6.44787718 16.9893414,5.43257773 14.9455568,6.28964869 C12.8885865,7.15990538 11.965587,9.58607553 12.862215,11.6957887 C13.2314149,12.5660454 13.8511431,13.2385164 14.6027284,13.6868305 L15.116971,13.9637304 L11.0689589,20.5433983 C11.108516,20.6093268 11.1612588,20.6884411 11.2008159,20.7939267 C11.8469156,21.9938261 11.3986015,23.5101824 10.1855165,24.1562821 C8.9856171,24.8023818 7.46926077,24.3276963 6.80997539,23.0882398 C6.16387572,21.861969 6.61218978,20.3456127 7.82527487,19.699513 C8.33951746,19.4226132 8.90650288,19.3566847 9.44711687,19.475356 L12.4930153,14.5043443 C11.873287,13.9373589 11.3458587,13.2253307 11.016216,12.4078169 Z"
            id="webhook"
            fill="currentColor"
            fillRule="nonzero"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const AvalaraLogoIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 28 28'}
      style={style}
      className={userProps.className || ''}
    >
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28.87 28.87" id="avalara">
        <g id="logo" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g fillRule="nonzero">
            <g transform="translate(2.000000, 2.206187)">
              <path
                d="M13.99562,10.8454946 L13.8366045,10.8454946 C14.0496853,10.6133319 14.3009298,10.451136 14.4313226,10.18399 C14.7716158,9.85323768 15.1182696,9.52566572 15.4999068,9.23625749 C15.8433804,8.98183262 16.1550508,8.68924409 16.504885,8.44117987 C16.6734414,8.32350841 16.7497688,8.39029491 16.8038341,8.51750731 C16.9119646,8.76875182 16.997833,9.02635699 17.1186849,9.27442121 C17.1791108,9.40163361 17.140947,9.52248543 17.0232755,9.62425535 C16.4921637,10.0854004 16.0119368,10.5974303 15.5348903,11.1158209 C14.6857474,12.0317503 13.8970304,12.9922041 13.1591984,13.9971821 C13.0542481,14.1434764 12.9365767,14.2802298 12.8252658,14.4201634 C12.7807415,14.4042619 12.7362171,14.4678681 12.6885124,14.4201634 C12.6503487,14.2198039 12.7648399,14.067149 12.8793311,13.9399366 C13.3563777,13.4151853 13.769818,12.8363688 14.1768977,12.2575524 C14.3041102,12.0794549 14.5012894,11.9522425 14.6094199,11.7805058 C14.7652552,11.5356219 14.9719753,11.3511639 15.1659743,11.147624 C15.3599732,10.9472645 15.6080374,10.7946096 15.7225286,10.5179225 C15.757512,10.4352345 15.8370197,10.3652677 15.9165275,10.3143827 C16.260001,10.1044822 16.454,9.73238593 16.7847522,9.50658383 C16.8578994,9.45569887 16.8833419,9.38255172 16.8674403,9.29350304 C16.8610797,9.26488026 16.8674403,9.30304398 16.8610797,9.27442121 C16.7656704,8.75285029 16.6034746,8.69878503 16.1836736,9.01045546 C15.8942654,9.22353625 15.5857752,9.41435485 15.3313504,9.67832061 C15.2391214,9.77054965 15.1850561,9.89458169 15.0801059,9.9740895 C14.9338116,10.0885807 14.8034189,10.2221537 14.6189609,10.2921205 C14.4472241,10.3557267 14.3327329,10.5083817 14.2341433,10.6610365 C14.1768977,10.7500852 14.1355537,10.8645764 13.99562,10.8454946"
                id="Fill-13"
                fill="#2CA6C4"
              />
              <path
                d="M17.8825954,19.9965197 C17.3832867,19.9965197 16.8807977,19.9901591 16.3814889,19.9997 C16.2002112,20.0060606 16.1079822,19.9106513 16.053917,19.7738979 C15.8662786,19.303212 15.6563782,18.8388867 15.4655595,18.3713811 C15.1252663,17.5349594 14.7658912,16.7080787 14.4096965,15.8811981 C14.3651721,15.7762478 14.3270084,15.6712976 14.2824841,15.5663473 C14.2284187,15.4454955 14.2347794,15.3055618 14.3397296,15.1815298 C14.7690716,14.6854013 15.1697907,14.1638304 15.5927719,13.6581611 C15.8694589,13.3274088 16.1906702,13.0380005 16.4482754,12.6913467 C16.5436847,12.5641343 16.6613562,12.5959374 16.7376836,12.7740348 C16.8680764,13.0761643 16.9921085,13.381474 17.1193209,13.6867839 C17.3101395,14.1352076 17.5073188,14.5836314 17.6949571,15.0320552 C17.9684638,15.6871991 18.2356098,16.3455234 18.5122968,16.997487 C18.8335082,17.7639418 19.1547196,18.5303966 19.4854718,19.2936711 C19.5458977,19.4336047 19.5745206,19.5830793 19.6444874,19.7198327 C19.7303558,19.8883892 19.6444874,19.9933394 19.4504885,19.9965197 C18.9289175,19.9965197 18.4073466,19.9965197 17.8825954,19.9965197"
                id="Fill-15"
                fill="#F1C9AB"
              />
              <path
                d="M1.22635573,16.9001695 C1.29632256,16.5757778 1.45533809,16.2831892 1.57618988,15.9778795 C1.87195875,15.2305065 2.18362917,14.4926745 2.49529959,13.7516622 C2.74654411,13.1569441 3.01687049,12.571767 3.25539377,11.9706884 C3.54798233,11.2296761 3.85965275,10.4982046 4.16814286,9.76355293 L4.92823704,7.95713666 C5.23354685,7.22884553 5.53249602,6.49737418 5.83780582,5.76908305 C6.13357469,5.06305416 6.44842542,4.36338587 6.73147307,3.65099634 C6.85550518,3.34568655 6.96363569,3.03083581 7.10038904,2.73506695 C7.28802735,2.3248069 7.44704289,1.90500593 7.62832055,1.49156558 C7.81595892,1.05586306 7.98451534,0.61379991 8.16261271,0.178097385 C8.20713708,0.0699668286 8.28028423,1.85322889e-14 8.41067693,1.85322889e-14 L11.2634154,1.85322889e-14 C11.3524641,1.85322889e-14 11.4192506,0.0254424808 11.4287915,0.130392724 C10.4746984,0.073147142 9.52060527,0.117671483 8.56651217,0.104950243 C8.50290597,0.10176993 8.43611941,0.0985896227 8.37887386,0.139933657 C8.19441584,0.496128421 8.04176096,0.865044431 7.89546666,1.23396043 C7.49474756,2.2293976 7.05904502,3.21211351 6.65196527,4.20437035 C6.13357469,5.46377327 5.59928254,6.71999585 5.07771164,7.97939878 C4.63246818,9.05752405 4.16496255,10.1292886 3.72607971,11.2074139 C3.30945905,12.2314738 2.87693683,13.2459928 2.45077524,14.2605118 C2.10412141,15.0905729 1.78609037,15.9333551 1.39491219,16.7443343 C1.36628939,16.8015799 1.34084691,16.8620058 1.28996194,16.9065301 C1.26769977,16.9192513 1.2454376,16.916071 1.22635573,16.9001695"
                id="Fill-19"
                fill="#EDBC9A"
              />
              <path
                d="M1.22635573,16.9001695 L1.27088008,16.9001695 C1.34402722,16.9733166 1.29950288,17.0528244 1.26769977,17.1227912 C0.968750594,17.7906564 0.730227314,18.4839641 0.421737205,19.1486489 C0.351770376,19.3013038 0.30088541,19.4603194 0.237279201,19.6161545 C0.183213924,19.7497276 0.22773827,19.8037929 0.367671928,19.8037929 C0.6952439,19.8037929 1.02281587,19.8037929 1.34720753,19.8037929 C1.93556495,19.8037929 2.52392238,19.7974323 3.10909949,19.8069732 C3.31581967,19.8101535 3.42395022,19.7306458 3.50345798,19.5430074 C3.91371803,18.5189474 4.35896148,17.5107891 4.78512308,16.4930897 C4.83600804,16.3690576 4.8519096,16.2036815 5.02046605,16.1559768 C5.04908884,16.18778 5.04590853,16.219583 5.02682667,16.2545665 C4.97594171,16.5503354 4.80102463,16.7983996 4.69289408,17.0687259 C4.39076459,17.8383611 4.06319261,18.5984553 3.74516158,19.3585494 C3.68155537,19.5048437 3.61476885,19.6479577 3.54798233,19.7910716 C3.48437612,19.9246447 3.39532743,19.9946115 3.22677098,19.9946115 C2.23769444,19.9850706 1.25179822,19.9914312 0.265901995,19.9882509 C0.0051165418,19.9882509 -0.0521290455,19.9023825 0.0432802667,19.6734002 C0.412196273,18.7924541 0.781112282,17.9115082 1.15002829,17.0305622 C1.16911015,16.9828575 1.20091325,16.9446938 1.22635573,16.9001695"
                id="Fill-27"
                fill="#EFA980"
              />
              <path
                d="M8.29046118,0.130392724 C8.33498556,0.0254424808 8.42721453,0.0413440345 8.51308292,0.0413440345 C9.39084857,0.0413440345 10.2686143,0.0445243479 11.1463799,0.0381637277 C11.2545105,0.0381637277 11.3435592,0.0667865153 11.4294276,0.130392724 C11.55664,0.422981285 11.6870327,0.71556984 11.8110648,1.01133871 C12.0400472,1.55517179 12.2658492,2.09582456 12.4916513,2.63965763 C12.6888305,3.11670419 12.8796491,3.59375075 13.0800087,4.06761701 C13.2358439,4.4333527 13.4139413,4.78318685 13.5475143,5.15210285 C13.7605951,5.74682087 14.0500034,6.30655551 14.2630842,6.89809328 C14.3298707,7.08891189 14.4125588,7.27336991 14.507968,7.44828698 C14.5970167,7.62002376 14.5461317,7.7440558 14.4030178,7.83946514 C14.0150199,8.10343089 13.6747267,8.42464226 13.3280729,8.74267327 C13.2740076,8.79037794 13.2199423,8.83490231 13.1658771,8.88260698 C13.1340739,8.91122975 13.1054512,8.91122975 13.073648,8.88260698 C13.0641071,8.79991888 13.1181724,8.74585363 13.1722377,8.69814896 C13.493449,8.41828161 13.7923982,8.10979155 14.1326914,7.85218638 C14.3871163,7.66136777 14.415739,7.56277814 14.2853463,7.2765502 C13.6556449,5.88993487 13.1118117,4.4651558 12.5075528,3.06899954 C12.1354565,2.21031573 11.7697208,1.34845161 11.4262472,0.480226873 C11.3467395,0.279867314 11.2449696,0.193998932 11.0159872,0.178097385 C10.1541231,0.124032103 9.29225901,0.133573037 8.43039489,0.168556451 C8.37950987,0.171736758 8.3286249,0.171736758 8.29046118,0.130392724"
                id="Fill-29"
                fill="#E7B08B"
              />
              <path
                d="M5.02619061,16.2539304 C5.02301029,16.2221273 5.01982999,16.1871439 5.01982999,16.1553408 C4.95622378,16.075833 5.00392843,16.0026859 5.03573154,15.9327191 C5.39828693,15.0931171 5.75448169,14.2503349 6.10431583,13.4075526 C6.51457588,12.4152958 6.95663903,11.4389405 7.36371872,10.4435033 C7.5354555,10.0237023 7.71991352,9.60390138 7.89801088,9.18092009 C7.93299431,9.09823199 7.94253526,8.97419988 8.07928861,8.97101959 C8.09837043,9.00600302 8.09519014,9.03780609 8.07610831,9.06960922 C7.92981402,9.44170555 7.78670001,9.81698217 7.63086483,10.1858981 C7.37962032,10.7965177 7.11883486,11.4039571 6.86122969,12.0113963 C6.62588671,12.5615901 6.38736346,13.1086034 6.1552008,13.6587971 C5.88805473,14.2853183 5.64317083,14.9182 5.3601232,15.5351803 C5.24881233,15.7768838 5.20110768,16.0472103 5.02619061,16.2539304"
                id="Fill-39"
                fill="#EAB994"
              />
              <path
                d="M13.0796907,8.88419713 C13.1083134,8.88419713 13.1369362,8.88419713 13.165559,8.88419713 C12.8697902,9.21812974 12.5231363,9.50435761 12.2114659,9.81920839 C11.9888442,10.0450104 11.9220577,10.0354695 11.8012059,9.74924154 C11.5881251,9.24357222 11.3814049,8.73472254 11.1683241,8.22587286 C10.942522,7.68522011 10.707179,7.14456729 10.4718361,6.60709484 C10.440033,6.53712805 10.4177708,6.47670214 10.4273118,6.4003747 C10.4432133,6.38765346 10.4654755,6.38447316 10.4877377,6.39401404 C10.5767864,6.44171871 10.6054091,6.53712805 10.6403926,6.62299644 C11.0220298,7.48804085 11.3814049,8.35626555 11.7503209,9.22767062 C11.8043861,9.36124367 11.8775333,9.48845608 11.9284183,9.62202913 C11.9920245,9.78104467 12.0651716,9.75878248 12.1637612,9.66019286 C12.4054649,9.42484988 12.6249062,9.17360536 12.8920523,8.97324581 C12.9492979,8.93190179 12.9938223,8.86193494 13.0796907,8.88419713"
                id="Fill-45"
                fill="#F2CBAD"
              />
              <path
                d="M8.07610831,9.06897315 C8.07292795,9.03398972 8.06974766,8.99900629 8.06656737,8.96402293 C8.0156824,8.88133484 8.06656737,8.80818769 8.09519014,8.73822083 C8.47364706,7.84137335 8.83620244,6.93816516 9.2432822,6.05721921 C9.3068884,5.91728557 9.28462621,5.72964722 9.4531827,5.64059853 C9.47544488,5.63741822 9.49770701,5.64377884 9.5167889,5.65650008 C9.54859197,5.77735186 9.43092051,5.85685966 9.42455992,5.96817053 C9.15105322,6.57560977 8.89026776,7.18940967 8.63584289,7.80320963 C8.48954866,8.16576502 8.30827093,8.51559916 8.19377977,8.89087578 C8.17151758,8.96084257 8.13971451,9.02444877 8.07610831,9.06897315"
                id="Fill-47"
                fill="#EEBD9B"
              />
              <path
                d="M9.50947417,5.66190661 C9.49357264,5.66190661 9.4776711,5.66190661 9.4617695,5.6587263 C9.42360578,5.39476054 9.51583482,5.16259788 9.67803066,4.96859894 C9.79888241,4.82230466 9.98334042,4.86682901 10.0564876,5.06082794 C10.1964212,5.43928488 10.3331746,5.82410245 10.4985507,6.19301842 C10.5271735,6.25980498 10.5303538,6.33613242 10.4731082,6.39655833 C10.4572067,6.39973863 10.4413051,6.39973863 10.4254036,6.39973863 C10.3522565,6.22800185 10.2822896,6.05308478 10.2091425,5.87816771 C10.1137332,5.69370971 10.0469466,5.49653047 9.9642586,5.30571185 C9.93563576,5.23574501 9.91019328,5.13397508 9.81478401,5.14351602 C9.72573526,5.15305694 9.70347314,5.24846626 9.67803066,5.32479371 C9.66848971,5.35023619 9.65894876,5.37885898 9.63668658,5.39794084 C9.61124409,5.49335016 9.60488351,5.5983004 9.50947417,5.66190661"
                id="Fill-51"
                fill="#E9A57B"
              />
              <path
                d="M9.63668658,5.39794084 C9.64304723,5.34705588 9.63668658,5.2929906 9.65576847,5.25164657 C9.69393219,5.17213881 9.70029278,5.05446732 9.82432489,5.05128702 C9.9451767,5.05128702 9.94199641,5.16259788 9.97061918,5.23574501 C10.0533073,5.44882581 10.1677984,5.65236568 10.2091425,5.87816771 C10.0819301,5.79229932 10.0469466,5.64282475 9.99924196,5.51879264 C9.87202956,5.17213881 9.8815705,5.16577819 9.63668658,5.39794084"
                id="Fill-59"
                fill="#FAE2CA"
              />
              <path
                d="M9.50947417,5.66190661 C9.55081819,5.57285792 9.59534256,5.48698954 9.63668658,5.39794084 C9.65894876,5.62374288 9.52855607,5.79229932 9.42360578,5.96721639 C9.38862236,5.84954493 9.49675293,5.76685684 9.50947417,5.66190661"
                id="Fill-61"
                fill="#FAE2CA"
              />
              <path
                d="M13.7348346,11.0611196 C13.4740491,11.2646594 13.2864108,11.5381661 13.0542481,11.7671485 C12.939757,11.8784594 12.8252658,11.9865899 12.6885124,12.0660977 C12.6948731,11.9166231 12.8220855,11.843476 12.9079539,11.7512469 C13.124215,11.5095433 13.359558,11.2869216 13.5917206,11.0579393 C13.6330647,11.0165953 13.6903102,10.9148253 13.7348346,11.0611196"
                id="Fill-81"
                fill="#2CA6C4"
              />
              <path
                d="M12.8265379,14.4201634 C12.5307691,14.8654069 12.2318199,15.30747 11.9424116,15.7558938 C11.4272014,16.5636927 10.9278926,17.3810324 10.4667476,18.2206343 C10.2981911,18.5291245 10.1137332,18.8312539 9.948357,19.1397441 C9.85612802,19.3114808 9.77980058,19.2764974 9.70347314,19.1461047 C9.49357264,18.7835493 9.28049185,18.4209939 9.08649288,18.0552582 C8.7843634,17.479622 8.42816867,16.9357889 8.09741636,16.3792346 C7.88751586,16.0325808 7.67125477,15.6922876 7.45181333,15.3551747 C7.38184654,15.2438638 7.37866618,15.1420939 7.42637085,15.0244224 C7.65853353,14.4901303 7.89387651,13.9558381 8.11649825,13.4151853 C8.18964534,13.2402682 8.2564319,13.2593501 8.35820182,13.3738413 C8.70485567,13.7841013 9.05150945,14.1911811 9.3981633,14.6014411 C9.50311358,14.7222929 9.61442445,14.8367841 9.70983372,14.9639965 C9.79570211,15.0880286 9.84976737,15.0912089 9.948357,14.9703571 C10.358617,14.4551469 10.778418,13.9462972 11.2204812,13.4565294 C11.5512335,13.093974 11.8724448,12.715517 12.2095577,12.356142 C12.867882,11.6532934 13.554829,10.9758873 14.2576776,10.3143827 C14.3085626,10.266678 14.3721688,10.2285143 14.4294144,10.18399 C14.4962009,10.2921205 14.4039719,10.3525464 14.3499066,10.4002511 C14.2163336,10.5274635 14.1145636,10.686479 13.9555481,10.7914292 C13.9078434,10.8264127 14.0000725,10.8073308 14.0000725,10.8391339 C13.9905315,10.9949692 13.8664994,11.0331329 13.7551886,11.0808375 C13.7043036,11.0712966 13.6565989,11.0649359 13.6152549,11.10628 C13.3131254,11.4147701 12.9887338,11.6978177 12.7311286,12.0444716 C12.7184074,12.0635534 12.7152271,12.0858156 12.7056861,12.1048975 C12.5498509,12.3529616 12.355852,12.5660424 12.1364106,12.7568611 C11.8788054,12.9826631 11.6720852,13.2466289 11.4908076,13.5392174 C11.3953982,13.6950527 11.2332024,13.8095438 11.10599,13.9494775 C10.778418,14.3152132 10.5112719,14.7350141 10.1487165,15.0721271 C10.0596679,15.1516349 9.98016013,15.2247819 9.86248861,15.2534048 C9.71937467,15.2883882 9.62078504,15.2629457 9.53173636,15.1293727 C9.31547527,14.8113416 9.07059135,14.5123924 8.75892092,14.2802298 C8.65397071,14.200722 8.58082356,14.0925915 8.5299386,13.9685594 C8.44407021,13.7586589 8.30095621,13.7427573 8.15784227,13.9176744 C8.13239979,13.9526578 8.10059665,13.9876412 8.09423607,14.0289853 C8.0433511,14.3311148 7.86843403,14.5759986 7.74758222,14.8431447 C7.59810763,15.1707167 7.64899259,15.4060597 7.92567958,15.6382223 C8.05925264,15.7527135 8.14194073,15.8799259 8.19282569,16.0484823 C8.27551372,16.3124482 8.43452926,16.5446108 8.58400385,16.7704128 C8.99108361,17.3778521 9.32183586,18.0266354 9.71301402,18.6404353 C9.79570211,18.7676477 9.86566897,18.7581068 9.948357,18.6563369 C10.0151436,18.5768291 10.0596679,18.4846002 10.0946513,18.3923711 C10.174159,18.1888313 10.317273,18.0139142 10.3967808,17.8262759 C10.5176326,17.5336873 10.7625165,17.3333277 10.8770077,17.0534605 C11.013761,16.7195278 11.2236615,16.4332999 11.4240211,16.1438917 C11.4494636,16.1057279 11.4780863,16.0771052 11.5194303,16.0580233 C11.7706749,15.9467124 11.7992977,15.6668451 11.9551329,15.4855674 C11.9996573,15.4315021 12.0918862,15.3774369 12.0219194,15.2820276 C12.0250997,15.1643561 12.079165,15.0880286 12.1872956,15.0530452 C12.3876551,14.938554 12.4830644,14.7509157 12.5530312,14.5441955 C12.5657525,14.4805893 12.5943753,14.426524 12.6579815,14.4042619 C12.7152271,14.394721 12.7724727,14.3724588 12.8265379,14.4201634"
                id="Fill-83"
                fill="#2DA6C7"
              />
              <path
                d="M13.99562,10.8454946 C14.1482749,10.778708 14.1864387,10.6165122 14.2754873,10.4956604 C14.4186013,10.3112024 14.6348624,10.2412355 14.8225007,10.1267444 C15.061024,9.97726979 15.2168593,9.73238593 15.4076779,9.53202631 C15.706627,9.2171756 16.1073461,9.04543882 16.4285575,8.76557153 C16.5748517,8.64153942 16.7084248,8.74967 16.7783917,8.88960364 C16.8483584,9.0231767 16.8897025,9.17265129 16.9342268,9.31894552 C16.9692103,9.43661704 16.8960631,9.51930507 16.8070144,9.56700974 C16.6766217,9.63061594 16.5812124,9.71966462 16.5080652,9.83733615 C16.3713119,10.0504169 16.126428,10.1521869 15.9737731,10.3366449 C15.6175784,10.7723474 15.1945971,11.1444437 14.8384023,11.5706053 C14.6062396,11.8409317 14.3263723,12.0794549 14.1450946,12.4006663 C13.9638169,12.7186974 13.6871299,12.9794828 13.4772294,13.2816123 C13.3054927,13.5328568 13.1019528,13.7491179 12.9079539,13.9781003 C12.806184,14.0989521 12.7330368,14.2579676 12.6885124,14.4201634 C12.6312669,14.4424256 12.6026441,14.4869499 12.6026441,14.5473758 C12.4690711,14.7191126 12.4086452,14.9480949 12.2114659,15.0753073 C12.1128763,15.1134711 12.0747126,15.2025198 12.0365488,15.2883882 C12.0810732,15.3965187 11.9888442,15.450584 11.9443199,15.5141902 C11.8202877,15.7050088 11.7407799,15.9244502 11.556322,16.0771052 C11.3655033,16.3347103 11.1492422,16.5732335 11.0220298,16.8753631 C10.9234402,17.1011651 10.8471128,17.3524096 10.6022288,17.479622 C10.583147,17.489163 10.5640651,17.5114252 10.5608848,17.5273267 C10.5227211,17.9439473 10.1442641,18.1983722 10.0520351,18.5927307 C10.0265927,18.7008612 9.94708492,18.8089918 9.81351186,18.8121721 C9.66721757,18.8185327 9.6831191,18.6785991 9.63859479,18.595911 C9.51138239,18.3510271 9.3460062,18.134766 9.19017102,17.9089639 C9.05023737,17.7086044 8.98027052,17.4732615 8.83715658,17.2729018 C8.62725608,16.977133 8.43007682,16.6654626 8.27742194,16.3347103 C8.1184064,15.9880565 7.87352254,15.7081891 7.66362204,15.3965187 C7.47598373,15.1166514 7.66680234,14.8177022 7.82581787,14.5728183 C7.91486656,14.436065 7.95621057,14.292951 7.99437429,14.1466567 C8.01981677,14.0544278 8.04843961,13.9653791 8.10250487,13.8954122 C8.16611107,13.8127241 8.22971727,13.7014133 8.36329033,13.7268557 C8.48414208,13.7522982 8.55410893,13.8222651 8.57955142,13.9590184 C8.6018136,14.0766899 8.69722294,14.1784599 8.79899286,14.2484267 C9.12338452,14.4710484 9.3428259,14.7858991 9.57816888,15.0848483 C9.68947975,15.2311426 9.92164244,15.2470441 10.0552154,15.1039302 C10.4463937,14.6904898 10.7739656,14.2229842 11.1619635,13.8063635 C11.3973065,13.5519387 11.5785841,13.2657108 11.7916649,13.001745 C11.9188774,12.8427295 12.1065157,12.725058 12.2496296,12.5692228 C12.3991042,12.407027 12.5422182,12.2352902 12.6885124,12.0667337 C12.9715601,11.7614239 13.267329,11.4688354 13.5503766,11.1635256 C13.6012615,11.1094603 13.6521465,11.055395 13.7348346,11.0617556 C13.820703,10.9886085 13.9288335,10.9440842 13.99562,10.8454946"
                id="Fill-85"
                fill="#01A9CE"
              />
              <path
                d="M11.5575941,16.075515 C11.7166096,15.8115492 11.8788054,15.5507638 12.037821,15.2899783 C12.069624,15.3026996 12.1618531,15.286798 12.1268697,15.3313224 C11.9296904,15.5698456 11.843822,15.891057 11.5925775,16.0882363 C11.5893972,16.0945969 11.5703153,16.0786953 11.5575941,16.075515"
                id="Fill-87"
                fill="#2CA6C4"
              />
              <path
                d="M12.2114659,15.0737172 C12.3736617,14.9210623 12.4054649,14.6729981 12.6026441,14.5457857 C12.5772016,14.7906696 12.3609405,14.927423 12.2687115,15.1341431 C12.2528099,15.1627659 12.2178265,15.1087006 12.2114659,15.0737172"
                id="Fill-89"
                fill="#2CA6C4"
              />
              <path
                d="M17.8517464,19.9033366 C17.3651589,19.9033366 16.8785714,19.9001563 16.3951642,19.9065169 C16.2679518,19.9065169 16.2043456,19.8683532 16.1534607,19.7443211 C15.7940856,18.872916 15.4219892,18.0046913 15.0530733,17.1364666 C14.8304515,16.6117153 14.6141904,16.0869641 14.3852081,15.5685735 C14.3279624,15.4445414 14.3470443,15.3554927 14.4233718,15.2537228 C14.8717956,14.6494638 15.358383,14.0738277 15.8481508,13.5045521 C16.0485104,13.2755697 16.2711321,13.0656692 16.4619507,12.827146 C16.5955238,12.6585895 16.6464087,12.7539988 16.6941134,12.8748506 C16.8372274,13.2119635 16.9708004,13.555437 17.1139144,13.8957302 C17.3842408,14.538153 17.6641081,15.1773953 17.9344344,15.8229984 C18.3001702,16.6975837 18.6531846,17.58171 19.01574,18.4562954 C19.1715752,18.8379326 19.333771,19.2163896 19.4991472,19.5916662 C19.6104581,19.8429107 19.5818352,19.9033366 19.3083285,19.9033366 C18.8249214,19.9065169 18.3383339,19.9033366 17.8517464,19.9033366 Z"
                id="Fill-107"
                fill="#FF4D00"
              />
              <path
                d="M10.4721541,6.39783041 C10.4689738,6.11478283 10.265434,5.89852168 10.2050081,5.63455594 C10.163664,5.4691798 10.0523531,5.32606583 10.0332713,5.14796845 C10.0173698,5.03665759 9.95376358,4.9507892 9.84881329,4.93488765 C9.73432213,4.91580579 9.63891279,5.02711665 9.62937191,5.10662441 C9.60392943,5.30380366 9.47671696,5.46281918 9.46081543,5.65999842 C9.25091493,6.15930714 9.03465384,6.65861587 8.82475334,7.1579246 C8.57032854,7.75900326 8.31908402,8.36326228 8.06783944,8.96434093 C7.72118566,9.72761542 7.40315459,10.5067915 7.08194328,11.2827872 C6.82751841,11.8965871 6.54765112,12.5008461 6.28368534,13.114646 C5.92112996,13.9606086 5.5681155,14.8129317 5.21510104,15.6620746 C5.14513422,15.8242704 5.04336429,15.9769254 5.0179218,16.1550228 C4.79848038,16.53666 4.66490734,16.956461 4.48999027,17.3571801 C4.17831986,18.0663893 3.87937067,18.7851395 3.5804215,19.5007093 C3.55815932,19.5452336 3.55497901,19.5961186 3.53271684,19.640643 C3.46911064,19.7773963 3.39914381,19.8729396 3.21150549,19.8729396 C2.24151081,19.8600844 1.27151614,19.8600844 0.298341161,19.8729396 C0.0757194326,19.8759859 0.101161916,19.7678554 0.152046883,19.640643 C0.361947369,19.1222524 0.594110028,18.616583 0.797649895,18.0950121 C0.950304792,17.694293 1.16338559,17.3190164 1.27151614,16.8992154 C1.67223525,16.0468922 2.00298753,15.1627659 2.38462479,14.2977215 C2.69311489,13.5948729 2.96662159,12.8729424 3.26557077,12.1605529 C3.59314274,11.3940981 3.89527223,10.614922 4.21966389,9.84528695 C4.54405555,9.06929115 4.89070938,8.30601666 5.21192074,7.52684064 C5.48224712,6.86851637 5.77165537,6.21655275 6.03562113,5.55822849 C6.51266769,4.37515302 7.01515672,3.20161848 7.51128516,2.02808394 C7.77843121,1.39838248 8.02967572,0.765500713 8.29046118,0.129438636 C9.26681653,0.0531111802 10.2431718,0.0849142877 11.2195271,0.123078015 C11.3880835,0.129438636 11.4008048,0.263011673 11.4389685,0.355240669 C11.8015239,1.18530169 12.1608989,2.01854301 12.4980118,2.86132527 C12.7587973,3.5132889 13.0513859,4.15253129 13.3153517,4.80449493 C13.6015796,5.52006477 13.8973484,6.22927399 14.1962976,6.93848322 C14.240822,7.04661374 14.2917069,7.14838372 14.3425919,7.25333394 C14.4920665,7.55864371 14.4570831,7.67313494 14.189937,7.88621573 C13.7987589,8.19470579 13.4234822,8.52227782 13.0800087,8.8848332 C12.7460761,9.1424383 12.4598481,9.44774813 12.1577187,9.74033666 C12.0432275,9.85482783 11.976441,9.8993522 11.8937529,9.6894517 C11.648869,9.08201239 11.3690017,8.48729439 11.1113965,7.88621573 C10.9237582,7.4505132 10.7424805,7.00845001 10.5580225,6.57274748 C10.5357603,6.51232164 10.5007769,6.45507602 10.4721541,6.39783041"
                id="Fill-113"
                fill="#FF4D00"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const NetSuiteLogoIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};
  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 24 24'}
      style={style}
      className={userProps.className || ''}
    >
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" id="netsuite">
        <g id="logo" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g transform="translate(2.000000, 2.000000)" fillRule="nonzero">
            <path
              d="M7.61624904,8.52855882 C7.94427054,8.93858569 8.2825427,9.34861256 8.60031352,9.75863942 C9.37936457,10.7427039 10.1481649,11.7370191 10.9169653,12.7210835 C11.3269922,13.2438678 11.7370191,13.766652 12.1367952,14.299687 C12.270054,14.4739484 12.4135634,14.5559538 12.6493288,14.5559538 C14.9967326,14.5457031 17.3441365,14.5559538 19.6915403,14.5457031 C19.9375564,14.5457031 20.0093111,14.6174578 19.9990604,14.8634739 C19.9888097,16.1755599 19.9888097,17.4978965 19.9990604,18.8099825 C19.9990604,19.0149959 19.9478071,19.0867506 19.7325429,19.0867506 C15.765533,19.0765 11.8087738,19.0765 7.84176382,19.0867506 C7.62649972,19.0867506 7.523993,19.0457479 7.523993,18.7894812 C7.53424367,15.4375115 7.523993,12.0752912 7.523993,8.72332158 C7.523993,8.66181755 7.53424367,8.6105642 7.54449434,8.54906017 C7.57524636,8.53880949 7.5957477,8.53880949 7.61624904,8.52855882 Z"
              fill={style.color || '#13527C'}
            />
            <path
              d="M12.4135634,10.5786932 C12.2803047,10.4249331 12.1880486,10.3429277 12.1162939,10.2506717 C10.7427039,8.49780681 9.34861256,6.7654433 8.00577457,4.9920771 C7.71875576,4.61280225 7.42148628,4.50004486 6.97045673,4.50004486 C4.75631165,4.5205462 2.55241724,4.51029553 0.338272165,4.51029553 C0.0102506717,4.51029553 0.0102506717,4.51029553 0.0102506717,4.17202337 C0.0102506717,2.89068941 0.0205013433,1.60935545 0,0.328021493 C0,0.0717547016 0.0717547016,-8.90048623e-15 0.338272165,-8.90048623e-15 C4.25402874,0.0102506717 8.18003599,0.0102506717 12.0957926,0.0102506717 C12.4545661,0.0102506717 12.4545661,0.0102506717 12.4545661,0.358773508 C12.4545661,3.64923911 12.4545661,6.92945404 12.4545661,10.2199196 C12.4443154,10.3121757 12.4238141,10.4044317 12.4135634,10.5786932 Z"
              fill={style.color || '#13527C'}
            />
            <path
              d="M19.107252,7.35998225 C19.107252,9.38961524 19.107252,11.4294989 19.107252,13.4591319 C19.107252,13.7974041 19.107252,13.8076547 18.7689798,13.8076547 C17.6721579,13.8076547 16.5855867,13.8076547 15.4887649,13.8076547 C15.1504927,13.8076547 15.1504927,13.8076547 15.1504927,13.4693826 C15.1504927,10.6299465 15.1504927,7.78025979 15.1504927,4.94082374 C15.1504927,4.56154889 15.1504927,4.56154889 14.7712179,4.56154889 C14.330439,4.56154889 13.8794094,4.55129822 13.4386305,4.56154889 C13.2541185,4.56154889 13.1823638,4.50004486 13.1823638,4.3052821 C13.1926144,3.25971359 13.1823638,2.20389441 13.1823638,1.1583259 C13.1823638,0.98406448 13.2438678,0.912309778 13.4181292,0.912309778 C15.2324981,0.912309778 17.0571176,0.912309778 18.8714865,0.912309778 C19.0970013,0.912309778 19.107252,1.03531784 19.107252,1.20957926 C19.107252,3.25971359 19.107252,5.30984792 19.107252,7.35998225 Z"
              fill={style.color || '#B4C6D4'}
            />
            <path
              d="M0.902059106,11.706267 C0.902059106,9.66638338 0.902059106,7.62649972 0.902059106,5.58661606 C0.902059106,5.36110128 0.953312465,5.27909591 1.19932858,5.27909591 C2.3166518,5.28934658 3.43397501,5.28934658 4.55129822,5.27909591 C4.79731434,5.27909591 4.86906904,5.35085061 4.86906904,5.59686673 C4.85881837,8.44655345 4.85881837,11.3064908 4.86906904,14.1561776 C4.86906904,14.5457031 4.86906904,14.5457031 5.23809322,14.5457031 C5.66862143,14.5457031 6.10940031,14.5559538 6.53992852,14.5457031 C6.73469128,14.5457031 6.81669666,14.6072071 6.81669666,14.8122206 C6.80644598,15.8577891 6.80644598,16.8931069 6.81669666,17.9386754 C6.81669666,18.1436888 6.73469128,18.1949422 6.53992852,18.1949422 C4.75631165,18.1949422 2.97269478,18.1846915 1.18907791,18.1949422 C0.953312465,18.1949422 0.902059106,18.1026862 0.902059106,17.8874221 C0.912309778,16.48308 0.902059106,15.0889887 0.902059106,13.6846467 C0.902059106,13.018353 0.902059106,12.36231 0.902059106,11.706267 Z"
              fill={style.color || '#B4C6D4'}
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SalesforceLogoIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <SvgIcon
      viewBox={userProps.viewBox || '0 0 28 28'}
      style={style}
      className={userProps.className || ''}
    >
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28.87 28.87" id="salesforce">
        <g id="logo" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g fillRule="nonzero">
            <g id="salesforca-seeklogo.combackup" transform="translate(2.000000, 5.000000)">
              <path
                d="M8.322792,12.5212537 C8.96780623,13.1959833 9.86581191,13.6144358 10.8589743,13.6144358 C12.1792023,13.6144358 13.3310541,12.8753509 13.9444444,11.7781645 C14.4774929,12.0172802 15.0675214,12.1502812 15.688319,12.1502812 C18.0695157,12.1502812 20,10.1953101 20,7.78384537 C20,5.37209465 18.0695157,3.41712357 15.688319,3.41712357 C15.3977208,3.41712357 15.1136752,3.44629796 14.8390313,3.50207256 C14.2988603,2.53474086 13.2695157,1.88117701 12.0880342,1.88117701 C11.5934473,1.88117701 11.125641,1.99587245 10.7091168,2.19980726 C10.1615384,0.90669471 8.88575497,1.28025718e-15 7.39886038,1.28025718e-15 C5.85042735,1.28025718e-15 4.53076923,0.983635052 4.02421652,2.36312672 C3.80284901,2.31593283 3.57350427,2.29133479 3.33817663,2.29133479 C1.49458689,2.29133479 1.28025718e-15,3.80725971 1.28025718e-15,5.67756782 C1.28025718e-15,6.93092308 0.671509971,8.02524923 1.66923077,8.61073953 C1.46381766,9.08525258 1.34957265,9.60896175 1.34957265,10.1595571 C1.34957265,12.3104544 3.08888888,14.0540541 5.23418802,14.0540541 C6.49373218,14.0540541 7.61310541,13.4528325 8.322792,12.5212537"
                fill="#00A1E0"
                transform="translate(10.000000, 7.027027) scale(-1, 1) rotate(-180.000000) translate(-10.000000, -7.027027) "
              />
              <path
                d="M2.88756924,5.85256351 C2.87457229,5.81814373 2.89229541,5.81096045 2.89643081,5.80497441 C2.93542169,5.77624135 2.97500335,5.75558948 3.0148804,5.73254317 C3.22637641,5.61880818 3.42605701,5.58558559 3.63489456,5.58558559 C4.06024969,5.58558559 4.32432432,5.81485139 4.32432432,6.18389153 L4.32432432,6.19107479 C4.32432432,6.53227978 4.02628036,6.65619106 3.74655029,6.74568256 L3.71021787,6.75765467 C3.49931263,6.82709286 3.31735515,6.88695341 3.31735515,7.02762562 L3.31735515,7.0351082 C3.31735515,7.15542786 3.42369393,7.24402142 3.58851905,7.24402142 C3.77165805,7.24402142 3.98906178,7.18236508 4.1290745,7.1039478 C4.1290745,7.1039478 4.1701331,7.07701056 4.18519775,7.11741641 C4.19346856,7.1389662 4.26436106,7.33231569 4.27174571,7.35326688 C4.27972112,7.37601389 4.26554261,7.39277483 4.25106872,7.40175391 C4.09126517,7.50022447 3.87031681,7.56756757 3.64168843,7.56756757 L3.59915292,7.56726827 C3.20983483,7.56726827 2.93808016,7.32902337 2.93808016,6.98751908 L2.93808016,6.98033582 C2.93808016,6.62027476 3.23789645,6.50354674 3.51880805,6.42213641 L3.56400205,6.40806919 C3.7687042,6.34431773 3.94504933,6.28954537 3.94504933,6.14348568 L3.94504933,6.13630241 C3.94504933,6.00281344 3.83043977,5.90344497 3.64552845,5.90344497 C3.57374977,5.90344497 3.344826,5.90494148 3.09758834,6.06327258 C3.0677544,6.08093142 3.05032665,6.09380144 3.02728658,6.10786867 C3.01517578,6.11565054 2.98475108,6.12911915 2.97145872,6.088414 L2.88756924,5.85256351 L2.88756924,5.85256351 Z"
                fill="#FFFFFF"
                transform="translate(3.603604, 6.576577) scale(-1, 1) rotate(-180.000000) translate(-3.603604, -6.576577) "
              />
              <path
                d="M8.83351517,5.85256351 C8.82051825,5.81814373 8.83824133,5.81096045 8.84237679,5.80497441 C8.88136768,5.77624135 8.92094932,5.75558948 8.96082632,5.73254317 C9.17232234,5.61880818 9.37200294,5.58558559 9.58084054,5.58558559 C10.0061956,5.58558559 10.2702703,5.81485139 10.2702703,6.18389153 L10.2702703,6.19107479 C10.2702703,6.53227978 9.97222634,6.65619106 9.69249625,6.74568256 L9.6561638,6.75765467 C9.44525854,6.82709286 9.2633011,6.88695341 9.2633011,7.02762562 L9.2633011,7.0351082 C9.2633011,7.15542786 9.36963991,7.24402142 9.53446503,7.24402142 C9.71760398,7.24402142 9.93500775,7.18236508 10.0750204,7.1039478 C10.0750204,7.1039478 10.116079,7.07701056 10.1311437,7.11741641 C10.1394145,7.1389662 10.210307,7.33231569 10.2176916,7.35326688 C10.225667,7.37601389 10.2114886,7.39277483 10.1970147,7.40175391 C10.0372111,7.50022447 9.81626276,7.56756757 9.58763434,7.56756757 L9.54509885,7.56726827 C9.15578077,7.56726827 8.88402609,7.32902337 8.88402609,6.98751908 L8.88402609,6.98033582 C8.88402609,6.62027476 9.18384238,6.50354674 9.46475398,6.42213641 L9.50994798,6.40806919 C9.71465011,6.34431773 9.89129065,6.28954537 9.89129065,6.14348568 L9.89129065,6.13630241 C9.89129065,6.00281344 9.77638575,5.90344497 9.59147436,5.90344497 C9.51969573,5.90344497 9.29077195,5.90494148 9.04353431,6.06327258 C9.01370034,6.08093142 8.99597719,6.09320283 8.97352795,6.10786867 C8.96584793,6.11295681 8.92981085,6.12702404 8.91740468,6.088414 L8.83351517,5.85256351 L8.83351517,5.85256351 Z"
                fill="#FFFFFF"
                transform="translate(9.549550, 6.576577) scale(-1, 1) rotate(-180.000000) translate(-9.549550, -6.576577) "
              />
              <path
                d="M12.9524415,6.66502368 C12.9524415,6.47416414 12.9165343,6.32383144 12.8458249,6.21758537 C12.7759441,6.11243463 12.6701561,6.06122841 12.5226606,6.06122841 C12.374889,6.06122841 12.2696534,6.11216079 12.2008774,6.21758537 C12.1312728,6.3235576 12.0959181,6.47416414 12.0959181,6.66502368 C12.0959181,6.85560941 12.1312728,7.00566829 12.2008774,7.11081903 C12.2696534,7.21487445 12.374889,7.26553301 12.5226606,7.26553301 C12.6701561,7.26553301 12.7759441,7.21487445 12.8461011,7.11081903 C12.9165343,7.00566829 12.9524415,6.85560941 12.9524415,6.66502368 M13.2844444,7.01881213 C13.2518517,7.12807032 13.2010293,7.2244585 13.1333582,7.3046907 C13.065687,7.38519674 12.9800623,7.44982063 12.8784175,7.49691941 C12.7770489,7.54374434 12.6571743,7.56756757 12.5226606,7.56756757 C12.3878708,7.56756757 12.2679961,7.54374434 12.1666275,7.49691941 C12.0649828,7.44982063 11.979358,7.38519674 11.9114107,7.3046907 C11.8440157,7.22418467 11.7931933,7.12779649 11.7603245,7.01881213 C11.7280081,6.91010159 11.7117117,6.79125935 11.7117117,6.66502368 C11.7117117,6.53878804 11.7280081,6.41967195 11.7603245,6.31123524 C11.7931933,6.20225088 11.8437395,6.1058627 11.9116869,6.02535667 C11.979358,5.94485063 12.065259,5.88050056 12.1666275,5.83477094 C12.2682723,5.78904132 12.3878708,5.76576577 12.5226606,5.76576577 C12.6571743,5.76576577 12.7767727,5.78904132 12.8784175,5.83477094 C12.9797861,5.88050056 13.065687,5.94485063 13.1333582,6.02535667 C13.2010293,6.10558886 13.2518517,6.20197706 13.2844444,6.31123524 C13.317037,6.41994578 13.3333333,6.53906186 13.3333333,6.66502368 C13.3333333,6.79098551 13.317037,6.91010159 13.2844444,7.01881213"
                fill="#FFFFFF"
                transform="translate(12.522523, 6.666667) scale(-1, 1) rotate(-180.000000) translate(-12.522523, -6.666667) "
              />
              <path
                d="M16.3074001,6.11118182 C16.2957134,6.14320533 16.2626984,6.13116231 16.2626984,6.13116231 C16.2115691,6.11282405 16.1572259,6.09585432 16.0993766,6.08736946 C16.0406508,6.07888459 15.9760817,6.07450532 15.906838,6.07450532 C15.7367963,6.07450532 15.6018147,6.12185632 15.5051071,6.21546353 C15.4081074,6.30907074 15.3537641,6.46042975 15.3543485,6.6651613 C15.3549328,6.85155458 15.4028483,6.99169168 15.4890379,7.09843672 C15.574643,7.20463438 15.70495,7.25910173 15.8787898,7.25910173 C16.0237052,7.25910173 16.1341446,7.24350052 16.2498431,7.20928737 C16.2498431,7.20928737 16.277599,7.19806544 16.2907466,7.2320049 C16.3214242,7.31192685 16.3442133,7.36913124 16.3769361,7.45699063 C16.3862855,7.48189782 16.3634964,7.49257232 16.3553157,7.49558308 C16.3097375,7.5122791 16.2022197,7.53937593 16.120997,7.55087155 C16.0450334,7.56181977 15.9562144,7.56756757 15.8574616,7.56756757 C15.7099168,7.56756757 15.5784412,7.54402892 15.4659566,7.4969516 C15.3537641,7.450148 15.2585174,7.38555357 15.1831381,7.30508422 C15.1077588,7.22461486 15.0504939,7.12827061 15.0122199,7.01933591 C14.9742381,6.91067492 14.954955,6.79133942 14.954955,6.6651613 C14.954955,6.39227714 15.0335481,6.17167068 15.1886893,6.01018458 C15.3441226,5.84815105 15.5775647,5.76576577 15.8820037,5.76576577 C16.0619791,5.76576577 16.2466292,5.79997892 16.3792734,5.84897216 C16.3792734,5.84897216 16.4046921,5.86046778 16.3935897,5.88811202 L16.3074001,6.11118182 L16.3074001,6.11118182 Z"
                fill="#FFFFFF"
                transform="translate(15.675676, 6.666667) scale(-1, 1) rotate(-180.000000) translate(-15.675676, -6.666667) "
              />
              <path
                d="M16.8060544,6.84805586 C16.8224753,6.9540117 16.8533005,7.04217134 16.9008346,7.11089206 C16.972568,7.21520518 17.0820406,7.27242682 17.2358785,7.27242682 C17.3897163,7.27242682 17.4914106,7.21493139 17.5642963,7.11089206 C17.6126947,7.04217134 17.633725,6.95017867 17.6420795,6.84805586 L16.8060544,6.84805586 L16.8060544,6.84805586 Z M17.9719378,7.08104919 C17.942553,7.18645746 17.8696673,7.29296089 17.8218451,7.34169509 C17.7463666,7.41890323 17.6726166,7.47283941 17.5994428,7.50295606 C17.5037983,7.54183391 17.3891401,7.56756995 17.2635347,7.56756995 C17.1171871,7.56756995 16.9843795,7.544298 16.8766355,7.49611136 C16.7686033,7.44792472 16.6778563,7.38221568 16.606699,7.30035316 C16.5355418,7.21876442 16.4819578,7.12156977 16.4479637,7.01123334 C16.4136815,6.90144445 16.3963964,6.78179923 16.3963964,6.6555831 C16.3963964,6.52717667 16.4142577,6.40753145 16.4496923,6.29993287 C16.4854149,6.19151294 16.5424559,6.09596103 16.6196629,6.01683638 C16.6965818,5.93716415 16.7956833,5.87474056 16.9143747,5.83120831 C17.0322018,5.78794986 17.1753804,5.76549927 17.3398774,5.76576815 C17.6783783,5.7668682 17.8567034,5.83860058 17.9301653,5.87720465 C17.9431292,5.88404934 17.9555169,5.896096 17.9399602,5.93059325 L17.8633294,6.13456509 C17.851806,6.16495554 17.8192523,6.15373024 17.8192523,6.15373024 C17.7354193,6.12416116 17.6161518,6.07104635 17.338149,6.07159393 C17.1563668,6.07186772 17.0215426,6.12279222 16.9371335,6.20246445 C16.8504197,6.28405318 16.808071,6.4039722 16.8005808,6.573173 L17.972802,6.57207786 C17.972802,6.57207786 18.0036272,6.57262543 18.0067961,6.60109934 C18.0079485,6.613146 18.0471281,6.82998588 17.9719378,7.08104919 L17.9719378,7.08104919 Z"
                fill="#FFFFFF"
                transform="translate(17.207207, 6.666668) scale(-1, 1) rotate(-180.000000) translate(-17.207207, -6.666668) "
              />
              <path
                d="M7.43669492,6.84805586 C7.45340429,6.9540117 7.48394213,7.04217134 7.53147742,7.11089206 C7.60321252,7.21520518 7.71268777,7.27242682 7.86652932,7.27242682 C8.02037085,7.27242682 8.12206757,7.21493139 8.19524308,7.11089206 C8.24335459,7.04217134 8.26438539,6.95017867 8.27274005,6.84805586 L7.43669492,6.84805586 L7.43669492,6.84805586 Z M8.60231818,7.08104919 C8.5729327,7.18645746 8.50033335,7.29296089 8.45250995,7.34169509 C8.37702966,7.41890323 8.30327787,7.47283941 8.23010236,7.50295606 C8.13445553,7.54183391 8.01979467,7.56756995 7.89418618,7.56756995 C7.74812317,7.56756995 7.61502432,7.544298 7.50727764,7.49611136 C7.39924286,7.44792472 7.30849364,7.38221568 7.23733474,7.30035316 C7.16617582,7.21876442 7.11259058,7.12156977 7.07859562,7.01123334 C7.04460068,6.90144445 7.02702703,6.78179923 7.02702703,6.6555831 C7.02702703,6.52717667 7.04488877,6.40753145 7.08032418,6.29993287 C7.11604768,6.19151294 7.17309005,6.09596103 7.2502989,6.01683638 C7.32721968,5.93716415 7.42632357,5.87474056 7.54501778,5.83120831 C7.66284773,5.78794986 7.80602986,5.76549927 7.97053081,5.76576815 C8.30903977,5.7668682 8.48736914,5.83860058 8.56083283,5.87720465 C8.57379695,5.88404934 8.58618501,5.896096 8.570628,5.93059325 L8.49428337,6.13456509 C8.48247159,6.16495554 8.44991708,6.15373024 8.44991708,6.15373024 C8.36608213,6.12416116 8.2470998,6.07104635 7.96851413,6.07159393 C7.78701573,6.07186772 7.65218829,6.12279222 7.56777712,6.20246445 C7.48106119,6.28405318 7.43871157,6.4039722 7.43122115,6.573173 L8.60347052,6.57207786 C8.60347052,6.57207786 8.6342965,6.57262543 8.63746546,6.60109934 C8.63861787,6.613146 8.67779847,6.82998588 8.60231818,7.08104919 L8.60231818,7.08104919 Z"
                fill="#FFFFFF"
                transform="translate(7.837838, 6.666668) scale(-1, 1) rotate(-180.000000) translate(-7.837838, -6.666668) "
              />
              <path
                d="M4.96333114,6.11808407 C4.91977685,6.15282355 4.91375047,6.16157679 4.89895845,6.18400699 C4.87704434,6.21819937 4.86581337,6.26688933 4.86581337,6.32870915 C4.86581337,6.42663614 4.89813667,6.49693568 4.96524863,6.54425795 C4.96442685,6.5439844 5.06112285,6.62768735 5.28848173,6.62467843 C5.44818077,6.62249011 5.59089641,6.59896575 5.59089641,6.59896575 L5.59089641,6.09291849 L5.59117034,6.09291849 C5.59117034,6.09291849 5.44955041,6.06255564 5.29012528,6.05298178 C5.06331425,6.03930483 4.96250937,6.11835762 4.96333114,6.11808407 M5.40681791,6.90013225 C5.36162005,6.90341472 5.30299982,6.9053295 5.23287467,6.9053295 C5.13727438,6.9053295 5.04496119,6.89329378 4.95840047,6.87004295 C4.87129189,6.84679214 4.79294895,6.81041144 4.72556306,6.76226858 C4.65790326,6.71385216 4.60339191,6.65203232 4.56394652,6.57872386 C4.52450114,6.50541539 4.5045045,6.41897705 4.5045045,6.32214422 C4.5045045,6.22367016 4.52148795,6.13805243 4.55545481,6.06802643 C4.58942168,5.99772688 4.63845449,5.93918953 4.70090969,5.89405559 C4.76281706,5.84892164 4.8392425,5.81582342 4.92799465,5.79585506 C5.01537715,5.7758867 5.11453849,5.76576577 5.22301332,5.76576577 C5.33724061,5.76576577 5.45119396,5.77506609 5.56158629,5.79394029 C5.6708829,5.81254094 5.80510681,5.83962132 5.84236079,5.84810103 C5.87934086,5.85685427 5.9204298,5.86806937 5.9204298,5.86806937 C5.94809638,5.87490786 5.94590497,5.90445007 5.94590497,5.90445007 L5.9453571,6.92228892 C5.9453571,7.14549679 5.88564115,7.31098793 5.76812676,7.41356508 C5.6511602,7.51586868 5.47886054,7.56756757 5.25615841,7.56756757 C5.17261088,7.56756757 5.03811303,7.55607894 4.95757868,7.53994013 C4.95757868,7.53994013 4.71405816,7.49289141 4.61380112,7.41465923 C4.61380112,7.41465923 4.59188701,7.40098229 4.60393977,7.37034591 L4.68283056,7.15862667 C4.69269191,7.13127276 4.71926277,7.1405731 4.71926277,7.1405731 C4.71926277,7.1405731 4.72775447,7.14385555 4.73761582,7.14959989 C4.95210016,7.26612751 5.22328724,7.26257151 5.22328724,7.26257151 C5.34381485,7.26257151 5.43640195,7.23850007 5.49885715,7.19063074 C5.55966881,7.1441291 5.59062249,7.07382957 5.59062249,6.92557139 L5.59062249,6.87852267 C5.49474827,6.89219962 5.40681791,6.90013225 5.40681791,6.90013225"
                fill="#FFFFFF"
                transform="translate(5.225225, 6.666667) scale(-1, 1) rotate(-180.000000) translate(-5.225225, -6.666667) "
              />
              <path
                d="M14.7724213,7.49149414 C14.7813222,7.51747468 14.7626591,7.52990014 14.7549067,7.5327241 C14.7350951,7.54034882 14.6357499,7.56096381 14.5590876,7.56576457 C14.4123669,7.57451888 14.3308234,7.55023273 14.2578938,7.51803945 C14.1855383,7.48584621 14.1051434,7.43388515 14.060352,7.37486416 L14.060352,7.5146507 C14.060352,7.5341361 14.0462828,7.54966792 14.0267584,7.54966792 L13.7272873,7.54966792 C13.7077627,7.54966792 13.6936937,7.5341361 13.6936937,7.5146507 L13.6936937,5.80078299 C13.6936937,5.78157999 13.7097726,5.76576576 13.7292971,5.76576576 L14.0362335,5.76576576 C14.055758,5.76576576 14.0715498,5.78157999 14.0715498,5.80078299 L14.0715498,6.65701086 C14.0715498,6.77194644 14.0844704,6.88659965 14.1103117,6.9586109 C14.1355786,7.02977497 14.1700336,7.08681917 14.2125281,7.12776673 C14.2553097,7.16843192 14.3038337,7.19695401 14.3569518,7.21305064 C14.4112184,7.22942969 14.4712275,7.23479522 14.5137219,7.23479522 C14.5748795,7.23479522 14.6420667,7.21926338 14.6420667,7.21926338 C14.6644625,7.21672181 14.6770959,7.23027686 14.6845612,7.25032706 C14.7046599,7.30285291 14.7615106,7.46014807 14.7724213,7.49149414"
                fill="#FFFFFF"
                transform="translate(14.234234, 6.666667) scale(-1, 1) rotate(-180.000000) translate(-14.234234, -6.666667) "
              />
              <path
                d="M11.8688733,8.24884658 C11.8326025,8.26027894 11.7996798,8.26799578 11.7567128,8.27628423 C11.7131879,8.28428697 11.6612928,8.28828829 11.6024226,8.28828829 C11.3970741,8.28828829 11.2352506,8.22883996 11.1216952,8.11165813 C11.0086977,7.99504796 10.9319711,7.8175604 10.8934683,7.58405423 L10.8795179,7.50545668 L10.6217164,7.50545668 C10.6217164,7.50545668 10.5904678,7.50659993 10.5837716,7.47173119 L10.5416417,7.22965074 C10.5385727,7.206786 10.5483378,7.19220973 10.5784705,7.19220973 L10.8292969,7.19220973 L10.5748434,5.73686894 C10.555034,5.61968714 10.5321555,5.52336942 10.506766,5.45020225 C10.4819344,5.37817831 10.4576609,5.32416036 10.4275282,5.28471868 C10.3985116,5.24699186 10.371169,5.21898256 10.323738,5.20269142 C10.2846772,5.18925838 10.2394782,5.18297058 10.1900942,5.18297058 C10.1627515,5.18297058 10.1262017,5.18754353 10.0991381,5.19325971 C10.0723536,5.19869009 10.0581243,5.20469209 10.0377569,5.21355218 C10.0377569,5.21355218 10.0084612,5.22498454 9.99674294,5.19497458 C9.98753578,5.17010917 9.92057431,4.98176086 9.91248314,4.95861031 C9.90467099,4.93545975 9.91583123,4.91745377 9.93006054,4.91202339 C9.96354124,4.90001941 9.98837277,4.89201674 10.0338508,4.88087018 C10.0969061,4.86572231 10.1501963,4.86486486 10.2001383,4.86486486 C10.3044866,4.86486486 10.3999066,4.88001276 10.4788653,4.9091653 C10.5581031,4.93860366 10.6272965,4.98976351 10.6886779,5.05892935 C10.7548023,5.13381139 10.7963742,5.21212312 10.835993,5.3193016 C10.8753328,5.42505103 10.9090926,5.5565233 10.9358772,5.70971706 L11.1917257,7.19220973 L11.5655937,7.19220973 C11.5655937,7.19220973 11.5971214,7.19106649 11.6035386,7.22622103 L11.6459475,7.46801568 C11.6487375,7.49116622 11.6392514,7.50545668 11.6088396,7.50545668 L11.2458528,7.50545668 C11.2478059,7.51374515 11.2642672,7.6446458 11.3058392,7.76782959 C11.3236955,7.8201327 11.3571763,7.86271825 11.3853559,7.89187081 C11.4132565,7.92045175 11.4453421,7.94074418 11.4804969,7.95246234 C11.5164887,7.9644664 11.5575025,7.97018254 11.6024226,7.97018254 C11.6364613,7.97018254 11.670221,7.96618121 11.6956105,7.96075088 C11.7307653,7.95303404 11.7444366,7.94903263 11.7536438,7.94617459 C11.7907516,7.93474223 11.7957737,7.94588879 11.8030279,7.96418059 L11.8897987,8.20826166 C11.8987269,8.23455611 11.8766854,8.24570267 11.8688733,8.24884658"
                fill="#FFFFFF"
                transform="translate(10.900901, 6.576577) scale(-1, 1) rotate(-180.000000) translate(-10.900901, -6.576577) "
              />
              <path
                d="M6.84684685,5.07978977 C6.84684685,5.0605813 6.83338827,5.04504505 6.81471105,5.04504505 L6.51834762,5.04504505 C6.4996704,5.04504505 6.48648649,5.0605813 6.48648649,5.07978977 L6.48648649,7.53282286 C6.48648649,7.55203132 6.4996704,7.56756757 6.51834762,7.56756757 L6.81471105,7.56756757 C6.83338827,7.56756757 6.84684685,7.55203132 6.84684685,7.53282286 L6.84684685,5.07978977 L6.84684685,5.07978977 Z"
                fill="#FFFFFF"
                transform="translate(6.666667, 6.306306) scale(-1, 1) rotate(-180.000000) translate(-6.666667, -6.306306) "
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CreditCardIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <SvgIcon viewBox="-5 0 30 22" style={style} className={userProps.className || ''}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        height="22px"
        viewBox="0 0 26 24"
        width="22px"
        fill="#FFFFFF"
      >
        <path d="M0 0h24v24H0V0z" fill="none" />
        <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z" />
      </svg>
    </SvgIcon>
  );
};

export const PeopleAltIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <SvgIcon viewBox="0 0 32 28" style={style} className={userProps.className || ''}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        enableBackground="new 0 0 20 20"
        height="28px"
        viewBox="0 0 20 20"
        width="28px"
        fill="#FFFFFF"
      >
        <g>
          <rect fill="none" height="20" width="20" x="0" />
        </g>
        <g>
          <g />
          <g>
            <g>
              <path d="M8.5,6C9.33,6,10,6.67,10,7.5S9.33,9,8.5,9S7,8.33,7,7.5S7.67,6,8.5,6 M8.5,5C7.12,5,6,6.12,6,7.5C6,8.88,7.12,10,8.5,10 S11,8.88,11,7.5C11,6.12,9.88,5,8.5,5L8.5,5z" />
            </g>
            <g>
              <path
                d="M10.99,9.95C11.16,9.98,11.33,10,11.5,10c1.38,0,2.5-1.12,2.5-2.5 C14,6.12,12.88,5,11.5,5c-0.17,0-0.34,0.02-0.51,0.05C11.61,5.68,12,6.55,12,7.5S11.61,9.32,10.99,9.95z"
                fillRule="evenodd"
              />
            </g>
            <g>
              <path d="M8.5,12c2.15,0,4.38,0.77,4.5,1.23V14H4l0-0.77C4.12,12.77,6.35,12,8.5,12 M8.5,11C6.66,11,3,11.66,3,13.23V15h11v-1.77 C14,11.66,10.34,11,8.5,11L8.5,11z" />
            </g>
            <g>
              <path
                d="M13.73,11.23c0.75,0.48,1.27,1.12,1.27,2V15h2v-1.77 C17,12.18,15.36,11.54,13.73,11.23z"
                fillRule="evenodd"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const TransferredIcon: React.FC<Props> = (userProps) => {
  const style = userProps.style || {};

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height="48"
      viewBox="0 -960 960 960"
      width="48"
      style={style}
      className={userProps.className || ''}
    >
      <path
        fill="rgb(110, 110, 110)"
        d="M450-280H280q-83 0-141.5-58.5T80-480q0-83 58.5-141.5T280-680h170v60H280q-58.333 0-99.167 40.765-40.833 40.764-40.833 99Q140-422 180.833-381q40.834 41 99.167 41h170v60ZM325-450v-60h310v60H325Zm185 170v-60h170q58.333 0 99.167-40.765 40.833-40.764 40.833-99Q820-538 779.167-579 738.333-620 680-620H510v-60h170q83 0 141.5 58.5T880-480q0 83-58.5 141.5T680-280H510Z"
      />
    </svg>
  );
};

export const AskNueGradientIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <defs>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="0%" id="linearGradient-1">
          <stop stopColor="#67D2C9" offset="0%" />
          <stop stopColor="#6239EB" offset="100%" />
        </linearGradient>
      </defs>
      <g id="ask-nue" stroke="none" strokeWidth="1" /* fill="none" fill-rule="evenodd" */>
        <g
          id="魔法笔backup-3"
          transform="translate(2.000000, 2.000000)"
          //   fill="url(#linearGradient-1)"
          fillRule="nonzero"
        >
          <path
            d="M10.2253983,8.86034516 L8.13156677,8.23032061 C7.98004255,8.18751389 7.85223538,8.08531354 7.77715135,7.94691414 C7.70206732,7.80851475 7.68607405,7.64565346 7.73280123,7.49529198 C7.77327222,7.37147859 7.85374198,7.2646253 7.96156014,7.19153015 L10.6404145,5.35645866 C10.8019169,5.24754685 10.898129,5.06497859 10.8966745,4.87018972 L10.8541728,1.69506603 C10.854504,1.53692443 10.9176433,1.3853915 11.0297007,1.27380293 C11.1417582,1.16221437 11.2935545,1.09971097 11.4516961,1.09997912 C11.5843057,1.09814 11.714018,1.13889421 11.8217105,1.21629738 L14.451813,3.1163714 C14.6131185,3.23167802 14.8197047,3.26324626 15.0080846,3.20137471 L18.1157057,2.18133497 C18.4288629,2.07570087 18.7691872,2.23865746 18.8832356,2.54884929 C18.9259226,2.67063874 18.9259226,2.8033245 18.8832356,2.92511395 L17.8294445,5.93398116 C17.7657305,6.11700039 17.799145,6.31984072 17.918198,6.47275215 L19.8807745,9.01910134 C19.9761847,9.14315151 20.0169122,9.30073083 19.9935589,9.45547634 C19.9702055,9.61022185 19.8847889,9.74876416 19.7570196,9.83913328 C19.6493271,9.91653645 19.5196148,9.95729066 19.3870052,9.95538781 L16.1068774,9.91538626 C15.9092473,9.91197609 15.7221174,10.0041446 15.6043579,10.1628959 L13.7105341,12.7567469 C13.5118957,13.0223383 13.1391811,13.0840898 12.8655012,12.8967524 C12.7574622,12.8233382 12.676969,12.7160139 12.6367423,12.5917405 L12.0292186,10.7004168 L4.35141951,19.2820011 C3.95157978,19.7210337 3.39148789,19.9801331 2.79802158,20.0006056 C2.20455526,20.0210781 1.62794005,19.801191 1.1987967,19.3907554 C0.776505032,18.98773 0.547089488,18.4231113 0.568597679,17.8397614 C0.590105869,17.2564115 0.860477131,16.71022 1.31130108,16.3393865 L10.2253983,8.86034516 Z M2.25883799,8.75034087 C1.01253944,8.75034087 0,7.77030269 0,6.56275565 C0,5.35520861 1.01253944,4.37517044 2.25883799,4.37517044 C3.50763664,4.37517044 4.51892604,5.35520861 4.51892604,6.56275565 C4.51892604,7.77030269 3.50638659,8.75034087 2.25883799,8.75034087 Z M7.50779247,2.50009739 C6.79526471,2.50009739 6.21649216,1.94007558 6.21649216,1.2500487 C6.21649216,0.560021816 6.79526471,0 7.50779247,0 C8.22032022,0 8.79909277,0.560021816 8.79909277,1.2500487 C8.79909277,1.94007558 8.22032022,2.50009739 7.50779247,2.50009739 Z M16.8481563,18.1257061 C15.6006077,18.1257061 14.5893183,17.1456679 14.5893183,15.9381209 C14.5893183,14.7305738 15.6006077,13.7505357 16.8481563,13.7505357 C18.0957049,13.7505357 19.1082444,14.7305738 19.1082444,15.9381209 C19.1082444,17.1456679 18.0957049,18.1257061 16.8481563,18.1257061 L16.8481563,18.1257061 Z"
            id="shape-1"
          />
        </g>
      </g>
    </SvgIcon>
  );
};

export const CleanupIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <g id="ask-nue" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="清空" fill="#6239EB" fillRule="nonzero">
          <path
            d="M13.0792768,19.9109875 C13.0792768,19.9109875 12.9902643,19.9109875 13.0792768,19.9109875 L11.2990264,18.9318498 L11.2990264,18.8428373 C11.4770515,18.4867872 12.9902643,15.2823366 12.8122392,15.3713491 C12.6342142,15.5493741 11.566064,16.7065369 10.7649513,17.8636996 L10.3198887,18.4867872 C10.3198887,18.4867872 8.53963839,17.240612 7.91655076,16.5285118 C8.27260083,15.9944367 10.2308762,13.5020862 10.2308762,13.5020862 L6.93741307,15.4603616 C6.75938804,15.2823366 5.69123783,14.1251739 5.33518776,13.5020862 C5.60222531,13.2350487 7.38247566,11.7218359 7.38247566,11.7218359 L4.89012517,12.6119611 L4,10.9207232 L4,10.8317107 C4.44506259,10.7426982 8.0945758,10.0305981 9.78581363,8.78442281 L9.96383866,8.78442281 L15.0375522,13.8581363 C15.0375522,13.8581363 15.1265647,13.9471488 15.0375522,14.0361613 C14.8595271,14.4812239 13.4353268,16.7955494 13.0792768,19.9109875 Z M19.6662031,6.02503477 L16.4617524,9.2294854 L16.4617524,9.31849791 C17.1738526,10.5646732 17.1738526,11.9888734 16.1057024,13.0570236 L16.0166898,13.0570236 L10.9429764,7.98331015 L10.9429764,7.89429764 C12.0111266,6.82614743 13.4353268,6.82614743 14.6815021,7.53824757 L14.7705146,7.53824757 L17.9749652,4.33379694 C18.4200278,3.88873435 19.2211405,3.88873435 19.6662031,4.33379694 C20.1112656,4.77885953 20.1112656,5.57997218 19.6662031,6.02503477 L19.6662031,6.02503477 Z"
            id="shape-1"
          />
        </g>
      </g>
    </SvgIcon>
  );
};

export const NueChatBubbleIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 70 25'}
    >
      <title>{props.title || ''}</title>
      <defs>
        <linearGradient x1="12.5429718%" y1="50%" x2="96.511522%" y2="50%" id="linearGradient-1">
          <stop stopColor="#623CEB" offset="0%" />
          <stop stopColor="#66D1C9" offset="100%" />
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
          <stop stopColor="#6239EB" offset="0%" />
          <stop stopColor="#67D4C9" offset="100%" />
        </linearGradient>
      </defs>
      <g id="ask-nue" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="group-13" transform="translate(0.845703, 1.000000)" fillRule="nonzero">
          <g id="nue" transform="translate(0.000000, 7.701172)" fill="url(#linearGradient-1)">
            <path
              d="M40.0585938,0 C41.2981771,0 42.4147135,0.232421875 43.4082031,0.697265625 C44.4016927,1.16210938 45.2220052,1.89583333 45.8691406,2.8984375 C46.452474,3.78255208 46.8307292,4.80794271 47.0039062,5.97460938 C47.1041667,6.65820312 47.1451823,7.64257812 47.1269531,8.92773438 L36.9277344,8.92773438 C36.9824219,10.422526 37.4518229,11.4707031 38.3359375,12.0722656 C38.8736979,12.4459635 39.5208333,12.6328125 40.2773438,12.6328125 C41.0794271,12.6328125 41.7311198,12.4049479 42.2324219,11.9492188 C42.5058594,11.703125 42.7473958,11.3613281 42.9570312,10.9238281 L46.9355469,10.9238281 C46.8352865,11.8079427 46.375,12.7057292 45.5546875,13.6171875 C44.2786458,15.0664062 42.4921875,15.7910156 40.1953125,15.7910156 C38.2994792,15.7910156 36.6269531,15.1803385 35.1777344,13.9589844 C33.7285156,12.7376302 33.0039062,10.750651 33.0039062,7.99804687 C33.0039062,5.41861979 33.6578776,3.44075521 34.9658203,2.06445312 C36.273763,0.688151042 37.9713542,0 40.0585938,0 Z M20.9589844,0.396484375 L20.9589844,9.37890625 C20.9589844,10.2265625 21.0592448,10.8645833 21.2597656,11.2929688 C21.6152344,12.0494792 22.3125,12.4277344 23.3515625,12.4277344 C24.6822917,12.4277344 25.59375,11.889974 26.0859375,10.8144531 C26.3411458,10.2311198 26.46875,9.4609375 26.46875,8.50390625 L26.46875,0.396484375 L30.4199219,0.396484375 L30.4199219,15.2988281 L26.6328125,15.2988281 L26.6328125,13.1933594 C26.5963542,13.2389323 26.5052083,13.375651 26.359375,13.6035156 C26.2135417,13.8313802 26.0403646,14.031901 25.8398438,14.2050781 C25.2291667,14.7519531 24.6389974,15.125651 24.0693359,15.3261719 C23.4996745,15.5266927 22.8320312,15.6269531 22.0664062,15.6269531 C19.8606771,15.6269531 18.375,14.8339844 17.609375,13.2480469 C17.1809896,12.3730469 16.9667969,11.0833333 16.9667969,9.37890625 L16.9667969,0.396484375 L20.9589844,0.396484375 Z M8.23046875,0.041015625 C9.77083333,0.041015625 11.0309245,0.444335938 12.0107422,1.25097656 C12.9905599,2.05761719 13.4804688,3.39518229 13.4804688,5.26367188 L13.4804688,15.2988281 L9.48828125,15.2988281 L9.48828125,6.234375 C9.48828125,5.45052083 9.38346354,4.84895833 9.17382812,4.4296875 C8.79101562,3.6640625 8.06184896,3.28125 6.98632812,3.28125 C5.66471354,3.28125 4.7578125,3.84179688 4.265625,4.96289062 C4.01041667,5.55533854 3.8828125,6.31184896 3.8828125,7.23242188 L3.8828125,15.2988281 L0,15.2988281 L0,0.423828125 L3.75976562,0.423828125 L3.75976562,2.59765625 C4.26106771,1.83203125 4.73502604,1.28059896 5.18164062,0.943359375 C5.98372396,0.341796875 7,0.041015625 8.23046875,0.041015625 Z M40.0585938,3.21289062 C39.15625,3.21289062 38.4567057,3.49544271 37.9599609,4.06054688 C37.4632161,4.62565104 37.1510417,5.39127604 37.0234375,6.35742188 L43.0800781,6.35742188 C43.016276,5.32747396 42.7041016,4.54589844 42.1435547,4.01269531 C41.5830078,3.47949219 40.8880208,3.21289062 40.0585938,3.21289062 Z"
              id="shape-1merge"
            />
          </g>
          <path
            d="M61.154303,0 C65.0203996,0 68.154303,3.13389724 68.154303,7 C68.154303,10.8660904 65.0203996,14 61.154303,14 C60.0963461,14.0013971 59.051942,13.7620196 58.1002057,13.2999883 C56.9081068,13.2873883 55.5928079,13.5204881 54.154303,14 C54.6338088,12.5614889 54.8669086,11.2461901 54.8543086,10.0540912 C54.3922772,9.10235487 54.1528997,8.0579508 54.154303,7 C54.154303,3.13389724 57.2882064,0 61.154303,0 Z M58.3543055,6.12499462 C57.8710568,6.12499462 57.4793063,6.51674511 57.4793063,6.99999384 C57.4793063,7.48324257 57.8710568,7.87499306 58.3543055,7.87499306 C58.6669126,7.87499306 58.9557735,7.70821917 59.1120771,7.43749346 C59.2683806,7.16676775 59.2683806,6.83321992 59.1120771,6.56249422 C58.9557735,6.29176851 58.6669126,6.12499462 58.3543055,6.12499462 Z M61.154303,6.12499462 C60.6710543,6.12499462 60.2793038,6.51674511 60.2793038,6.99999384 C60.2793038,7.48324257 60.6710543,7.87499306 61.154303,7.87499306 C61.4669102,7.87499306 61.7557711,7.70821917 61.9120746,7.43749346 C62.0683782,7.16676775 62.0683782,6.83321992 61.9120746,6.56249422 C61.7557711,6.29176851 61.4669102,6.12499462 61.154303,6.12499462 Z M63.9543006,6.12499462 C63.4710518,6.12499462 63.0793014,6.51674511 63.0793014,6.99999384 C63.0793014,7.48324257 63.4710518,7.87499306 63.9543006,7.87499306 C64.2669077,7.87499306 64.5557686,7.70821917 64.7120722,7.43749346 C64.8683757,7.16676775 64.8683757,6.83321992 64.7120722,6.56249422 C64.5557686,6.29176851 64.2669077,6.12499462 63.9543006,6.12499462 Z"
            id="shape-1backup-3"
            fill="url(#linearGradient-2)"
          />
        </g>
      </g>
    </SvgIcon>
  );
};

export const SendIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 32 32'}
    >
      <title>{props.title || ''}</title>
      <g id="ask-nue" stroke="none" strokeWidth="1" /* fill="none" fill-rule="evenodd" */>
        <g
          id="send@2x"
          transform="translate(16.000000, 16.212803) scale(-1, 1) translate(-16.000000, -16.212803) translate(4.000000, 5.000000)"
          //   fill="#6239eb"
          fillRule="nonzero"
        >
          <path
            d="M7.11801271,13.6444029 L7.07331617,13.604176 L14.8826996,7.08026571 L4.48985482,11.2719094 L0.294635413,7.48342871 C0.0516177513,7.26384199 -0.0518540376,6.92893591 0.024949188,6.61053808 C0.101752414,6.29214024 0.346531485,6.04123847 0.6629351,5.95659412 L22.8771269,0.0298298699 C23.1877383,-0.0524566403 23.5186161,0.0382940121 23.7437853,0.26753036 C23.9689546,0.496766707 24.0537682,0.829215888 23.9659352,1.13830463 L18.0481102,21.7773895 C17.9632604,22.074169 17.731306,22.306177 17.4345461,22.3910954 C17.1377863,22.4760138 16.8182135,22.4018264 16.5892144,22.1948555 L10.8430243,17.0064784 L7.11801271,20.7305961 L7.11801271,13.6444029 Z"
            id="path"
          />
        </g>
      </g>
    </SvgIcon>
  );
};

export const BookIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <g id="ask-nue" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="书本backup"
          transform="translate(2.000000, 3.000000)"
          fill="#6239EB"
          fillRule="nonzero"
        >
          <path
            d="M9.99775331,18.74875 C9.71916423,18.74875 9.44057515,18.6858427 9.20691979,18.5555349 C7.46798472,17.6074334 4.4079982,16.600918 2.49382161,16.3492892 L2.22421928,16.3133422 C1.00651539,16.1650609 0,15.019251 0,13.7835736 L0,2.5501429 C0,1.81322985 0.287575826,1.14371738 0.817793754,0.662926546 C1.34801168,0.177642341 2.03549764,-0.0560130178 2.76342395,0.0113875664 C4.80790834,0.168655596 7.89934846,1.19314447 9.64727028,2.28952731 L9.87193889,2.41983511 C9.9348461,2.45578208 10.078634,2.45578208 10.1325545,2.42882185 L10.2808358,2.33446103 C12.0287576,1.2380782 15.1157043,0.19561583 17.1736688,0.0203743109 L17.2860031,0.0203743109 C17.9645024,-0.0425329009 18.6609751,0.19561583 19.1822062,0.680900035 C19.7124242,1.16618424 20,1.83569671 20,2.56811639 L20,13.7925603 C20,15.0372245 18.9934846,16.1740476 17.766794,16.3223289 L17.4612447,16.3582759 C15.5470681,16.6099048 12.4780948,17.6209135 10.7751067,18.5645217 C10.5459447,18.6948295 10.2763424,18.74875 9.99775331,18.74875 Z M2.53875534,1.39534623 C2.24219277,1.39534623 1.97259043,1.49869379 1.75690856,1.6919088 C1.5232532,1.90759067 1.39294541,2.21313998 1.39294541,2.54564953 L1.39294541,13.7790802 C1.39294541,14.3272716 1.86924287,14.8574896 2.3994608,14.9338769 L2.67804988,14.9698239 C4.77196136,15.248413 7.98022916,16.2998621 9.84048528,17.3153642 C9.92585936,17.3513112 10.0471804,17.3602979 10.0921141,17.3423244 C11.9523703,16.3088488 15.1786115,15.248413 17.2815098,14.9698239 L17.5960458,14.9338769 C18.1262638,14.8709697 18.6025612,14.3272716 18.6025612,13.7790802 L18.6025612,2.55912965 C18.6025612,2.21313998 18.4722534,1.91657741 18.2385981,1.69640217 C17.995956,1.4807203 17.6904067,1.38186611 17.344417,1.3998396 L17.2320827,1.3998396 C15.4572006,1.55261426 12.5904291,2.51419592 11.0312289,3.48925771 L10.8829477,3.59260527 C10.3707032,3.90714133 9.64727028,3.90714133 9.15299933,3.60159201 L8.92833071,3.47128422 C7.33767693,2.49622243 4.47539879,1.54812088 2.65108964,1.39534623 L2.53875534,1.39534623 Z"
            id="shape-1"
          />
          <path
            d="M9.99775331,17.9713966 C9.61581667,17.9713966 9.30128061,17.6568605 9.30128061,17.2749239 L9.30128061,3.32300293 C9.30128061,2.94106629 9.61581667,2.62653023 9.99775331,2.62653023 C10.37969,2.62653023 10.694226,2.94106629 10.694226,3.32300293 L10.694226,17.2749239 C10.694226,17.6613539 10.37969,17.9713966 9.99775331,17.9713966 Z M6.04358571,6.80985982 L3.94967423,6.80985982 C3.56773759,6.80985982 3.25320153,6.49532376 3.25320153,6.11338712 C3.25320153,5.73145047 3.56773759,5.41691441 3.94967423,5.41691441 L6.04358571,5.41691441 C6.42552235,5.41691441 6.74005841,5.73145047 6.74005841,6.11338712 C6.74455179,6.49532376 6.42552235,6.80985982 6.04358571,6.80985982 Z M6.74455179,9.600244 L3.9541676,9.600244 C3.57223096,9.600244 3.2576949,9.28570794 3.2576949,8.9037713 C3.2576949,8.52183466 3.57223096,8.2072986 3.9541676,8.2072986 L6.74455179,8.2072986 C7.12648843,8.2072986 7.44102449,8.52183466 7.44102449,8.9037713 C7.44102449,9.28121457 7.12199506,9.600244 6.74455179,9.600244 Z"
            id="shape-1"
          />
        </g>
      </g>
    </SvgIcon>
  );
};

export const ThumbIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 20 20'}
    >
      <svg width="20px" height="20px" viewBox="0 0 20 20">
        <title>{props.title || ''}</title>
        <g id="ask-nue" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="like-backup"
            // transform="translate(2.000000, 2.000000)"
            fill="#787878"
            fillRule="nonzero"
          >
            <path
              d="M17.7071234,5.86853153 L11.7957013,5.86853153 C12.0764153,4.88067547 12.218558,4.00281929 12.218558,3.229963 C12.218558,1.47639349 10.6157026,-0.202890377 9.0814186,0.019966521 C7.72856294,0.216394877 7.08284937,1.35710791 7.08284937,2.8221063 L7.08284937,4.14567628 C7.08284937,5.84353156 5.78356508,7.29138711 4.13142404,7.45067265 L1.50356978,7.44638694 C0.673171119,7.44638694 0,8.11955806 0,8.94995672 L0,17.63209 C0,18.4628034 0.672856404,19.1356598 1.50356978,19.1356598 L16.0614109,19.1356598 C17.1630576,19.1356598 18.1090367,18.3523091 18.3142656,17.2699476 L19.9599781,8.58781426 C20.0869625,7.91694616 19.908354,7.2243869 19.4727584,6.69860609 C19.0371627,6.17282528 18.3899038,5.86853069 17.7071234,5.86853153 Z M1.42856986,17.63209 L1.42856986,8.94995672 C1.42856986,8.90852819 1.46214125,8.8749568 1.50356978,8.8749568 L3.21428219,8.8749568 L3.21428219,17.70709 L1.50356978,17.70709 C1.46214847,17.70709 1.42856986,17.6735114 1.42856986,17.63209 L1.42856986,17.63209 Z M18.5564082,8.32210026 L16.9106957,17.0042336 C16.8331532,17.4120891 16.4765723,17.70709 16.0614109,17.70709 L4.64285205,17.70709 L4.64285205,8.81209973 C6.88405659,8.38424713 8.50671119,6.42663539 8.51141923,4.14496199 L8.51141923,2.8221063 C8.51141923,1.98353579 8.78070465,1.50710775 9.28641838,1.43425068 C9.88998914,1.34639364 10.7899882,2.28996403 10.7899882,3.23067728 C10.7899882,4.06353351 10.5778455,5.10567522 10.1457031,6.34924529 C10.0698663,6.56766408 10.104443,6.80928092 10.2384953,6.99766344 C10.3725476,7.18604595 10.5894926,7.29789019 10.8207024,7.29781568 L17.7071234,7.29781568 C17.9643527,7.29783382 18.2082068,7.41242757 18.3723914,7.61044341 C18.5365761,7.80845925 18.6040275,8.06931719 18.5564082,8.32210026 Z"
              id="shape-1"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ThumbIconFilled: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 20 20'}
    >
      <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" overflow="visible">
        <title>{props.title || ''}</title>
        <g id="ask-nue" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <path
            d="M19.7071234,8.86853153 L13.7957013,8.86853153 C14.0764153,7.88067547 14.218558,7.00281929 14.218558,6.229963 C14.218558,4.47639349 12.6157026,2.79710962 11.0814186,3.01996652 C9.72856294,3.21639488 9.08284937,4.35710791 9.08284937,5.8221063 L9.08284937,7.14567628 C9.08284937,8.84353156 7.78356508,10.2913871 6.13142404,10.4506727 L3.50356978,10.4463869 C2.67317112,10.4463869 2,11.1195581 2,11.9499567 L2,20.63209 C2,21.4628034 2.6728564,22.1356598 3.50356978,22.1356598 L18.0614109,22.1356598 C19.1630576,22.1356598 20.1090367,21.3523091 20.3142656,20.2699476 L21.9599781,11.5878143 C22.0869625,10.9169462 21.908354,10.2243869 21.4727584,9.69860609 C21.0371627,9.17282528 20.3899038,8.86853069 19.7071234,8.86853153 Z M3.42856986,11.9499567 C3.42856986,11.9085282 3.46214125,11.8749568 3.50356978,11.8749568 L5.21428219,11.8749568 L5.21428219,20.70709 L3.50356978,20.70709 C3.46214847,20.70709 3.42856986,20.6735114 3.42856986,20.63209 L3.42856986,11.9499567 Z"
            id="shape-1backup-3"
            fill="#6239EB"
            fillRule="nonzero"
          />
        </g>
      </svg>
    </SvgIcon>
  );
};
export const CopyIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 20 20'}
    >
      <title>{props.title || ''}</title>
      <g id="ask-nue" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="group-7">
          <rect
            id="rectangle"
            stroke="#979797"
            fill="#D8D8D8"
            opacity="0"
            x="0"
            y="0"
            width="20"
            height="20"
          />
          <g
            id="12-文件shape-1/-·-折角复制_32-=-File-file_duplicate_32-o-320153"
            // transform="translate(2.000000, 0.000000)"
            fill="#787878"
          >
            <path
              d="M13.3333333,16.6666667 L13.3333333,18.3333333 C13.3333333,19.25 12.5833333,20 11.6666667,20 L1.65833333,20 C0.741666667,20 0,19.25 0,18.3333333 L0.00833333334,5 C0.00833333334,4.08333333 0.75,3.******** 1.********,3.******** L3.340625,3.******** L3.34166667,1.******** C3.34166667,0.75 4.08333333,0 5,0 L13.3333333,0 L16.6666667,3.******** L16.6666667,15 C16.6666667,15.9166667 15.9166667,16.6666667 15,16.6666667 L13.3333333,16.6666667 L13.3333333,16.6666667 Z M11.6666667,16.6666667 L4.99166667,16.6666667 C4.075,16.6666667 3.********,15.9166667 3.********,15 L3.33958333,5 L1.67418256,5 L1.********,18.3333333 L11.6666667,18.3333333 L11.6666667,16.6666667 Z M5,15 L15,15 L15,4.02368927 L12.6429774,1.******** L5.00751589,1.******** L5,15 Z M6.********,11.6666667 L6.********,10 L11.6666667,10 L11.6666667,11.6666667 L6.********,11.6666667 Z M6.********,7.5 L6.********,5.83333333 L13.3333333,5.83333333 L13.3333333,7.5 L6.********,7.5 Z"
              id="Shape"
            />
          </g>
        </g>
      </g>
    </SvgIcon>
  );
};

export const CurrentColorCopyIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 20 20'}
    >
      <title>{props.title || ''}</title>
      <g id="ask-nue" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="group-7">
          <rect
            id="rectangle"
            stroke="#979797"
            fill="#D8D8D8"
            opacity="0"
            x="0"
            y="0"
            width="20"
            height="20"
          />
          <g
            id="12-文件shape-1/-·-折角复制_32-=-File-file_duplicate_32-o-320153"
            // transform="translate(2.000000, 0.000000)"
            fill="currentColor"
          >
            <path
              d="M13.3333333,16.6666667 L13.3333333,18.3333333 C13.3333333,19.25 12.5833333,20 11.6666667,20 L1.65833333,20 C0.741666667,20 0,19.25 0,18.3333333 L0.00833333334,5 C0.00833333334,4.08333333 0.75,3.******** 1.********,3.******** L3.340625,3.******** L3.34166667,1.******** C3.34166667,0.75 4.08333333,0 5,0 L13.3333333,0 L16.6666667,3.******** L16.6666667,15 C16.6666667,15.9166667 15.9166667,16.6666667 15,16.6666667 L13.3333333,16.6666667 L13.3333333,16.6666667 Z M11.6666667,16.6666667 L4.99166667,16.6666667 C4.075,16.6666667 3.********,15.9166667 3.********,15 L3.33958333,5 L1.67418256,5 L1.********,18.3333333 L11.6666667,18.3333333 L11.6666667,16.6666667 Z M5,15 L15,15 L15,4.02368927 L12.6429774,1.******** L5.00751589,1.******** L5,15 Z M6.********,11.6666667 L6.********,10 L11.6666667,10 L11.6666667,11.6666667 L6.********,11.6666667 Z M6.********,7.5 L6.********,5.83333333 L13.3333333,5.83333333 L13.3333333,7.5 L6.********,7.5 Z"
              id="Shape"
            />
          </g>
        </g>
      </g>
    </SvgIcon>
  );
};

export const AdjustPriceIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 28 28'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 28 28"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="icon" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="icon-7" fill="currentColor" fillRule="nonzero">
            <path
              d="M1.01116205,11.499984 L3.01364679,11.5001352 C3.00458849,11.665606 3,11.8322671 3,12 C3,16.9705627 7.02943725,21 12,21 C12.5113275,21 13.0126956,20.9573586 13.5007422,20.8754378 L13.5008814,22.8984611 C13.0101467,22.9654132 12.5091125,23 12,23 C5.92486775,23 1,18.0751322 1,12 C1,11.8324264 1.00374709,11.665728 1.01116205,11.499984 Z M13.1666667,7 L13.1666667,7.83333333 L15.25,7.83333333 L15.25,9.5 L11.5,9.5 C11.0397627,9.5 10.6666667,9.87309604 10.6666667,10.3333333 C10.6666667,10.7935706 11.0397627,11.1666667 11.5,11.1666667 L13.1666667,11.1666667 C14.5473785,11.1666667 15.6666667,12.2859548 15.6666667,13.6666667 C15.6666667,15.0473785 14.5473785,16.1666667 13.1666667,16.1666667 L13.1666667,17 L11.5,17 L11.5,16.1666667 L9.41666667,16.1666667 L9.41666667,14.5 L13.1666667,14.5 C13.626904,14.5 14,14.126904 14,13.6666667 C14,13.2064294 13.626904,12.8333333 13.1666667,12.8333333 L11.5,12.8333333 C10.1192881,12.8333333 9,11.7140452 9,10.3333333 C9,8.95262146 10.1192881,7.83333333 11.5,7.83333333 L11.5,7 L13.1666667,7 Z M12,1 C18.0751322,1 23,5.92486775 23,12 C23,12.1679107 22.9962378,12.3349428 22.9887931,12.5010164 L20.9862985,12.5008636 C20.995393,12.3350647 21,12.1680704 21,12 C21,7.02943725 16.9705627,3 12,3 C11.8322671,3 11.665606,3.00458849 11.5001352,3.01364679 L11.499984,1.01116205 C11.665728,1.00374709 11.8324264,1 12,1 Z"
              id="shape"
            />
          </g>
          <path
            d="M5,1 L9,5.40969988 L6.01933794,5.40969988 L6.01933794,9 L3.98907294,9 L3.98907294,5.40969988 L1,5.40969988 L5,1 Z"
            id="Combined-Shape"
            fill="currentColor"
          />
          <path
            d="M19,15 L23,19.4096999 L20.0237712,19.4096999 L20.0237712,23 L17.9900277,23 L17.9900277,19.4096999 L15,19.4096999 L19,15 Z"
            id="Combined-Shape-2"
            fill="currentColor"
            transform="translate(19.000000, 19.000000) scale(1, -1) translate(-19.000000, -19.000000) "
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RenewWithQuantityUpdateIcon: React.FC<Props> = ({
  style = {},
  width = 24,
  height = 24,
}) => {
  let iconWidth = typeof width === 'string' ? parseInt(width) : width;
  let iconHeight = typeof height === 'string' ? parseInt(height) : height;
  let bottomMargin = 5;

  // Only used on the more actions list, so adjust the size there
  if (style.fontSize) {
    iconWidth = 20;
    iconHeight = 20;
    bottomMargin = 0;
  }

  const svgWidth = iconWidth / 2;
  const svgHeight = iconHeight / 2;

  return (
    <div style={{ position: 'relative', ...style }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: bottomMargin,
          width: iconWidth,
          height: iconHeight,
          border: '2px solid #6239eb',
          borderRadius: '50%',
          backgroundColor: 'transparent',
        }}
      >
        {/* Triangle */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width={svgWidth}
          height={svgHeight}
          viewBox="0 0 24 24"
          fill="#6239eb"
          stroke="none"
          strokeWidth="0"
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{ marginLeft: '3px' }}
        >
          <polygon points="5 3 19 12 5 21 5 3" />
        </svg>
      </div>

      <div
        style={{
          position: 'absolute',
          bottom: '-1px',
          right: '-6px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f2ecfe',
          borderRadius: '6px',
          padding: '2px',
        }}
      >
        {/* Arrows */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width={svgWidth}
          height={svgHeight}
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{
            color: '#6239eb',
            WebkitTextStrokeWidth: '3px',
          }}
        >
          <path d="m21 16-4 4-4-4" />
          <path d="M17 20V4" />
          <path d="m3 8 4-4 4 4" />
          <path d="M7 4v16" />
        </svg>
      </div>
    </div>
  );
};

export const CreateMilestoneIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        enableBackground="new 0 0 24 24"
        height="24px"
        viewBox="0 0 24 19"
        width="24px"
        fill="currentColor"
        scale={0.5}
      >
        <g>
          <rect fill="none" height="24" width="24" />
        </g>

        <g
          // @ts-expect-error
          transformOrigin="4px 9px"
          transform="scale(0.77)"
        >
          <g>
            <path d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8 S16.41,20,12,20z" />
            <path d="M15,9l-1-2H8v11h1.5v-5H12l1,2h5V9H15z M16.5,13.5h-2.57l-1-2H9.5v-3h3.57l1,2h2.43V13.5z" />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const OneColumnsIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 30 30'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="30px"
        height="30px"
        viewBox="0 0 30 30"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>two</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-623.000000, -467.000000)">
            <g id="g-16" transform="translate(578.000000, 462.000000)">
              <g id="two" transform="translate(45.000000, 5.000000)">
                <path
                  d="M14.3204812,8 L14.3204812,22.278481 C14.3204812,24.48762 12.5296202,26.278481 10.3204812,26.278481 L6.17273287,26.278481 L6.17273287,26.278481 C4.97276564,26.278481 4,25.3053673 4,24.1049707 L4,6.17351034 C4,4.97311373 4.97276564,4 6.17273287,4 L10.3204812,4 C12.5296202,4 14.3204812,5.790861 14.3204812,8 Z M24.0977791,4 C25.2977463,4 26.270512,4.97311373 26.270512,6.17351034 L26.270512,24.1049707 C26.270512,25.3053673 25.2977463,26.278481 24.0977791,26.278481 L20.493214,26.278481 C18.284075,26.278481 16.493214,24.48762 16.493214,22.278481 L16.493214,8 C16.493214,5.790861 18.284075,4 20.493214,4 L24.0977791,4 L24.0977791,4 Z"
                  id="id"
                  fill="#6239EB"
                />
                <rect
                  id="id"
                  stroke="#979797"
                  fill="#D8D8D8"
                  opacity="0"
                  x="0"
                  y="0"
                  width="30"
                  height="30"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const TwoColumnsIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 30 30'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="30px"
        height="30px"
        viewBox="0 0 30 30"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>two columns</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-623.000000, -467.000000)">
            <g id="g-16" transform="translate(578.000000, 462.000000)">
              <g id="two" transform="translate(45.000000, 5.000000)">
                <path
                  d="M14.3204812,8 L14.3204812,22.278481 C14.3204812,24.48762 12.5296202,26.278481 10.3204812,26.278481 L6.17273287,26.278481 L6.17273287,26.278481 C4.97276564,26.278481 4,25.3053673 4,24.1049707 L4,6.17351034 C4,4.97311373 4.97276564,4 6.17273287,4 L10.3204812,4 C12.5296202,4 14.3204812,5.790861 14.3204812,8 Z M24.0977791,4 C25.2977463,4 26.270512,4.97311373 26.270512,6.17351034 L26.270512,24.1049707 C26.270512,25.3053673 25.2977463,26.278481 24.0977791,26.278481 L20.493214,26.278481 C18.284075,26.278481 16.493214,24.48762 16.493214,22.278481 L16.493214,8 C16.493214,5.790861 18.284075,4 20.493214,4 L24.0977791,4 L24.0977791,4 Z"
                  id="id"
                  fill="currentColor"
                />
                <rect
                  id="id"
                  stroke="#979797"
                  fill="#D8D8D8"
                  opacity="0"
                  x="0"
                  y="0"
                  width="30"
                  height="30"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ThreeColumnsIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 30 30'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="30px"
        height="30px"
        viewBox="0 0 30 30"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>three columns</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-660.000000, -467.000000)">
            <g id="id-16" transform="translate(578.000000, 462.000000)">
              <g id="three" transform="translate(82.000000, 5.000000)">
                <path
                  d="M10.0201753,7.91620658 L10.0201753,22.5901225 C10.0201753,24.6900652 8.31783538,26.3924051 6.21789272,26.3924051 L6.21789272,26.3924051 L6.21789272,26.3924051 C5.01792549,26.3924051 4.04515985,25.4192913 4.04515985,24.2188947 L4.04515985,6.28743439 C4.04515985,5.08703778 5.01792549,4.11392405 6.21789272,4.11392405 C8.31783538,4.11392405 10.0201753,5.81626392 10.0201753,7.91620658 Z M18.1679235,7.10143175 L18.1679235,23.4048974 C18.1679235,25.0548523 16.8303708,26.3924051 15.1804158,26.3924051 C13.5304609,26.3924051 12.1929081,25.0548523 12.1929081,23.4048974 L12.1929081,7.10143175 C12.1929081,5.45147681 13.5304609,4.11392405 15.1804158,4.11392405 C16.8303708,4.11392405 18.1679235,5.45147681 18.1679235,7.10143175 Z M24.1429389,4.11392405 C25.3429062,4.11392405 26.3156718,5.08703778 26.3156718,6.28743439 L26.3156718,24.2188947 C26.3156718,25.4192913 25.3429062,26.3924051 24.1429389,26.3924051 C22.0429963,26.3924051 20.3406564,24.6900652 20.3406564,22.5901225 L20.3406564,7.91620658 C20.3406564,5.81626392 22.0429963,4.11392405 24.1429389,4.11392405 L24.1429389,4.11392405 L24.1429389,4.11392405 Z"
                  id="id"
                  fill="currentColor"
                />
                <rect
                  id="id"
                  stroke="#979797"
                  fill="#D8D8D8"
                  opacity="0"
                  x="0"
                  y="0"
                  width="30"
                  height="30"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ListColumnsIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 30 30'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="30px"
        height="30px"
        viewBox="0 0 30 30"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>list</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-699.000000, -467.000000)">
            <g id="list-16" transform="translate(578.000000, 462.000000)">
              <g id="list" transform="translate(121.000000, 5.000000)">
                <path
                  d="M7.47976749,4 C9.40159001,4 10.959535,5.55794497 10.959535,7.47976749 L10.959535,22.7987135 C10.959535,24.720536 9.40159001,26.278481 7.47976749,26.278481 C5.55794497,26.278481 4,24.720536 4,22.7987135 L4,7.47976749 C4,5.55794497 5.55794497,4 7.47976749,4 Z M19.5429615,4 C21.464784,4 23.022729,5.55794497 23.022729,7.47976749 L23.022729,22.7987135 C23.022729,24.720536 21.464784,26.278481 19.5429615,26.278481 C17.621139,26.278481 16.063194,24.720536 16.063194,22.7987135 L16.063194,7.47976749 C16.063194,5.55794497 17.621139,4 19.5429615,4 Z M25.342574,4 C25.85506,4 26.270512,4.41545199 26.270512,4.927938 L26.270512,25.350543 C26.270512,25.863029 25.85506,26.278481 25.342574,26.278481 C24.830088,26.278481 24.414636,25.863029 24.414636,25.350543 L24.414636,4.927938 C24.414636,4.41545199 24.830088,4 25.342574,4 Z M13.616812,4 C14.1758876,4 14.629108,4.45322036 14.629108,5.012296 L14.629108,25.266185 C14.629108,25.8252607 14.1758876,26.278481 13.616812,26.278481 C13.0577363,26.278481 12.604516,25.8252607 12.604516,25.266185 L12.604516,5.012296 C12.604516,4.45322036 13.0577363,4 13.616812,4 Z"
                  id="list"
                  fill="#ECECEC"
                />
                <rect
                  id="list"
                  stroke="#979797"
                  fill="#D8D8D8"
                  opacity="0"
                  x="0"
                  y="0"
                  width="30"
                  height="30"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const LeftAlignmentIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 17 17'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="17px"
        height="17px"
        viewBox="0 0 17 17"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>left</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-767.000000, -473.000000)">
            <g id="left-16" transform="translate(578.000000, 462.000000)">
              <g id="left-13" transform="translate(176.507937, 6.984127)">
                <g id="left" transform="translate(13.000000, 5.000000)">
                  <path
                    d="M11.1074524,11.1413117 C11.7390617,11.1413117 12.2510823,11.6533322 12.2510823,12.2849416 C12.2510823,12.9165509 11.7390617,13.4285714 11.1074524,13.4285714 L3.14362987,13.4285714 C2.51202053,13.4285714 2,12.9165509 2,12.2849416 C2,11.6533322 2.51202053,11.1413117 3.14362987,11.1413117 L11.1074524,11.1413117 Z M8.05983334,6.54361055 C8.69144267,6.54361055 9.2034632,7.05563108 9.2034632,7.68724041 C9.2034632,8.31884975 8.69144267,8.83087028 8.05983334,8.83087028 L3.14362987,8.83087028 C2.51202053,8.83087028 2,8.31884975 2,7.68724041 C2,7.05563108 2.51202053,6.54361055 3.14362987,6.54361055 L8.05983334,6.54361055 Z M13.0468463,2 C13.6784557,2 14.1904762,2.51202053 14.1904762,3.14362987 C14.1904762,3.7752392 13.6784557,4.28725973 13.0468463,4.28725973 L3.14362987,4.28725973 C2.51202053,4.28725973 2,3.7752392 2,3.14362987 C2,2.51202053 2.51202053,2 3.14362987,2 L13.0468463,2 Z"
                    id="left"
                    fill="#E3E3E3"
                  />
                  <rect
                    id="left"
                    stroke="#979797"
                    fill="#D8D8D8"
                    opacity="0"
                    x="0"
                    y="0"
                    width="16"
                    height="16"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RightAlignmentIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 17 17'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="17px"
        height="17px"
        viewBox="0 0 17 17"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>right</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-851.000000, -473.000000)">
            <g id="right-16" transform="translate(578.000000, 462.000000)">
              <g id="right-13" transform="translate(176.507937, 6.984127)">
                <g id="right" transform="translate(97.000000, 5.000000)">
                  <path
                    d="M13.0122143,11.1428571 C13.6438236,11.1428571 14.1558442,11.6548777 14.1558442,12.286487 C14.1558442,12.9180963 13.6438236,13.4301169 13.0122143,13.4301169 L5.04839177,13.4301169 C4.41678244,13.4301169 3.9047619,12.9180963 3.9047619,12.286487 C3.9047619,11.6548777 4.41678244,11.1428571 5.04839177,11.1428571 L13.0122143,11.1428571 Z M13.0122143,6.57142857 C13.6438236,6.57142857 14.1558442,7.0834491 14.1558442,7.71505844 C14.1558442,8.34666777 13.6438236,8.8586883 13.0122143,8.8586883 L8.09601082,8.8586883 C7.46440148,8.8586883 6.95238095,8.34666777 6.95238095,7.71505844 C6.95238095,7.0834491 7.46440148,6.57142857 8.09601082,6.57142857 L13.0122143,6.57142857 Z M13.0468463,2 C13.6784557,2 14.1904762,2.51202053 14.1904762,3.14362987 C14.1904762,3.7752392 13.6784557,4.28725973 13.0468463,4.28725973 L3.14362987,4.28725973 C2.51202053,4.28725973 2,3.7752392 2,3.14362987 C2,2.51202053 2.51202053,2 3.14362987,2 L13.0468463,2 Z"
                    id="right-3"
                    fill="#E3E3E3"
                  />
                  <rect
                    id="right"
                    stroke="#979797"
                    fill="#D8D8D8"
                    opacity="0"
                    x="0"
                    y="0"
                    width="16"
                    height="16"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CenterAlignmentIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 17 17'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="17px"
        height="17px"
        viewBox="0 0 17 17"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>center</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-810.000000, -473.000000)">
            <g id="center-16" transform="translate(578.000000, 462.000000)">
              <g id="center-13" transform="translate(176.507937, 6.984127)">
                <g id="center" transform="translate(56.000000, 5.000000)">
                  <path
                    d="M12.2503095,11.1428571 C12.8819189,11.1428571 13.3939394,11.6548777 13.3939394,12.286487 C13.3939394,12.9180963 12.8819189,13.4301169 12.2503095,13.4301169 L4.28648701,13.4301169 C3.65487767,13.4301169 3.14285714,12.9180963 3.14285714,12.286487 C3.14285714,11.6548777 3.65487767,11.1428571 4.28648701,11.1428571 L12.2503095,11.1428571 Z M10.7265,6.57142857 C11.3581093,6.57142857 11.8701299,7.0834491 11.8701299,7.71505844 C11.8701299,8.34666777 11.3581093,8.8586883 10.7265,8.8586883 L5.81029653,8.8586883 C5.1786872,8.8586883 4.********,8.34666777 4.********,7.71505844 C4.********,7.0834491 5.1786872,6.57142857 5.81029653,6.57142857 L10.7265,6.57142857 Z M13.0468463,2 C13.6784557,2 14.1904762,2.51202053 14.1904762,3.14362987 C14.1904762,3.7752392 13.6784557,4.28725973 13.0468463,4.28725973 L3.14362987,4.28725973 C2.51202053,4.28725973 2,3.7752392 2,3.14362987 C2,2.51202053 2.51202053,2 3.14362987,2 L13.0468463,2 Z"
                    id="center-2"
                    fill="#E3E3E3"
                  />
                  <rect
                    id="center"
                    stroke="#979797"
                    fill="#D8D8D8"
                    opacity="0"
                    x="0"
                    y="0"
                    width="16"
                    height="16"
                  />
                </g>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DeleteComponentIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 16 16'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>delete</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-909.000000, -474.000000)">
            <g id="delete-16" transform="translate(578.000000, 462.000000)">
              <g id="delete" transform="translate(331.000000, 12.000000)">
                <path
                  d="M6.3476414,3.94936709 C6.3476414,2.97063462 7.14077703,2.17721519 8.1191594,2.17721519 C9.09754177,2.17721519 9.8906774,2.97063462 9.8906774,3.94936709 L12.6744914,3.94936709 C12.9540292,3.94936709 13.1806394,4.17605835 13.1806394,4.4556962 C13.1806394,4.73533405 12.9540292,4.96202532 12.6744914,4.96202532 L3.56382741,4.96202532 C3.28428959,4.96202532 3.05767941,4.73533405 3.05767941,4.4556962 C3.05767941,4.17605835 3.28428959,3.94936709 3.56382741,3.94936709 L6.3476414,3.94936709 Z M8.8783814,3.94936709 C8.8783814,3.52991032 8.53846613,3.18987342 8.1191594,3.18987342 C7.69985267,3.18987342 7.3599374,3.52991032 7.3599374,3.94936709 L8.8783814,3.94936709 Z M12.1683434,5.72151899 L12.1683434,13.5221519 C12.1683434,13.9678247 11.8284281,14.3291139 11.4091214,14.3291139 L4.82919741,14.3291139 C4.40989068,14.3291139 4.06997541,13.9678247 4.06997541,13.5221519 L4.06997541,5.72151899 L12.1683434,5.72151899 Z M7.6130114,7.74683544 L8.6253074,7.74683544 L8.6253074,12.8101266 L7.6130114,12.8101266 L7.6130114,7.74683544 Z"
                  id="Shape"
                  fill="currentColor"
                />
                <rect
                  id="delete"
                  stroke="#979797"
                  fill="#D8D8D8"
                  opacity="0"
                  x="0"
                  y="0"
                  width="16"
                  height="16"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ColorThemeIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>Color Theme</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="2" transform="translate(-56.000000, -316.000000)">
            <g id="Color-Theme" transform="translate(56.000000, 316.000000)">
              <g
                id="eglass-color"
                transform="translate(4.000000, 6.000000)"
                fill="#000000"
                fillRule="nonzero"
              >
                <path
                  d="M16.4534488,2.01720179 C16.2296178,2.01474649 15.9793408,2.2129732 15.8089511,2.37953781 L9.71174554,8.30262013 C9.48817084,8.52132655 8.87385838,10.0060658 8.83893855,10.0628929 L8.70566489,10.2796776 L8.92270584,10.1468249 C8.98024668,10.1116305 10.3349637,9.28166253 10.5580625,9.06354177 L16.6552498,3.14044114 C16.9262261,2.87546781 17.1322677,2.50666839 16.8976204,2.26623774 C16.8330517,2.20033297 16.5451407,2.01831533 16.4534488,2.01720179 Z"
                  id="ColorTheme"
                />
                <path
                  d="M12.0922385,4.10847787 C12.0922385,4.60226023 11.6914842,5.00299629 11.1977201,5.00299629 C10.7038646,5.00299629 10.3030919,4.60226023 10.3030919,4.10847787 C10.3030919,3.6146772 10.7038645,3.21390455 11.1977201,3.21390455 C11.6914842,3.21390455 12.0922385,3.6146772 12.0922385,4.10847787 L12.0922385,4.10847787 Z"
                  id="ColorTheme"
                />
                <path
                  d="M2.69950239,7.23929231 C2.20570172,7.23929231 1.80496568,6.83861116 1.80496568,6.3448288 C1.80496568,5.85099154 2.20570173,5.45021889 2.69950239,5.45021889 C3.19326645,5.45021889 3.59405739,5.85099154 3.59405739,6.3448288 C3.59405739,6.83861116 3.19326645,7.23929231 2.69950239,7.23929231 L2.69950239,7.23929231 Z"
                  id="ColorTheme"
                />
                <path
                  d="M4.04133492,4.10847787 C4.04133492,3.6146772 4.44210757,3.21390455 4.93585333,3.21390455 C5.429654,3.21390455 5.83042665,3.6146772 5.83042665,4.10847787 C5.83042665,4.60226023 5.4296723,5.00299629 4.93585333,5.00299629 C4.44207097,5.00299629 4.04133492,4.60269947 4.04133492,4.10847787 L4.04133492,4.10847787 Z"
                  id="ColorTheme"
                />
                <path
                  d="M7.17224087,3.21390455 C7.17224087,2.72012219 7.57297693,2.31938614 8.06675928,2.31938614 C8.56059655,2.31938614 8.9613509,2.72012219 8.9613509,3.21390455 C8.9613509,3.70768691 8.56057825,4.10847787 8.06675928,4.10847787 C7.57297691,4.10847787 7.17224087,3.70812617 7.17224087,3.21390455 L7.17224087,3.21390455 Z"
                  id="ColorTheme"
                />
                <path
                  d="M14.0909407,6.24762799 C13.6797909,8.91189314 11.2520215,11.6120847 7.79638689,11.6120847 C7.13854726,11.6120847 6.43714936,11.4819772 5.87200831,11.2551265 C5.27094085,11.013854 4.99831735,10.7301396 4.94364989,10.5452183 C4.9151357,10.4486763 4.88834188,10.3431848 4.85995579,10.231489 C4.77679246,9.90416142 4.68250161,9.53316578 4.49774502,9.22529303 C4.16909974,8.67768509 3.69623631,8.56274983 3.35739692,8.56274983 C3.22922948,8.56274983 3.0927347,8.57909332 2.9518475,8.61124957 C2.72175736,8.66375741 2.49823758,8.69036823 2.28758395,8.69036823 C1.83825665,8.69036823 1.08100167,8.57116866 1.0577218,7.77273466 C0.939858258,3.69599208 4.29014779,1.4817314 7.6626008,1.09581982 C7.90112806,1.06855015 8.15627334,1.05475059 8.42097217,1.05475059 C9.37109143,1.05475059 10.4159225,1.233614 11.1477745,1.52153782 C11.3131678,1.5866007 11.4048781,1.62913406 11.8489345,1.84676067 C12.3077422,2.07157988 12.6778593,2.39103768 12.9738542,2.69883723 L13.5132263,1.74959646 C13.1908219,1.44531086 12.7918612,1.1342353 12.3131046,0.899606311 C11.8679501,0.681467239 11.7426926,0.62213284 11.5339424,0.540012695 C10.6872831,0.206938365 9.49446379,0 8.42100877,0 C8.11648524,0 7.82105771,0.0161055693 7.54281555,0.0478957853 C5.4768543,0.284336647 3.59583267,1.10412883 2.2463134,2.35639238 C0.717015619,3.77542186 -0.0585594461,5.65891423 0.00344703766,7.80335355 C0.0214194035,8.41984943 0.27381941,8.93041454 0.733340816,9.27986897 C1.13361932,9.58426439 1.67106976,9.74513714 2.28760225,9.74513714 C2.5771,9.74513714 2.87953712,9.70963166 3.1866412,9.6395541 C3.25069748,9.62493096 3.3081468,9.61751874 3.35741522,9.61751874 C3.56254171,9.61751874 3.66127988,9.79715083 3.83767256,10.4912646 C3.86724826,10.6076823 3.89784886,10.7280715 3.9321281,10.8441415 C4.10085233,11.4148829 4.63577711,11.895488 5.47908711,12.2339979 C6.16441603,12.5091104 7.00904376,12.6668719 7.79638688,12.6668719 C8.76403924,12.6668719 9.70067009,12.4856109 10.5803457,12.128122 C11.4073671,11.7920096 12.1631946,11.3096476 12.8268176,10.6943779 C13.4660992,10.101656 13.9941791,9.41039734 14.3963427,8.63969056 C14.79922,7.86772096 15.0560857,7.05389515 15.1598752,6.22041321 C15.1645604,6.18254679 15.1689712,6.14312474 15.172961,6.10330004 L15.1756147,6.07701866 L15.1756147,6.07599377 C15.1795862,6.03421078 15.1828439,5.99202514 15.1858271,5.94963819 L14.0909407,6.24762799 L14.0909407,6.24762799 Z"
                  id="ColorTheme"
                />
              </g>
              <rect
                id="ColorTheme"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const CompanyLogoIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>Company Logo</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="2" transform="translate(-56.000000, -168.000000)">
            <g id="Company-Logo" transform="translate(56.000000, 168.000000)">
              <g
                id="CompanyLogo"
                transform="translate(4.000000, 5.000000)"
                fill="#000000"
                fillRule="nonzero"
              >
                <path
                  d="M15.462766,12.7009489 L14.8405047,12.7009489 L14.8405047,5.24233796 C14.8405047,4.06600847 13.9710438,3.19654754 12.7947143,3.19654754 L9.51292547,3.19654754 L9.51292547,2.04579042 C9.51292547,0.86946093 8.63494041,0 7.46713505,0 L3.20507166,0 C2.02874217,0 1.15928124,0.********* 1.15928124,2.04579042 L1.15928124,12.7009489 L0.*********,12.7009489 C0.*********,12.7009489 0,12.9055279 0,13.2038724 C0,13.5022168 0.*********,13.6812235 0.*********,13.6812235 L15.4542418,13.6812235 C15.7525863,13.6812235 15.9997859,13.5022168 15.9997859,13.2038724 C16.0083101,12.8970038 15.7611104,12.7009489 15.462766,12.7009489 Z M8.53265089,3.11130627 L8.53265089,12.7009489 L2.13955582,12.7009489 L2.13955582,2.04579042 C2.13955582,1.45762568 2.61690692,0.********* 3.20507166,0.********* L7.46713505,0.********* C8.05529979,0.********* 8.53265089,1.45762568 8.53265089,2.04579042 L8.53265089,3.11130627 Z M13.8602301,12.7009489 L9.51292547,12.7009489 L9.51292547,4.17682212 L12.7947143,4.17682212 C13.382879,4.17682212 13.8602301,4.65417321 13.8602301,5.24233796 L13.8602301,12.7009489 Z"
                  id="CompanyLogo"
                />
                <path
                  d="M6.97273569,4.08305672 L3.69094689,4.08305672 C3.41817483,4.08305672 3.20507166,3.86142943 3.20507166,3.5971815 C3.20507166,3.32440944 3.42669896,3.11130627 3.69094689,3.11130627 L6.97273569,3.11130627 C7.24550775,3.11130627 7.45861092,3.33293357 7.45861092,3.5971815 C7.46713505,3.86142943 7.24550775,4.08305672 6.97273569,4.08305672 Z M6.97273569,7.33074902 L3.69094689,7.33074902 C3.41817483,7.33074902 3.20507166,7.10912172 3.20507166,6.84487379 C3.20507166,6.57210174 3.42669896,6.35899857 3.69094689,6.35899857 L6.97273569,6.35899857 C7.24550775,6.35899857 7.45861092,6.58062586 7.45861092,6.84487379 C7.46713505,7.10912172 7.24550775,7.33074902 6.97273569,7.33074902 Z M6.97273569,10.4932 L3.69094689,10.4932 C3.41817483,10.4932 3.20507166,10.2715728 3.20507166,9.9988007 C3.20507166,9.72602864 3.42669896,9.51292547 3.69094689,9.51292547 L6.97273569,9.51292547 C7.24550775,9.51292547 7.45861092,9.73455277 7.45861092,9.9988007 C7.46713505,10.2715728 7.24550775,10.4932 6.97273569,10.4932 Z M12.3685079,10.4932 L11.1325096,10.4932 C10.8597375,10.4932 10.6381102,10.2715728 10.6381102,9.9988007 C10.6381102,9.72602864 10.8597375,9.51292547 11.1325096,9.51292547 L12.3685079,9.51292547 C12.64128,9.51292547 12.8543832,9.73455277 12.8543832,9.9988007 C12.8629073,10.2715728 12.64128,10.4932 12.3685079,10.4932 Z M12.3685079,7.33074902 L11.1325096,7.33074902 C10.8597375,7.33074902 10.6381102,7.10912172 10.6381102,6.84487379 C10.6381102,6.57210174 10.8597375,6.35899857 11.1325096,6.35899857 L12.3685079,6.35899857 C12.64128,6.35899857 12.8543832,6.58062586 12.8543832,6.84487379 C12.8629073,7.10912172 12.64128,7.33074902 12.3685079,7.33074902 Z"
                  id="CompanyLogo"
                />
              </g>
              <rect
                id="CompanyLogo"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const PageMarginIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 18 18'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 18 18"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>Page Margins</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="2" transform="translate(-56.000000, -485.000000)">
            <g id="Page-Margins" transform="translate(56.000000, 485.000000)">
              <g
                id="margin"
                transform="translate(4.000000, 4.000000)"
                fill="#000000"
                fillRule="nonzero"
              >
                <path
                  d="M12.6666667,1.******** L3.33333332,1.******** C2.93333333,1.******** 2.********,1.06666667 2.********,0.********1 C2.********,0.266666672 2.93333334,0 3.33333334,0 L12.6666667,0 C13.0666667,0 13.3333333,0.266666672 13.3333333,0.********1 C13.3333333,1.06666667 13.0666667,1.******** 12.6666667,1.******** Z"
                  id="PageMargin"
                  opacity={0.8}
                />
                <path
                  d="M12.6666667,16 L3.33333332,16 C2.93333333,16 2.66666665,15.7333333 2.66666665,15.3333333 C2.66666665,14.9333333 2.93333333,14.6666666 3.33333332,14.6666666 L12.6666667,14.6666666 C13.0666666,14.6666666 13.3333333,14.9333333 13.3333333,15.3333333 C13.3333333,15.7333333 13.0666667,16 12.6666667,16 Z"
                  id="PageMargin"
                  opacity={0.8}
                />
                <path
                  d="M15.3333333,13.3333333 C14.9333333,13.3333333 14.6666666,13.0666666 14.6666666,12.6666667 L14.6666666,3.33333332 C14.6666666,2.93333333 14.9333333,2.66666665 15.3333333,2.66666665 C15.7333333,2.66666665 16,2.93333333 16,3.33333332 L16,12.6666667 C16,13.0666667 15.7333333,13.3333333 15.3333333,13.3333333 Z"
                  id="PageMargin"
                  opacity={0.8}
                />
                <path
                  d="M0.********1,13.3333333 C0.266666672,13.3333333 0,13.0666666 0,12.6666667 L0,3.33333332 C0,2.93333333 0.266666672,2.66666665 0.********1,2.66666665 C1.06666667,2.66666665 1.33333334,2.93333333 1.33333334,3.33333332 L1.33333334,12.6666667 C1.33333334,13.0666667 1.06666667,13.3333333 0.********1,13.3333333 Z"
                  id="PageMargin"
                  opacity={0.8}
                />
                <path
                  d="M12.6666667,13.3333333 L3.33333332,13.3333333 C2.93333333,13.3333333 2.66666665,13.0666666 2.66666665,12.6666667 L2.66666665,3.33333332 C2.66666665,2.93333333 2.93333334,2.66666665 3.33333334,2.66666665 L12.6666667,2.66666665 C13.0666667,2.66666665 13.3333333,2.93333333 13.3333333,3.33333332 L13.3333333,12.6666667 C13.3333333,13.0666667 13.0666667,13.3333333 12.6666667,13.3333333 Z M4,12 L12,12 L12,4 L4,4 L4,12 Z"
                  id="PageMargin"
                  opacity={0.8}
                />
              </g>
              <rect
                id="PageMargin"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const WatermarkIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        height="24px"
        viewBox="0 -960 960 960"
        width="20px"
        fill="#000000"
      >
        <path
          d="M491-200q12-1 20.5-9.5T520-230q0-14-9-22.5t-23-7.5q-41 3-87-22.5T343-375q-2-11-10.5-18t-19.5-7q-14 0-23 10.5t-6 24.5q17 91 80 130t127 35ZM480-80q-137 0-228.5-94T160-408q0-100 79.5-217.5T480-880q161 137 240.5 254.5T800-408q0 140-91.5 234T480-80Zm0-80q104 0 172-70.5T720-408q0-73-60.5-165T480-774Q361-665 300.5-573T240-408q0 107 68 177.5T480-160Zm0-320Z"
          opacity={0.8}
        />
      </svg>
    </SvgIcon>
  );
};

export const FontStyleIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>Extermal ID</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6" transform="translate(-76.000000, -635.000000)">
            <g id="Extermal-ID" transform="translate(76.000000, 635.000000)">
              <path
                d="M18.2222222,5.******** L5.77777778,5.******** C5.53637967,5.******** 5.********,5.53637967 5.********,5.77777778 L5.********,18.2222222 C5.********,18.4636203 5.53637967,18.6666667 5.77777778,18.6666667 L18.2222222,18.6666667 C18.4636203,18.6666667 18.6666667,18.4636203 18.6666667,18.2222222 L18.6666667,5.77777778 C18.6666667,5.53637967 18.4636203,5.******** 18.2222222,5.******** Z M18.2222222,4 C19.2,4 20,4.8 20,5.77777778 L20,18.2222222 C20,19.2 19.2,20 18.2222222,20 L5.77777778,20 C4.8,20 4,19.2 4,18.2222222 L4,5.77777778 C4,4.8 4.8,4 5.77777778,4 L18.2222222,4 Z M12.6666667,9.******** L12.6666667,16 L11.3333333,16 L11.3333333,9.******** L8,9.******** L8,8 L16,8 L16,9.******** L12.6666667,9.******** Z"
                id="FontStyle"
                fill="#000000"
              />
              <rect
                id="FontStyle"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const FontSizeIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>FontSize</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6" transform="translate(-76.000000, -496.000000)">
            <g id="Free-Trial-Unit" transform="translate(76.000000, 496.000000)">
              <path
                d="M8.36363636,6.90909091 L18.5454545,6.90909091 C19.3487778,6.90909091 20,7.56031309 20,8.36363636 L20,18.5454545 C20,19.3487778 19.3487778,20 18.5454545,20 L8.36363636,20 C7.56031309,20 6.90909091,19.3487778 6.90909091,18.5454545 L6.90909091,8.36363636 C6.90909091,7.56031309 7.56031309,6.90909091 8.36363636,6.90909091 Z M17.8539601,8.14409684 C18.4062448,8.14409684 18.8539601,8.59181209 18.8539601,9.14409684 L18.8539601,17.9050878 C18.8539601,18.4573725 18.4062448,18.9050878 17.8539601,18.9050878 L9.16862457,18.9050878 C8.61633982,18.9050878 8.16862457,18.4573725 8.16862457,17.9050878 L8.16862457,9.14409684 C8.16862457,8.59181209 8.61633982,8.14409684 9.16862457,8.14409684 L17.8539601,8.14409684 Z M14.1818182,9.81818182 L12.7272727,9.81818182 L11.2727273,10.5454545 L11.2727273,11.2727273 L12.7272727,11.2727273 L12.7272727,15.6363636 L12.0011355,15.9994323 L11.2727273,16.3636364 L11.2727273,17.0909091 L15.6363636,17.0909091 L15.6363636,16.3636364 L14.1818182,15.6363636 L14.1818182,9.81818182 Z M14.1818182,4 C14.9851415,4 15.5832777,4.46483451 15.5832777,5.26815778 L15.5832777,5.26815778 L14.1287323,5.26815778 L5.24483705,5.24604392 L5.29792297,14.1597043 L5.29792297,15.6142498 C4.4945997,15.6142498 4,14.9851415 4,14.1818182 L4,14.1818182 L4,5.45454545 C4,4.65122218 4.65122218,4 5.45454545,4 L5.45454545,4 Z"
                id="FontSize"
                fill="#000000"
              />
              <rect
                id="FontSize"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const VisibilityIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={{
        marginTop: '3px',
        marginRight: '5px',
        opacity: '0.9',
        fontSize: '21px',
      }}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg focusable="false" viewBox="0 0 24 24" aria-hidden="true">
        <path d="M12 6c3.79 0 7.17 2.13 8.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5C4.83 8.13 8.21 6 12 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 5c1.38 0 2.5 1.12 2.5 2.5S13.38 14 12 14s-2.5-1.12-2.5-2.5S10.62 9 12 9m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7z" />
      </svg>
    </SvgIcon>
  );
};

export const DisplayFieldsIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>Displayed Fields</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="3" transform="translate(-56.000000, -178.000000)">
            <g id="Displayed-Fields" transform="translate(56.000000, 178.000000)">
              <rect
                id="DisplayFields"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
              <g
                id="DisplayFields"
                transform="translate(12.000000, 11.998482) scale(1, -1) translate(-12.000000, -11.998482) translate(4.000000, 4.000000)"
                fill="#000000"
                fillRule="nonzero"
              >
                <path
                  d="M6.1438857,1.28592956 L6.1438857,6.1438857 L1.28592956,6.1438857 L1.28592956,1.28592956 L6.1438857,1.28592956 M6.1438857,0 L1.28592956,0 C0.575730276,0 0,0.575730276 0,1.28592956 L0,6.1438857 C0,6.85408498 0.575730276,7.42981526 1.28592956,7.42981526 L6.1438857,7.42981526 C6.85408498,7.42981526 7.42981526,6.85408498 7.42981526,6.1438857 L7.42981526,1.28592956 C7.42981526,0.575730276 6.85408498,0 6.1438857,0 Z M6.1438857,9.85307808 L6.1438857,14.7110342 L1.28592956,14.7110342 L1.28592956,9.85307808 L6.1438857,9.85307808 M6.1438857,8.56714852 L1.28592956,8.56714852 C0.575730276,8.56714852 0,9.14287879 0,9.85307808 L0,14.7110342 C0,15.4212335 0.575730276,15.9969638 1.28592956,15.9969638 L6.1438857,15.9969638 C6.85408498,15.9969638 7.42981526,15.4212335 7.42981526,14.7110342 L7.42981526,9.85307808 C7.42981526,9.14287879 6.85408498,8.56714852 6.1438857,8.56714852 L6.1438857,8.56714852 Z M14.7140704,9.85307808 L14.7140704,14.7110342 L9.8561143,14.7110342 L9.8561143,9.85307808 L14.7140704,9.85307808 M14.7140704,8.56714852 L9.8561143,8.56714852 C9.14591502,8.56714852 8.57018474,9.14287879 8.57018474,9.85307808 L8.57018474,14.7110342 C8.57018474,15.4212335 9.14591502,15.9969638 9.8561143,15.9969638 L14.7140704,15.9969638 C15.4242697,15.9969638 16,15.4212335 16,14.7110342 L16,9.85307808 C16,9.14287879 15.4242697,8.56714852 14.7140704,8.56714852 L14.7140704,8.56714852 Z"
                  id="DisplayFields"
                />
                <path
                  d="M9.21314952,3.08730256 L15.3570352,3.08730256 C15.7856784,3.08730256 16,3.30162416 16,3.73026734 L16,3.73026734 C16,4.15891053 15.7856784,4.37323213 15.3570352,4.37323213 L9.21314952,4.37323213 C8.78450633,4.37323213 8.57018474,4.15891053 8.57018474,3.73026734 L8.57018474,3.73026734 C8.57018474,3.30162416 8.78450633,3.08730256 9.21314952,3.08730256 Z"
                  id="DisplayFields"
                  transform="translate(12.285092, 3.730267) rotate(-90.000000) translate(-12.285092, -3.730267) "
                />
                <path
                  d="M9.21314952,3.05944075 L15.3570352,3.05944075 C15.7856784,3.05944075 16,3.27376235 16,3.70240554 L16,3.70240554 C16,4.13104872 15.7856784,4.34537032 15.3570352,4.34537032 L9.21314952,4.34537032 C8.78450633,4.34537032 8.57018474,4.13104872 8.57018474,3.70240554 L8.57018474,3.70240554 C8.57018474,3.27376235 8.78450633,3.05944075 9.21314952,3.05944075 Z"
                  id="DisplayFields"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SummaryFieldsIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>Displayed Fields 2</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="3" transform="translate(-56.000000, -543.000000)">
            <g id="Displayed-Fields" transform="translate(56.000000, 543.000000)">
              <g
                id="SummaryFields"
                transform="translate(5.000000, 5.000000)"
                fill="#000000"
                fillRule="nonzero"
              >
                <path
                  d="M0,1.65857945 C0.75768189,2.41626134 6.63412104,7.46796283 6.63412104,8.09757875 C4.41816846,9.90148445 2.25619677,12.0281128 0,14.2439044 L0,16 L14.0487648,16 L14.0487648,11.9024794 L12.3902211,11.9024794 C12.221124,12.6544376 12.0839002,13.4204009 11.9024436,14.1463704 C11.4983025,14.1761332 3.52782383,14.244441 3.12192984,14.2439223 C5.22074499,12.1451071 7.46197091,10.1965906 9.56094704,8.09759664 C8.71247425,7.22463749 3.26623658,1.86377117 3.12192984,1.61516938 C2.98098574,1.61516938 9.8236969,1.77707624 11.9024436,1.85366541 C12.0756725,2.54661675 12.2461827,3.40022291 12.3902211,4.09755639 L14.0487648,4.09755639 L14.0487648,0 L0,0 L0,1.65857945 Z"
                  id="SummaryFields"
                />
              </g>
              <rect
                id="SummaryFields"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const DescriptionIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>Description</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6" transform="translate(-76.000000, -542.000000)">
            <g id="Price-Book" transform="translate(76.000000, 542.000000)">
              <path
                d="M18.6666667,4 C19.4030463,4 20,4.******** 20,5.******** L20,18.6666667 C20,19.4030463 19.4030463,20 18.6666667,20 L5.********,20 C4.********,20 4,19.4030463 4,18.6666667 L4,5.******** C4,4.******** 4.********,4 5.********,4 L18.6666667,4 Z M18.6666667,5.******** L5.********,5.******** L5.********,18.6666667 L18.6666667,18.6666667 L18.6666667,5.******** Z M16.7991596,10.7461648 L17.0245857,10.9715909 C17.2849352,11.2319404 17.2849352,11.6540504 17.0245857,11.9144 L11.8022965,17.1366891 L10.399723,17.3737553 L10.6340614,15.968454 L15.8563506,10.7461648 C16.1167001,10.4858153 16.5388101,10.4858153 16.7991596,10.7461648 Z M10.9977182,10.6666667 C11.1830733,10.6666667 11.3333333,10.8093567 11.3333333,11.003123 L11.3333333,11.6635437 C11.3333333,11.8493634 11.178065,12 10.9977182,12 L7.00228182,12 C6.81692669,12 6.********,11.85731 6.********,11.6635437 L6.********,11.003123 C6.********,10.8173033 6.82193502,10.6666667 7.00228182,10.6666667 L10.9977182,10.6666667 Z M13.626257,8 C13.8326696,8 14,8.14269002 14,8.3364563 L14,8.99687703 C14,9.18269672 13.8330546,9.******** 13.626257,9.******** L7.04040964,9.******** C6.8339971,9.******** 6.********,9.19064331 6.********,8.99687703 L6.********,8.3364563 C6.********,8.15063662 6.83361204,8 7.04040964,8 L13.626257,8 Z"
                id="Shape"
                fill="#000000"
              />
              <rect
                id="Description"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SquareDownloadIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>download</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-1883.000000, -138.000000)">
            <g id="download-5" transform="translate(1183.000000, 90.000000)">
              <g id="download" transform="translate(700.000000, 48.000000)">
                <g
                  id="download(2)"
                  transform="translate(2.000000, 2.000000)"
                  fill="#6239EB"
                  fillRule="nonzero"
                >
                  <path
                    d="M20,12.5695002 L20,19.0295002 C20,19.5472672 19.580267,19.9670002 19.0625,19.9670002 L0.9375,19.9670002 C0.419733047,19.9670002 0,19.5472672 0,19.0295002 L0,12.5695002 C0,12.0517333 0.419733047,11.6320002 0.9375,11.6320002 C1.45526695,11.6320002 1.875,12.0517333 1.875,12.5695002 L1.875,18.0920002 L18.125,18.0920002 L18.125,12.5695002 C18.125,12.0517333 18.544733,11.6320002 19.0625,11.6320002 C19.580267,11.6320002 20,12.0517333 20,12.5695002 Z M9.3525,15.4207502 C9.5281693,15.5970028 9.76677833,15.6960696 10.015625,15.6960696 C10.2644717,15.6960696 10.5030807,15.5970028 10.67875,15.4207502 L16.53625,9.56450023 C16.7731618,9.3273652 16.8655672,8.98185247 16.7786581,8.65811336 C16.691749,8.33437424 16.438729,8.08159241 16.1149081,7.99498834 C15.7910873,7.90838427 15.4456617,8.00111518 15.20875,8.23825023 L10.955,12.4932502 L10.955,0.904500228 C10.9365707,0.399707563 10.522004,-2.77555756e-17 10.016875,-2.77555756e-17 C9.51174603,-2.77555756e-17 9.09717935,0.399707563 9.07875,0.904500228 L9.07875,12.4957502 L4.8225,8.23950023 C4.58558826,8.00258847 4.24028186,7.91006389 3.91665439,7.99677961 C3.59302692,8.08349532 3.34024509,8.33627715 3.25352938,8.65990462 C3.16681366,8.98353209 3.25933824,9.32883849 3.49625,9.56575023 L9.3525,15.4207502 L9.3525,15.4207502 Z"
                    id="download"
                  />
                </g>
                <rect
                  id="download"
                  stroke="#979797"
                  fill="#D8D8D8"
                  opacity="0"
                  x="0"
                  y="0"
                  width="24"
                  height="24"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RefreshTokenIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        height="24px"
        viewBox="0 -960 960 960"
        width="24px"
        fill="#6239EB"
      >
        <path d="M838-65 720-183v89h-80v-226h226v80h-90l118 118-56 57ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 20-2 40t-6 40h-82q5-20 7.5-40t2.5-40q0-20-2.5-40t-7.5-40H654q3 20 4.5 40t1.5 40q0 20-1.5 40t-4.5 40h-80q3-20 4.5-40t1.5-40q0-20-1.5-40t-4.5-40H386q-3 20-4.5 40t-1.5 40q0 20 1.5 40t4.5 40h134v80H404q12 43 31 82.5t45 75.5q20 0 40-2.5t40-4.5v82q-20 2-40 4.5T480-80ZM170-400h136q-3-20-4.5-40t-1.5-40q0-20 1.5-40t4.5-40H170q-5 20-7.5 40t-2.5 40q0 20 2.5 40t7.5 40Zm34-240h118q9-37 22.5-72.5T376-782q-55 18-99 54.5T204-640Zm172 462q-18-34-31.5-69.5T322-320H204q29 51 73 87.5t99 54.5Zm28-462h152q-12-43-31-82.5T480-798q-26 36-45 75.5T404-640Zm234 0h118q-29-51-73-87.5T584-782q18 34 31.5 69.5T638-640Z" />
      </svg>
    </SvgIcon>
  );
};

export const SendEmailIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        height="24px"
        viewBox="0 -960 960 960"
        width="24px"
        fill="#6239EB"
      >
        <path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm320-280L160-640v400h640v-400L480-440Zm0-80 320-200H160l320 200ZM160-640v-80 480-400Z" />
      </svg>
    </SvgIcon>
  );
};

export const SmallSizeIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 24 24'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>one column</title>
        <g
          id="Layout"
          stroke="none"
          strokeWidth="1"
          fill="none"
          fillRule="evenodd"
          transform="scale(0.8)"
        >
          <g
            id="1-2-image-url"
            transform="translate(-1104.000000, -276.000000)"
            fill="currentColor"
          >
            <g id="samll-4" transform="translate(1077.000000, 140.000000)">
              <g id="samll-3" transform="translate(0.000000, 111.000000)">
                <rect id="samll" x="27" y="28" width="24" height="24" rx="4" />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const MiddleSizeIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 32 32'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="32px"
        height="32px"
        viewBox="0 0 32 32"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>middle</title>
        <g id="Layout" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="1-2-image-url"
            transform="translate(-1170.000000, -275.000000)"
            fill="currentColor"
          >
            <g id="middle-4" transform="translate(1077.000000, 140.000000)">
              <g id="middle-3" transform="translate(0.000000, 111.000000)">
                <rect id="middle" x="93" y="24" width="32" height="32" rx="4" />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const LargeSizeIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 44 44'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="44px"
        height="44px"
        viewBox="0 0 44 44"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>large</title>
        <g id="Layout" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g
            id="1-2-image-url"
            transform="translate(-1244.000000, -269.000000)"
            fill="currentColor"
          >
            <g id="large-4" transform="translate(1077.000000, 140.000000)">
              <g id="large-3" transform="translate(0.000000, 111.000000)">
                <rect id="large" x="167" y="18" width="44" height="44" rx="4" />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const SmallImageIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 23 23'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="23px"
        height="23px"
        viewBox="0 0 23 23"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>SmallImageIcon</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-490.000000, -471.000000)">
            <g id="SmallImageIcon" transform="translate(490.000000, 471.000000)">
              <rect
                id="SmallImageIcon"
                fill="currentColor"
                x="0"
                y="0"
                width="22.270512"
                height="22.278481"
                rx="4"
              />
              <path
                d="M11.1193703,15.9996312 C11.7090202,15.9996312 12.2409674,15.8790773 12.7161135,15.6379695 C13.1753589,15.4113825 13.5639387,15.0624266 13.8395139,14.6291234 C14.1143358,14.1930569 14.2568836,13.6859631 14.2497444,13.1697864 C14.2497444,12.6368111 14.1090939,12.1944779 13.8268914,11.8436931 C13.5585631,11.5035598 13.2178208,11.2282093 12.8297158,11.0378853 C12.3603266,10.8173009 11.8776183,10.6265771 11.3844423,10.4668404 C10.9655371,10.3352672 10.5563934,10.1741281 10.1600621,9.98462463 C9.85712263,9.83234599 9.62360681,9.64381052 9.45951462,9.41992465 C9.29542243,9.19603877 9.21337633,8.91867409 9.21337633,8.58873702 C9.21337633,8.29233751 9.28279995,8.01315999 9.42164719,7.75120446 C9.5708181,7.47845152 9.79408947,7.25398776 10.0653935,7.1040202 C10.3557104,6.93451957 10.7073366,6.85022245 11.1202719,6.85022245 C11.6161549,6.85022245 12.0390078,6.99615616 12.3879291,7.28802357 C12.7377521,7.57989098 12.9586454,7.96693254 13.0506091,8.44914826 C13.0756574,8.55859848 13.1358595,8.65671042 13.2219142,8.72832578 C13.3071956,8.80327947 13.416996,8.84394829 13.5302632,8.84253477 C13.675062,8.84593218 13.8121393,8.77702753 13.8963151,8.6585314 C13.9862255,8.54640638 14.0230923,8.40046806 13.9972949,8.25879995 C13.862054,7.55632405 13.5347713,7.00431395 13.0127417,6.60276965 C12.4916138,6.20122535 11.8604899,6 11.1193703,6 C10.6234873,6 10.1564556,6.096987 9.71827539,6.29186741 C9.28099674,6.48674782 8.92486259,6.78042807 8.65167614,7.17381458 C8.37848968,7.56720109 8.24144566,8.03853977 8.24144566,8.58873702 C8.24144566,9.10539673 8.37848968,9.53594648 8.65167614,9.88310553 C8.9257642,10.2302646 9.26025982,10.5003779 9.655163,10.6952583 C10.0509678,10.8901387 10.5261139,11.080487 11.0815028,11.2663033 C11.5954179,11.4358039 12.0029436,11.5980532 12.305883,11.7548639 C12.6088225,11.9116747 12.8468463,12.1038358 13.0190529,12.3322538 C13.1921612,12.5606718 13.2778137,12.8398493 13.2778137,13.1697864 C13.2778137,13.4997234 13.1939644,13.8169706 13.0253642,14.1215279 C12.8499482,14.4332751 12.5922109,14.690193 12.2806381,14.8638863 C11.9524537,15.0542346 11.565665,15.1494088 11.1193703,15.1494088 C10.5306219,15.1494088 10.0374437,14.9590605 9.64254053,14.5783639 C9.24763735,14.1976672 9.02403919,13.7109194 8.97354928,13.1190268 C8.96795103,12.9891586 8.90558575,12.8684062 8.80314585,12.7890897 C8.70232423,12.7058887 8.573471,12.6649809 8.44340528,12.6748808 C8.31501826,12.6883751 8.19709871,12.7522093 8.11522089,12.8525392 C8.03198582,12.9511479 7.99112209,13.0789584 8.0016186,13.207856 C8.05210851,13.7662111 8.2162007,14.2547717 8.49389518,14.673538 C8.77158966,15.0923043 9.13764147,15.4177093 9.59205062,15.6506593 C10.0661482,15.8878883 10.5898537,16.007548 11.1193703,15.9996312 L11.1193703,15.9996312 Z"
                id="路径"
                stroke="white"
                strokeWidth="0.5"
                fill="white"
                fillRule="nonzero"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const MiddleImageIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 23 23'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="23px"
        height="23px"
        viewBox="0 0 23 23"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>MiddleImageIcon</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-523.000000, -471.000000)">
            <g id="MiddleImageIcon" transform="translate(523.000000, 471.000000)">
              <rect
                id="MiddleImageIcon"
                fill="currentColor"
                x="0"
                y="0"
                width="22.270512"
                height="22.278481"
                rx="4"
              />
              <path
                d="M6.50661265,6.03289831 C6.80790211,5.94139558 7.13374108,6.05744782 7.30781943,6.32079713 L10.9991733,11.8555961 L14.6905271,6.31856535 C14.8646054,6.05744782 15.1904444,5.93916381 15.4917339,6.03066654 C15.7930233,6.12216926 15.9983465,6.40114098 15.9983465,6.71582109 L15.9983465,15.2858324 C15.9983465,15.6808563 15.6792029,16 15.2841789,16 C14.889155,16 14.5700113,15.6808563 14.5700113,15.2858324 L14.5700113,9.07480597 L11.5928251,13.5405853 C11.4611504,13.7392132 11.2379731,13.8574972 10.9991733,13.8574972 C10.7603735,13.8574972 10.5371961,13.7392132 10.4055214,13.5405853 L7.42833522,9.07480597 L7.42833522,15.2858324 C7.42833522,15.6808563 7.10919157,16 6.71416761,16 C6.31914365,16 6,15.6808563 6,15.2858324 L6,6.71582109 C6,6.40114098 6.20532319,6.12440103 6.50661265,6.03289831 Z"
                id="路径"
                fill="white"
                fillRule="nonzero"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const LargeImageIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 23 23'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="23px"
        height="23px"
        viewBox="0 0 23 23"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>LargeImageIcon</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-556.000000, -471.000000)">
            <g id="LargeImageIcon" transform="translate(556.000000, 471.000000)">
              <rect
                id="LargeImageIcon"
                fill="currentColor"
                x="0"
                y="0"
                width="22.270512"
                height="22.278481"
                rx="4"
              />
              <path
                d="M8.71428571,6 C9.109375,6 9.42857143,6.31919643 9.42857143,6.71428571 L9.42857143,14.5714286 L13.7142857,14.5714286 C14.109375,14.5714286 14.4285714,14.890625 14.4285714,15.2857143 C14.4285714,15.6808036 14.109375,16 13.7142857,16 L8.71428571,16 C8.31919643,16 8,15.6808036 8,15.2857143 L8,6.71428571 C8,6.31919643 8.31919643,6 8.71428571,6 Z"
                id="路径"
                fill="white"
                fillRule="nonzero"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const TemplateUploadIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon
      style={props.style}
      className={props.className || ''}
      viewBox={props.viewBox || '0 0 23 23'}
    >
      <title>{props.title || ''}</title>
      <svg
        width="24px"
        height="24px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>publish</title>
        <g id="Layout2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="9" transform="translate(-1700.000000, -17.000000)">
            <g id="upload" transform="translate(1700.000000, 17.000000)">
              <g
                id="upload"
                transform="translate(2.000000, 3.000000)"
                fill="#6239EB"
                fillRule="nonzero"
              >
                <path
                  d="M9.93130238,0 C10.8660151,0 11.6955056,0.599036386 11.9894454,1.48629862 L12.4696863,2.93601004 L16.9385194,2.93601004 C18.7413877,2.93601004 20.2063212,4.38292095 20.2354329,6.17885571 L20.2358846,6.23337516 L20.2358846,14.7026349 C20.2358846,16.523729 18.7596136,18 16.9385194,18 L3.29736512,18 C1.47627102,18 0,16.523729 0,14.7026349 L0,3.29736512 C0,1.47627102 1.47627102,0 3.29736512,0 L9.93130238,0 Z M9.93130238,1.44542033 L3.29736512,1.44542033 C2.27457215,1.44542033 1.44542033,2.27457215 1.44542033,3.29736512 L1.44542033,14.7026349 C1.44542033,15.7254279 2.27457215,16.5545797 3.29736512,16.5545797 L16.9385194,16.5545797 C17.9613124,16.5545797 18.7904642,15.7254279 18.7904642,14.7026349 L18.7904642,6.23337516 C18.7904642,5.21058218 17.9613124,4.38143036 16.9385194,4.38143036 L11.9477767,4.38143036 C11.6362035,4.38143455 11.3597015,4.18175633 11.2617215,3.88598996 L10.6173576,1.94086073 C10.5193776,1.64509436 10.2428755,1.44542033 9.93130238,1.44542033 Z M10.6409134,8.0381857 L13.4414153,10.9741957 C13.880123,11.4341782 13.5540903,12.195734 12.9184442,12.195734 L12.1505646,12.195734 L12.1505646,14.544542 C12.1505646,14.9396838 11.8334304,15.2607704 11.4398243,15.2671619 L11.4278545,15.2672522 L8.80803011,15.2672522 C8.40889084,15.2672522 8.08531995,14.9436813 8.08531995,14.544542 L8.08529737,12.195734 L7.3174404,12.195734 C6.68811794,12.195734 6.36228858,11.4493099 6.78155082,10.9880853 L6.79449184,10.9741957 L9.59499373,8.0381857 C9.87971895,7.73968381 10.3561656,7.73968381 10.6408908,8.0381857 L10.6409134,8.0381857 Z M10.1179197,9.58408532 L8.98475533,10.7720853 L8.99279548,10.7741405 C9.29875031,10.854813 9.52529737,11.131069 9.53064994,11.4610765 L9.53074028,11.4730238 L9.53074028,13.8218319 L10.7051443,13.8218319 L10.7051443,11.4730238 C10.7051443,11.1377541 10.9334755,10.8557842 11.2430891,10.7741405 L11.2511066,10.7720853 L10.1179197,9.58408532 Z M13.5056462,5.71392723 C13.9047854,5.71392723 14.2283563,6.03749812 14.2283563,6.43663739 C14.2283563,6.83577666 13.9047854,7.15934755 13.5056462,7.15934755 L6.73023839,7.15934755 C6.33109912,7.15934755 6.00752823,6.83577666 6.00752823,6.43663739 C6.00752823,6.03749812 6.33109912,5.71392723 6.73023839,5.71392723 L13.5056462,5.71392723 Z"
                  id="publish"
                />
              </g>
              <rect
                id="upload"
                stroke="#979797"
                fill="#D8D8D8"
                opacity="0"
                x="0"
                y="0"
                width="24"
                height="24"
              />
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const ColumnChooserIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 20 20">
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g opacity="0.2">
          <mask id="mask0_305_10231" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect width="20" height="20" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_305_10231)">
            <path
              d="M8.39575 18L7.93742 15.625C7.61797 15.5 7.30895 15.3542 7.01034 15.1875C6.71172 15.0208 6.43047 14.8264 6.16659 14.6042L3.87492 15.375L2.27075 12.6042L4.08325 11.0208C4.05547 10.8542 4.03464 10.6875 4.02075 10.5208C4.00686 10.3542 3.99992 10.1806 3.99992 10C3.99992 9.81944 4.00686 9.64583 4.02075 9.47917C4.03464 9.3125 4.05547 9.14583 4.08325 8.97917L2.27075 7.39583L3.87492 4.625L6.16659 5.39583C6.43047 5.17361 6.71172 4.97917 7.01034 4.8125C7.30895 4.64583 7.61797 4.5 7.93742 4.375L8.39575 2H11.6041L12.0624 4.375C12.3819 4.5 12.6909 4.64583 12.9895 4.8125C13.2881 4.97917 13.5694 5.17361 13.8333 5.39583L16.1249 4.625L17.7291 7.39583L15.9166 8.97917C15.9444 9.14583 15.9652 9.3125 15.9791 9.47917C15.993 9.64583 15.9999 9.81944 15.9999 10C15.9999 10.1806 15.993 10.3542 15.9791 10.5208C15.9652 10.6875 15.9444 10.8542 15.9166 11.0208L17.7291 12.6042L16.1249 15.375L13.8333 14.6042C13.5694 14.8264 13.2881 15.0208 12.9895 15.1875C12.6909 15.3542 12.3819 15.5 12.0624 15.625L11.6041 18H8.39575ZM9.62492 16.5H10.3749L10.7708 14.4375C11.2985 14.3403 11.7916 14.1597 12.2499 13.8958C12.7083 13.6319 13.1041 13.2986 13.4374 12.8958L15.4374 13.5625L15.8124 12.9375L14.2291 11.5417C14.3124 11.3056 14.3784 11.059 14.427 10.8021C14.4756 10.5451 14.4999 10.2778 14.4999 10C14.4999 9.72222 14.4756 9.45486 14.427 9.19792C14.3784 8.94097 14.3124 8.69444 14.2291 8.45833L15.8124 7.0625L15.4374 6.4375L13.4374 7.10417C13.1041 6.70139 12.7083 6.36806 12.2499 6.10417C11.7916 5.84028 11.2985 5.65972 10.7708 5.5625L10.3749 3.5H9.62492L9.22909 5.5625C8.70131 5.65972 8.20825 5.84028 7.74992 6.10417C7.29159 6.36806 6.89575 6.70139 6.56242 7.10417L4.56242 6.4375L4.18742 7.0625L5.77075 8.45833C5.68742 8.69444 5.62145 8.94097 5.57284 9.19792C5.52422 9.45486 5.49992 9.72222 5.49992 10C5.49992 10.2778 5.52422 10.5451 5.57284 10.8021C5.62145 11.059 5.68742 11.3056 5.77075 11.5417L4.18742 12.9375L4.56242 13.5625L6.56242 12.8958C6.89575 13.2986 7.29159 13.6319 7.74992 13.8958C8.20825 14.1597 8.70131 14.3403 9.22909 14.4375L9.62492 16.5ZM9.99992 13C10.8333 13 11.5416 12.7083 12.1249 12.125C12.7083 11.5417 12.9999 10.8333 12.9999 10C12.9999 9.16667 12.7083 8.45833 12.1249 7.875C11.5416 7.29167 10.8333 7 9.99992 7C9.16659 7 8.45825 7.29167 7.87492 7.875C7.29159 8.45833 6.99992 9.16667 6.99992 10C6.99992 10.8333 7.29159 11.5417 7.87492 12.125C8.45825 12.7083 9.16659 13 9.99992 13Z"
              fill="#1C1B1F"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export const RefreherIcon: React.FC<Props> = (props: Props) => {
  return (
    <SvgIcon {...props} viewBox="0 0 20 20">
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g opacity="0.2">
          <mask id="mask0_305_10226" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
            <rect width="20" height="20" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_305_10226)">
            <path
              d="M4.72917 12.7917C4.50694 12.375 4.32986 11.934 4.19792 11.4688C4.06597 11.0035 4 10.5139 4 10C4 8.31944 4.59028 6.88889 5.77083 5.70833C6.95139 4.52778 8.40278 3.95833 10.125 4L8.9375 2.8125L10 1.75L13 4.75L10 7.75L8.9375 6.6875L10.125 5.5C8.81944 5.47222 7.72222 5.90278 6.83333 6.79167C5.94444 7.68056 5.5 8.75 5.5 10C5.5 10.3056 5.52778 10.5972 5.58333 10.875C5.63889 11.1528 5.72222 11.4236 5.83333 11.6875L4.72917 12.7917ZM10 18.25L7 15.25L10 12.25L11.0625 13.3125L9.875 14.5C11.1806 14.5278 12.2778 14.0972 13.1667 13.2083C14.0556 12.3194 14.5 11.25 14.5 10C14.5 9.69444 14.4722 9.40278 14.4167 9.125C14.3611 8.84722 14.2778 8.57639 14.1667 8.3125L15.2708 7.20833C15.4931 7.625 15.6701 8.06597 15.8021 8.53125C15.934 8.99653 16 9.48611 16 10C16 11.6667 15.4097 13.0938 14.2292 14.2812C13.0486 15.4688 11.5972 16.0417 9.875 16L11.0625 17.1875L10 18.25Z"
              fill="#1C1B1F"
            />
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default {
  HintIcon,
  OrganizationIcon,
  UsersIcon,
  NotificationIcon,
  SalesforceIcon,
  ImportExportIcon,
  BusinessObjectsIcon,
  ScheduledJobIcon,
  FolderIcon,
  StackIcon,
  DollarCircleIcon,
  LayoutIcon,
  AccountCircleIcon,
  SearchIcon,
  MultiBoxIcon,
  FundsBoxIcon,
  ClipboardIcon,
  FoldersIcon,
  BuildingsIcon,
  UserVoiceIcon,
  WalletIcon,
  NetSuiteIcon,
  InfoIcon,
  CalendarIcon,
  FocusIcon,
  AlertIcon,
  CheckCircleIcon,
  FileListIcon,
  BulletListIcon,
  RightArrowIcon,
  CancelIcon,
  DataFlowManagerIcon,
  ActivatedIcon,
  BucketIcon,
  CompanyIcon,
  PrimaryContactIcon,
  AddressIcon,
  TotalAmountIcon,
  EditBucketIcon,
  PreviewBucketIcon,
  CreateBucketIcon,
  DiamondIcon,
  CloseMenuIcon,
  RocketIcon,
  BackIcon,
  PreviewIcon,
  FinalizeIcon,
  FinalizeAndActivateIcon,
  LayersIcon,
  NextIcon,
  TrolleyIcon,
  BalanceIcon,
  CheckboxIcon,
  CurrencyIcon,
  DateIcon,
  ImageIcon,
  MultipleSelectIcon,
  NumberIcon,
  PercentageIcon,
  SelectIcon,
  TextAreaIcon,
  TextIcon,
  OthersIcon,
  BellIcon,
  CollapseBucketIcon,
  MoreBucketIcon,
  ViewIcon,
  SalesforceSettingIcon,
  AvalaraSettingIcon,
  QuickbookSettingIcon,
  QuickbookLogoIcon,
  StripeSettingIcon,
  RightRevSettingIcon,
  EvergreenIcon,
  StripeLogoIcon,
  WebhookConnectionIcon,
  AvalaraLogoIcon,
  NetSuiteLogoIcon,
  SalesforceLogoIcon,
  CreditCardIcon,
  PeopleAltIcon,
  TransferredIcon,
  AskNueGradientIcon,
  CleanupIcon,
  NueChatBubbleIcon,
  BookIcon,
  CopyIcon,
  ThumbIcon,
  ThumbIconFilled,
  AdjustPriceIcon,
  CreateMilestoneIcon,
  RoundUploadIcon,
  RoundDownloadIcon,
  RoundRefreshIcon,
  RoundSettingsIcon,
  QuickbookStyleIcon,
  QuickbookLogoIconForTrans,
  SalesforceSyncSuccessIcon,
  TwoColumnsIcon,
  ThreeColumnsIcon,
  ListColumnsIcon,
  LeftAlignmentIcon,
  CenterAlignmentIcon,
  RightAlignmentIcon,
  DeleteComponentIcon,
  CompanyLogoIcon,
  ColorThemeIcon,
  PageMarginIcon,
  WatermarkIcon,
  FontStyleIcon,
  FontSizeIcon,
  DisplayFieldsIcon,
  SummaryFieldsIcon,
  DescriptionIcon,
  DownloadIcon,
  SquareDownloadIcon,
  SmallSizeIcon,
  MiddleSizeIcon,
  LargeSizeIcon,
  CurrentColorCopyIcon,
  UpgradeIcon,
  LargeImageIcon,
  SmallImageIcon,
  MiddleImageIcon,
  TemplateUploadIcon,
  RefreshTokenIcon,
  SendEmailIcon,
  InvoiceDownloadIcon,
  ColumnChooserIcon,
  CashOutIcon,
  VisibilityIcon,
};

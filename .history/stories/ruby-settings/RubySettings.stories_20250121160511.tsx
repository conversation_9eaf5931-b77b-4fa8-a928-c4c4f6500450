import React from 'react';

import FolderOpenIcon from '@material-ui/icons/FolderOpen';
import InsertLinkIcon from '@material-ui/icons/InsertLink';
import SecurityOutlinedIcon from '@material-ui/icons/SecurityOutlined';
import VpnKeyOutlinedIcon from '@material-ui/icons/VpnKeyOutlined';
import { Meta, Story } from '@storybook/react/types-6-0';
import { uuid } from 'short-uuid';

import PaymentIcon from '@material-ui/icons/Payment';
import {
  BusinessObjectsServiceContext,
  BusinessObjectsServiceContextInterface,
  IntegrationConfigureDetail,
  IntegrationUpdateDetail,
  MetadataObject,
  PaymentRule,
  UserContext,
  UserFacade,
  WebhookRegisterObject,
} from '../../components';
import ApiKeysContext from '../../components/api-key-editor/api-keys-context';
import AvalaraConnectionContext from '../../components/avalara-connection/avalara-connection-context';
import AvalaraEInvoicingConnectionContext from '../../components/avalara-e-invoicing-connection/avalara-e-invoicing-connection-context';
import { UpsertTaxIntegrationRequestBody } from '../../components/avalara-connection/interface';
import CollectionsContext from '../../components/collections/collections-context';
import { ComposeProviders, Provider } from '../../components/compose-providers';
import {
  Condition,
  OrderByField,
  RubyFilter,
} from '../../components/graph-ql-query-constructor/interface';
import {
  AvalaraSettingIcon,
  DataFlowManagerIcon,
  DollarCircleIcon,
  FoldersIcon,
  LayoutIcon,
  MultiBoxIcon,
  NetSuiteLogoIcon,
  OrganizationIcon,
  QuickbookSettingIcon,
  RightRevSettingIcon,
  SalesforceIcon,
  SalesforceSettingIcon,
  StackIcon,
  StripeSettingIcon,
  UsersIcon,
  WalletIcon,
} from '../../components/icons';
import { RubyObject } from '../../components/metadata/interface';
import { NetSuiteConnectionContext } from '../../components/netsuite-connection';
import { PickListOption } from '../../components/pick-list';
import QuickBooksConnectionContext from '../../components/quickbooks-connection/quickbooks-connection-context';
import { UpsertRightRevIntegrationRequestBody } from '../../components/rightrev-connection/interface';
import RightRevConnectionContext from '../../components/rightrev-connection/rightrev-connection-context';
import RubySettings, {
  AutoSetBcdMode,
  BillingSettings,
  BillingSettingsContext,
  BillingSettingsService,
  BillingSettingsSfCustomFields,
  BillSysSetting,
  CreditPools,
  CreditSettings,
  CreditTypes,
  DataFlowManagerContext,
  DataFlowManagerService,
  FieldAsPricingAttribute,
  GraphqlGeneratorContext,
  GraphqlGeneratorService,
  KEY_ACTIVATE_INVOICES_ON_ORDER_ACTIVATION,
  KEY_BILL_ON_ORDER_ACTIVATION,
  MappingObjectLoaderContext,
  OrderSetting,
  PricingAttribute,
  PricingAttributeService,
  PricingAttributeServiceContext,
  Props,
  QuantityTierAttribute,
  QuantityTierAttributeService,
  QuantityTierAttributeServiceContext,
  RevenueRuleSettingsService,
  RevenueRulesSettings,
  RevenueRulesSettingsContext,
  RoleSettingContext,
  RoleSettingService,
  ScheduleSyncRequest,
  SecuritySettings,
  SecuritySettingsContext,
  SecuritySettingsService,
  SettingsLoaderContext,
  Subcategory,
  SubscriptionDateFields,
  SyncNowRequest,
  UsageSettings,
  ValueLabel,
  ValueSet,
} from '../../components/ruby-settings';
import { TenantPropsContext } from '../../components/ruby-settings/setting-editors/organization-settings-editor';
import {
  ConnectionRequest,
  CreateDigitalSignatureRequest,
  UpdateIntegrationRequest,
  UpdateRubyUserRequest,
} from '../../components/salesforce-connection-context/interface';
import SalesforceConnectionContext from '../../components/salesforce-connection-context/salesforce-connection-context';
import StripeConnectionContext, {
  StripeConnectionRequest,
} from '../../components/stripe-connection/stripe-connection-context';
import {
  Tenant,
  Props as TenantDetailProps,
  UserMFAConfig,
} from '../../components/tenant-detail/interface';
import { Props as TenantEditorProps } from '../../components/tenant-editor/interface';
import { Role } from '../../components/user-creator';
import WebhookConnectionContext from '../../components/webhook-connection/webhook-connection-context';
import { later } from '../utils/doc-utils';
import {
  activatedObjects,
  billingTimingPickListValues,
  mappingObjects,
  priceModelPickListValues,
  productCategoryPickListValues,
  revenueModelPickListValues,
  revenueRulesPickListValues,
  taxCodePickListValues,
  uomPickListValues,
} from './mappingObjects';
import { dataFlowJobMetadata } from './metadata';
import { adminUser } from './mock/adminUser';
import { objects, PricebookEntryMetadata } from './mock/objectMetadata';

export default {
  title: 'Components/RubySettings',
  component: RubySettings,
} as Meta;

let count = 0;

const Template: Story<Props> = (args) => {
  const salesforceConnectionService = {
    getUsers: async () => {
      return [];
    },
    updateRubyWithSalesforceUserName: async (userId: string, request: UpdateRubyUserRequest) => {},
    user: {
      name: 'Stephen Chan',
      id: '0001',
      email: '<EMAIL>',
      currencyIsoCode: 'USD',
    },
    createDigitalSignature: async (
      createDigitalSignatureRequest: CreateDigitalSignatureRequest,
    ) => {
      return '';
    },
    testIntegration: async (connection: ConnectionRequest) => {},
    activateIntegration: async (connection: ConnectionRequest) => {},
    disableIntegration: async (connection: ConnectionRequest) => {},
    getIntegration: async (appCategory: 'RevenueBuilder', integrationType: 'Salesforce') => {
      return {
        url: 'https://test.salesforce.com',
        clientId:
          '3MVG98dostKihXN602W7HGEc1wPp.Cd9O6H0OBj4kv8SaQLa67D.TfNddEmj3anZ4lpMrltTQ_bkqNYeYgQVk',
        privateKey:
          '-----BEGIN PRIVATE KEY-----MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDRmpvZzMAudBBMDbw79xXATdD0iZPS9czNNYOynHY796Z+5aH0xAzbU9ORy+rKHmdxEy1yI9dlyeOJhH2AFnNLbo4gPZtrvOykjjp52qpxvhefeX/5s3iNug1+1zTRzNcP5dy/BBlvkeNmQMHFVp7eOb2mYkfDlYCYCX1RKaDBxecyQDB/640wbJ0ByrXGtb1NyPYkl2wCCM/cpLNafVEy+NXR4jnzHMUdojYpxsNta6eWCcx8G3v1Q0FiUy8g0/eSz7gk/7x10sbc1c5PfMbz5Yc0fV4nQxVSKYaREeYla+Mx06BqTVZWcod/fZSDJPIM8lg5Mw2GlIGK2JHD5CCjAgMBAAECggEAcPJCNzwY9y89tO3V1+5OP0Dvb0ZPbOddA9tpay7am3fYtRqiihW2490o7bCXhXCaxxjRbWcY49XFkc3eHs56NFSPq5nnDUhRsF6f7hfBrucDLcE4D+MA/EYcjZQD92XNIdj4ecoIttFDW09b2y3RD+jHZEgjaaY/FC5Q5BJteNxkejVJnAd3knqOuZCY2F/NZGXgzCtrPTfqaK0A5E6XvrNF88l+cB2hPSB8TiX5aYYjkcJfN+oFatiCnZ581GD85Ne7hQNLFuh7/I15dd+9EFQUnw+6miOcKfQb7gakCLMSvEaVeOIBo9IjEM2hll39Ec0kUSa6Jnv4X1lBXQbRYQKBgQD4KiCmdPQQCDraCySHVrKECmZzNAofKFYt9tiH2+f0FVdX55RPdBCNLc2Hid9IZpsGmN+JLnzdPJ9wUyvufAxwXTgf806dypTYXyvqbahzC7hDilp6sv90Gj/P6fHVdWmcnAwA5nanmMGqKedeB62PBHGxIQ9J0VboO3gzRgCFEQKBgQDYOM1fZPhnuNONMG3PUrM0A3I/a/VHC+kplLp+HA0JrzLhltQQr+QsFh9LHMY0EcHL8LRUD5hVKomUOWodTF04DQ7nZiW2cqxoZdUqt5/AOprKXYTCoaYezdQVS53Ogi/Y6WjhQqxo3Iepbn1LhropcOqPddVSBQmHr3GHmVq6cwKBgQCsNV+u4x2yOmPMh0rz97+tTSJG82fKs00KXG9p57oQcPbJc1RBxoLt1Uj4sz4Umc8eRttn8LCJPdZONgkYV1CycJ3P3iFB8iBT/ccVyL7KBun5RHsBBlGqmcSKXQeKXw3ItajGvya7qo9JrAZ4A8/2nNjgV9aqHPC3+vywZ/VYYQKBgGT8eqcwUHiZMW1DDjaDqKEFbwnCEWfiXz8FcOjPjyNTyY7lVByLGiWhG3KIeOX+2SW4QXD1a69zU5iADFisvukb6DcZVUnWW0HTNfFim+E7PeqALS5+M0k11vUvQQAfe9tygIKdEktTnKT8rtZkkXQjgdoJLd3F7oKJ5Ztwe5iTAoGAVpLQGnkdftzozt49YFAaj7TWEgM4zu0UWvPxSFS5w6L/voMiihhSuc8swnxrOZ2+WfG36Gts+2wWw2WV1SdcCWL/9cCpb3iez5jXRa6s4+S76Q/8mPSfSRnahGbpMs/8lkKgAuJLzDtUTrBggLfnWPnkXyyeQItvkQK8ZrLLH0Q=-----END PRIVATE KEY-----',
        enabled: true,
      };
    },
    updateIntegration: async (updateConnectionRequest: UpdateIntegrationRequest) => {},
    createIntegration: async (createConnectionRequest: UpdateIntegrationRequest) => {
      return {
        id: 'connection-id',
      };
    },
    userProfileMetadata: {
      apiName: 'userProfile',
      name: 'User Profile',
      customizable: true,
      objectType: 'Entity',
      fields: [
        {
          apiName: 'salesforceUser',
          name: 'Salesforce User',
          type: 'text',
          creatable: true,
          updatable: true,
          xs: 6 as const,
        },
        {
          apiName: 'userName',
          name: 'User Name',
          type: 'text',
          creatable: true,
          updatable: true,
          xs: 6 as const,
        },
      ],
    },
    executeQuery: async (
      query: string,
      objectMetadata: RubyObject,
      filters: RubyFilter[],
      extraRelationFields?: string,
      orderByFields?: OrderByField[],
    ) => {
      return [
        {
          userName: '<EMAIL>',
          salesforceUser: '<EMAIL>',
        },
      ];
    },
    getOrgInfo: async () => {
      return '{"Id": "00D1F000000eE2pUAE", "Name": "Nue IO", "LastModifiedDate": "2022-02-16T17:58:26.000+0000"}';
    },
    getOpportunityStages: async () => {
      return [
        { label: 'Prospecting', value: 'Prospecting' },
        { label: 'Qualification', value: 'Qualification' },
        { label: 'Negotiation/Review', value: 'Negotiation/Review' },
        { label: 'Closed Won', value: 'Closed Won' },
        { label: 'Closed Lost', value: 'Closed Lost' },
      ];
    },
    getOpportunityRecordTypes: async () => {
      return [
        { apiName: 'Master', label: 'Master', master: true },
        { apiName: 'RecordType2', label: 'Record Type2', master: false },
        { apiName: 'RecordType3', label: 'Record Type3', master: false },
        { apiName: 'RecordType4', label: 'Record Type4', master: false },
      ];
    },
    getPendingStatus: async () => {},

    checkAccessToNue: async () => {
      return Date.now() % 2 === 1
        ? 'Failed to access Nue service from Salesforce org, please check the configured Named Credential in Salesforce org'
        : null;
    },
    generateArrByPeriod: async () => {
      return { success: true, message: '', timestamp: new Date().toJSON() };
    },
    refreshProfileLayoutSettings: async () => {
      console.log('refreshProfileLayoutSetting');
    },
  };
  const pricingAttributeService: PricingAttributeService = {
    getPricingAttributeOptions(mapping: string): Promise<Array<{ name: string; value: string }>> {
      console.debug('getPricingAttributeOptions with mapping=' + mapping);
      if (mapping === 'region') {
        return Promise.resolve([
          { name: 'Europe', value: 'Europe' },
          { name: 'Asia', value: 'Asia' },
        ]);
      } else {
        return Promise.resolve([
          { name: 'Golden', value: 'golden' },
          { name: 'Silver', value: 'silver' },
        ]);
      }
    },
    getAttributeMappingOptions(): Promise<PickListOption[]> | PickListOption[] {
      return [
        { name: 'Account.Region', value: 'region' },
        { name: 'Account.Pattern Level', value: 'level' },
        { name: 'Quote.Currency', value: 'currency' },
        { name: 'Quote.UOM.Id', value: 'UOM.Id' },
      ];
    },
    getAll(): Promise<PricingAttribute[]> | PricingAttribute[] {
      return [
        {
          apiName: 'uom',
          name: 'UOM',
          description: 'UOM Attribute',
          mapping: 'UOM.Id',
          options: ['User/Month', 'User/Year'],
        },
        {
          apiName: 'currency',
          name: 'Currency',
          description: 'Currency Attribute',
          mapping: 'Currency.Code',
          options: ['USD', 'NZD'],
        },
        {
          apiName: 'region',
          name: 'Region',
          description: 'Region Attribute',
          mapping: 'Quote.Customer.Region',
          options: [],
        },
      ];
    },
    remove(pricingAttributeName: string): void | Promise<void> {
      console.debug('remove', pricingAttributeName);
      return undefined;
    },
    async saveOrUpdate(pricingAttribute: PricingAttribute) {
      console.debug('saveOrUpdate', pricingAttribute);
      await later(1000, null);
    },
    getFieldsAsPricingAttributes: function (): Promise<FieldAsPricingAttribute[]> {
      return Promise.resolve([
        { apiName: 'Ruby__TestPicklist__c', name: 'Test Picklist', isPricingAttribute: false },
        { apiName: 'Ruby__TestPicklist2__c', name: 'Test Picklist 2', isPricingAttribute: false },
        { apiName: 'Ruby__TestPicklist3__c', name: 'Test Picklist 3', isPricingAttribute: false },
      ]);
    },
    saveFieldAsPricingAttribute: async (fields: FieldAsPricingAttribute[]) => {
      console.debug('saveFieldAsPricingAttribute:', fields);
      await later(200, null);
    },
  };
  const quantityTierAttributeService: QuantityTierAttributeService = {
    getQuantityTierAttributes: async () => {
      return later(500, [
        {
          name: 'Number of Users',
          apiName: 'NumberOfUsers',
          description: 'The actual number of users of the account',
          active: true,
          mapping: 'xxx',
        },
        {
          name: 'Number of Users 2',
          apiName: 'NumberOfUsers 2',
          description: 'The actual number of users of the account',
          active: false,
          mapping: '',
        },
      ]);
    },
    createQuantityTierAttribute: async (attribute: QuantityTierAttribute) => {
      console.debug('createQuantityTierAttribute', attribute);
    },
    updateQuantityTierAttribute: async (attribute: QuantityTierAttribute) => {
      console.debug('updateQuantityTierAttribute', attribute);
    },
    getAttributeMappingOptions: async () => {
      return Promise.resolve([
        {
          name: 'QuoteLineItem.TestNumeric',
          label: 'QuoteLineItem.TestNumeric',
          value: 'QuoteLineItem.TestNumeric',
        },
      ]);
    },
    deleteQuantityTierAttribute: async (attribute) => {
      console.debug('deleteQuantityTierAttribute', attribute);
    },
  };
  const tenant: Tenant = {
    id: '*********',
    name: 'Future Inc.',
    status: 'Active',
    type: 'Production',
    imgUrl: 'dummyUrl',
    address: '555 Twin Dolphin Dr. Redwood City, CA 94065',
    phone: '+1(755)655-2451',
    fax: '+1(650)555-6666',
    primaryContact: {
      id: '0001',
      firstName: 'Tina',
      lastName: 'Lee',
      email: '<EMAIL>',
      imgUrl: 'xxx',
    },
    secondaryContact: {
      firstName: 'Deborah',
      lastName: 'Nelson',
      email: '<EMAIL>',
      imgUrl: 'xxx',
    },
    currency: 'USD',
    language: 'zh',
    activeUsers: 15,
    timeZone: 'GMT-07:00',
    rubyEdition: 'Nue Enterprise Edition',
  };
  const tenantDetailProps: TenantDetailProps = {
    adminUser: adminUser,
    resetUserPasswordHandler: async () => {},
    userListRowActionHandler: async () => {},
    changeEffectiveRoleHandler: async () => {},
    fetchFunctionGroupsHandler: async () => {
      return [
        {
          id: '0001',
          label: 'Organization Administration',
          name: 'OrganizationAdmin',
          functionItems: [
            { id: '0001', name: 'ManageOrganization', label: 'Manage Organization' },
            { id: '0002', name: 'Manage user and roles', label: 'Manage user and roles' },
          ],
        },
        {
          id: '0002',
          label: 'Data Management',
          name: 'Data Management',
          functionItems: [
            { id: '0003', name: 'Bulk API Access', label: 'Bulk API Access' },
            { id: '0004', name: 'Manage imports and exports', label: 'Manage imports and exports' },
          ],
        },
        {
          id: '0003',
          label: 'Revenue Builder',
          name: 'RevenueBuilder',
          functionItems: [
            { id: '0005', name: 'Manage Product Catalog', label: 'Manage Product Catalog' },
            { id: '0006', name: 'Manage Price Books', label: 'Manage Price Books' },
            { id: '0007', name: 'Manage Price Groups', label: 'Manage Price Groups' },
            { id: '0008', name: 'Manage Price Tags', label: 'Manage Price Tags' },
          ],
        },
      ];
    },
    saveRoleHandler(role: any): Promise<any> {
      return Promise.resolve({ ...role, id: 'dummy' });
    },
    batchUserRoleAssignmentHandler(assignments: {
      userIds: string[];
      addRoleIds: string[];
      removeRoleIds: string[];
    }): Promise<void> {
      console.log('batchUserRoleAssignmentHandler', assignments);
      return Promise.resolve();
    },
    fetchAssignedRolesOfUser(userId: string): Promise<any[]> {
      return Promise.resolve(
        userId === '0001'
          ? [{ id: '001', name: 'OrganizationAdmin', functions: [{ id: '0001' }] }]
          : [{ id: '002', name: 'Admin', functions: [{ id: '0002' }] }],
      );
    },
    searchRolesHandler(searchText: string): Promise<Role[]> {
      return Promise.resolve([
        { id: '001', name: 'OrganizationAdmin', status: 'Active' },
        { id: '002', name: 'Admin', status: 'Inactive' },
      ]);
    },
    getUserByIdHandler: async (userId: string) => {
      return Promise.resolve({
        id: userId,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
      });
    },
    fetchAllRolesHandler: async () => {
      return [
        {
          id: '0001',
          name: 'dummy role',
        },
      ];
    },
    saveUserHandler(user: any): Promise<any> {
      console.log('save user:', user);
      return Promise.resolve({ id: 'dummy-user-id' });
    },
    fetchTenantLicenses: async (tenantId: string) => {
      return [];
    },
    fetchUserLoginHistoryHandler: async () => {
      return later(300, [
        {
          userId: '1',
          username: 'Alex Li',
          loginTime: '2020-08-29T17:45:17.000Z',
          browser: 'Chrome',
          loginUrl: 'http://localhost',
          tenantId: 'lala',
          status: 'Success',
          role: 'Admin',
        },
        {
          userId: '2',
          username: 'Wenbo Lyu',
          loginTime: '2020-08-29T17:45:17.000Z',
          browser: 'Chrome',
          loginUrl: 'http://localhost',
          tenantId: 'lala',
          status: 'Success',
          role: 'Admin',
        },
        {
          userId: '3',
          username: 'Stephen Chan',
          loginTime: '2020-08-29T17:45:17.000Z',
          browser: 'Chrome',
          loginUrl: 'http://localhost',
          tenantId: 'lala',
          status: 'Error',
        },
      ]);
    },
    queryUsersHandler: () => {
      return Promise.resolve(
        Array(22)
          .fill(0)
          .map((_, idx) => ({
            id: `000${idx}`,
            fullName: `Demo user${idx}`,
            userName: `demouser${idx}@getRuby.io`,
            primaryRole: 'System Admin',
          })),
      );
    },
    searchConfigs: {},
    fetchTenant: async () => tenant,
    handleUserMFAToggle: async (id: string, enabled: boolean) => {
      alert(`Toggling MFA for user ${id} and set to ${enabled}`);
    },
    getUserAdminMFA: async (id: string) => {
      return later(500, {
        authenticatorAssociated: false,
        mfaEnabled: false,
        mfaEnforcement: 'Required',
      } as UserMFAConfig);
    },
  };

  const tenantEditorProps: TenantEditorProps = {
    savePrimaryContactHandler: async () => {
      return {
        id: '0002',
        name: 'Stephen Chan',
      };
    },
    canEditLicenses: true,
    //@ts-ignore
    fetchContactOptions: async () => {
      return later(500, [
        { id: '0001', name: 'Alex Li' },
        { id: '0002', name: 'Stephen Chan' },
      ]);
    },
    saveTenantHandler: async (tenant: any) => {
      console.log('saveTenantHandler', tenant);
    },
    fetchTenant: async () => tenant,
    adminUser: adminUser,
  };
  const roleSettingService: RoleSettingService = {
    fetchTenantFunctionGroups: async () => {
      return [
        {
          id: '0001',
          label: 'Organization Administration',
          name: 'OrganizationAdmin',
          functionItems: [
            { id: '0001', name: 'ManageOrganization', label: 'Manage Organization' },
            { id: '0002', name: 'Manage user and roles', label: 'Manage user and roles' },
          ],
        },
        {
          id: '0002',
          label: 'Data Management',
          name: 'Data Management',
          functionItems: [
            { id: '0003', name: 'Bulk API Access', label: 'Bulk API Access' },
            { id: '0004', name: 'Manage imports and exports', label: 'Manage imports and exports' },
          ],
        },
      ];
    },
    //@ts-ignore
    fetchTenantRoles: async () => {
      return [
        { id: '001', name: 'Sales', functions: [{ id: '0001' }] },
        { id: '002', name: 'Admin', functions: [{ id: '0002' }] },
      ];
    },
    //@ts-ignore
    fetchReadOnlyRoleName: async () => {
      return 'Admin';
    },
    //@ts-ignore
    fetchReadOnlyFunctions: async () => {
      return ['Manage Organization'];
    },
    //@ts-ignore
    fetchFunctionsRelation: async () => {
      return [
        {
          origin: 'Manage Organization',
          target: 'Bulk API Access',
          relation: 'Inclusive',
        },
        {
          origin: 'Manage user and roles',
          target: 'Manage imports and exports',
          relation: 'Exclusive',
        },
      ];
    },
    //@ts-ignore
    saveRoleHandler: async (role: Role) => {
      console.log('saveRoleHandler', role);
      return {
        ...role,
        id: uuid().toLowerCase(),
      };
    },
  };

  const graphqlGeneratorService: GraphqlGeneratorService = {
    getQueryObjects: async () => {
      return queryObjects;
    },
    getQueryFields: async (objectName) => {
      if (objectName === 'Product') {
        return queryFields;
      } else {
        return queryFields1;
      }
    },
    getObjectMetadata: async (objectName) => {
      if (objectName === 'Asset') {
        return objectMetadataForAsset;
      } else {
        return objectMetadata1;
      }
    },
    updateFilters: async (
      objectMetadataApiName: string,
      referenceFilterId: string,
      filterName: string,
      conditions: Array<Condition>,
    ) => {
      console.log('API call to update filter');
      const updatedFilter: RubyFilter = {
        id: referenceFilterId,
        name: filterName,
        conditions: conditions,
      };
      return Promise.resolve(updatedFilter);
    },
    listFilters: async (objectApiName: string) => {
      return Promise.resolve([]);
    },
    deleteFilter: async (objectApiName: string, filterId: string) => {
      return Promise.resolve({});
    },
    saveFilter: async (objectMetadataApiName, newFilterName, conditions) => {
      console.log('Invoke api to save new filteyyYyyyyyyyyy');
      const newSavedFilter: RubyFilter = {
        id: newFilterName,
        name: newFilterName,
        conditions: conditions,
      };
      return Promise.resolve(newSavedFilter);
    },
  };

  const mockIntergrationSettings = [
    {
      integrationName: 'Salesforce',
      status: 'Connected',
    },
    {
      integrationName: 'Stripe',
      status: 'Connected',
    },
    {
      integrationName: 'RightRev',
      status: 'Connected',
    },
    {
      integrationName: 'Avalara',
      status: 'Connected',
    },
    {
      integrationName: 'QuickBooks',
      status: 'Unconnected',
    },
    {
      integrationName: 'WebHook',
      status: 'Connected',
    },
  ];

  const queryObjects = ['Product', 'Order', 'Customer', 'Billing Schedule', 'Asset'];
  const queryFields = ['Id', 'Status', 'Name', 'CreateBy', 'CreateDate', 'UpdateBy', 'UpdateDate'];
  const queryFields1 = [
    'Id1',
    'Status',
    'Name1',
    'CreateBy1',
    'CreateDate1',
    'UpdateBy1',
    'UpdateDate1',
  ];

  const objectMetadata = {
    apiName: 'Customer',
    name: 'Customer',
    fields: [
      {
        apiName: 'name',
        name: 'Name',
        type: 'text',
        required: true,
        updatable: true,
        filterable: true,
        sortable: true,
      },
      {
        apiName: 'customerSince',
        name: 'Customer Since',
        type: 'date',
        required: true,
        updatable: true,
        filterable: true,
        sortable: true,
      },
      {
        apiName: 'customerSince',
        name: 'Customer Since',
        type: 'date',
        required: true,
        updatable: true,
        filterable: true,
        sortable: true,
      },
      {
        apiName: 'status',
        name: 'Status',
        type: 'pickList',
        valueSet: {
          alphabeticallyOrdered: true,
          apiName: 'ProductStatus',
          defaultValue: 'Draft',
          name: 'Product Status',
          valuePairs: [
            { name: 'Active', apiName: 'Active' },
            { name: 'Draft', apiName: 'Draft' },
            { name: 'Inactive', apiName: 'Inactive' },
          ],
        },
        filterable: true,
      },
      {
        apiName: 'configurable',
        name: 'Configurable',
        type: 'boolean',
        required: true,
        updatable: true,
        filterable: true,
      },
    ],
  };

  const objectMetadataForAsset = {
    apiName: 'Customer',
    name: 'Customer',
    fields: [
      {
        apiName: 'nameAsset',
        name: 'NameAsset',
        type: 'text',
        required: true,
        updatable: true,
        filterable: true,
        sortable: true,
      },
      {
        apiName: 'customerSinceAsset',
        name: 'Customer SinceAsset',
        type: 'date',
        required: true,
        updatable: true,
        filterable: true,
        sortable: true,
      },
      {
        apiName: 'customerSinceAsset',
        name: 'Customer SinceAsset',
        type: 'date',
        required: true,
        updatable: true,
        filterable: true,
        sortable: true,
      },
      {
        apiName: 'statusAsset',
        name: 'StatusAsset',
        type: 'pickList',
        valueSet: {
          alphabeticallyOrdered: true,
          apiName: 'ProductStatus',
          defaultValue: 'Draft',
          name: 'Product Status',
          valuePairs: [
            { name: 'Active', apiName: 'Active' },
            { name: 'Draft', apiName: 'Draft' },
            { name: 'Inactive', apiName: 'Inactive' },
          ],
        },
        filterable: true,
      },
      {
        apiName: 'configurableAsset',
        name: 'ConfigurableAsset',
        type: 'boolean',
        required: true,
        updatable: true,
        filterable: true,
      },
    ],
  };

  const objectMetadata1 = {
    apiName: 'Customer',
    name: 'Customer',
    fields: [
      {
        apiName: 'name1',
        name: 'Name1',
        type: 'text',
        required: true,
        updatable: true,
        filterable: true,
        sortable: true,
      },
      {
        apiName: 'customerSince1',
        name: 'Customer Since1',
        type: 'date',
        required: true,
        updatable: true,
        filterable: true,
        sortable: true,
      },
      {
        apiName: 'customerSince1',
        name: 'Customer Since1',
        type: 'date',
        required: true,
        updatable: true,
        filterable: true,
        sortable: true,
      },
      {
        apiName: 'status1',
        name: 'Status1',
        type: 'pickList',
        valueSet: {
          alphabeticallyOrdered: true,
          apiName: 'ProductStatus',
          defaultValue: 'Draft',
          name: 'Product Status',
          valuePairs: [
            { name: 'Active', apiName: 'Active' },
            { name: 'Draft', apiName: 'Draft' },
            { name: 'Inactive', apiName: 'Inactive' },
          ],
        },
        filterable: true,
      },
      {
        apiName: 'configurable1',
        name: 'Configurable1',
        type: 'boolean',
        required: true,
        updatable: true,
        filterable: true,
      },
    ],
  };

  const tenantBillingSettings: BillingSettings = {
    billCycleDay: '5th of Month',
    billingStart: '02',
    billingPeriod: 'Quarter',
    billingTiming: 'In Advance',
    revenueModelMapping: new Map([
      ['OneTime', 'In Advance'],
      ['Recurring', 'In Advance'],
      ['CRBD', 'In Advance'],
      ['Usage', 'In Arrears'],
    ]),
    paymentTerm: 'Net 30',
    prorationEnabled: false,
    hideZeroItems: false,
    creditMemoMode: 'Disabled',
    groupingAttributes: [
      {
        conditions: [],
        description: 'Generate invoices for each account',
        id: 'Customer',
        name: 'Customer',
        status: 'Active',
        isStandard: true,
        groupingField: '',
      },
      {
        conditions: [],
        description: 'Generate invoices for different currencies independently',
        id: 'Currency',
        name: 'Currency',
        status: 'Active',
        isStandard: true,
        groupingField: '',
      },
    ],
  };

  const billSysSetting: BillSysSetting = {
    key: 'EnableInvoicePendingActivation',
    value: 'true',
  };

  const dontCollectZeroInvoices: BillSysSetting = {
    key: 'dont_collect_zero_invoices',
    value: 'false',
  };

  const tenantRevenueRulesSettings: RevenueRulesSettings = {
    revenueRule: 'Recurring Services',
    revenueRuleMapping: new Map([
      [
        'OneTime',
        {
          name: 'One Time Products',
          externalId: 'One Time Products',
          integrationId: 'xxx',
          sequenceNo: 2000000,
        },
      ],
      [
        'Recurring',
        {
          name: 'Recurring Services',
          externalId: 'Recurring Services',
          integrationId: 'xxx',
          sequenceNo: 4000000,
        },
      ],
      [
        'CRBD',
        {
          name: 'Credit Burndown',
          externalId: 'Credit Burndown',
          integrationId: 'xxx',
          sequenceNo: 3000000,
        },
      ],
      [
        'Usage',
        {
          name: 'Usage',
          externalId: 'Usage',
          integrationId: 'xxx',
          sequenceNo: 1000000,
        },
      ],
    ]),
  };

  const tenantUsageSettings: UsageSettings = {
    ratingUserId: null,
    windowSize: null,
    syncUsageEnabled: false,
    termAligned: 'StartDate',
  };

  const usageRatingUsers: ValueLabel[] = [
    { value: 'user-001', label: 'Bolt Zhang' },
    { value: 'user-002', label: 'Kaiyu Ma' },
    { value: 'user-003', label: 'Chunyu Jia' },
    { value: 'user-004', label: 'Jianqiang Ma' },
  ];

  const autosetBcdBySubscription: Record<string, any> = {
    field: null,
  };

  const autosetBcdMode: AutoSetBcdMode = {
    mode: null,
    setting: null,
  };

  const tenantOrderSettingMap: Map<String, OrderSetting> = new Map();
  tenantOrderSettingMap.set(KEY_BILL_ON_ORDER_ACTIVATION, {
    key: KEY_BILL_ON_ORDER_ACTIVATION,
    value: 'false',
  });
  tenantOrderSettingMap.set(KEY_ACTIVATE_INVOICES_ON_ORDER_ACTIVATION, {
    key: KEY_ACTIVATE_INVOICES_ON_ORDER_ACTIVATION,
    value: 'false',
  });

  const creditTypes: CreditTypes = {
    creditTypeId: '00241df0-30e5-20cb-3b1a-db4c1d481535',
    creditType: 'Credit',
    creditTypeName: 'default credit pool label',
    status: 'Active',
  };

  const creditPools: CreditPools = {
    creditPoolId: 'b47af615-f767-8468-5748-c0e7e39c5f5d',
    creditPoolName: 'Default Credit Pool',
    creditTypeId: '00241df0-30e5-20cb-3b1a-db4c1d481535',
    isDefault: false,
  };

  const creditSettings: CreditSettings = {
    rollover: true,
    rolloverPeriod: 3,
    rolloverUnit: 'Month',
  };

  const billingSettingsSfCustomFields: BillingSettingsSfCustomFields = {
    autoAlignBillingPeriodEnabled: false,
    billingPeriodAlign: 'Order',
    autoAlignPaymentTermEnabled: false,
    paymentMethod: 'ACH',
    autoAlignPaymentMethodEnabled: false,
  };

  const billSysSettings: BillSysSetting = {
    key: '',
    value: 'Order',
  };

  const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

  const billingSettingsContext: BillingSettingsService = {
    syncCustomField: async () => {
      return true;
    },
    getTenantBillingSettings: async () => {
      await sleep(500);
      return tenantBillingSettings;
    },
    saveTenantBillingSettings: async (settings: BillingSettings) => {
      await sleep(500);
      tenantBillingSettings.billCycleDay = settings.billCycleDay;
      tenantBillingSettings.billingPeriod = settings.billingPeriod;
      tenantBillingSettings.billingStart = settings.billingStart;
      tenantBillingSettings.billingTiming = settings.billingTiming;
      tenantBillingSettings.paymentTerm = settings.paymentTerm;
      tenantBillingSettings.prorationEnabled = settings.prorationEnabled;
      tenantBillingSettings.hideZeroItems = settings.hideZeroItems;
      tenantBillingSettings.creditMemoMode = settings.creditMemoMode;
      tenantBillingSettings.groupingAttributes = settings.groupingAttributes;
      return '200';
    },
    getOrderSettings: async (keys: String[]) => {
      await sleep(500);
      let settings: OrderSetting[] = [];
      for (let i = 0; i < keys.length; ++i) {
        let key = keys[i];
        let setting: OrderSetting | undefined = tenantOrderSettingMap.get(key);
        if (setting) {
          settings.push(setting);
        }
      }
      return settings;
    },
    saveOrderSettings: async (settings: OrderSetting[]) => {
      await sleep(500);
      settings.forEach((setting) => {
        tenantOrderSettingMap.set(setting.key, setting);
      });
    },
    getSubscriptionDateFields: async () => {
      await sleep(500);
      let rs: SubscriptionDateFields = {
        options: [
          { key: 'Ruby__SubscriptionStartDate__c', value: 'Subscription Start Date' },
          { key: 'Ruby__SubscriptionEndDate__c', value: 'Subscription End Date' },
          { key: 'Ruby__OrderOnDate__c', value: 'Ordered On' },
        ],
      };
      return rs;
    },
    setAutoSetBcdMode: async (mode: string | null, setting: string) => {
      await sleep(500);
      autosetBcdMode.mode = mode;
      autosetBcdMode.setting = setting;
    },
    getAutoSetBcdMode: async () => {
      await sleep(500);
      return autosetBcdMode;
    },
    getInvoiceTemplateList: async () => {
      return [];
    },
    cloneInvoiceTemplate: async () => {
      return true;
    },
    deleteInvoiceTemplate: async () => {
      return true;
    },
    getInvoiceList: async () => {
      return [];
    },
    getCreditMemoList: async () => {
      return [];
    },
    getOrderList: async () => {
      return [];
    },
    getQuoteList: async () => {
      return [];
    },
    getInvoiceById: async () => {
      return true;
    },
    fetchMetadataForQuoteLineItemsAndLinePriceDimension: async () => {
      return true;
    },
    saveInvoiceTemplateImage: async () => {
      return true;
    },
    setRefreshPdfUrl: async () => {
      return true;
    },
    sendInvoiceEmail: async () => {
      return true;
    },
    getDownloadPdf: async () => {
      return true;
    },
    publishInvoiceTemplate: async () => {
      return true;
    },
    getInvoiceTemplateById: async () => {
      return true;
    },
    getViewInvoiceToken: async () => {
      return true;
    },
    updateInvoiceTemplate: async () => {
      return true;
    },
    checkInvoiceTokenByInvoiceId: async () => {
      return true;
    },
    metadataObjects: [
      {
        apiName: 'FormTemplate',
        name: 'FormTemplate',
        customizable: true,
        feature: 'InvoiceManagement',
        objectType: 'Entity',
        fields: [],
        relations: [],
      },
    ],
    markDefaultInvoiceTemplate: async () => {
      return true;
    },
    isUsageMgmtGranted: () => {
      return true;
    },
    isCrbdMgmtGranted: async () => {
      return true;
    },
    getUsageSettings: async () => {
      return tenantUsageSettings;
    },
    getRatingUserOptions: async () => {
      return usageRatingUsers;
    },
    unPublishInvoiceTemplate: async () => {
      return true;
    },
    saveProfileImg: async () => {
      return true;
    },
    saveUsageSettings: async (settings: UsageSettings) => {
      tenantUsageSettings.ratingUserId = settings.ratingUserId;
      tenantUsageSettings.windowSize = settings.windowSize;
      tenantUsageSettings.syncUsageEnabled = settings.syncUsageEnabled;
    },
    getCreditTypes: async () => {
      const cts: CreditTypes[] = [];
      cts.push(creditTypes);
      return cts;
    },
    saveCreditTypes: async (setting: CreditTypes) => {
      await sleep(2000);
      creditTypes.creditTypeId = setting.creditTypeId;
      creditTypes.creditType = setting.creditType;
      creditTypes.creditTypeName = setting.creditTypeName;
      creditTypes.status = setting.status;
    },
    saveCreditPools: async (setting: CreditPools) => {
      await sleep(2000);
      creditPools.creditPoolId = setting.creditPoolId;
      creditPools.creditPoolName = setting.creditPoolName;
      creditPools.creditTypeId = setting.creditTypeId;
      creditPools.isDefault = false;
    },
    deleteCreditPools: async (setting: CreditPools) => {
      await sleep(2000);
      creditPools.creditPoolId = setting.creditPoolId;
      creditPools.creditPoolName = setting.creditPoolName;
      creditPools.creditTypeId = setting.creditTypeId;
      creditPools.isDefault = false;
    },
    getCreditPools: async () => {
      return [];
    },
    // saveCreditPools: async (settings: CreditPools) => {
    //   creditPools.creditPoolId = settings.creditPoolId;
    //   creditPools.creditPoolName = settings.creditPoolName;
    //   creditPools.creditTypeId = settings.creditTypeId;
    // },
    getTenantCreditSettings: async () => {
      return creditSettings;
    },
    saveTenantCreditSettings: async (settings: CreditSettings) => {
      await sleep(2000);
      creditSettings.rollover = settings.rollover;
      creditSettings.rolloverPeriod = settings.rolloverPeriod;
      creditSettings.rolloverUnit = settings.rolloverUnit;
    },
    setBillingSettingCustomField: async (fieldName: string, settings: BillingSettings) => {
      await sleep(500);
      tenantBillingSettings.billingPeriod = settings.billingPeriod;
      tenantBillingSettings.billingStart = settings.billingStart;
    },
    getBillingSettingSfCustomFields: async () => {
      return billingSettingsSfCustomFields;
    },
    getBillSystemSettings: async () => {
      const settings: BillSysSetting[] = [billSysSettings, dontCollectZeroInvoices];
      return settings;
    },
    saveBillSystemSettings: async (settings: BillSysSetting[]) => {
      await sleep(500);
      settings.forEach((setting) => {
        if (setting.key === dontCollectZeroInvoices.key) {
          dontCollectZeroInvoices.value = setting.value;
        }
        if (billSysSetting.key === setting.key) {
          billSysSetting.value = setting.value;
        }
      });
    },
    setBillingSettingSfCustomField: async (
      fieldName: string,
      settings: BillingSettingsSfCustomFields,
    ) => {
      await sleep(500);
      settings.autoAlignBillingPeriodEnabled = false;
    },
    customerMetadata: {
      apiName: 'Customer',
      customizable: false,
      objectType: '',
      name: 'Customer',
      fields: [
        {
          apiName: 'accountNumber',
          name: 'Account Number',
          type: 'date',
          required: true,
          updatable: false,
          filterable: true,
        },
        {
          apiName: 'name',
          name: 'Name',
          type: 'text',
          required: true,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'recordType',
          name: 'Record Type',
          type: 'picklist',
          required: true,
          updatable: true,
          filterable: true,
          valueSet: {
            valuePairs: [
              {
                apiName: 'Business',
                name: 'Businesses',
                orderNumber: 1,
              },
              {
                apiName: 'Consumer',
                name: 'Consumers',
                orderNumber: 2,
              },
            ],
          },
        },
        {
          apiName: 'customerSince',
          name: 'Customer Since',
          type: 'date',
          required: false,
          updatable: false,
          filterable: true,
        },
        {
          apiName: 'latestOrderDate',
          name: 'Latest Order Date',
          type: 'date',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'lastInvoiceDate',
          name: 'Last Invoice Date',
          type: 'date',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'todayARR',
          name: `Today's ARR`,
          type: 'currency',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'todayCMRR',
          name: `Today's CMRR`,
          type: 'currency',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'totalTCV',
          name: `Total TCV`,
          type: 'currency',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'invoiceBalance',
          name: 'Invoice Balance',
          type: 'currency',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'parentId',
          name: `Parent Customer`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingAddress',
          name: `Billing Address`,
          type: 'address',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingAddress',
          name: `Billing Address`,
          type: 'address',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingCountry',
          name: `Billing Country`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingCountryCode',
          name: `Billing Country Code`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingGeocodeAccuracy',
          name: `Billing Geocode Accuracy`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingLatitude',
          name: `Billing Latitude`,
          type: 'decimal',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingLongitude',
          name: `Billing Longitude`,
          type: 'decimal',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingPostalCode',
          name: `Billing Postal Code`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingState',
          name: `Billing State`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingStateCode',
          name: `Billing State Code`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingStreet',
          name: `Billing Street`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'description',
          name: `Description`,
          type: 'longText',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'phone',
          name: `Phone`,
          type: 'phone',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'fax',
          name: `Fax`,
          type: 'phone',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'industry',
          name: `Industry`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'photoUrl',
          name: `Photo URL`,
          type: 'url',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'firstName',
          name: `First Name`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'lastName',
          name: `Last Name`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'middleName',
          name: `Middle Name`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'suffix',
          name: `Suffix`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'personEmail',
          name: `Email`,
          type: 'email',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'personBirthDate',
          name: `Birth Date`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'personMobilePhone',
          name: `Mobile Phone`,
          type: 'phone',
          required: false,
          updatable: true,
          filterable: true,
        },
      ],
    },
    orderMetadata: {
      apiName: 'Order',
      name: 'Order',
      customizable: true,
      feature: 'ProductAndBundles',
      objectType: 'Entity',
      fields: [
        {
          apiName: 'accountNumber',
          name: 'Account Number',
          type: 'text',
          creatable: true,
          updatable: true,
          filterable: false,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          minLength: 0,
          maxLength: 40,
        },
        {
          apiName: 'activatedById',
          name: 'Activated By',
          type: 'lookup',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'UserProfile',
            referenceField: 'id' as const,
            relationName: 'orderActivatedBy',
            relationLabel: 'Order Activated By',
          },
        },
        {
          apiName: 'activatedDate',
          name: 'Activated Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'billToContactId',
          name: 'Bill To Contact',
          type: 'bLookup',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'Contact',
            referenceField: 'id' as const,
            relationName: 'orderContact',
            relationLabel: 'Contacts',
          },
        },
        {
          apiName: 'billingAddress',
          name: 'Billing Address',
          type: 'text',
          creatable: true,
          updatable: true,
          filterable: false,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'companyAuthorizedById',
          name: 'Company Authorized By',
          type: 'bLookup',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'UserProfile',
            referenceField: 'id' as const,
            relationName: 'orderCompanyAuthorizedBy',
            relationLabel: 'Order Company Authorized By',
          },
        },
        {
          apiName: 'companyAuthorizedDate',
          name: 'Company Authorized Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'contractEndDate',
          name: 'Contract End Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'contractName',
          name: 'Contract Name',
          type: 'text',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          minLength: 0,
          maxLength: 80,
        },
        {
          apiName: 'createdById',
          name: 'Created By',
          type: 'bLookup',
          creatable: false,
          updatable: false,
          filterable: true,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'UserProfile',
            referenceField: 'id' as const,
            relationName: 'createdBy',
            relationLabel: 'Created By',
          },
        },
        {
          apiName: 'createdDate',
          name: 'Created Date',
          type: 'dateTime',
          creatable: false,
          updatable: false,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'customerAuthorizedById',
          name: 'Customer Authorized By',
          type: 'bLookup',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'Contact',
            referenceField: 'id' as const,
            relationName: 'orderCustomerAuthorizedBy',
            relationLabel: 'Order Customer Authorized By',
          },
        },
        {
          apiName: 'customerAuthorizedDate',
          name: 'Customer Authorized Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'customerId',
          name: 'Customer Id',
          type: 'bLookup',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: true,
          unique: false,
          lookupRelation: {
            referenceTo: 'Customer',
            referenceField: 'id' as const,
            relationName: 'customer',
            relationLabel: 'Customers',
          },
        },
        {
          apiName: 'deltaARR',
          name: 'Delta ARR',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'deltaCMRR',
          name: 'Delta CMRR',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'description',
          name: 'Description',
          type: 'longText',
          creatable: true,
          updatable: true,
          filterable: false,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'discount',
          name: 'Discount',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'discountAmount',
          name: 'Discount Amount',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'endDate',
          name: 'End Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'grandTotal',
          name: 'Grand Total',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'id',
          name: 'Id',
          type: 'bId',
          creatable: false,
          updatable: false,
          filterable: true,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'isReductionOrder',
          name: 'Reduction Order',
          type: 'boolean',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'isShippingAddressSameAsBilling',
          name: 'Same as Billing Address',
          type: 'boolean',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'lastModifiedById',
          name: 'Last Modified By',
          type: 'bLookup',
          creatable: false,
          updatable: false,
          filterable: true,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'UserProfile',
            referenceField: 'id' as const,
            relationName: 'lastModifiedBy',
            relationLabel: 'Last Modified By',
          },
        },
        {
          apiName: 'lastModifiedDate',
          name: 'Last Modified Date',
          type: 'dateTime',
          creatable: false,
          updatable: false,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'listTotal',
          name: 'List Total',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'name',
          name: 'Order Name',
          type: 'text',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: true,
          unique: false,
          minLength: 0,
          maxLength: 80,
        },
        {
          apiName: 'nextInvoiceDate',
          name: 'Next Invoice Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'orderACV',
          name: 'Order ACV',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'orderNumber',
          name: 'Order Number',
          type: 'autoNumber',
          creatable: false,
          updatable: false,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'orderPlacedDate',
          name: 'Order Placed Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'orderReferenceNumber',
          name: 'Order Reference Number',
          type: 'text',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          minLength: 0,
          maxLength: 80,
        },
        {
          apiName: 'orderStartDate',
          name: 'Order Start Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'orderTCV',
          name: 'Order TCV',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'originalOrderId',
          name: 'Original Order',
          type: 'bLookup',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'Order',
            referenceField: 'id' as const,
            relationName: 'originalOrder',
            relationLabel: 'Original Order',
          },
        },
        {
          apiName: 'ownerId',
          name: 'Order Owner',
          type: 'bLookup',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'UserProfile',
            referenceField: 'id' as const,
            relationName: 'owner',
            relationLabel: 'Order Owner',
          },
        },
        {
          apiName: 'poDate',
          name: 'PO Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'poNumber',
          name: 'PO Number',
          type: 'text',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          minLength: 0,
          maxLength: 80,
        },
        {
          apiName: 'priceBookId',
          name: 'Price Book',
          type: 'bLookup',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'PriceBook',
            referenceField: 'id' as const,
            relationName: 'orderPriceBook',
            relationLabel: 'Order Price Book',
          },
        },
        {
          apiName: 'shipToContactId',
          name: 'Ship To Contact',
          type: 'bLookup',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
          lookupRelation: {
            referenceTo: 'Contact',
            referenceField: 'id' as const,
            relationName: 'shipToContact',
            relationLabel: 'Ship To Contact',
          },
        },
        {
          apiName: 'shippingAddress',
          name: 'Shipping Address',
          type: 'text',
          creatable: true,
          updatable: true,
          filterable: false,
          sortable: false,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'shippingAndHandling',
          name: 'Shipping And Handling',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'soldToAddress',
          name: 'Sold-to-Address',
          type: 'longText',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'status',
          name: 'Status',
          type: 'pickList',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          defaultValue: 'Draft',
          required: false,
          unique: false,
          valueSet: {
            apiName: 'OrderStatus',
            name: 'Order Status',
            defaultValue: 'Draft',
            alphabeticallyOrdered: false,
            valuePairs: [
              {
                apiName: 'Draft',
                name: 'Draft',
                orderNumber: 1,
              },
              {
                apiName: 'Activated',
                name: 'Activated',
                orderNumber: 2,
              },
              {
                apiName: 'Cancelled',
                name: 'Cancelled',
                orderNumber: 3,
              },
            ],
          },
        },
        {
          apiName: 'subscriptionEndDate',
          name: 'Subscription End Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'subscriptionStartDate',
          name: 'Subscription Start Date',
          type: 'date',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'subscriptionTerm',
          name: 'Subscription Term',
          type: 'decimal',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'subtotal',
          name: 'Subtotal',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'systemDiscount',
          name: 'System Discount',
          type: 'percent',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'systemDiscountAmount',
          name: 'System Discount Amount',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'tax',
          name: 'Tax',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'totalAmount',
          name: 'Total Amount',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
        {
          apiName: 'totalPrice',
          name: 'Total Amount',
          type: 'currency',
          creatable: true,
          updatable: true,
          filterable: true,
          sortable: true,
          customField: false,
          encrypted: false,
          required: false,
          unique: false,
        },
      ],
      relations: [
        {
          referenceObjectName: 'UserProfile',
          referenceFieldName: 'id',
          relationFieldName: 'createdById',
          relationName: 'createdBy',
          relationLabel: 'Created By',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'Customer',
          referenceFieldName: 'id',
          relationFieldName: 'customerId',
          relationName: 'customer',
          relationLabel: 'Customers',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'UserProfile',
          referenceFieldName: 'id',
          relationFieldName: 'lastModifiedById',
          relationName: 'lastModifiedBy',
          relationLabel: 'Last Modified By',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'UserProfile',
          referenceFieldName: 'id',
          relationFieldName: 'activatedById',
          relationName: 'orderActivatedBy',
          relationLabel: 'Order Activated By',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'UserProfile',
          referenceFieldName: 'id',
          relationFieldName: 'companyAuthorizedById',
          relationName: 'orderCompanyAuthorizedBy',
          relationLabel: 'Order Company Authorized By',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'Contact',
          referenceFieldName: 'id',
          relationFieldName: 'billToContactId',
          relationName: 'orderContact',
          relationLabel: 'Contacts',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'Contact',
          referenceFieldName: 'id',
          relationFieldName: 'customerAuthorizedById',
          relationName: 'orderCustomerAuthorizedBy',
          relationLabel: 'Order Customer Authorized By',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'UserProfile',
          referenceFieldName: 'id',
          relationFieldName: 'ownerId',
          relationName: 'orderOwner',
          relationLabel: 'Order Owner',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'PriceBook',
          referenceFieldName: 'id',
          relationFieldName: 'priceBookId',
          relationName: 'orderPriceBook',
          relationLabel: 'Order Price Book',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'OrderProduct',
          referenceFieldName: 'orderId',
          relationFieldName: 'id',
          relationName: 'orderProductOrders',
          relationLabel: 'orderProductOrders',
          relationType: 'LookUpChild',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'Order',
          referenceFieldName: 'id',
          relationFieldName: 'originalOrderId',
          relationName: 'originalOrder',
          relationLabel: 'Original Order',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'Order',
          referenceFieldName: 'originalOrderId',
          relationFieldName: 'id',
          relationName: 'originalOrders',
          relationLabel: 'originalOrders',
          relationType: 'LookUpChild',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
        {
          referenceObjectName: 'Contact',
          referenceFieldName: 'id',
          relationFieldName: 'shipToContactId',
          relationName: 'shipToContact',
          relationLabel: 'Ship To Contact',
          relationType: 'LookUp',
          detailAccessPolicy: {
            viewable: true,
            creatable: true,
            updatable: true,
          },
        },
      ],
    },
    productMetadata: {
      apiName: 'Product',
      customizable: false,
      objectType: '',
      name: 'Product',
      fields: [
        {
          apiName: 'accountNumber',
          name: 'Account Number',
          type: 'date',
          required: true,
          updatable: false,
          filterable: true,
        },
        {
          apiName: 'name',
          name: 'Name',
          type: 'text',
          required: true,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'recordType',
          name: 'Record Type',
          type: 'picklist',
          required: true,
          updatable: true,
          filterable: true,
          valueSet: {
            valuePairs: [
              {
                apiName: 'Business',
                name: 'Businesses',
                orderNumber: 1,
              },
              {
                apiName: 'Consumer',
                name: 'Consumers',
                orderNumber: 2,
              },
            ],
          },
        },
        {
          apiName: 'customerSince',
          name: 'Customer Since',
          type: 'date',
          required: false,
          updatable: false,
          filterable: true,
        },
        {
          apiName: 'latestOrderDate',
          name: 'Latest Order Date',
          type: 'date',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'lastInvoiceDate',
          name: 'Last Invoice Date',
          type: 'date',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'todayARR',
          name: `Today's ARR`,
          type: 'currency',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'todayCMRR',
          name: `Today's CMRR`,
          type: 'currency',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'totalTCV',
          name: `Total TCV`,
          type: 'currency',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'invoiceBalance',
          name: 'Invoice Balance',
          type: 'currency',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'parentId',
          name: `Parent Customer`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingAddress',
          name: `Billing Address`,
          type: 'address',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingAddress',
          name: `Billing Address`,
          type: 'address',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingCountry',
          name: `Billing Country`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingCountryCode',
          name: `Billing Country Code`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingGeocodeAccuracy',
          name: `Billing Geocode Accuracy`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingLatitude',
          name: `Billing Latitude`,
          type: 'decimal',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingLongitude',
          name: `Billing Longitude`,
          type: 'decimal',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingPostalCode',
          name: `Billing Postal Code`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingState',
          name: `Billing State`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingStateCode',
          name: `Billing State Code`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'billingStreet',
          name: `Billing Street`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'description',
          name: `Description`,
          type: 'longText',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'phone',
          name: `Phone`,
          type: 'phone',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'fax',
          name: `Fax`,
          type: 'phone',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'industry',
          name: `Industry`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'photoUrl',
          name: `Photo URL`,
          type: 'url',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'firstName',
          name: `First Name`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'lastName',
          name: `Last Name`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'middleName',
          name: `Middle Name`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'suffix',
          name: `Suffix`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'personEmail',
          name: `Email`,
          type: 'email',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'personBirthDate',
          name: `Birth Date`,
          type: 'text',
          required: false,
          updatable: true,
          filterable: true,
        },
        {
          apiName: 'personMobilePhone',
          name: `Mobile Phone`,
          type: 'phone',
          required: false,
          updatable: true,
          filterable: true,
        },
      ],
    },
    getValueSets: async (names: string[]) => {
      await sleep(500);
      let valueSets: ValueSet[] = [];
      valueSets[0].apiName = 'PaymentMethod';
      valueSets[0].name = 'Payment Method';
      valueSets[0].values = [
        {
          apiName: 'ACH',
          name: 'ACH',
          orderNumber: 1,
          active: true,
        },
      ];
      return valueSets;
    },
    deleteGroupingAttributes: async (attributeIds: string[]) => {
      await sleep(500);
      return '200';
    },
    getCustomFieldsOfAsset: async () => {
      await sleep(500);
      return {};
    },
  };

  const revenueRulesSettingsContext: RevenueRuleSettingsService = {
    getTenantRevenueRuleSettings: async () => {
      await sleep(500);
      return tenantRevenueRulesSettings;
    },
    //@ts-ignore
    saveTenantRevenueRuleSettings: async (settings: RevenueRulesSettings) => {
      await sleep(500);
      tenantRevenueRulesSettings.revenueRule = settings.revenueRule;
      tenantRevenueRulesSettings.revenueRuleMapping = settings.revenueRuleMapping;
      return '200';
    },
  };

  const dataFlowManagerService: DataFlowManagerService = {
    getConnectionDetails: async () => {
      return {
        id: '123',
        groupId: 'groupId',
        dailySyncTime: '00:00',
        syncFrequency: 1440,
        status: {
          setupState: 'connected',
          syncState: 'scheduled',
          updateState: 'on_schedule',
        },
        sourceSyncDetails: {
          connectingUserEmail: '<EMAIL>',
          organizationId: '123',
          username: '<EMAIL>',
        },
        succeededAt: '2022-06-24T00:18:31.622965Z',
      };
    },
    //@ts-ignore
    scheduleSync: async (scheduleSyncRequests: Array<ScheduleSyncRequest>) => {
      console.log('scheduleSyncRequests: ', scheduleSyncRequests);
    },
    //@ts-ignore
    syncNow: async (syncNowRequests: Array<SyncNowRequest>) => {
      console.log('syncNowRequests: ', syncNowRequests);
    },
    //@ts-ignore
    fullSync: async (fullSyncRequests: Array<SyncNowRequest>) => {
      console.log('fullSyncRequests: ', fullSyncRequests);
    },
    dataFlowJobMetadata,
    user: {
      id: 'aa0002d7-df78-44a6-9cb8-db9a36f61706',
      email: '<EMAIL>',
      firstName: 'Ruby',
      name: 'Ruby Administrator',
      lastName: 'Administrator',
      userName: '<EMAIL>',
      salesforceUserName: '<EMAIL>',
      currencyIsoCode: 'USD',
    },
    //@ts-ignore
    getIntegration: async (appCategory: 'RevenueBuilder', integrationType: string) => {
      if (integrationType === 'Salesforce') {
        return {
          url: 'https://test.salesforce.com',
          clientId:
            '3MVG98dostKihXN602W7HGEc1wPp.Cd9O6H0OBj4kv8SaQLa67D.TfNddEmj3anZ4lpMrltTQ_bkqNYeYgQVk',
          enabled: true,
          appPending: '',
          externalSystemId: '123',
        };
      }
      return {
        id: '123',
      };
    },
    //@ts-ignore
    getDataFlowJobs: async () => {
      return [
        {
          id: 'e5a449dc-64fa-4427-ab6b-3b6cf1c0128f',
          status: 'Completed',
          startTime: '2022-06-23T12:41:01-07:00',
          endTime: '2022-06-23T12:41:17-07:00',
          scheduleType: 'Scheduled',
          connectorType: 'SalesforceBigObject',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '41c42571-889c-43d1-9f8e-9479c149d089',
        },
        {
          id: '4e93170b-6f88-4a1e-ae14-6030f2728365',
          status: 'Completed',
          startTime: '2022-06-23T12:37:48-07:00',
          endTime: '2022-06-23T12:41:24-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: 'c34c952b-11ab-43d2-9dd6-9b4a195e5be9',
        },
        {
          id: '9cb1440e-79fb-48e4-be27-d8df4dfef9f8',
          status: 'Completed',
          startTime: '2022-06-23T12:29:43-07:00',
          endTime: '2022-06-23T12:31:52-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: 'd8688bc4-8b24-4528-8f98-8d827322126d',
        },
        {
          id: '91b08ca6-7702-465d-ad7c-e4d700109b2e',
          status: 'Completed',
          startTime: '2022-06-23T12:25:40-07:00',
          endTime: '2022-06-23T12:26:09-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: 'c5ff948f-79c1-41f0-b3d5-bde241a34bdb',
        },
        {
          id: 'c5119a2b-8eae-4ddb-8624-44882a7e3ac1',
          status: 'Completed',
          startTime: '2022-06-23T12:25:35-07:00',
          endTime: '2022-06-23T12:26:14-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '1c03adf5-9db3-4ac1-ac51-857c6355ee97',
        },
        {
          id: 'd3ca4d55-f158-475c-8d3d-6d2b6aa3ce9d',
          status: 'Completed',
          startTime: '2022-06-23T12:22:01-07:00',
          endTime: '2022-06-23T12:22:01-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '2269447a-7d6f-4bf4-9824-38e0861afdda',
        },
        {
          id: 'a54dc454-85a6-419d-a020-bfd1689dbf5f',
          status: 'Completed',
          startTime: '2022-06-23T12:21:49-07:00',
          endTime: '2022-06-23T12:22:40-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '3f0bcf4e-bc39-427b-a579-f9d9a29f1cb3',
        },
        {
          id: 'a53d80b8-8cc8-4448-a5a8-f0f7f20726ae',
          status: 'Completed',
          startTime: '2022-06-23T12:21:38-07:00',
          endTime: '2022-06-23T12:21:38-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '4ecef4bd-2a51-4476-819a-effc63ae1117',
        },
        {
          id: 'dc01792a-ffe1-4a3c-bd1f-47aef2859098',
          status: 'Completed',
          startTime: '2022-06-23T12:18:07-07:00',
          endTime: '2022-06-23T12:18:07-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: 'a59a6dfb-3936-4844-8edd-eab6dfbc9239',
        },
        {
          id: 'fbf08a70-ca85-43a9-9ee5-f57cd4c0bf0e',
          status: 'Completed',
          startTime: '2022-06-23T12:17:54-07:00',
          endTime: '2022-06-23T12:17:54-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '185853d2-9000-4ec1-815a-8bc3465be8cf',
        },
        {
          id: '153ed239-be10-485b-a886-6cffe2166604',
          status: 'Completed',
          startTime: '2022-06-23T12:17:45-07:00',
          endTime: '2022-06-23T12:18:43-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '5a5d348d-3fe5-4e2b-877b-1f75b5fb239d',
        },
        {
          id: '4276935f-7080-49cb-beb7-4efd448a0895',
          status: 'Completed',
          startTime: '2022-06-23T12:09:52-07:00',
          endTime: '2022-06-23T12:17:25-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '3d89334c-7b7c-4a86-9856-ac7e6f7e3c7a',
        },
        {
          id: 'f5bcf78b-4121-478b-a403-6c5f4cd37e8c',
          status: 'Completed',
          startTime: '2022-06-23T11:10:36-07:00',
          endTime: '2022-06-23T11:11:16-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '99853a85-e452-4586-a503-766e8d7db362',
        },
        {
          id: 'eacbda33-a11e-464b-9f36-51c7de5e7448',
          status: 'Completed',
          startTime: '2022-06-23T11:10:34-07:00',
          endTime: '2022-06-23T11:11-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: 'a6526878-feef-4698-9334-59c3b47cd276',
        },
        {
          id: 'd0d62025-b54b-4159-aa45-a9f4aeed8121',
          status: 'Completed',
          startTime: '2022-06-23T10:55:39-07:00',
          endTime: '2022-06-23T10:41:14-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: 'a6526878-feef-4698-9334-59c3b47cd276',
        },
        {
          id: '09173a79-369c-4004-a0e1-d210224183cd',
          status: 'Completed',
          startTime: '2022-06-23T10:10:37-07:00',
          endTime: '2022-06-23T10:11:15-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '17b97803-c678-4bda-aa5f-c94d6be703c8',
        },
        {
          id: '718611e1-42c7-4178-b48a-d3a32396eb75',
          status: 'Completed',
          startTime: '2022-06-23T10:10:36-07:00',
          endTime: '2022-06-23T10:11:02-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '17b97803-c678-4bda-aa5f-c94d6be703c8',
        },
        {
          id: 'a94b05d7-9864-4a5f-b202-4dcd49599e7f',
          status: 'Completed',
          startTime: '2022-06-23T07:10:35-07:00',
          endTime: '2022-06-22T23:11:25-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: 'ce0684ed-95c0-4a9b-924e-07170fa7797e',
        },
        {
          id: '0e213949-2f65-4dee-a780-1b05f29739f9',
          status: 'Completed',
          startTime: '2022-06-23T06:25:36-07:00',
          endTime: '2022-06-23T05:26:13-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '3d197cc0-90c9-4498-86bf-b48522eb478b',
        },
        {
          id: '5504383b-15ff-4726-964d-7eca84981c75',
          status: 'Completed',
          startTime: '2022-06-23T06:25:36-07:00',
          endTime: '2022-06-23T04:41:12-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '7b6f3f73-c6dd-4c1a-81bc-7ecfd9f5da8b',
        },
        {
          id: 'b4437b8c-fa73-41c5-9a61-c69b441ac817',
          status: 'Completed',
          startTime: '2022-06-23T05:40:47-07:00',
          endTime: '2022-06-23T00:41:08-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: 'e0223b86-334c-4244-92b7-7521acae28c7',
        },
        {
          id: '004996ad-4731-4c6a-8085-ec908c487c76',
          status: 'Completed',
          startTime: '2022-06-23T05:40:39-07:00',
          endTime: '2022-06-23T04:56:39-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: 'e0223b86-334c-4244-92b7-7521acae28c7',
        },
        {
          id: 'c896ed93-58e3-484a-a49a-999d1928dd40',
          status: 'Completed',
          startTime: '2022-06-23T02:10:37-07:00',
          endTime: '2022-06-23T10:26:02-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '7b6f3f73-c6dd-4c1a-81bc-7ecfd9f5da8b',
        },
        {
          id: '9e352096-69d1-4ad2-aca8-599c04aa655f',
          status: 'Completed',
          startTime: '2022-06-22T22:40:36-07:00',
          endTime: '2022-06-22T22:26:01-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '3d197cc0-90c9-4498-86bf-b48522eb478b',
        },
        {
          id: '4937d632-9bc3-4027-bd6f-ac72902021d4',
          status: 'Completed',
          startTime: '2022-06-22T13:40:24-07:00',
          endTime: '2022-06-22T13:41:48-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: '_connector_1',
          dataSource: 'Salesforce',
          parentSyncId: '7c440ad1-029e-4cc2-8aae-dc0799c2e572',
        },
        {
          id: '8c3df228-f78e-430d-a6ca-a8eadeabb82a',
          status: 'InProgress',
          startTime: '2022-06-22T13:38:19-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: '_connector_1',
          dataSource: 'Salesforce',
          parentSyncId: '7b6f3f73-c6dd-4c1a-81bc-7ecfd9f5da8b',
        },
        {
          id: 'e4342845-13b4-4bcd-89db-e172944c46ea',
          status: 'Completed',
          startTime: '2022-06-22T13:34:08-07:00',
          endTime: '2022-06-23T10:56:14-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '99853a85-e452-4586-a503-766e8d7db362',
        },
        {
          id: 'd8b80fd3-fc69-4862-be18-1d76c63eb378',
          status: 'InProgress',
          startTime: '2022-06-22T13:34:08-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: '_connector_1',
          dataSource: 'Salesforce',
          parentSyncId: '99853a85-e452-4586-a503-766e8d7db362',
        },
        {
          id: 'f242d08c-b758-4682-8bda-d08ef2187e66',
          status: 'InProgress',
          startTime: '2022-06-22T13:24:58-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: '_connector_1',
          dataSource: 'Salesforce',
          parentSyncId: 'ce0684ed-95c0-4a9b-924e-07170fa7797e',
        },
        {
          id: '16823d28-61fe-4675-ad96-9a1444da3313',
          status: 'Completed',
          startTime: '2022-06-22T13:24:58-07:00',
          endTime: '2022-06-22T21:41:09-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: 'ce0684ed-95c0-4a9b-924e-07170fa7797e',
        },
        {
          id: '0bcac420-69bd-49c8-b6fc-9af6974d571f',
          status: 'Completed',
          startTime: '2022-06-22T13:21:26-07:00',
          endTime: '2022-06-23T03:41:01-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '7c440ad1-029e-4cc2-8aae-dc0799c2e572',
        },
        {
          id: '2d3d8a36-3d4f-476d-92d2-c62cf2da970c',
          status: 'Completed',
          startTime: '2022-06-22T13:21:26-07:00',
          endTime: '2022-06-23T07:11:13-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '7c440ad1-029e-4cc2-8aae-dc0799c2e572',
        },
        {
          id: '3267745e-d4a4-4cf4-b0f6-2c02ac2b2822',
          status: 'Completed',
          startTime: '2022-06-22T00:00-07:00',
          endTime: '2022-06-23T10:02:28-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: '_connector_1',
          dataSource: 'Salesforce',
          parentSyncId: 'e0223b86-334c-4244-92b7-7521acae28c7',
        },
        {
          id: '297acca6-b35e-44c8-9bf7-b7ac830f8c42',
          status: 'Completed',
          startTime: '2022-06-22T00:00-07:00',
          endTime: '2022-06-22T14:17:49-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: '_connector_1',
          dataSource: 'Salesforce',
          parentSyncId: '3d197cc0-90c9-4498-86bf-b48522eb478b',
        },
        {
          id: 'daa985fa-59fd-49f0-8064-a976f8cdb1dd',
          status: 'Completed',
          startTime: '2022-06-21T19:55:42-07:00',
          endTime: '2022-06-21T19:56:11-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '2ebdbec3-dd06-4697-ae92-107fb7197cef',
          status: 'Completed',
          startTime: '2022-06-21T16:34:51-07:00',
          endTime: '2022-06-20T19:26:17-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'd80b3ffb-2b22-42fb-84e7-0258c2c4aa3b',
          status: 'Completed',
          startTime: '2022-06-21T16:03:22-07:00',
          endTime: '2022-06-21T16:03:51-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '860b8fd1-3d58-4e97-8e05-98094156c96a',
          status: 'Completed',
          startTime: '2022-06-21T16:03:22-07:00',
          endTime: '2022-06-21T16:03:51-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'flank_quo',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '0073cde5-3161-4dda-b32a-d63fc315b1c8',
          status: 'Completed',
          startTime: '2022-06-21T13:55:46-07:00',
          endTime: '2022-06-21T13:56:31-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '4548276a-74cb-47fe-9ec6-37a407b1755c',
          status: 'Completed',
          startTime: '2022-06-21T13:55:46-07:00',
          endTime: '2022-06-21T13:56:31-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '40e78574-9e94-427f-bcce-11cf2c92a594',
          status: 'Completed',
          startTime: '2022-06-21T01:55:42-07:00',
          endTime: '2022-06-21T01:56:16-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '78257a1b-3481-4522-a8b0-1228a48f3756',
          status: 'Completed',
          startTime: '2022-06-21T01:55:42-07:00',
          endTime: '2022-06-20T17:41:12-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'a85a9194-3349-4b50-a823-91bb1b71bbca',
          status: 'Completed',
          startTime: '2022-06-20T19:55:45-07:00',
          endTime: '2022-06-20T19:56:23-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '4ec60290-9bc5-4a96-9804-a78a6af277bf',
          status: 'Completed',
          startTime: '2022-06-20T19:40:36-07:00',
          endTime: '2022-06-20T19:41:10-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '7ed03f4b-9551-4513-bf34-54a543dc0243',
          status: 'Completed',
          startTime: '2022-06-20T19:25:43-07:00',
          endTime: '2022-06-20T19:26:17-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '45e51aca-d6f5-40a9-9bfe-c36351532979',
          status: 'Completed',
          startTime: '2022-06-20T19:10:36-07:00',
          endTime: '2022-06-20T19:11:10-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '6f6b85d7-eb52-4d47-b08c-9e05ed9bf790',
          status: 'Completed',
          startTime: '2022-06-20T18:55:44-07:00',
          endTime: '2022-06-20T18:56:18-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'd0b6b009-eaf6-48e5-8648-3d166df859d3',
          status: 'Completed',
          startTime: '2022-06-20T18:55:44-07:00',
          endTime: '2022-06-20T18:56:18-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'd5a9f253-3e8c-4472-8c51-5fceb3e6354c',
          status: 'Completed',
          startTime: '2022-06-20T18:40:35-07:00',
          endTime: '2022-06-20T18:41:11-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '9d94da77-dd38-4555-a8b1-6e880df71b21',
          status: 'Completed',
          startTime: '2022-06-20T18:25:36-07:00',
          endTime: '2022-06-20T18:26:11-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'ce7db5ea-b512-4952-badd-8dba69e6985b',
          status: 'Completed',
          startTime: '2022-06-20T18:25:36-07:00',
          endTime: '2022-06-20T18:26:11-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'ea40c420-950b-4749-9878-9505ba551497',
          status: 'Completed',
          startTime: '2022-06-20T18:10:40-07:00',
          endTime: '2022-06-20T18:11:13-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '4931ff86-94ed-4ece-8237-0a67fe2607f5',
          status: 'Completed',
          startTime: '2022-06-20T17:57:29-07:00',
          endTime: '2022-06-20T17:58:03-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'aaf373e6-5f55-43eb-843e-55570f9922cb',
          status: 'Completed',
          startTime: '2022-06-20T17:26:11-07:00',
          endTime: '2022-06-20T15:20:28-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '9eaf8308-3214-47cd-ba66-c8135d96ecb3',
          status: 'Completed',
          startTime: '2022-06-20T17:10:34-07:00',
          endTime: '2022-06-20T17:41:12-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '876b3568-71ab-4ec6-85f5-d65d75dbfecf',
          status: 'Completed',
          startTime: '2022-06-20T17:01:36-07:00',
          endTime: '2022-06-20T17:02:14-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '60858a3d-55c0-45f3-9de4-5b6a83627332',
          status: 'Completed',
          startTime: '2022-06-20T15:30:45-07:00',
          endTime: '2022-06-20T17:02:14-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '5cba4ac7-d961-43f5-8766-6ffdd6293e9a',
          status: 'Completed',
          startTime: '2022-06-20T15:30:45-07:00',
          endTime: '2022-06-20T15:31:17-07:00',
          scheduleType: 'OnDemand',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'f731dc75-7fc9-42ab-b658-5acc2489d5d1',
          status: 'Completed',
          startTime: '2022-06-20T15:17:41-07:00',
          endTime: '2022-06-20T15:18:30-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'afff2391-871c-4802-a889-05fa31b22f4a',
          status: 'Completed',
          startTime: '2022-06-20T15:02:47-07:00',
          endTime: '2022-06-20T15:03:21-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'ed82dc26-57a8-46c5-9804-fe4a727b86f5',
          status: 'Completed',
          startTime: '2022-06-20T14:22:36-07:00',
          endTime: '2022-06-20T14:23:08-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '7210484f-c39c-4f28-b63b-6dac3ed8748c',
          status: 'Completed',
          startTime: '2022-06-20T14:20:20-07:00',
          endTime: '2022-06-20T14:20:54-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'a6773165-936a-44a9-a9cc-f2f22f48c5bb',
          status: 'Completed',
          startTime: '2022-06-20T00:00-07:00',
          endTime: '2022-06-20T15:31:17-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: '39d36c55-e779-4669-b92f-c8778f26e980',
          status: 'Completed',
          startTime: '2022-06-20T00:00-07:00',
          endTime: '2022-06-20T15:31:17-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
        {
          id: 'd06dbb5d-d4aa-459c-aab7-4613ff06c66a',
          status: 'Completed',
          startTime: '2022-06-20T00:00-07:00',
          endTime: '2022-06-20T17:26:44-07:00',
          scheduleType: 'Scheduled',
          externalId: 'Fivetran',
          connectorId: 'unworried_mercy',
          dataSource: 'Salesforce',
          parentSyncId: '00000000-0000-0000-0000-000000000000',
        },
      ];
    },
  };
  const tenantSecuritySettings: SecuritySettings = {
    sessionTimeout: 15,
    mfaEnforcement: 'Optional',
  };
  const securitySettingsContext: SecuritySettingsService = {
    getTenantSecuritySettings: async () => {
      await sleep(500);
      return tenantSecuritySettings;
    },
    saveTenantSecuritySettings: async (settings: SecuritySettings) => {
      await sleep(500);
      console.log('Saved settings: ', settings);
      tenantSecuritySettings.sessionTimeout = settings.sessionTimeout;
      return '200';
    },
    enableTenantMFA: async () => {
      await sleep(500);
      return;
    },
    disableTenantMFA: async () => {
      await sleep(500);
      return;
    },
  };

  const userFacade: UserFacade = {
    userContextService: {
      getUserConfig: () => {
        return {
          user: adminUser,
        };
      },
    },
  };

  const businessObjectsContext: BusinessObjectsServiceContextInterface = {
    fetchMetadataObjects: async () => {
      return Promise.resolve(objects as MetadataObject[]);
    },
    fetchObject: async (apiName: string) => {
      return Promise.resolve(PricebookEntryMetadata as MetadataObject);
    },
    syncCustomFields: async () => {
      await later(500, null);
    },
  };

  const collectionsContext = {
    getPaymentRuleList: async () => {
      await sleep(500);
      return [];
    },
    deletePaymentRule: async () => {
      await sleep(500);
      return;
    },
    activePaymentRule: async () => {
      await sleep(500);
      return;
    },
    cancelPaymentRule: async () => {
      await sleep(500);
      return;
    },
    duplicatePaymentRule: async (): Promise<PaymentRule> => {
      const paymentRule: PaymentRule = {
        id: '1',
        name: 'Sample Payment Rule',
        status: 'Active',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        conditions: [],
        description: 'This is a sample payment rule for testing purposes.',
      };
      return paymentRule;
    },
    createPaymentRule: async () => {
      await sleep(500);
      return;
    },
    updatePaymentRule: async () => {
      await sleep(500);
      return;
    },
    metadataObjects: [],
    getInvoiceActionTargetList: async () => {
      await sleep(500);
      return [];
    },
    getBillSystemSettings: async () => {
      await sleep(500);
      return [];
    },
    saveBillSystemSettings: async () => {
      await sleep(500);
      return;
    },
    getSpecifyRelatedObjects: {
      Cusomer: ['id', 'name'],
    },
  };
  const BusinessObjectServiceContextProvider: Provider = ({ children }) => (
    <BusinessObjectsServiceContext.Provider value={businessObjectsContext}>
      {children}
    </BusinessObjectsServiceContext.Provider>
  );
  const DataFlowManagerServiceProvider: Provider = ({ children }) => (
    <DataFlowManagerContext.Provider value={dataFlowManagerService}>
      {children}
    </DataFlowManagerContext.Provider>
  );

  //replace other providers except ComposeProviders
  const QuantityTierAttributeServiceContextProvider: Provider = ({ children }) => (
    <QuantityTierAttributeServiceContext.Provider value={quantityTierAttributeService}>
      {children}
    </QuantityTierAttributeServiceContext.Provider>
  );
  const PricingAttributeServiceContextProvider: Provider = ({ children }) => (
    <PricingAttributeServiceContext.Provider value={pricingAttributeService}>
      {children}
    </PricingAttributeServiceContext.Provider>
  );
  const TenantPropsContextProvider: Provider = ({ children }) => (
    <TenantPropsContext.Provider
      value={{
        getTenantDetailProps: async () => {
          return tenantDetailProps;
        },
        getTenantEditorProps: async () => {
          return tenantEditorProps;
        },
      }}
    >
      {children}
    </TenantPropsContext.Provider>
  );
  const RoleSettingContextProvider: Provider = ({ children }) => (
    <RoleSettingContext.Provider value={roleSettingService}>{children}</RoleSettingContext.Provider>
  );
  const SalesforceConnectionContextProvider: Provider = ({ children }) => (
    //@ts-ignore
    <SalesforceConnectionContext.Provider value={salesforceConnectionService}>
      {children}
    </SalesforceConnectionContext.Provider>
  );

  const MappingObjectLoaderContextProvider: Provider = ({ children }) => (
    <MappingObjectLoaderContext.Provider
      value={{
        loadMappingObjects: async () => {
          return mappingObjects;
        },
        loadProductCategoryValues: async () => {
          return productCategoryPickListValues;
        },
        loadActivatedObjects: async () => {
          return activatedObjects;
        },
        loadPriceModelValues: async () => {
          return priceModelPickListValues;
        },
        loadBillingTimingValues: async () => {
          return billingTimingPickListValues;
        },
        loadRevenueRuleValues: async () => {
          return revenueRulesPickListValues;
        },
        loadUomValues: async () => {
          return uomPickListValues;
        },
        loadRevenueModelValues: async () => {
          return revenueModelPickListValues;
        },
        loadTaxCodeValues: async () => {
          return taxCodePickListValues;
        },
      }}
    >
      {children}
    </MappingObjectLoaderContext.Provider>
  );

  const BillingSettingsContextProvider: Provider = ({ children }) => (
    <BillingSettingsContext.Provider value={billingSettingsContext}>
      {children}
    </BillingSettingsContext.Provider>
  );
  const RevenueRulesSettingsContextProvider: Provider = ({ children }) => (
    <RevenueRulesSettingsContext.Provider value={revenueRulesSettingsContext}>
      {children}
    </RevenueRulesSettingsContext.Provider>
  );
  const GraphqlGeneratorContextProvider: Provider = ({ children }) => (
    <GraphqlGeneratorContext.Provider value={graphqlGeneratorService}>
      {children}
    </GraphqlGeneratorContext.Provider>
  );
  const SecuritySettingsContextProvider: Provider = ({ children }) => (
    <SecuritySettingsContext.Provider value={securitySettingsContext}>
      {children}
    </SecuritySettingsContext.Provider>
  );

  const SettingsLoaderContextProvider: Provider = ({ children }) => (
    <SettingsLoaderContext.Provider
      value={{
        loadIntegrationSettings: async () => {
          return mockIntergrationSettings;
        },
      }}
    >
      {children}
    </SettingsLoaderContext.Provider>
  );
  const ApiKeysContextProvider: Provider = ({ children }) => (
    <ApiKeysContext.Provider
      value={{
        user: {
          name: 'Stephen Chan',
          id: '0001',
          email: '<EMAIL>',
          currencyIsoCode: 'USD',
          roles: [
            {
              id: '9ff32b0b-b64a-45b7-bcf3-305d5b927e1b',
              name: 'System Administrator',
              effective: true,
              functions: [
                'ManageDashboards',
                'DeleteCreditMemos',
                'ViewUsage',
                'ViewCustomer',
                'ViewPipelineDashboard',
                'ManageUsers',
                'ManageQuote',
                'ViewBillingAndCollectionsDashboard',
                'ManageAssets',
                'ManageConnection',
                'ManageRightRevIntegration',
                'ManageRevenueContract',
                'ViewPriceBook',
                'ActivateCreditMemos',
                'ViewDebitMemos',
                'ActivateDebitMemos',
                'CancelDebitMemos',
                'DeleteDebitMemos',
                'CreateDebitMemos',
                'ManageAvalaraIntegration',
                'ManageTax',
                'ViewAPIKeys',
                'ManageAPIKeys',
                'Security',
                'BasicFunction',
                'DeleteInvoices',
                'UsageRating',
                'ViewOrder',
                'ViewInvoices',
                'ViewBookingDashboard',
                'LifecycleManagerSettings',
                'ViewCreditMemos',
                'ManagePriceBook',
                'ManageBillingSchedule',
                'OrganizationSettings',
                'ManageBundle',
                'CancelCreditMemos',
                'ViewPriceTag',
                'ManageCustomer',
                'ManagePriceTag',
                'ManageStripeIntegration',
                'ViewPaymentApplication',
                'ManagePaymentApplication',
                'ManageCredits',
                'ManageQuickBooksIntegration',
                'ViewBundle',
                'ViewQuote',
                'ActivateInvoices',
                'ManageOrder',
                'ViewAssets',
                'CancelInvoices',
                'PublishProduct',
                'PublishPriceBook',
                'ManageEvent',
                'DataImport&Export',
                'ManageRole',
              ],
              features: [
                'Analytics',
                'CreditMemoManagement',
                'UsageManagement',
                'CustomerManagement',
                'SystemSettings',
                'NueOnSalesforceQuote',
                'AssetManagement',
                'SalesforceIntegration',
                'Integrations',
                'RevenueRecognition',
                'AdvancedPricing&Discounting',
                'DebitMemoManagement',
                'Taxation',
                'InvoiceManagement',
                'OrderManagement',
                'BillingSchedule',
                'BundleBuilder',
                'Collections',
                'SelfService',
              ],
            },
          ],
        },
        loadApiKeyLog: (
          apiKeyIds: string[],
          page: number,
          pageSize: number,
          statusCode?: number,
          accessDate?: string,
          endPoint?: string,
          method?: string,
        ) => {
          if (page === 0) {
            return Promise.resolve({
              data: {
                '2024-04-19': [
                  {
                    id: 105,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:31.636Z',
                  },
                  {
                    id: 104,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:30.803Z',
                  },
                  {
                    id: 103,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:29.923Z',
                  },
                  {
                    id: 102,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:28.992Z',
                  },
                  {
                    id: 101,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:27.806Z',
                  },
                  {
                    id: 100,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:26.905Z',
                  },
                  {
                    id: 99,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:25.864Z',
                  },
                  {
                    id: 98,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:24.68Z',
                  },
                  {
                    id: 97,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:23.546Z',
                  },
                  {
                    id: 96,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:22.426Z',
                  },
                  {
                    id: 95,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:21.376Z',
                  },
                  {
                    id: 94,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:20.294Z',
                  },
                  {
                    id: 93,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:18.901Z',
                  },
                  {
                    id: 92,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:50:14.714Z',
                  },
                  {
                    id: 91,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:49:46.091Z',
                  },
                  {
                    id: 90,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:49:07.274Z',
                  },
                  {
                    id: 89,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'GET',
                    url: '/billing/accounts/001DS00001Do4NhYAJ/account-credit-pools',
                    statusCode: 200,
                    accessTime: '2024-04-19T08:38:32.669Z',
                  },
                  {
                    id: 88,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'GET',
                    url: '/api/usage/tenants/current',
                    statusCode: 200,
                    accessTime: '2024-04-19T08:38:19.26Z',
                  },
                  {
                    id: 87,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'GET',
                    url: '/api/billing/accounts/111/account-credit-pools',
                    statusCode: 200,
                    accessTime: '2024-04-19T08:37:56.449Z',
                  },
                  {
                    id: 86,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'GET',
                    url: '/api/billing/account-credit-pool/50b5ece6-49c1-44f7-bbe4-f46c96489a01/credit-stats',
                    statusCode: 200,
                    accessTime: '2024-04-19T08:37:22.669Z',
                  },
                ],
              },
            });
          }

          if (page === 1) {
            return Promise.resolve({
              data: {
                '2024-03-19': [
                  {
                    id: 1001,
                    apiKeyId: '3229dc82-1483-4c4d-a685-c2175f878244',
                    method: 'GET',
                    url: '/api/access-logs',
                    statusCode: 405,
                    accessTime: '2024-04-19T08:33:37.673Z',
                  },
                ],
                '2024-04-19': [
                  {
                    id: 83,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'GET',
                    url: '/api/access-logs',
                    statusCode: 405,
                    accessTime: '2024-04-19T08:33:37.673Z',
                  },
                  {
                    id: 82,
                    apiKeyId: '7829dc82-1483-4c4d-a685-c2175f878244',
                    method: 'POST',
                    url: '/api/orders/sync',
                    statusCode: 500,
                    accessTime: '2024-04-19T08:31:37.728Z',
                  },
                ],
              },
            });
          }
          return Promise.resolve({});
        },
        //@ts-ignore
        fetchTenantRoles: async () => {
          return [
            { id: '001', name: 'Sales', functions: [{ id: '0001' }] },
            {
              id: '96cb2ef7-66d0-4b2a-ab31-5e864024d76b',
              name: 'Admin',
              functions: [{ id: '0002' }],
            },
          ];
        },
        createApiKey: async (name: string, roleId: string) => {
          return Promise.resolve();
        },
        updateApiKey: async (id: string, name: string, roleId: string) => {
          return Promise.resolve();
        },
        getEventRequestDetails: async () => {
          return Promise.resolve({
            id: 11,
            apiKeyId: '041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b',
            method: 'GET',
            url: '/api/access-log',
            statusCode: 200,
            requestBody: '{\n    "apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b"\n    \n}',
            responseBody:
              '{"data":{"2024-03-20":[{"id":10,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"GET","url":"/api/access-log","statusCode":200,"accessTime":"2024-03-20T14:35:50.086Z"},{"id":9,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"GET","url":"/api/access-log","statusCode":200,"accessTime":"2024-03-20T14:22:28.592Z"},{"id":8,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"GET","url":"/api/access-log","statusCode":200,"accessTime":"2024-03-20T14:18:10.074Z"},{"id":7,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"GET","url":"/api/access-log","statusCode":200,"accessTime":"2024-03-20T14:17:08.935Z"},{"id":4,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"POST","url":"/api/cpq/async/imports","statusCode":500,"accessTime":"2024-03-20T08:29:06.86Z"},{"id":3,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"GET","url":"/api/billing/account-credit-pool/53a48961-dde1-4708-80e8-695977b9e640/credit-stats","statusCode":200,"accessTime":"2024-03-20T08:24:19.765Z"},{"id":2,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"GET","url":"/api/billing/account-credit-pool/53a48961-dde1-4708-80e8-695977b9e640/credit-stats","statusCode":200,"accessTime":"2024-03-20T08:24:14.353Z"},{"id":1,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"GET","url":"/api/billing/account-credit-pool/53a48961-dde1-4708-80e8-695977b9e640/credit-stats","statusCode":200,"accessTime":"2024-03-20T08:21:40.617Z"}],"2024-03-18":[{"id":6,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"GET","url":"/api/billing/account-credit-pool/53a48961-dde1-4708-80e8-695977b9e640/credit-stats","statusCode":200,"accessTime":"2024-03-18T09:09:12.307Z"}],"2024-03-09":[{"id":5,"apiKeyId":"041669a9-6c9b-4aa3-9b86-59ae1c0e0e3b","method":"POST","url":"/api/cpq/async/imports","statusCode":201,"accessTime":"2024-03-09T16:00:00Z"}]}}',
            accessTime: '2024-03-20T14:36:08.807Z',
          });
        },
        deleteApiKey: async (id: string) => {
          console.log(id);
        },
        getApiKeysData: () => {
          return Promise.resolve([
            {
              createdDate: '2024-03-27T04:26:11Z',
              id: '1161d479-b123-48b9-8a58-3088c29f33b8',
              lastUsedDate: '2024-03-27T15:16:34.493+08:00',
              masterKey: false,
              name: 'Default API Key 2',
              roleId: '1357d111-e385-40e9-a565-d5bbc51c006c',
              rootId: 'c2286197-5cab-46d0-b44a-a8bdc8a710d8',
            },
            {
              createdDate: '2024-03-27T06:13:43Z',
              id: 'c3369e4c-5a27-4675-9e66-53c1ac9d802a',
              lastUsedDate: '2024-03-27T18:46:57.513+08:00',
              masterKey: false,
              name: 'Default API Key 3',
              roleId: 'ae117d86-2641-4db8-a9c8-f689323f7d0b',
              rootId: '66bd7f2a-d746-4eb2-a2e8-9c3b0379ceac',
            },
          ]);
        },
        authUser: () => {
          return Promise.resolve(true);
        },
        revealApiKey: (id: string) => {
          return Promise.resolve({
            id: '0dc7e112-5dfa-405b-bbcb-26be78bcffd8',
            apiKey: 'eLkg8D.37aba7c328fa4755b7492c3e7173aea6.e1f1214e',
          });
        },
        rollApiKey: (id: string, isTody: boolean, expireTime: string) => {
          return Promise.resolve({
            runImmediately: false,
            expirationTime: '2024-03-09T14:54:21.775+08:00',
          });
        },
      }}
    >
      {children}
    </ApiKeysContext.Provider>
  );
  const StripeConnectionContextProvider: Provider = ({ children }) => (
    <StripeConnectionContext.Provider
      value={{
        recoverStripeWebhooks: (id: string) => {
          return Promise.resolve({
            id: 'test',
            checkHealth: true,
            urlStatus: 'Effective',
            webhookStatus: 'Enabled',
            usedEvents: [
              'PAYMENT_INTENT__SUCCESSED',
              'INVOICE__PAID',
              'INVOICE__VOIDED',
              'PAYMENT_INTENT__PAYMENT_FAILED',
              'CHARGE__REFUNDED',
            ],
            deletedEvents: [],
          });
        },
        onCreateStripeIntegration: (request: StripeConnectionRequest) => {
          return Promise.resolve('success');
        },
        getStripeWebhooks: () => {
          return Promise.resolve({
            id: 'test',
            checkHealth: false,
            urlStatus: 'Effective',
            webhookStatus: 'Enabled',
            usedEvents: [
              'PAYMENT_INTENT__SUCCESSED',
              'INVOICE__PAID',
              'INVOICE__VOIDED',
              'PAYMENT_INTENT__PAYMENT_FAILED',
            ],
            deletedEvents: ['CHARGE__REFUNDED'],
          });
        },
        getStripeIntegrationDetail: () => {
          return Promise.resolve({
            publishableKey: '111',
            secretKey: 'secret',
            enabled: true,
            lastModifiedDate: '2023-01-31T02:53:17.000+0000',
            integrationStatus: 'Enabled',
          });
        },
        onUpdateStripeIntegration: () => {
          return Promise.resolve();
        },
        onTestStripeIntegration: (request: StripeConnectionRequest) => {
          return Promise.resolve();
        },
        getStripeCollectionMethod: () => {
          return Promise.resolve('AutomaticallyCharge');
        },
        updateStripeCollectionMethod: (collectionMethod: string) => {
          return Promise.resolve(true);
        },
        handleUpdateRows: (rows: []) => {
          console.log('handleUpdateRows', rows);
          return Promise.resolve();
        },
        handleDeleteRow: (row: any) => {
          console.log('handleDeleteRow', row);
          return Promise.resolve();
        },
        handleUploadFiles: () => {
          return Promise.resolve([
            {
              lineNo: 2,
              reason:
                'The column of Salesforce Account Id or QuickBooks Customer ID should not be empty, please input the Salesforce Account ID and upload it again.',
              sourceCustomerId: 'sourceId01',
              sourceCustomerName: 'sourceName01',
              paymentCustomerId: 'paymentId01',
              paymentCustomerName: 'paymentName01',
            },
            {
              lineNo: 2,
              reason:
                'The column of Salesforce Account Id or QuickBooks Customer ID should not be empty, please input the Salesforce Account ID and upload it again.',
              sourceCustomerId: 'sourceId02',
              sourceCustomerName: 'sourceName02',
              paymentCustomerId: 'paymentId02',
              paymentCustomerName: 'paymentName02',
            },
          ]);
        },
        loadMappingData: (nPageSize?: number, nCurrentPage?: number, nKeyword?: string) => {
          return Promise.resolve({
            keywords: '',
            data: [
              {
                id: '9',
                createdTime: '2023-02-10T10:46:20.592',
                sourceCustomerName: 'sourceId01',
                sourceCustomerId: 'sourceCustomerId01',
                paymentCustomerName: 'paymentId01',
                paymentCustomerId: 'paymentCustomerId01',
              },
            ],
            totalCounts: 8,
            totalPages: 2,
            pageSize: 5,
            currentPage: 0,
          });
        },
        getStripeTaxIntegration: () => {
          console.debug('getStripeTaxIntegration');
          return later(
            1000,
            hasSetupStripeTax
              ? {
                  id: 'newIntegrationId',
                  status: 'Draft',
                  integrationType: 'Stripe',
                  config: {
                    enableTaxInvoiceCollection: true,
                    enableTaxOrderQuote: false,
                  },
                }
              : null,
          );
        },
        updateStripeTaxIntegration: (request) => {
          hasSetupStripeTax = true;
          console.debug('updateStripeTaxIntegration', request);
          return later(1000, { success: true, error: '' });
        },
        setupStripeTaxIntegration: () => {
          console.debug('setupStripeTaxIntegration');
          return later(1000, { id: 'newIntegrationId' });
        },
        forceActivateIntegration: () => {
          console.debug('forceActivateIntegration');
          return later(1000, { success: true, error: '' });
        },
        getActiveIntegration: () => {
          console.debug('getActiveIntegration');
          return later(1000, {
            id: 'newIntegrationId',
            status: 'Draft',
            integrationType: 'Stripe',
            config: {
              enableTaxInvoiceCollection: true,
              enableTaxOrderQuote: false,
            },
          });
        },
      }}
    >
      {children}
    </StripeConnectionContext.Provider>
  );

  const RightRevConnectionContextProvider: Provider = ({ children }) => (
    <RightRevConnectionContext.Provider
      value={{
        createIntegration: (request: UpsertRightRevIntegrationRequestBody) => {
          console.debug('createIntegration', request);
          return Promise.resolve({ id: 'newIntegrationId' });
        },
        getIntegration: () => {
          console.debug('getIntegration');
          return Promise.resolve({
            id: 'test',
            status: 'Draft',
            integrationType: 'RightRev',
            sandbox: true,
            realm: 'realm',
            clientId: 'clientId',
            userName: 'userName',
          });
        },
        updateIntegration: (request: UpsertRightRevIntegrationRequestBody) => {
          console.debug('updateIntegration', request);
          return Promise.resolve();
        },
        activateIntegration: (id: string) => {
          console.debug('updateIntegration', id);
          return Promise.resolve();
        },
        disableIntegration: (id: string) => {
          console.debug('updateIntegration', id);
          return Promise.resolve();
        },
        testIntegration: (id: string) => {
          console.debug('testIntegration', id);
          return Promise.resolve({ success: true, error: '' });
        },
        getProvisioningStatuses: () => {
          console.debug('getProvisioningStatuses');
          if (count) {
            return Promise.resolve([
              {
                status: 'Completed',
                policyType: 'TenantConfiguration',
                errorMessage: null,
              },
              {
                status: 'Failed',
                policyType: 'TenantConfiguration',
                errorMessage: 'Error happened',
              },
            ]);
          }

          count++;

          return Promise.resolve([
            {
              status: 'Completed',
              policyType: 'LegalEntity',
              errorMessage: null,
            },
            {
              status: 'Completed',
              policyType: 'SSP',
              errorMessage: null,
            },
            {
              status: 'In Progress',
              policyType: 'ContractAmendment',
              errorMessage: null,
            },
            {
              status: 'Started',
              policyType: 'OrderPolicy',
              errorMessage: null,
            },
            {
              status: 'Not Started',
              policyType: 'EventDefinition',
              errorMessage: null,
            },
          ]);
        },
      }}
    >
      {children}
    </RightRevConnectionContext.Provider>
  );

  const AvalaraConnectionContextProvider: Provider = ({ children }) => (
    <AvalaraConnectionContext.Provider
      value={{
        createIntegration: (request: UpsertTaxIntegrationRequestBody) => {
          console.debug('Integration Create Request: ', request);
          return Promise.resolve({ id: 'newIntegrationId' });
        },
        //@ts-ignore
        getIntegration: () => {
          return Promise.resolve({
            id: 'sadasdasda',
            status: 'Draft',
            integrationType: 'Avalara',
            config: {
              environment: 'Sandbox',
              accountId: 'testAccountId',
              companyCode: null,
              enableLogging: true,
              commitDocuments: true,
            },
          });
        },
        updateIntegration: (request: UpsertTaxIntegrationRequestBody) => {
          console.debug('Integration Update Request: ', request);
          return Promise.resolve();
        },
        activateIntegration: (id: string) => {
          return Promise.resolve({ success: true, error: '' });
        },
        disableIntegration: (id: string) => {
          return Promise.resolve();
        },
        testIntegration: (id: string) => {
          return Promise.resolve({ success: true, error: '' });
        },
        getCompanies: () => {
          return Promise.resolve([
            {
              id: 'idDefault',
              name: 'Default',
              code: 'default',
              accountId: 'accountId',
              taxpayerIdNumber: 'taxpayerIdNumber',
              baseCurrencyCode: 'baseCurrencyCode',
              defaultCountry: 'defaultCountry',
              isDefault: true,
              isReportingEntity: true,
            },
            {
              id: 'idNotDefault',
              name: 'ANot Default',
              code: 'notDefault',
              accountId: 'accountId',
              taxpayerIdNumber: 'taxpayerIdNumber',
              baseCurrencyCode: 'baseCurrencyCode',
              defaultCountry: 'defaultCountry',
              isDefault: false,
              isReportingEntity: true,
            },
          ]);
        },
      }}
    >
      {children}
    </AvalaraConnectionContext.Provider>
  );

  const QuickBooksConnectionContextProvider: Provider = ({ children }) => (
    <QuickBooksConnectionContext.Provider
      value={{
        handleUpdateRows: (rows: []) => {
          console.log('handleUpdateRows', rows);
          return Promise.resolve();
        },
        handleDeleteRow: (row: any) => {
          console.log('handleDeleteRow', row);
          return Promise.resolve();
        },
        handleUploadFiles: (data: any) => {
          return Promise.resolve([
            {
              lineNo: 2,
              reason:
                'The column of Salesforce Account Id or Stripe Customer ID should not be empty, please input the Salesforce Account ID and upload it again.',
              sourceCustomerId: 'sourceId01',
              sourceCustomerName: 'sourceName01',
              paymentCustomerId: 'paymentId01',
              paymentCustomerName: 'paymentName01',
            },
            {
              lineNo: 2,
              reason:
                'The column of Salesforce Account Id or Stripe Customer ID should not be empty, please input the Salesforce Account ID and upload it again.',
              sourceCustomerId: 'sourceId02',
              sourceCustomerName: 'sourceName02',
              paymentCustomerId: 'paymentId02',
              paymentCustomerName: 'paymentName02',
            },
          ]);
        },
        loadMappingData: (
          type: string,
          env: string,
          nPageSize?: number,
          nCurrentPage?: number,
          nKeyword?: string,
        ) => {
          if (type === 'customer') {
            return Promise.resolve({
              keywords: '',
              data: [
                {
                  id: '9',
                  createdTime: '2023-02-10T10:46:20.592',
                  sourceCustomerName: 'sourceId01',
                  sourceCustomerId: 'sourceCustomerId01',
                  paymentCustomerName: 'paymentId01',
                  paymentCustomerId: 'paymentCustomerId01',
                },
              ],
              totalCounts: 8,
              totalPages: 2,
              pageSize: 5,
              currentPage: 0,
            });
          }
          return Promise.resolve({
            keywords: '',
            data: [
              {
                id: '9',
                createdTime: '2023-02-10T10:46:20.592',
                sourceProductName: 'sourceProductName1',
                sourceProductId: 'sourceProductId1',
                paymentProductName: 'paymentProductName1',
                paymentProductId: 'paymentProductId1',
              },
            ],
            totalCounts: 8,
            totalPages: 2,
            pageSize: 5,
            currentPage: 0,
          });
        },
        queryQuickBookConfigure: () => {
          return Promise.resolve({
            authenticationId: 'e323c80f-09c6-43d0-9b86-ade0b630e133',
            solutionInstanceId: '3c9f4aee-52df-4e77-9565-e9a4b8736674',
            lastModifiedDate: '2023-08-14 08:53:00.0',
            enabled: true,
            companyName: 'xxxxx',
            environment: 'Sandbox',
          });
        },
        createQuickBooksIntegration: () => {
          return Promise.resolve({
            trayUsername: 'Kaiyu Test(d0c1a918-200a-4e85-9e3e-91cafb03764d)',
            trayEmbeddedUrl:
              'https://embedded.tray.io/external/solutions/nue/configure/a4159dbf-3ec2-42eb-9ac8-77494fc223a5?code=0d0a92cb01d68d91b67c4b4f41a4cb74a019fc85&enhancedTokenExchangeSecurity=true',
            webhookPublicUrl: 'https://35a9c13b-ad2c-468f-bc11-4a6d6150972b.trayapp.io',
          });
        },
        updateQuickBooksIntegration: (request: IntegrationUpdateDetail) => {
          return Promise.resolve();
        },
        configureQuickBooksIntegration: (configuration: IntegrationConfigureDetail) => {
          return Promise.resolve({
            trayUsername: 'Kaiyu Test(d0c1a918-200a-4e85-9e3e-91cafb03764d)',
            trayEmbeddedUrl:
              'https://embedded.tray.io/external/solutions/nue/configure/a4159dbf-3ec2-42eb-9ac8-77494fc223a5?code=0d0a92cb01d68d91b67c4b4f41a4cb74a019fc85&enhancedTokenExchangeSecurity=true',
            webhookPublicUrl: 'https://35a9c13b-ad2c-468f-bc11-4a6d6150972b.trayapp.io',
          });
        },
      }}
    >
      {children}
    </QuickBooksConnectionContext.Provider>
  );

  const WebhookConnectionContextProvider: Provider = ({ children }) => (
    <WebhookConnectionContext.Provider
      value={{
        getAvailableWebhookEvents: () => {
          return Promise.resolve([
            'order.created',
            'order.activated',
            'order.canceled',
            'invoice.activated',
            'invoice.canceled',
            'credit_memo.activated',
            'credit_memo.canceled',
          ]);
        },
        registerWebhook: (object: WebhookRegisterObject) => {
          return Promise.resolve({
            id: '45fe2e57-6b67-4e1d-9871-e7fcfe7359c9',
            name: 'Order Activation1',
            object: 'webhook_endpoint',
            createdDate: '2023-10-07T13:16:42.867+08:00',
            createdBy: '887bd97c-88b8-44ac-bdfc-f3883c8b873f',
            lastModifiedDate: '2023-10-07T13:16:42.867+08:00',
            lastModifiedBy: '887bd97c-88b8-44ac-bdfc-f3883c8b873f',
            description: 'order activation webhook',
            status: 'enabled',
            events: ['order.activated', 'invoice.activated'],
            endpoint: 'https://webhook.site/23975ce4-a601-4643-85fb-ff51c9f4edea',
          });
        },
        getAllWebhookList: () => {
          return Promise.resolve({
            webhooks: [
              {
                id: '45fe2e57-6b67-4e1d-9871-e7fcfe7359c9',
                name: 'Order Activation1',
                eventTypes: ['order.activated', 'invoice.activated'],
                actionType: 'Webhook',
                endpoint: 'https://webhook.site/23975ce4-a601-4643-85fb-ff51c9f4edea',
                description: 'order activation webhook',
                status: 'enabled',
              },
            ],
          });
        },
        deleteWebhook: () => {
          return Promise.resolve();
        },
        disableWebhook: () => {
          return Promise.resolve();
        },
        enableWebhook: () => {
          return Promise.resolve();
        },
        updateWebhook: () => {
          return Promise.resolve();
        },
        getEventLogs: (id: string) => {
          return Promise.resolve({
            id: [
              {
                id: 'id',
                eventId: 'eventId',
                eventType: 'eventType',
                registryId: 'registryId',
                status: 'Posted',
                target: 'target',
                createdAt: 'createdAt',
              },
            ],
          });
        },
        getEventResponseById: () => {
          return Promise.resolve();
        },
        getEventRequestById: () => {
          return Promise.resolve([]);
        },
        resendWebhook: () => {
          return Promise.resolve();
        },
      }}
    >
      {children}
    </WebhookConnectionContext.Provider>
  );

  const UserContextProvider: Provider = ({ children }) => (
    <UserContext.Provider value={userFacade}>{children}</UserContext.Provider>
  );

  const NetSuiteConnectionContextProvider: Provider = ({ children }) => (
    <NetSuiteConnectionContext.Provider
      value={{
        handleUpdateRows: (rows: []) => {
          console.log('handleUpdateRows', rows);
          return Promise.resolve();
        },
        handleDeleteRow: (row: any) => {
          console.log('handleDeleteRow', row);
          return Promise.resolve();
        },
        handleUploadFiles: (data: any) => {
          return Promise.resolve([
            {
              lineNo: 2,
              reason:
                'The column of Salesforce Account Id or Stripe Customer ID should not be empty, please input the Salesforce Account ID and upload it again.',
              sourceCustomerId: 'sourceId01',
              sourceCustomerName: 'sourceName01',
              paymentCustomerId: 'paymentId01',
              paymentCustomerName: 'paymentName01',
            },
            {
              lineNo: 2,
              reason:
                'The column of Salesforce Account Id or Stripe Customer ID should not be empty, please input the Salesforce Account ID and upload it again.',
              sourceCustomerId: 'sourceId02',
              sourceCustomerName: 'sourceName02',
              paymentCustomerId: 'paymentId02',
              paymentCustomerName: 'paymentName02',
            },
          ]);
        },
        loadMappingData: (
          type: string,
          env: string,
          nPageSize?: number,
          nCurrentPage?: number,
          nKeyword?: string,
        ) => {
          if (type === 'customer') {
            return Promise.resolve({
              keywords: '',
              data: [
                {
                  id: '9',
                  createdTime: '2023-02-10T10:46:20.592',
                  sourceCustomerName: 'sourceId01',
                  sourceCustomerId: 'sourceCustomerId01',
                  paymentCustomerName: 'paymentId01',
                  paymentCustomerId: 'paymentCustomerId01',
                },
              ],
              totalCounts: 8,
              totalPages: 2,
              pageSize: 5,
              currentPage: 0,
            });
          }
          return Promise.resolve({
            keywords: '',
            data: [
              {
                id: '9',
                createdTime: '2023-02-10T10:46:20.592',
                sourceProductName: 'sourceProductName1',
                sourceProductId: 'sourceProductId1',
                paymentProductName: 'paymentProductName1',
                paymentProductId: 'paymentProductId1',
              },
            ],
            totalCounts: 8,
            totalPages: 2,
            pageSize: 5,
            currentPage: 0,
          });
        },
        queryNetSuiteConfigure: () => {
          return Promise.resolve({
            authenticationId: 'e323c80f-09c6-43d0-9b86-ade0b630e133',
            solutionInstanceId: '3c9f4aee-52df-4e77-9565-e9a4b8736674',
            lastModifiedDate: '2023-08-14 08:53:00.0',
            enabled: true,
            companyName: 'xxxxx',
            environment: 'Sandbox',
          });
        },
        createNetSuiteIntegration: () => {
          return Promise.resolve({
            trayUsername: 'Kaiyu Test(d0c1a918-200a-4e85-9e3e-91cafb03764d)',
            trayEmbeddedUrl:
              'https://embedded.tray.io/external/solutions/nue/configure/a4159dbf-3ec2-42eb-9ac8-77494fc223a5?code=0d0a92cb01d68d91b67c4b4f41a4cb74a019fc85&enhancedTokenExchangeSecurity=true',
            webhookPublicUrl: 'https://35a9c13b-ad2c-468f-bc11-4a6d6150972b.trayapp.io',
          });
        },
        updateNetSuiteIntegration: (request: IntegrationUpdateDetail) => {
          return Promise.resolve();
        },
        configureNetSuiteIntegration: (configuration: IntegrationConfigureDetail) => {
          return Promise.resolve({
            trayUsername: 'Kaiyu Test(d0c1a918-200a-4e85-9e3e-91cafb03764d)',
            trayEmbeddedUrl:
              'https://embedded.tray.io/external/solutions/nue/configure/a4159dbf-3ec2-42eb-9ac8-77494fc223a5?code=0d0a92cb01d68d91b67c4b4f41a4cb74a019fc85&enhancedTokenExchangeSecurity=true',
            webhookPublicUrl: 'https://35a9c13b-ad2c-468f-bc11-4a6d6150972b.trayapp.io',
          });
        },
      }}
    >
      {children}
    </NetSuiteConnectionContext.Provider>
  );

  const CollectionsContextProvider: Provider = ({ children }) => (
    <CollectionsContext.Provider value={collectionsContext}>{children}</CollectionsContext.Provider>
  );
  let hasSetupStripeTax = false;
  return (
    <ComposeProviders
      providers={[
        BusinessObjectServiceContextProvider,
        DataFlowManagerServiceProvider,
        QuantityTierAttributeServiceContextProvider,
        PricingAttributeServiceContextProvider,
        TenantPropsContextProvider,
        RoleSettingContextProvider,
        SalesforceConnectionContextProvider,
        MappingObjectLoaderContextProvider,
        BillingSettingsContextProvider,
        RevenueRulesSettingsContextProvider,
        GraphqlGeneratorContextProvider,
        SecuritySettingsContextProvider,
        SettingsLoaderContextProvider,
        ApiKeysContextProvider,
        StripeConnectionContextProvider,
        RightRevConnectionContextProvider,
        AvalaraConnectionContextProvider,
        QuickBooksConnectionContextProvider,
        WebhookConnectionContextProvider,
        UserContextProvider,
        NetSuiteConnectionContextProvider,
        CollectionsContextProvider,
      ]}
    >
      <RubySettings
        {...args}
        onSubcategoryChange={(settingItem) => {
          console.log('onSettingItemValueChange', settingItem);
          let found: Subcategory | null = null;
          for (let i = 0; i < Example.args!.categories!.length; ++i) {
            for (let i = 0; i < Example.args!.categories!.length; ++i) {
              let category = Example.args!.categories![i];
              if (category?.subCategories) {
                for (let j = 0; j < category.subCategories.length; ++j) {
                  let subCategory = category.subCategories[j];
                  if (subCategory.name === settingItem.name) {
                    found = subCategory;
                    break;
                  }
                }
                if (found) {
                  break;
                }
              }
            }
          }
          if (found) {
            found.settings = {
              ...found.settings,
              ...settingItem.settings,
            };
          }
        }}
      />
    </ComposeProviders>
  );
};

export const Example = Template.bind({});
Example.args = {
  categories: [
    {
      name: 'Ruby Platform',
      subCategories: [
        {
          name: 'Organization Settings',
          icon: <OrganizationIcon />,
        },
        {
          name: 'Roles',
          icon: <UsersIcon />,
        },
        {
          name: 'GraphQL Generator',
          icon: <OrganizationIcon />,
        },
        {
          name: 'Business Objects',
          icon: <UsersIcon />,
        },
        {
          name: 'Data Flow Manager',
          icon: <DataFlowManagerIcon />,
        },
        {
          name: 'Security',
          icon: <SecurityOutlinedIcon />,
          labels: ['Multi-factor Authentication (MFA)'],
        },
        {
          name: 'API Keys',
          icon: <VpnKeyOutlinedIcon />,
        },
      ],
    },
    {
      name: 'Price Builder',

      subCategories: [
        {
          name: 'Product Categories',
          icon: <InsertLinkIcon />,
          settings: {
            productCategoryToActivationObjectMappings: [
              {
                fromField: 'RecurringServices',
                toField: 'Subscription__c',
                developerName: 'developerName',
                updatable: true,
                fromObject: 'ProductCategory',
                toObject: 'ActivationObject',
              },
              {
                fromObject: 'ProductCategory',
                toObject: 'ActivationObject',
                fromField: 'PhysicalGoods',
                toField: 'Asset',
                updatable: true,
                developerName: 'developerName',
              },
              {
                fromObject: 'ProductCategory',
                toObject: 'ActivationObject',
                toField: 'Entitlement__c',
                fromField: 'ProfessionalServices',
                updatable: true,
                developerName: 'developerName',
              },
            ],
            productCategoryToUOMMappings: [
              {
                fromField: 'RecurringServices',
                developerName: 'developerName',
                updatable: true,
                fromObject: 'ProductCategory',
                toObject: 'Uom',
                toField: 'Account',
              },
              {
                fromObject: 'ProductCategory',
                toObject: 'Uom',
                fromField: 'PhysicalGoods',
                toField: 'Hour',
                updatable: true,
                developerName: 'developerName',
              },
              {
                fromObject: 'ProductCategory',
                toObject: 'Uom',
                toField: 'KDollarAmount',
                fromField: 'ProfessionalServices',
                updatable: true,
                developerName: 'developerName',
              },
            ],
            productCategoryToRevenueModelMappings: [
              // {
              //   fromField: 'RecurringServices',
              //   developerName: 'developerName',
              //   updatable: true,
              //   fromObject: 'ProductCategory',
              //   toObject: 'RevenueModel',
              //   toField: 'OneTime',
              // },
              // {
              //   fromObject: 'ProductCategory',
              //   toObject: 'RevenueModel',
              //   fromField: 'PhysicalGoods',
              //   toField: 'Recurring',
              //   updatable: true,
              //   developerName: 'developerName',
              // },
              // {
              //   fromObject: 'ProductCategory',
              //   toObject: 'RevenueModel',
              //   toField: 'OneTime',
              //   fromField: 'ProfessionalServices',
              //   updatable: true,
              //   developerName: 'developerName',
              // },
            ],
            productCategoryToTaxCodeMappings: [
              {
                fromField: 'RecurringServices',
                developerName: 'developerName',
                updatable: true,
                fromObject: 'ProductCategory',
                toObject: 'TaxCode',
                toField: 'taxcode1',
              },
              {
                fromObject: 'ProductCategory',
                toObject: 'TaxCode',
                fromField: 'PhysicalGoods',
                toField: 'taxcode2',
                updatable: true,
                developerName: 'developerName',
              },
              {
                fromObject: 'ProductCategory',
                toObject: 'TaxCode',
                toField: 'taxcode3',
                fromField: 'ProfessionalServices',
                updatable: true,
                developerName: 'developerName',
              },
            ],
          },
        },
      ],
    },
    {
      name: 'Lifecycle Manager',
      subCategories: [
        {
          name: 'Quotes and Orders',
          icon: <MultiBoxIcon />,
          settings: {
            autoListSellableProducts: 'true',
            useExistingOrder: 'true',
            useExistingQuote: 'true',
            createNewQuote: 'true',
            createNewOrder: 'true',
            opportunityStageToGenerateOrder: 'Closed Won',
            allowCancelActiveOrder: 'false',
            allowQuoteSplit: false,
            allowOrderSplit: false,
            metricsCalculationSchedulerEnabled: 'false',
            Auto_Map_Line_Items: true,
            Auto_Map_Line_Buckets: false,
            finalizeQuoteWithDraftBucketsBehavior: 'individualOrders',
            lineBucketFieldMappings: [
              {
                fromObject: 'LineBucket__c',
                toObject: 'Order',
                fromField: 'Name',
                toField: 'Name',
                updatable: false,
                developerName: 'Name',
              },
              {
                fromObject: 'LineBucket__c',
                toObject: 'Order',
                fromField: 'BillingAccount__c',
                toField: 'AccountId',
                updatable: true,
                developerName: 'AccountId',
              },
            ],
            previewInvoicesForQuotesAndOrders: 'true',
            productConfiguratorDefaultPageSize: 20,
            lineEditorDefaultPageSize: 20,
            rampItemDefaultVisibility: 'expanded',
            autoApplyPriceTagsToChildItems: false,
          },
        },
        {
          name: 'Subscriptions',
          icon: <FoldersIcon />,
          labels: ['Default Subscription Term'],
          settings: {
            defaultSubscriptionTerm: '12',
            subscriptionTermBasis: '30/360',
            updateQuantity: 'true',
            updateSubscriptionTerm: 'true',
            renew: 'true',
            pauseAndResume: 'false',
            skip: 'false',
            cancel: 'true',
            reconfigure: 'true',
            subscriptionTermDimension: 'Month',
            generateArrByPeriodLastExecutionDate: '2023-11-13T23:17:08.000+0000',
          },
        },
        {
          name: 'Renewals',
          icon: <FoldersIcon />,
          labels: ['Default Renewal Term'],
          settings: {
            defaultRenewalTerm: '0',
            renewBeforeSubscriptionEndDate: '30',
            renewalEmailTemplate: '',
            autoRenewDaysBeforeSubscriptionEnd: '2',
            autoRenewGenerateQuotesDaysBefore: '30',
            autoRenewGenerateOpportunities: 'true',
            autoRenewGenerateQuotes: 'true',
            autoRenewStatusMonthsToKeep: '6',
            renewalTermDimension: 'Month',
            allowEarlyRenewals: 'true',
          },
        },
        {
          name: 'Customer Lifecycle',
          icon: <FoldersIcon />,
          settings: {
            showGeneralInfoOnCustomerLifecycle: 'true',
            showOrderOnCustomerLifecycle: 'true',
            showSubscriptionOnCustomerLifecycle: 'true',
            showAssetOnCustomerLifecycle: 'true',
            showEntitlementOnCustomerLifecycle: 'true',
            showInvoiceOnCustomerLifecycle: 'false',
            showUsageOnCustomerLifecycle: 'false',
            showCreditOnCustomerLifecycle: 'false',
            showCreditMemoOnCustomerLifecycle: 'false',
            showRevContractsOnCustomerLifecycle: 'false',
          },
        },
        {
          name: 'Order Forms',
          icon: <FoldersIcon />,
          settings: {
            orderFormEmailTemplate: 'OrderFormEmailTemplate',
            attachQuotePDFToQuote: true,
            attachQuotePDFToOpportunity: false,
          },
        },
      ],
    },
    {
      name: 'Revenue Builder',
      subCategories: [
        {
          name: 'Bundle Builder',
          icon: <StackIcon />,
        },
        {
          name: 'Pricing Attributes',
          icon: <DollarCircleIcon />,
        },
        {
          name: 'Quantity Tier Attributes',
          icon: <DollarCircleIcon />,
        },
        {
          name: 'Bundle Suite Templates',
          icon: <LayoutIcon />,
        },
      ],
    },
    {
      name: 'Revenue Manager',
      subCategories: [
        {
          name: 'Billing Schedules',
          icon: <WalletIcon />,
          settings: {
            enableProrateCreditsForPartialTerm: 'true',
          },
        },
        {
          name: 'Revenue Settings',
          icon: <WalletIcon />,
        },
        {
          name: 'Collections',
          labels: ['Payment Rules'],
          icon: <PaymentIcon />,
        },
      ],
    },
    {
      name: 'Integrations',
      subCategories: [
        {
          name: 'Integration Overview',
          icon: <FolderOpenIcon />,
          settings: {
            Salesforce: {
              description: 'Connect to Salesforce to manage your customer lifecycle',
              getIcon: () => {
                return <SalesforceSettingIcon style={{ height: 80, width: 110, paddingLeft: 8 }} />;
              },
              navCategory: 'Salesforce Integration',
            },
            Stripe: {
              description: 'Connect to Stripe to manage your collections',
              getIcon: () => {
                return (
                  <div style={{ color: '#6239EB' }}>
                    <StripeSettingIcon
                      style={{ height: 80, width: 120, paddingLeft: 8 }}
                      viewBox={'0 0 120 60'}
                    />
                  </div>
                );
              },
            },
            RightRev: {
              description: 'Connect to RightRev to manage your revenue recogniton',
              getIcon: () => {
                return <RightRevSettingIcon style={{ height: 80, width: 120 }} />;
              },
              navCategory: 'RightRev Integration',
            },
            Avalara: {
              description: 'Connect to Avalara to manage your taxes',
              getIcon: () => {
                return <AvalaraSettingIcon style={{ height: 80, width: 120 }} />;
              },
              navCategory: 'Avalara Integration',
            },
            AvalaraEInvoicing: {
              description: 'Connect to Avalara to manage your taxes',
              getIcon: () => {
                return <AvalaraSettingIcon style={{ height: 80, width: 120 }} />;
              },
              navCategory: 'Avalara E-Invoicing',
            },
            QuickBooks: {
              description: 'Connect to QuickBooks to manage your invoices',
              getIcon: () => {
                return (
                  <QuickbookSettingIcon
                    style={{ width: 170, color: '#6239EB' }}
                    viewBox={'0 0 120 48'}
                  />
                );
              },
            },
            Webhook: {
              description: 'Webhook Endpoints',
              getIcon: () => {
                return (
                  <QuickbookSettingIcon
                    style={{ width: 170, color: '#6239EB' }}
                    viewBox={'0 0 120 48'}
                  />
                );
              },
              navCategory: 'Webhook Endpoints',
            },
          },
        },
        {
          name: 'Stripe Integration',
          icon: <StripeSettingIcon style={{ height: 48, width: 170 }} viewBox={'0 0 120 48'} />,
        },
        {
          name: 'Salesforce Integration',
          icon: <SalesforceIcon />,
        },
        {
          name: 'Avalara Integration',
          icon: (
            <AvalaraSettingIcon
              style={{ height: 56, width: 170, color: '#6239EB' }}
              viewBox={'-20 10 140 56'}
            />
          ),
        },
        {
          name: 'RightRev Integration',
          icon: (
            <RightRevSettingIcon
              style={{ height: 56, width: 170, color: '#6239EB' }}
              viewBox={'0 0 120 48'}
            />
          ),
        },
        {
          name: 'QuickBooks Integration',
          icon: (
            <QuickbookSettingIcon
              style={{ height: 56, width: 170, color: '#6239EB' }}
              viewBox={'0 0 120 48'}
            />
          ),
        },
        {
          name: 'Webhook Endpoints',
          icon: (
            <QuickbookSettingIcon
              style={{ height: 56, width: 170, color: '#6239EB' }}
              viewBox={'0 0 120 48'}
            />
          ),
        },
        {
          name: 'NetSuite Integration',
          icon: (
            <NetSuiteLogoIcon
              style={{ height: 56, width: 170, color: '#6239EB' }}
              viewBox={'0 0 120 48'}
            />
          ),
        },
      ],
    },
  ],
};

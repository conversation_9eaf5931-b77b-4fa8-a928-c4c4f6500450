export default [
  {
    apiName: 'orderNumber',
    name: 'Order Number',
    type: 'autoNumber',
    creatable: false,
    updatable: false,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    autoNumberSetting: {
      displayFormat: '{00000000}',
      startValue: 0,
    },
  },
  {
    apiName: 'ownerId',
    name: 'Order Owner',
    type: 'bLookup',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    lookupRelation: {
      referenceTo: 'UserProfile',
      referenceField: 'id',
      relationName: 'owner',
      relationLabel: 'Order Owner',
    },
  },
  {
    apiName: 'status',
    name: 'Status',
    type: 'pickList',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    defaultValue: 'Draft',
    required: false,
    unique: false,
    valueSet: {
      apiName: 'OrderStatus',
      name: 'Order Status',
      defaultValue: 'Draft',
      alphabeticallyOrdered: false,
      valuePairs: [
        {
          apiName: 'Draft',
          name: 'Draft',
          orderNumber: 1,
        },
        {
          apiName: 'Active',
          name: 'Active',
          orderNumber: 2,
        },
        {
          apiName: 'Cancelled',
          name: 'Cancelled',
          orderNumber: 3,
        },
      ],
    },
  },
  {
    apiName: 'orderPlacedDate',
    name: 'Order Placed Date',
    type: 'date',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'orderTCV',
    name: 'Order TCV',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'orderACV',
    name: 'Order ACV',
    type: 'currency',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'nextInvoiceDate',
    name: 'Next Invoice Date',
    type: 'date',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
];
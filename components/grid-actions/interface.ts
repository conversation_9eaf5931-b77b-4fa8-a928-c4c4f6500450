import type React from 'react';
import type { MouseEventHandler } from 'react';

import type { SvgIconComponent } from '@material-ui/icons';
import type { PopupState } from 'material-ui-popup-state/hooks';

import type { ColumnConfig } from '../metadata/interface';

export interface Props {
  actions: ButtonProps[];
  actionHelpers: any;
  additionalActions?: any;
}

export interface ActionHelperProps {
  objectApiName: string;
  row: any;
  updateObjects: Function;
  handleDelete: Function;
  iconStyles?: object;
  viewBox?: string;
  currentRowId?: string;
}

export interface ButtonProps {
  id?: string;
  tooltipText?: NonNullable<React.ReactNode>;
  iconAlt?: string;
  itemProps?: object;
  Icon?: any;
  onClickHandler?: Function;
  Renderer?: Function;
  ToolTip?: any;
  style?: React.CSSProperties;
}

export interface MuiButtonProps extends ButtonProps {
  Icon: SvgIconComponent;
}

export interface PopoverButtonProps extends MuiButtonProps {
  PopoverComponent: React.FC<any>;
  popoverArgs: any;
}

export interface GridActionProps {
  actions: ButtonProps[];
  actionHelpers: any;
  additionalActions?: any;
}

export interface AdditionalActionProps {
  shouldDisplayHandler?: (cellProps: CellRendererProps) => boolean;
  handleClick?: MouseEventHandler;
  title?: string;
  icon?: any;
}
export interface CellRendererProps {
  actionHelpers?: ActionHelperProps;
  additionalActions?: AdditionalActionProps[];
  icon: any;
  popupState: PopupState;
}

export interface CellRendererRowProps {
  row: any;
  column?: ColumnConfig;
  helper: any;
}

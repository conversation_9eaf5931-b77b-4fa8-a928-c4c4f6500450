export const GraphQLConverter = new Map();

/**
 * Note: much of this code was taken from https://github.com/logipro/logi-filter-builder and refractored for our case to construct filters into GraphQL
 * TODO: Refractor to make this into a functional component and clean up code
 */
const DateConverter = (value: string) => {
  // @ts-ignore
  if (!value || isNaN(new Date(value))) {
    if (typeof value === 'string' && 'false' !== value && 'true' !== value) {
      const newValue = value.replace(/"+/g, '');
      return `"${newValue}"`;
    }
    const d = new Date();
    return `"${d.toISOString().substring(0, 10)}"`;
  }
  const d = new Date(value);
  return `"${d.toISOString().substring(0, 10)}"`;
};

const DateTimeConverter = (value: string) => {
  // @ts-ignore
  if (!value || isNaN(new Date(value))) {
    const newValue = value.replace(/"+/g, '');
    return `"${newValue}"`;
  }
  const d = new Date(value);
  return `"${d.toISOString()}"`;
};

const unquote = (str: string) => {
  if ((str.startsWith("'") && str.endsWith("'")) || (str.startsWith('"') && str.endsWith('"'))) {
    return str.slice(1, str.length - 1);
  }
  return str;
};

const PickListConverter = (value: Array<string> | string) => {
  if (Array.isArray(value)) {
    let arrayValues = `[`;
    value.forEach((item) => {
      arrayValues += `"${item}", `;
    });
    if (value.length > 0) {
      arrayValues = arrayValues.substring(0, arrayValues.length - 2);
    }
    arrayValues += ']';
    return arrayValues;
  } else if (typeof value === 'string' && value.includes(',')) {
    return (
      '[' +
      value
        .split(',')
        .map((_) => `"${unquote(_.trim())}"`)
        .join(', ') +
      ']'
    );
  }
  return `"${value}"`;
};

GraphQLConverter.set('date', DateConverter).set('Date', DateConverter);
GraphQLConverter.set('dateTime', DateTimeConverter);

GraphQLConverter.set('autoNumber', (value: string) => `"${value}"`);

GraphQLConverter.set('integer', (value: string) => value);
GraphQLConverter.set('Decimal', (value: string) => value);
GraphQLConverter.set('decimal', (value: string) => value);
GraphQLConverter.set('Double', (value: string) => value);

GraphQLConverter.set('text', (value: string) => `"${value}"`);
GraphQLConverter.set('TextArea', (value: string) => `"${value}"`);

GraphQLConverter.set('string', (value: string) => `"${value}"`);
GraphQLConverter.set('String', (value: string) => `"${value}"`);

GraphQLConverter.set('id', (value: string) => `"${value}"`);

GraphQLConverter.set('phone', (value: string) => `"${value}"`);

GraphQLConverter.set('email', (value: string) => `"${value}"`);

GraphQLConverter.set('currency', (value: string) => `"${value}"`);

GraphQLConverter.set('longText', (value: string) => `"${value}"`);

GraphQLConverter.set('bId', (value: string) => `"${value}"`);

GraphQLConverter.set('bLookup', (value: string) => `"${value}"`);
GraphQLConverter.set('lookup', (value: string) => `"${value}"`);
GraphQLConverter.set('Reference', (value: string) => `"${value}"`);

GraphQLConverter.set('bMasterDetail', (value: string) => `"${value}"`);

GraphQLConverter.set('richText', (value: string) => `"${value}"`);

GraphQLConverter.set('pickList', PickListConverter);
GraphQLConverter.set('Picklist', PickListConverter);
GraphQLConverter.set('multiPickList', PickListConverter);
GraphQLConverter.set('MultiPicklist', PickListConverter);
GraphQLConverter.set('currencyIsoCode', PickListConverter);

GraphQLConverter.set('boolean', (value: string) => (value === 'true' ? 'true' : 'false'));
GraphQLConverter.set('Boolean', (value: string) => (value === 'true' ? 'true' : 'false'));

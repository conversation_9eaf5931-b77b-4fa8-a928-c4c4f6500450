import type { StripeTaxConfig } from '../stripe-connection';

export interface TaxIntegration {
  id: string;
  status: string;
  integrationType: string;
  environment: string;
  accountId: string;
  licenseKey: string;
  companyCode: string;
  enableLogging: boolean;
  commitDocuments: boolean;
}

export interface TaxIntegrationConfig extends Omit<TaxIntegration, 'environment'> {
  environment: boolean;
}

export interface TaxIntegrationResponse {
  id: string;
  status: string;
  integrationType: string;
  config: AvalaraConfig | StripeTaxConfig;
}

export interface AvalaraConfig {
  environment: string;
  accountId: string;
  licenseKey: string;
  companyCode: string;
  enableLogging: boolean;
  commitDocuments: boolean;
}

export interface UpsertTaxIntegrationRequestBody {
  integrationType: string;
  environment: string;
  accountId: string;
  licenseKey: string;
  companyCode: string;
  enableLogging: boolean;
  commitDocuments: boolean;
  id?: string;
}

export interface Company {
  id: string;
  code: string;
  name: string;
  accountId: string;
  taxpayerIdNumber: string;
  baseCurrencyCode: string;
  defaultCountry: string;
  isDefault: boolean;
  isReportingEntity: boolean;
}

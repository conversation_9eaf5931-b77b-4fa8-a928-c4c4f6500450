import React, { useContext, useState } from 'react';
import { ChangeSet, EditingState } from '@devexpress/dx-react-grid';
import {
  Table,
  TableColumnResizing,
  TableEditRow,
  Grid as TableGrid,
  TableHeaderRow,
} from '@devexpress/dx-react-grid-material-ui';
import { Checkbox } from '@material-ui/core';
import shortUUID from 'short-uuid';
import {
  BooleanTypeProvider,
  CurrencyTypeProvider,
  SwitchTypeProvider,
  NumberTypeProvider,
} from '../ruby-grid/data-providers';
import { SelectTypeProvider } from './select-type-provider';
import { DateTypeProvider } from '../ruby-grid/data-providers';
import TableComponent from '../table-component';
import { LookupDataProvider } from '../price-book-entry-grid/LookupDataProvider';
import EditColumn from './edit-column';
import type { Props } from '../price-book-entry-grid/interface';
import { RowEditStateFlag, ValidationStatusType } from '../price-book-entry-grid/interface';
import { MultiPicklistProvider } from '../price-book-entry-grid/multi-picklist-data-provider';
import TableRow from '../price-book-entry-grid/table-row';
import { TextDataProvider } from './text-data-provider';

const defaultProps = {};

const EditBooleanCell = (props: any) => {
  return (
    <Table.Cell {...props}>
      <Checkbox
        readOnly={!props.editingEnabled}
        onChange={(_, checked) => {
          props.onValueChange(checked);
        }}
        checked={!!props.value}
        size="small"
      />
    </Table.Cell>
  );
};

const AvalaraEInvoicingConnectionMappingsEditor: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const {
    columns,
    rows,
    setRows,
    deletedRowIds,
    setDeletedRowIds,
    rowChanges,
    setRowChanges,
    editingRowIds,
    setEditingRowIds,
    columnWidths,
    locale,
    getCurrencyIsoCode,
    validationStatus = new Map(),
    setValidationStatus = () => {},
    //@ts-ignore
    activeFilterRow,
  } = props;

  const getRowId = (row: any) => {
    return row.id;
  };

  const doValidate = async (rs: any[]) => {
    const status = await props.validate(rs);
    setValidationStatus(status);
  };

  const commitChanges = (changes: ChangeSet) => {
    const { deleted, added, changed } = changes;
    let changedRows = [];
    if (added) {
      // should not happen, we use duplicate rather than add
    }
    if (deleted) {
      const deletedSet = new Set(deleted);
      changedRows = rows
        .filter((row) => !(row.id.includes('_System') && deletedSet.has(row.id)))
        .map((row) => {
          if (deletedSet.has(row.id)) {
            row[RowEditStateFlag] = 'deleted';
          }
          return row;
        });
    }
    if (changed) {
      changedRows = rows.map((row) => {
        if (changed[row.id]) {
          const next = { ...row, ...changed[row.id] };
          if (!next[RowEditStateFlag]) {
            next[RowEditStateFlag] = 'changed';
          }
          if (next.id.includes('system')) {
            next.id = next.id.replace('system', 'update');
          }
          if (!next.id.includes('system')) {
            next.id = next.id + '_update';
          }
          return next;
        } else {
          return row;
        }
      });
    }
    setRows(changedRows);
    doValidate(changedRows);
  };

  const forTypes = (types: string[]) => {
    return columns.filter((_) => types.indexOf(_.type) >= 0).map((_) => _.name);
  };

  const columnExtensions: EditingState.ColumnExtension[] = columns.map((column) => {
    return {
      columnName: column.name,
      editingEnabled: column.updatable,
    };
  });

  //custom table cell to render by validation status
  const Cell = React.useCallback(
    (cellProps: Table.DataCellProps) => {
      const {
        tableRow: { rowId },
        column: { name: columnName },
      } = cellProps;
      let style: React.CSSProperties = {};
      let error;
      if (rowId) {
        const columnStatus = validationStatus.get(rowId)?.[columnName];
        const valid = !columnStatus || columnStatus.isValid;
        style = {
          ...(!valid ? { border: '1px solid red' } : null),
        };
        error = columnStatus?.error || '';
      }

      const { children, ...restProps } = cellProps;

      return (
        <Table.Cell
          {...restProps}
          style={style}
          // title={title}
        >
          {children}
          {error && (
            <div style={{ color: 'red' }}>
              <span>{error}</span>
            </div>
          )}
        </Table.Cell>
      );
    },
    [validationStatus],
  );

  return (
    <TableGrid rows={rows} columns={columns} getRowId={getRowId}>
      <SelectTypeProvider for={forTypes(['picklist', 'pickList'])} />
      <CurrencyTypeProvider
        for={forTypes(['currency'])}
        getCurrencyIsoCode={getCurrencyIsoCode}
        locale={locale}
      />
      <SwitchTypeProvider for={forTypes(['switch'])} />
      <BooleanTypeProvider
        for={forTypes(['boolean'])}
        columnExtensions={[]}
        editCell={EditBooleanCell}
      />
      <LookupDataProvider for={forTypes(['bLookup'])} />
      <TextDataProvider for={forTypes(['text'])} />
      <MultiPicklistProvider for={forTypes(['multiPicklist', 'multiPickList'])} />
      <DateTypeProvider for={forTypes(['date'])} editorMode="date" />
      <NumberTypeProvider for={forTypes(['decimal'])} />
      <EditingState
        editingRowIds={editingRowIds}
        rowChanges={rowChanges}
        onRowChangesChange={setRowChanges}
        onEditingRowIdsChange={(id) => {
          setEditingRowIds(id);
        }}
        onCommitChanges={commitChanges}
        onDeletedRowIdsChange={(toDeleteRowIds) => {
          setDeletedRowIds([...deletedRowIds, ...toDeleteRowIds]);
        }}
        onAddedRowsChange={(newAddedRows) => {
          //no need for now
        }}
        columnExtensions={[]}
      />
      <Table tableComponent={TableComponent} rowComponent={TableRow} cellComponent={Cell} />
      {columnWidths && (
        <TableColumnResizing defaultColumnWidths={columnWidths} resizingMode={'widget'} />
      )}
      <TableHeaderRow />
      <TableEditRow />

      {!activeFilterRow?.active && (
        <EditColumn
          onDuplicateRow={({ row }, actions, getters) => {
            const rowIndex = rows.findIndex((_) => _.id === row.id);
            const cloned = Object.assign({}, row, {
              id: shortUUID().generate().toString() + '_update',
            });
            cloned[RowEditStateFlag] = 'added';
            const afterInsert = [
              ...rows.slice(0, rowIndex + 1),
              cloned,
              ...rows.slice(rowIndex + 1),
            ];
            setRows(afterInsert);
            setEditingRowIds([...editingRowIds, cloned.id]);
          }}
          onCancelDeleteRow={({ row }) => {
            const nextRows = rows?.map((_) => {
              if (_.id === row.id) {
                delete _[RowEditStateFlag];
              }
              return _;
            });
            setRows(nextRows);
          }}
          onEditPriceTags={props.onEditPriceTags}
        />
      )}
    </TableGrid>
  );
};

export default AvalaraEInvoicingConnectionMappingsEditor;

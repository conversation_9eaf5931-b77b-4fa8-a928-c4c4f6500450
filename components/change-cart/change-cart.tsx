import React, { useState } from 'react';

import { <PERSON><PERSON>, <PERSON>, Typography } from '@material-ui/core';
import Drawer from '@material-ui/core/Drawer';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import CloseIcon from '@material-ui/icons/Close';
import FlashOnRoundedIcon from '@material-ui/icons/FlashOnRounded';

import { useRubySnackbarContext } from '../ruby-notifier';
import ChangeCartContent, { ExpressCheckoutError } from './change-cart-content';
import { getTotalNumChanges } from './change-order-util';
import CheckoutDialog from './checkout-dialog';
import ExpressCheckoutDialog from './express-checkout-dialog';
import type {
  ChangeGroup,
  ChangeItem,
  ChangeItemRequest,
  ExpressCheckoutRequest,
  Props,
} from './interface';
import { getValidateChangeItemsResult } from './change-cart-helper';
import { validateConflictingChanges } from '../subscription-card/subscription-action-dialog-validation-util';

const defaultProps = {};

const useStyles = makeStyles({
  list: {
    padding: '40px',
    width: 650,
  },
  closeIcon: {
    cursor: 'pointer',
    opacity: 0.8,
    paddingBottom: '10px',
  },
  changeCardContainer: {
    paddingTop: '24px',
  },
  title: {
    fontSize: '1.5rem',
    fontWeight: 500,
    color: '#000000',
  },
  expressCheckout: {
    cursor: 'pointer',
  },
});

const ChangeCart: React.FC<Props> = (userProps: Props) => {
  const props = { ...defaultProps, ...userProps };

  const {
    open,
    handleOpenChangeCart,
    changeGroups,
    handleRemoveChangesFromCart,
    handleRemoveMultipleGroupsFromCart,
    handleRemoveChangeItems,
    executeQuery,
    orderMetadata,
    quoteMetadata,
    opportunityMetadata,
    navigateToQuoteBuilder,
    rubySettings,
    customerId,
    childrenCustomerIds,
    checkOut,
    handleNavigateToOrder,
  } = props;

  const classes = useStyles();
  const [openCheckoutDialog, setOpenCheckoutDialog] = useState(false);
  const [openExpressCheckoutDialog, setOpenExpressCheckoutDialog] = useState(false);
  const [checkedOutChangeGroup, setCheckedOutChangeGroup] = useState<ChangeGroup>();
  const totalNumChanges = getTotalNumChanges(changeGroups);
  const [expressCheckoutErrors, setExpressCheckoutErrors] = useState<ExpressCheckoutError[]>([]);
  const snackbarService = useRubySnackbarContext();

  const handleExpressCheckout = async ({
    orderStatusOption,
    includeChildren,
  }: {
    orderStatusOption: string;
    includeChildren: boolean;
  }) => {
    setOpenExpressCheckoutDialog(false);
    if (!checkOut) {
      return;
    }

    const changeGroupsToCheckout = Object.values(changeGroups).filter(
      (changeGroup) =>
        changeGroup.customer.id === customerId ||
        (includeChildren && childrenCustomerIds?.includes(changeGroup.customer.id)),
    ) as ChangeGroup[];

    const checkedOutGroupIds: string[] = changeGroupsToCheckout.map((cg) => cg.id);

    const assetChanges: ChangeItemRequest[] = changeGroupsToCheckout
      .flatMap((cg) => cg.changeItems as ChangeItem[])
      .filter(
        (ci) =>
          !!ci &&
          (ci.request.objectType === 'Subscription' ||
            ci.request.objectType === 'Asset' ||
            ci.request.objectType === 'Entitlement'),
      )
      .map((ci) => ({
        ...ci.request,
        startDate: ci.request.startDate || ci.request.subscriptionStartDate,
        assetNumber: ci.request.assetNumber || ci.asset.name,
        evergreen: ci.asset?.evergreen,
        billingAccountId: ci.asset?.billingAccountId,
        milestoneIds: ci.asset?.milestoneIds,
        billingPeriod: ci.asset?.billingPeriod,
      }));

    const expressChecoutRequest: ExpressCheckoutRequest = {
      options: {
        activateOrder: orderStatusOption !== 'draft',
        proceedOption: 'CreateOrder',
      },
      assetChanges: assetChanges,
    };

    try {
      const result = await checkOut(expressChecoutRequest);
      if (result) {
        handleRemoveMultipleGroupsFromCart(checkedOutGroupIds);
        snackbarService.showSnackbar(
          'confirm',
          'The request has been successfully submitted.',
          //@ts-ignore
          <div>
            Processing the request may take some time. Please navigate to{' '}
            <Link onClick={handleNavigateToOrder} style={{ fontWeight: 'bold', color: '#6239eb' }}>
              the orders
            </Link>
            {` `}
            to see the results.
          </div>,
        );
      } else {
        snackbarService.showSnackbar(
          'error',
          'Error',
          'An error occured when checking out your changes.',
        );
      }
    } catch (err) {
      snackbarService.showSnackbar(
        'error',
        'Error',
        'An error occured when checking out your changes.',
      );
    }
  };

  return (
    <>
      <Drawer
        anchor="right"
        variant="temporary"
        open={open}
        onClose={(event: React.KeyboardEvent | React.MouseEvent) => {
          if (
            event.type === 'keydown' &&
            ((event as React.KeyboardEvent).key === 'Tab' ||
              (event as React.KeyboardEvent).key === 'Shift')
          ) {
            return;
          }
          handleOpenChangeCart(false);
        }}
      >
        <Grid container className={classes.list}>
          <Grid item xs={12} container justifyContent="space-between" alignItems="center">
            <Grid item style={{ display: 'flex' }}>
              <Typography className={classes.title} variant="h2">
                Cart ({totalNumChanges})
              </Typography>
              {checkOut && totalNumChanges > 0 && rubySettings.createNewOrder && (
                <div style={{ color: '#0EB3D3' }}>
                  <Button
                    startIcon={<FlashOnRoundedIcon />}
                    variant="text"
                    onClick={async () => {
                      const changeGroupsToCheckout = Object.values(changeGroups) as ChangeGroup[];
                      let errorMessages: ExpressCheckoutError[] = [];

                      try {
                        validateConflictingChanges(changeGroupsToCheckout);
                      } catch (err) {
                        if (err instanceof Error) {
                          errorMessages = errorMessages.concat({
                            id: 'bundle-addon-conflict',
                            messages: [err.message],
                            isExpressCheckOut: true,
                          });
                        }
                      }

                      changeGroupsToCheckout.forEach((x) => {
                        const messages = getValidateChangeItemsResult(x.changeItems, rubySettings);
                        if (messages.length > 0) {
                          errorMessages = errorMessages.concat({
                            id: x.id,
                            messages: messages,
                            isExpressCheckOut: true,
                          });
                        }
                      });
                      if (errorMessages.length > 0) {
                        setExpressCheckoutErrors(errorMessages);
                        return;
                      } else {
                        setExpressCheckoutErrors([]);
                      }

                      setOpenExpressCheckoutDialog(true);
                    }}
                    style={{ marginLeft: '20px' }}
                    color="inherit"
                  >
                    Express Checkout
                  </Button>
                </div>
              )}
            </Grid>

            <CloseIcon
              className={classes.closeIcon}
              onClick={(event: React.KeyboardEvent | React.MouseEvent) => {
                if (
                  event.type === 'keydown' &&
                  ((event as React.KeyboardEvent).key === 'Tab' ||
                    (event as React.KeyboardEvent).key === 'Shift')
                ) {
                  return;
                }
                handleOpenChangeCart(false);
                setExpressCheckoutErrors([]);
              }}
            />
          </Grid>
          <ChangeCartContent
            changeGroups={changeGroups}
            handleRemoveChangeItems={handleRemoveChangeItems}
            handleRemoveChangesFromCart={handleRemoveChangesFromCart}
            customerId={customerId}
            onCheckOut={async (changeGroup: ChangeGroup) => {
              setOpenCheckoutDialog(true);
              setCheckedOutChangeGroup(changeGroup);
            }}
            rubySettings={rubySettings}
            expressCheckoutErrors={expressCheckoutErrors}
          />
        </Grid>
      </Drawer>
      <CheckoutDialog
        executeQuery={executeQuery}
        orderMetadata={orderMetadata}
        quoteMetadata={quoteMetadata}
        opportunityMetadata={opportunityMetadata}
        navigateToQuoteBuilder={navigateToQuoteBuilder}
        handleOpenChangeCart={handleOpenChangeCart}
        checkedOutChangeGroup={checkedOutChangeGroup}
        open={openCheckoutDialog}
        handleOpen={(newOpen: boolean) => setOpenCheckoutDialog(newOpen)}
        rubySettings={rubySettings}
      />
      <ExpressCheckoutDialog
        open={openExpressCheckoutDialog}
        onClose={() => setOpenExpressCheckoutDialog(false)}
        handleExpressCheckout={handleExpressCheckout}
      />
    </>
  );
};

export default ChangeCart;

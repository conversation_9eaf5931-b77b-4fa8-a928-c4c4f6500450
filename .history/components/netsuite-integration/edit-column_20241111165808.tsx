import React from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON>, Plugin, Template, TemplateConnector } from '@devexpress/dx-react-core';
import { Table } from '@devexpress/dx-react-grid';
import { TableEditColumn } from '@devexpress/dx-react-grid-material-ui';
import { IconButton } from '@material-ui/core';
import Tooltip from '@material-ui/core/Tooltip';
import { makeStyles } from '@material-ui/core/styles';
import DeleteIcon from '@material-ui/icons/Delete';
import EditIcon from '@material-ui/icons/Edit';
import SaveIcon from '@material-ui/icons/Save';
import UndoIcon from '@material-ui/icons/Undo';

import { EditColumnProps, NSRowEditStateFlag } from './interface';

const TABLE_EDIT_COMMAND_TYPE = Symbol.for('editCommand');
const pluginDependencies = [{ name: 'EditingState' }, { name: 'Table' }];

const useStyles = makeSty<PERSON>({
  command: {
    '&:hover': {
      color: '#5132BB',
    },
  },
});

export type CommandProps = {
  id:
    | 'duplicate'
    | 'add'
    | 'edit'
    | 'delete'
    | 'commit'
    | 'cancel'
    | 'cancelDelete'
    | 'editPriceTags';
  text: string;
  onExecute: () => void | Promise<void>;
  Icon: React.FC<any>;
};

const Command: React.FC<CommandProps> = (props) => {
  const { onExecute, id, text, Icon } = props;
  const classes = useStyles();
  return (
    <Tooltip title={text || ''} placement="bottom" arrow>
      <IconButton onClick={onExecute} type="button">
        <Icon className={classes.command} />
      </IconButton>
    </Tooltip>
  );
};

export type IsSpecificRowFn = (tableRow: any) => boolean;

export const isAddedTableRow: IsSpecificRowFn = (tableRow) => {
  return tableRow.type?.toString() === 'Symbol(edit)' && tableRow.row?.id?.endsWith('_temp');
};

export const isEditTableRow: IsSpecificRowFn = (tableRow) =>
  tableRow.type?.toString() === 'Symbol(edit)';

export const isDeletedTableRow: IsSpecificRowFn = (tableRow) => {
  return tableRow.row[QBRowEditStateFlag] === 'deleted';
};

class EditColumn extends React.PureComponent<EditColumnProps> {
  render() {
    const tableColumnsComputed = ({ tableColumns }: Getters) => [
      ...(tableColumns || []),
      {
        width: 240,
        key: TABLE_EDIT_COMMAND_TYPE.toString(),
        type: TABLE_EDIT_COMMAND_TYPE,
      },
    ];

    const { onDeleteRow, onCommitRow } = this.props;

    return (
      <Plugin name="TableEditColumn" dependencies={pluginDependencies}>
        <Getter name="tableColumns" computed={tableColumnsComputed} />

        {/* Data Rows */}
        <Template
          name={'tableCell'}
          predicate={({ tableRow, tableColumn }: any) => {
            return tableRow.row && tableColumn.type === TABLE_EDIT_COMMAND_TYPE;
          }}
        >
          {(params: Table.CellProps) => {
            const rowIds = [params.tableRow.rowId];
            return (
              <TemplateConnector>
                {(getters, actions) => {
                  const row = params.tableRow.row;
                  const isAdded = isAddedTableRow(params.tableRow);
                  const isEdit = isEditTableRow(params.tableRow);
                  const isDeleted = isDeletedTableRow(params.tableRow);
                  return (
                    <TableEditColumn.Cell {...params} row={row}>
                      {!isEdit && !isDeleted && (
                        <Command
                          id={'edit'}
                          text={'Edit'}
                          Icon={EditIcon}
                          onExecute={() => {
                            actions.startEditRows({
                              rowIds: [row.id],
                            });
                          }}
                        />
                      )}
                      {isEdit && (
                        <Command
                          id={'cancel'}
                          text={'Cancel'}
                          Icon={UndoIcon}
                          onExecute={() => {
                            actions.cancelChangedRows({ rowIds: [row.id] });
                            actions.stopEditRows({ rowIds: [row.id] });
                          }}
                        />
                      )}
                      {isEdit && (
                        <Command
                          id="commit"
                          Icon={SaveIcon}
                          text={'commit'}
                          onExecute={() => {
                            if (onCommitRow) {
                              const success = onCommitRow({ row });
                              if (success) {
                                actions.commitChangedRows({ rowIds });
                                actions.stopEditRows({ rowIds });
                              }
                            }
                          }}
                        />
                      )}
                      {!isEdit && !isDeleted && (
                        <Command
                          id="delete"
                          text={'Delete'}
                          Icon={DeleteIcon}
                          onExecute={() => {
                            if (onDeleteRow) {
                              onDeleteRow({ row });
                            }
                          }}
                        />
                      )}
                    </TableEditColumn.Cell>
                  );
                }}
              </TemplateConnector>
            );
          }}
        </Template>
      </Plugin>
    );
  }
}

export default EditColumn;

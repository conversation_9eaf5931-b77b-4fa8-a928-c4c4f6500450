import type { CardA<PERSON>, GridViewMetadata, RubyField, RubyObject } from '../metadata/interface';
import { ProductRelationWithSubscription } from '../modals/subscription-bulkactions/types';
import type { RubySettings } from '../ruby-settings';

export interface Props {
  fieldSetMetadata: RubyField[];
  gridViewMetadata: GridViewMetadata;
  object: any;
  currencyIsoCode: string;
  objectMetadata: RubyObject;
  handleCardClick?: (object: any) => void;
  orderGridViewMetadata?: GridViewMetadata | null;
  orderFieldSetMetadata?: RubyField[] | null;
  subscriptionPricingFieldSet?: RubyField[] | null;
  actionEventHandler: (argument: {
    object: any;
    objectMetadata: RubyObject;
    action: CardAction;
    assetHierarchy?: any;
    ref?: string;
  }) => void | Promise<void>;
  getFieldSetMetadata?: (fieldSetApiName: string, objectApiName: string) => Promise<RubyField[]>;
  showSubmitBtn?: boolean;
  onShowDetails?: (object: any) => void;
  showDetails?: boolean;
  isPopup?: boolean;
  rubySettings?: RubySettings;
  locale?: string;
  transactionHub?: JSX.Element | null;
  pageSize?: number;
  handlePageSizeChange?: (newPageSize: number) => void;
  productRelationsByActionType?: Record<string, ProductRelationWithSubscription[]>;
}

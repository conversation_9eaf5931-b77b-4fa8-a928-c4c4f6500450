import React, { useRef } from 'react';

import Grid from '@material-ui/core/Grid';
import IconButton from '@material-ui/core/IconButton';
import Menu from '@material-ui/core/Menu';
import MenuItem from '@material-ui/core/MenuItem';
import Tooltip from '@material-ui/core/Tooltip';
import { makeStyles } from '@material-ui/core/styles';
import HighlightOffOutlinedIcon from '@material-ui/icons/HighlightOffOutlined';
import MoreHorizIcon from '@material-ui/icons/MoreHoriz';
import PlaylistAdd from '@material-ui/icons/PlaylistAdd';
import {
  bindMenu,
  bindPopover,
  bindToggle,
  bindTrigger,
  usePopupState,
} from 'material-ui-popup-state/hooks';

import EditIcon from '../edit-icon';
import {
  AvalaraLogoIcon,
  NetSuiteLogoIcon,
  QuickbookLogoIconForTrans,
  SalesforceLogoIcon,
  SalesforceSyncSuccessIcon,
  StripeLogoIcon,
  ViewIcon,
} from '../icons';
import type {
  ActionHelperProps,
  ButtonProps,
  CellRendererProps,
  MuiButtonProps,
  PopoverButtonProps,
  Props,
} from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  gridIcon: {
    background: 'transparent',
    border: 'none',
    color: '#6239eb',
    cursor: 'pointer',
    width: '18px',
    height: '18px',
  },
  salesforceIcon: {
    background: 'transparent',
    border: 'none',
    cursor: 'pointer',
    width: '18px',
    height: '18px',
  },
  webhookGridIcon: {
    background: 'transparent',
    border: 'none',
    color: '#6239eb',
    cursor: 'pointer',
    width: '10px',
    height: '10px',
  },
  container: {
    width: '100%',
  },
  list: {
    display: 'inline',
    marginTop: '10px',
    color: '#6e6e6e',
    '& ul': {
      paddingTop: 0,
      paddingBottom: 0,
    },
  },
  // more button dropdown list -> horizontal
  menuBtn: {
    borderColor: '#6239eb',
    backgroundColor: '#6239eb',
    color: '#fff',
    display: 'inline-block',
    padding: '10px 15px',
    fontSize: '.875rem',
    '&:hover': {
      backgroundColor: '#7d57fd',
    },
  },
  // more button dropdown list -> vertical
  menuBtnWithIcon: {
    borderColor: '#6239eb',
    backgroundColor: 'white',
    color: '#6239eb',
    width: '100%',
    padding: '10px 45px 10px 15px',
    fontSize: '.875rem',
    '&:hover': {
      backgroundColor: '#7d57fd',
      color: 'white',
    },
    display: 'flex',
    alignItems: 'center',
  },
  actionIcon: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: '5px',
  },
});

const GridActions: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  const classes = useStyles();

  const icon = useRef(null);

  const popupState = usePopupState({ variant: 'popover', popupId: 'popup' });

  return (
    <Grid container justifyContent="flex-start" direction="row" className={classes.container}>
      {props.actions.map((action, index) => {
        const { Renderer, itemProps, ...restProps } = action;
        const ActionRenderer = Renderer;
        const { actionHelpers, additionalActions } = props;
        if (ActionRenderer) {
          return (
            <Grid key={index}>
              <ActionRenderer
                {...{ ...restProps, actionHelpers, additionalActions, icon, popupState }}
              />
            </Grid>
          );
        }
        return <Grid />;
      })}
    </Grid>
  );
};

export const EditButton = ({
  id = 'edit-btn',
  tooltipText = 'Edit',
  iconAlt = 'edit-icon',
  itemProps = { xs: 3 },
  Icon = EditIcon,
  onClickHandler = (actionHelpers: any) => {
    const { history, editPath, row } = actionHelpers;
    history.push(`${editPath}/${row.id}`);
  },
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText || ''} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={() => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers);
              }
            }}
          >
            <Icon />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const QuickBooksButton = ({
  id = 'quickBooks-btn',
  tooltipText = 'quickBooks',
  iconAlt = 'quickBooks-icon',
  itemProps = { xs: 3 },
  Icon = QuickbookLogoIconForTrans,
  style = {},
  onClickHandler = (actionHelpers: any) => {
    const { history, editPath, row } = actionHelpers;
    history.push(`${editPath}/${row.id}`);
  },
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText || ''} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
            style={{ marginRight: '5px' }}
          >
            <Icon style={style} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const StripeButton = ({
  id = 'stripe-btn',
  tooltipText = 'stripe',
  iconAlt = 'stripe-icon',
  itemProps = { xs: 3 },
  Icon = StripeLogoIcon,
  style = {},
  onClickHandler = (actionHelpers: any) => {
    const { history, editPath, row } = actionHelpers;
    history.push(`${editPath}/${row.id}`);
  },
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText || ''} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
            style={{ marginRight: '5px' }}
          >
            <Icon style={style} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const AvalaraButton = ({
  id = 'avalara-btn',
  tooltipText = 'avalara',
  iconAlt = 'avalara-icon',
  itemProps = { xs: 3 },
  Icon = AvalaraLogoIcon,
  onClickHandler = (actionHelpers: any) => {
    const { history, editPath, row } = actionHelpers;
    history.push(`${editPath}/${row.id}`);
  },
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText || ''} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
            style={{ marginRight: '5px' }}
          >
            <Icon />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const SalesforceButton = ({
  id = 'salesforce-btn',
  tooltipText = 'salesforce',
  iconAlt = 'salesforce-icon',
  itemProps = { xs: 3 },
  Icon = SalesforceLogoIcon,
  onClickHandler = (actionHelpers: any) => {
    const { history, editPath, row } = actionHelpers;
    history.push(`${editPath}/${row.id}`);
  },
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText || ''} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
            style={{ marginRight: '5px' }}
          >
            <Icon />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const SalesforceSyncSuccessButton = ({
  id = 'salesforce-btn',
  tooltipText = 'salesforce',
  iconAlt = 'salesforce-icon',
  itemProps = { xs: 3 },
  Icon = SalesforceSyncSuccessIcon,
  style = {},
  onClickHandler = (actionHelpers: any) => {
    const { history, editPath, row } = actionHelpers;
    history.push(`${editPath}/${row.id}`);
  },
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText || ''} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.salesforceIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
            style={{ marginRight: '5px' }}
          >
            <Icon style={style} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const NetSuiteButton = ({
  id = 'netsuite-btn',
  tooltipText = 'netsuite',
  iconAlt = 'netsuite-icon',
  itemProps = { xs: 3 },
  Icon = NetSuiteLogoIcon,
  style = {},
  onClickHandler = (actionHelpers: any) => {
    const { history, editPath, row } = actionHelpers;
    history.push(`${editPath}/${row.id}`);
  },
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText || ''} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
            style={{ marginRight: '5px' }}
          >
            <Icon style={style} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const ToolTipButton = ({
  id = 'tooltip-button',
  Icon,
  itemProps = { xs: 3 },
  ToolTip,
  iconAlt,
  onClickHandler = () => {},
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <ToolTip {...actionHelpers} arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={() => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers);
              }
            }}
          >
            <Icon viewBox={actionHelpers?.viewBox} styles={actionHelpers?.iconStyles} />
          </IconButton>
        </ToolTip>
      );
    },
  };
};

export const GridButton = ({
  id = 'grid-btn',
  tooltipText = '',
  iconAlt,
  itemProps = { xs: 3 },
  Icon,
  onClickHandler = (actionHelpers: ActionHelperProps) => {},
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={() => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers);
              }
            }}
          >
            <Icon viewBox={actionHelpers?.viewBox} styles={actionHelpers?.iconStyles} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const MuiGridButton = ({
  id = '',
  tooltipText = '',
  itemProps = { xs: 3 },
  Icon,
  onClickHandler = (actionHelpers: ActionHelperProps) => {},
}: MuiButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText} placement="bottom" arrow>
          <IconButton
            className={classes.gridIcon}
            id={id}
            onClick={(event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
          >
            <Icon fontSize="small" className={classes.gridIcon} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const DeleteButton = ({
  id = 'delete-btn',
  tooltipText = 'Delete',
  itemProps = { xs: 3 },
  Icon = HighlightOffOutlinedIcon,
  onClickHandler = (actionHelpers: ActionHelperProps) => {
    // TODO: call API for delete button
  },
}: ButtonProps) => {
  return MuiGridButton({
    id,
    tooltipText,
    itemProps,
    Icon,
    onClickHandler,
  });
};

export const AddAdditionalChangesButton = ({
  id = 'add-additional-changes-btn',
  tooltipText = 'Add Additional Changes',
  itemProps = { xs: 3 },
  Icon = PlaylistAdd,
  onClickHandler = (actionHelpers: ActionHelperProps) => {},
}: ButtonProps) => {
  return MuiGridButton({
    id,
    tooltipText,
    itemProps,
    Icon,
    onClickHandler,
  });
};

export const ViewButton = ({
  id = 'view-btn',
  tooltipText = 'View',
  itemProps = { xs: 3 },
  Icon = ViewIcon,
  onClickHandler = (actionHelpers: ActionHelperProps) => {},
}: ButtonProps) => {
  return MuiGridButton({
    id,
    tooltipText,
    itemProps,
    Icon,
    onClickHandler,
  });
};

export const MoreButton = ({
  id = 'more-btn',
  itemProps = { xs: 3 },
  Icon = MoreHorizIcon,
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: (props: CellRendererProps) => {
      const { additionalActions, icon, popupState } = props;
      const classes = useStyles();

      if (!Array.isArray(additionalActions) || additionalActions.length === 0) {
        return null;
      }
      return (
        <IconButton {...bindToggle(popupState)} style={{ padding: 0 }}>
          <Icon ref={icon} fontSize="small" className={classes.gridIcon} />
          <Menu
            {...bindMenu(popupState)}
            getContentAnchorEl={null}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'center',
            }}
            // TODO: if this css doesn't work, check Popover.css and override the Mui-list class
            className={classes.list}
          >
            {additionalActions.map((action, index) => {
              let shouldDisplay = true;
              if (action.shouldDisplayHandler) {
                shouldDisplay = action.shouldDisplayHandler(props);
              }
              return shouldDisplay ? (
                <MenuItem
                  key={index}
                  className={action.icon ? classes.menuBtnWithIcon : classes.menuBtn}
                  onClick={action.handleClick}
                >
                  {action.icon && <span className={classes.actionIcon}>{action.icon}</span>}{' '}
                  {action.title}
                </MenuItem>
              ) : null;
            })}
          </Menu>
        </IconButton>
      );
    },
  };
};

export const CommentButton = ({
  id = 'comment-btn',
  tooltipText = '',
  iconAlt,
  itemProps = { xs: 3 },
  Icon,
  onClickHandler = (actionHelpers: ActionHelperProps) => {},
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText} placement="bottom" arrow>
          <IconButton
            id={`${id}`}
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
          >
            <Icon viewBox={actionHelpers?.viewBox} styles={actionHelpers?.iconStyles} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const WebhookButton = ({
  id = 'webhook-btn',
  tooltipText = 'webhook',
  iconAlt = 'webhook-icon',
  itemProps = { xs: 2 },
  Icon = ViewIcon,
  style = {},
  onClickHandler = (actionHelpers: any) => {
    const { history, editPath, row } = actionHelpers;
    history.push(`${editPath}/${row.id}`);
  },
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText || ''} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.webhookGridIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
          >
            <Icon style={style} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const IntegrationButton = ({
  id = 'integration-btn',
  tooltipText,
  iconAlt,
  itemProps = { xs: 2 },
  Icon = ViewIcon,
  style = {},
  onClickHandler = (actionHelpers: any) => {
    const { history, editPath, row } = actionHelpers;
    history.push(`${editPath}/${row.id}`);
  },
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText || ''} placement="bottom" arrow>
          <IconButton
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
          >
            <Icon style={style} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const DownloadButton = ({
  id = 'download-btn',
  tooltipText = '',
  iconAlt,
  itemProps = { xs: 3 },
  Icon,
  onClickHandler = (actionHelpers: ActionHelperProps) => {},
}: ButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      return (
        <Tooltip title={tooltipText} placement="bottom" arrow>
          <IconButton
            id={`${id}`}
            aria-label={iconAlt}
            className={classes.gridIcon}
            onClick={(event) => {
              if (typeof onClickHandler === 'function') {
                onClickHandler(actionHelpers, event);
              }
            }}
          >
            <Icon viewBox={actionHelpers?.viewBox} styles={actionHelpers?.iconStyles} />
          </IconButton>
        </Tooltip>
      );
    },
  };
};

export const PopoverButton = ({
  id = '',
  tooltipText = '',
  itemProps = { xs: 3 },
  Icon,
  style,
  PopoverComponent,
  popoverArgs,
}: PopoverButtonProps) => {
  return {
    id,
    itemProps,
    Renderer: ({ actionHelpers }: CellRendererProps) => {
      const classes = useStyles();
      const popupState = usePopupState({ variant: 'popover', popupId: 'popup' });
      if (!PopoverComponent) {
        return null;
      }
      return (
        <>
          <Tooltip title={tooltipText} placement="bottom" arrow>
            <IconButton className={classes.gridIcon} id={id} {...bindTrigger(popupState)}>
              <Icon fontSize="small" style={style} />
            </IconButton>
          </Tooltip>
          <PopoverComponent {...bindPopover(popupState)} {...popoverArgs} />
        </>
      );
    },
  };
};

export default GridActions;

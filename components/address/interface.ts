import React from 'react';
import { RubyFormControlProps } from '../form-field';
import { countryStateList } from './country-state-list';

export type Country = {
  name: string;
  code: string;
  states?: State[];
};

export type State = {
  name: string;
  code: string;
};

export interface CountryStateListService {
  countryList: Country[];
  getStateList: (country: Country) => State[] | Promise<State[]>;
}

export const CountryStateListContext = React.createContext<CountryStateListService>({
  countryList: countryStateList,
  getStateList: (country) => {
    return country.states || [];
  },
});

export default CountryStateListContext;

export interface Props extends RubyFormControlProps {}

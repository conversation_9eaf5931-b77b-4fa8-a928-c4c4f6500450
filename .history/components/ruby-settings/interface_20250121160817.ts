import React from 'react';
import type * as yup from 'yup';
import { GroupingConditionItem } from '../billing-grouping-attribute-form';
import type { DeleteFilterResponse } from '../filter-builder';
import type { Condition, RubyFilter } from '../graph-ql-query-constructor/interface';
import type { RubyField, RubyObject, User, ValuePair } from '../metadata';
import type { AfterFieldChangeListener, CardAction } from '../metadata/interface';
import type { PickListOption } from '../pick-list';
import type { QuickBooksConnectionService } from '../quickbooks-connection';
import type { Subscription } from '../revenue-builder-types';
import type { FunctionGroup, FunctionItem, FunctionRelation } from '../ruby-function-selector';
import type {
  GetIntegrationResponse,
  GetIntegrationSecretResponse,
} from '../salesforce-connection-context/interface';
import { Invoice } from '../self-service/models';
import { TemplateSchema } from '../template-builder-context/template-builder-context';

export type PricingAttribute = {
  apiName: string;
  name: string;
  description: string;
  mapping: string;
  options: string[];
};

export type FieldAsPricingAttribute = {
  apiName: string;
  name: string;
  description?: string;
  isPricingAttribute: boolean;
};

export type QuantityTierAttribute = {
  apiName: string;
  name: string;
  description?: string;
  mapping: string;
  active: boolean;
};

export type QuantityTierMappingOption = {
  apiName: string;
  name: string;
};

export interface PricingAttributeService {
  getAll: () => Promise<PricingAttribute[]> | PricingAttribute[];
  saveOrUpdate: (pricingAttribute: PricingAttribute) => void | Promise<void>;
  remove: (apiName: string) => void | Promise<void>;
  getAttributeMappingOptions: () => Promise<PickListOption[]> | PickListOption[];
  getPricingAttributeOptions: (mapping: string) => Promise<{ name: string; value: string }[]>;
  getFieldsAsPricingAttributes: () => Promise<FieldAsPricingAttribute[]>;
  saveFieldAsPricingAttribute: (fields: FieldAsPricingAttribute[]) => Promise<void>;
}

export interface QuantityTierAttributeService {
  getQuantityTierAttributes: () => Promise<QuantityTierAttribute[]>;
  createQuantityTierAttribute: (attribute: QuantityTierAttribute) => Promise<void>;
  updateQuantityTierAttribute: (attribute: QuantityTierAttribute) => Promise<void>;
  getAttributeMappingOptions: () => Promise<PickListOption[]>;
  deleteQuantityTierAttribute: (attribute: QuantityTierAttribute) => Promise<void>;
}

export interface WebhookAttributeService {
  getAll: () => Promise<PricingAttribute[]> | PricingAttribute[];
  saveOrUpdate: (pricingAttribute: PricingAttribute) => void | Promise<void>;
  remove: (apiName: string) => void | Promise<void>;
  getAttributeEventOptions: () => Promise<PickListOption[]> | PickListOption[];
  getWebhookAttributeOptions: () => Promise<{ name: string; value: string }[]>;
}

export type FieldChangeEventHandler = (
  event: React.ChangeEvent<{ name?: string; value: unknown }>,
  child: React.ReactNode,
) => void;

export enum SubscriptionTermDimensionEnum {
  Month = 'Month',
  Year = 'Year',
}

export interface Category {
  name: string;
  subCategories: Subcategory[];
}

export interface Subcategory {
  name: string;
  labels?: string[];
  icon?: React.ReactNode;
  settings?: Record<string, any>;
  hideName?: boolean;
  id?: string;
}

export interface SettingItemCardProps {
  settingItem: Subcategory;
  onClick: (subcategory: Subcategory) => void | Promise<void>;
  isSelected: boolean;
  noCardSelected: boolean;
}

export interface SubcategoryEditorProps {
  category: Subcategory;
  onSubcategoryChange: (subcategory: Subcategory) => void | Promise<void>;
  onSwithCategory: (settingCategoryName: string) => void | Promise<void>;
}

export interface Props {
  categories: Category[];
  onSubcategoryChange: (subcategory: Subcategory) => void | Promise<void>;
  style?: React.CSSProperties;
}

export interface RubySettingService {
  listAllSettings: () => Promise<Subcategory[]>;
  updateSubcategory: (subcategory: Subcategory) => void | Promise<void>;
  pricingAttributeService: PricingAttributeService;
  webhookAttributeService: WebhookAttributeService;
}

export const RubySettingServiceContext = React.createContext<RubySettingService | null>(null);

export interface RubySettingEditorService {
  closeSettingEditorPanel: () => void;
}

export const RubySettingEditorContext = React.createContext<RubySettingEditorService | null>(null);

export type Field = {
  name: string;
  apiName: string;
};

export type FieldMapping = {
  developerName: string;
  fromField: string;
  toField: string;
  updatable: boolean;
  fromObject: string;
  toObject: string;
};

interface MappingObjectLoader {
  loadMappingObjects: () => Promise<{
    quoteFields: Field[];
    orderFields: Field[];
    quoteLineItemFields: Field[];
    orderLineItemFields: Field[];
    lineBucketFields: Field[];
  }>;
  loadProductCategoryValues: () => Promise<Field[]>;
  loadPriceModelValues: () => Promise<Field[]>;
  loadBillingTimingValues: () => Promise<Field[]>;
  loadRevenueRuleValues: () => Promise<Field[]>;
  loadActivatedObjects: () => Promise<Field[]>;
  loadUomValues: () => Promise<Field[]>;
  loadRevenueModelValues: () => Promise<Field[]>;
  loadTaxCodeValues: () => Promise<Field[]>;
}

export interface IntegrationSetting {
  integrationName: string;
  status: string;
}

export interface MappedIntegrationSetting {
  name: string;
  description: string;
  status: string;
  getIcon?: () => JSX.Element;
}

interface SettingsLoader {
  loadIntegrationSettings: () => Promise<IntegrationSetting[]>;
}

export interface GraphqlGeneratorSettings {
  queryObject: string;
}

export interface RenewalSettings {
  defaultRenewalTerm: string;
  renewBeforeSubscriptionEndDate: string;
  renewalEmailTemplate: string;
  autoRenewDaysBeforeSubscriptionEnd: string;
  autoRenewGenerateQuotesDaysBefore: string;
  autoRenewGenerateQuotes: string;
  autoRenewGenerateOpportunities: string;
  autoRenewStatusMonthsToKeep: string;
  autoRenewEnabled: boolean;
  renewalTermDimension: SubscriptionTermDimensionEnum;
  autoRenewOpportunityRecordType: string;
  autoRenewUsePreviousNetSalesPrice: boolean;
  renewalNotificationEnabled: string;
  allowEarlyRenewals: string;
}

export interface RubySettings {
  Only_From_Approved_Quote: boolean;
  Only_From_Primary_Quote: boolean;
  Allow_Update_Generated_From_Quote: boolean;
  Auto_Map_Header_Objects: boolean;
  Auto_Map_Line_Items: boolean;
  Auto_Map_Line_Buckets: boolean;
  headerObjectFieldMappings: Record<string, any>[];
  lineItemFieldMappings: Record<string, any>[];
  lineBucketFieldMappings: Record<string, any>[];
  convertFreeTrial: string;
  convertQuoteToPrimaryAfterCreateOrder: boolean;
  defaultRenewalTerm: string;
  defaultSubscriptionTerm: string;
  productConfiguratorDefaultView: 'GridView' | 'ListView';
  productConfiguratorShowProductImages: boolean;
  productConfiguratorDefaultPageSize: '10' | '20' | '40' | '100';
  lineEditorDefaultPageSize: '10' | '20' | '40' | '100';
  showIncludedUnitsQuantityRampLineItems: boolean;
  createNewQuote: boolean;
  createNewOrder: boolean;
  useExistingQuote: boolean;
  useExistingOrder: boolean;
  renewBeforeSubscriptionEndDate: boolean;
  renewalEmailTemplate: string;
  autoRenewDaysBeforeSubscriptionEnd: string;
  autoRenewGenerateQuotesDaysBefore: string;
  autoRenewGenerateQuotes: boolean;
  autoRenewGenerateOpportunities: boolean;
  autoRenewStatusMonthsToKeep: string;
  updateQuantity: boolean;
  updateSubscriptionTerm: boolean;
  renew: boolean;
  pauseAndResume: boolean;
  skip: boolean;
  cancel: boolean;
  reconfigure: boolean;
  syncPrimaryQuoteLinesToOpptyProducts: boolean;
  syncPrimaryQuoteLinesForClosedOppty: boolean;
  calculateRampBasedOnSummaryQuantity?: boolean;
  subscriptionTermDimension: string;
  renewalTermDimension: string;
  allowCancelActiveOrder: boolean;
  allowQuoteSplit: boolean;
  allowOrderSplit: boolean;
  allowChangeOrderBackDate: boolean;
  subscriptionTermBasis: string;
  usePreviousSubscriptionPrice?: boolean;
  considerPreviousSubscriptionPrice?: boolean;
  finalizeQuoteWithDraftBucketsBehavior: string;
  showGeneralInfoOnCustomerLifecycle: boolean;
  showOrderOnCustomerLifecycle: boolean;
  showSubscriptionOnCustomerLifecycle: boolean;
  showAssetOnCustomerLifecycle: boolean;
  showEntitlementOnCustomerLifecycle: boolean;
  showInvoiceOnCustomerLifecycle: boolean;
  showUsageOnCustomerLifecycle: boolean;
  showCreditOnCustomerLifecycle: boolean;
  showCreditMemoOnCustomerLifecycle: boolean;
  showRevContractsOnCustomerLifecycle: boolean;
  enableProrateCreditsForPartialTerm: boolean;
  autoListSellableProducts: boolean;
  enableTaxation: boolean;
  adjustPrice: boolean;
  previewInvoicesForQuotesAndOrders: boolean;
  renewalNotificationEnabled: boolean;
  splitBySalesAccount: boolean;
  AutoSetBcdBySubscriptionStartDate: boolean;
  allowEarlyRenewals: boolean;
  rampItemDefaultVisibility: 'collapsed' | 'expanded';
  useCustomFieldsAsPricingAttributes: [];
  autoApplyPriceTagsToChildItems: boolean;
  attachQuotePDFToQuote: boolean;
  attachQuotePDFToOpportunity: boolean;
}

export const MappingObjectLoaderContext = React.createContext<MappingObjectLoader | null>(null);

export const SettingsLoaderContext = React.createContext<SettingsLoader | null>(null);

export type Role = {
  id: string;
  name: string;
  effective?: boolean;
  functions: FunctionItem[];
};

export interface RoleSettingService {
  fetchTenantFunctionGroups: () => Promise<FunctionGroup[]>;
  fetchTenantRoles: () => Promise<Role[]>;
  saveRoleHandler: (data: Role) => Promise<Role>;
  fetchReadOnlyRoleName: () => Promise<string>;
  fetchReadOnlyFunctions: () => Promise<string[]>;
  fetchFunctionsRelation: () => Promise<FunctionRelation[]>;
}

export const RoleSettingContext = React.createContext<RoleSettingService | null>(null);

export interface GraphqlGeneratorService {
  getQueryObjects: () => Promise<string[]>;
  getQueryFields: (objectName: string) => Promise<string[]>;
  getObjectMetadata: (objectName: string) => Promise<RubyObject>;
  listFilters: (objectApiName: string) => Promise<RubyFilter[]>;
  saveFilter: (
    objectMetadataApiName: string,
    filterName: string,
    conditions: Condition[],
  ) => Promise<RubyFilter>;
  deleteFilter: (objectApiName: string, filterId: string) => Promise<DeleteFilterResponse>;
  updateFilters: (
    objectMetadataApiName: string,
    referenceFilterId: string,
    filterName: string,
    conditions: Condition[],
  ) => Promise<RubyFilter>;
}

export const GraphqlGeneratorContext = React.createContext<GraphqlGeneratorService | null>(null);

export interface ProductConfiguratorOption {
  value: 'GridView' | 'ListView';
  name: 'Grid View' | 'List View';
}

export type BillCycleDayTypeEnum =
  | 'DayOfMonth'
  | 'AutoSetToFirstSubscription'
  | 'AutoSetSubscriptionStartDate';

export type BillingPeriodEnum =
  | 'Month'
  | 'Quarter'
  | 'Semi-Annual'
  | 'Annual'
  | 'SameAsSubscriptionTerm';

export type StartMonthEnum =
  | '01'
  | '02'
  | '03'
  | '04'
  | '05'
  | '06'
  | '07'
  | '08'
  | '09'
  | '10'
  | '11'
  | '12';

export type BillCycleDayEnum =
  | '1st of Month'
  | '2nd of Month'
  | '3rd of Month'
  | '4th of Month'
  | '5th of Month'
  | '6th of Month'
  | '7th of Month'
  | '8th of Month'
  | '9th of Month'
  | '10th of Month'
  | '11th of Month'
  | '12th of Month'
  | '13th of Month'
  | '14th of Month'
  | '15th of Month'
  | '16th of Month'
  | '17th of Month'
  | '18th of Month'
  | '19th of Month'
  | '20th of Month'
  | '21st of Month'
  | '22nd of Month'
  | '23rd of Month'
  | '24th of Month'
  | '25th of Month'
  | '26th of Month'
  | '27th of Month'
  | '28th of Month'
  | '29th of Month'
  | '30th of Month'
  | '31st of Month';

export type BillingTimingEnum = 'In Advance' | 'In Arrears';

export type RevenueRuleEnum =
  | 'Recurring Services'
  | 'Credit Burndown'
  | 'One Time Products'
  | 'Usage';

export type PaymentTermEnum =
  | 'Due Upon Receipt'
  | 'Net 15'
  | 'Net 30'
  | 'Net 45'
  | 'Net 60'
  | `${number}`;

export type CreditMemoMode = 'Disabled' | 'Invoice' | 'InvoiceItem';

export type BillingPeriodAlignEnum = 'Order' | 'Subscription';

export type EnableMulticurrencies = {
  orderJobId: string;
  usageJobId: string;
};
export type BillingSettingsSfCustomFields = {
  autoAlignBillingPeriodEnabled: boolean;
  billingPeriodAlign: BillingPeriodAlignEnum;
  autoAlignPaymentTermEnabled: boolean;
  paymentMethod: string;
  autoAlignPaymentMethodEnabled: boolean;
};
export type BillSystemSetting = {
  autoAlignBillingPeriodEnabled: boolean;
  billingPeriodAlign: BillingPeriodAlignEnum;
  autoAlignPaymentTermEnabled: boolean;
};
export const getDefaultBillingSettingsSfCustomFields = () => {
  const x: BillingSettingsSfCustomFields = {
    autoAlignBillingPeriodEnabled: false,
    billingPeriodAlign: 'Order',
    autoAlignPaymentTermEnabled: false,
    paymentMethod: '',
    autoAlignPaymentMethodEnabled: false,
  };
  return x;
};
export type BillingSettings = {
  billingPeriod: BillingPeriodEnum;
  billingStart: StartMonthEnum;
  billCycleDay: BillCycleDayEnum | null;
  billingTiming: BillingTimingEnum;
  revenueModelMapping: Map<string, string>;
  paymentTerm: PaymentTermEnum;
  prorationEnabled: boolean;
  hideZeroItems: boolean;
  creditMemoMode: CreditMemoMode;
  //splitBySalesAccount: boolean;
  //splitForMilestone: boolean;
  groupingAttributes: {
    conditions: GroupingConditionItem[];
    description: string;
    id: string;
    name: string;
    status: 'Active' | 'Inactive';
    isStandard: true | false;
    groupingField: string;
  }[];
};

export const getDefaultBillingSettings = () => {
  const x: BillingSettings = {
    billCycleDay: '1st of Month',
    billingStart: '01',
    billingPeriod: 'Month',
    billingTiming: 'In Advance',
    paymentTerm: 'Net 30',
    prorationEnabled: true,
    hideZeroItems: true,
    creditMemoMode: 'Disabled',
    revenueModelMapping: new Map([
      ['OneTime', 'In Advance'],
      ['Recurring', 'In Advance'],
      ['CRBD', 'In Advance'],
      ['Usage', 'In Arrears'],
      ['Credit', 'In Advance'],
    ]),
    groupingAttributes: [
      {
        conditions: [],
        description: 'Generate invoices for each account',
        id: 'Customer',
        name: 'Customer',
        status: 'Active',
        isStandard: true,
        groupingField: '',
      },
      {
        conditions: [],
        description: 'Generate invoices for different currencies independently',
        id: 'Currency',
        name: 'Currency',
        status: 'Active',
        isStandard: true,
        groupingField: '',
      },
    ],
  };
  return x;
};

export type RevenueRulesSettings = {
  revenueRule: RevenueRuleEnum;
  revenueRuleMapping: Object;
};

export type RevenueRuleMapping = {
  integrationType?: string;
  pricingModel?: string;
  applicationLevel?: string;
  name: string;
  externalId: string;
  sequenceNo: number;
  revenueTrigger: string;
  userDefinedEvents: string;
  distributionMethod: string;
  priorPeriodRevenueMethod: string;
  prorateMethod?: string;
  ruleType?: string;
  active?: boolean;
};

export type EventDefinition = {
  active: boolean;
  eventCategory: string;
  eventName: string;
  eventType: string;
};

export type RevenueSettings = {
  rampRevRecAsSingleContract: boolean;
};

export interface RevenueSettingsItemControlProps {
  revenueSettings: RevenueSettings;
  onChange: (revenueSettings: RevenueSettings) => void;
}

export const getDefaultRevenueRulesSettings = () => {
  const x: RevenueRulesSettings = {
    revenueRule: 'Recurring Services',
    revenueRuleMapping: {
      OneTime: {
        name: 'One Time Products',
        externalId: 'One Time Products',
        sequenceNo: 2000000,
        revenueTrigger: 'Billing',
        userDefinedEvents: 'Billing',
        distributionMethod: 'Point In Time',
        priorPeriodRevenueMethod: 'Catch-up in current period',
      },

      Recurring: {
        name: 'Recurring Services',
        externalId: 'Recurring Services',
        sequenceNo: 4000000,
        revenueTrigger: 'Billing',
        userDefinedEvents: 'Billing',
        distributionMethod: 'Point In Time',
        priorPeriodRevenueMethod: 'Catch-up in current period',
      },

      CRBD: {
        name: 'Credit Burndown',
        externalId: 'Credit Burndown',
        sequenceNo: 3000000,
        revenueTrigger: 'Billing',
        userDefinedEvents: 'Billing',
        distributionMethod: 'Point In Time',
        priorPeriodRevenueMethod: 'Catch-up in current period',
      },
      Usage: {
        name: 'Usage',
        externalId: 'Usage',
        sequenceNo: 1000000,
        revenueTrigger: 'Billing',
        userDefinedEvents: 'Billing',
        distributionMethod: 'Point In Time',
        priorPeriodRevenueMethod: 'Catch-up in current period',
      },
      Credit: {
        name: 'Credit',
        externalId: 'Credit',
        sequenceNo: 6000000,
        revenueTrigger: 'Billing',
        userDefinedEvents: 'Billing',
        distributionMethod: 'Point In Time',
        priorPeriodRevenueMethod: 'Catch-up in current period',
      },
    },
  };
  return x;
};

export type OrderSetting = {
  key: string;
  value: string; // Value type can be vary on the backend
};

export type KeyValue = {
  key: string; // Field API Name
  value: string; // Field Label
};

export type ValueLabel = {
  value: string;
  label: string;
};

export type SubscriptionDateFields = {
  options: KeyValue[];
};
export type TermAlignedEnum = 'StartDate' | 'BillCycleDay';
export type UsageSettings = {
  ratingUserId: string | undefined | null;
  windowSize: string | undefined | null;
  syncUsageEnabled: boolean;
  termAligned: TermAlignedEnum;
};

export type CreditTypes = {
  creditTypeId: string;
  creditType: DefaultCreditType;
  creditTypeName: string;
  status: CreditStatus;
};

export type BillSysSetting = {
  key: string;
  value: string;
};

export type CreditStatus = 'Active' | 'Inactive';

export type CreditPools = {
  creditPoolId: string;
  creditPoolName: string;
  creditTypeId: string;
  isDefault: boolean;
};

export type CreditSettings = {
  rollover: boolean;
  rolloverPeriod: number;
  rolloverUnit: RolloverUnit;
};

export const getCreditTypes = () => {
  const x: CreditTypes = {
    creditTypeId: '',
    creditType: 'None',
    creditTypeName: '',
    status: 'Active',
  };
  return [x];
};

export const getCreditPools = () => {
  // const x: CreditPools = {
  //   creditPoolId: '',
  //   creditPoolName: 'Default Credit Pool',
  //   creditTypeId: '',
  // };
  return [];
};

export const getTenantCreditSettings = () => {
  const x: CreditSettings = {
    rollover: false,
    rolloverUnit: 'Month',
    rolloverPeriod: 0,
  };
  return x;
};

export interface BillingSettingsService {
  getTenantBillingSettings: () => Promise<BillingSettings>;
  saveTenantBillingSettings: (settings: BillingSettings) => Promise<string>;
  getOrderSettings: (keys: string[]) => Promise<OrderSetting[]>;
  saveOrderSettings: (settings: OrderSetting[]) => Promise<void>;
  getSubscriptionDateFields: () => Promise<SubscriptionDateFields>;
  setAutoSetBcdMode: (mode: string | null, setting: string) => Promise<void>;
  getAutoSetBcdMode: () => Promise<AutoSetBcdMode>;
  isUsageMgmtGranted: () => boolean;
  isCrbdMgmtGranted: () => Promise<boolean>;
  getUsageSettings: () => Promise<UsageSettings>;
  getRatingUserOptions: () => Promise<ValueLabel[]>;
  saveUsageSettings: (settings: UsageSettings) => Promise<void>;
  getCreditTypes: () => Promise<CreditTypes[]>;
  saveCreditTypes: (settings: CreditTypes) => Promise<void>;
  saveCreditPools: (settings: CreditPools) => Promise<void>;
  deleteCreditPools: (settings: CreditPools) => Promise<void>;
  getCreditPools: () => Promise<CreditPools[]>;
  getTenantCreditSettings: () => Promise<CreditSettings>;
  saveTenantCreditSettings: (settings: CreditSettings) => Promise<void>;
  setBillingSettingCustomField: (fieldName: string, settings: BillingSettings) => Promise<void>;
  getBillingSettingSfCustomFields: () => Promise<BillingSettingsSfCustomFields>;
  setBillingSettingSfCustomField: (
    fieldName: string,
    settings: BillingSettingsSfCustomFields,
  ) => Promise<void>;
  enableMultiCurrenciesInNue?: () => Promise<EnableMulticurrencies>;
  customerMetadata: RubyObject;
  orderMetadata: RubyObject;
  productMetadata: RubyObject;
  metadataObjects: Array<RubyObject>;
  saveBillSystemSettings: (settings: BillSysSetting[]) => Promise<void>;
  getBillSystemSettings: () => Promise<BillSysSetting[]>;
  getValueSets: (names: string[]) => Promise<any[]>;
  syncCustomField: (fieldName: string) => Promise<boolean>;
  getInvoiceTemplateList: (
    currentPage: number,
    pageSize: number,
    type?: string,
  ) => Promise<TemplateSchema[]>;
  cloneInvoiceTemplate: (templateId: string, type?: string) => Promise<any>;
  deleteInvoiceTemplate: (templateId: string, type?: string) => Promise<any>;
  getInvoiceList: () => Promise<any[]>;
  getCreditMemoList: () => Promise<any[]>;
  getOrderList: () => Promise<any[]>;
  getQuoteList: () => Promise<any[]>;
  getInvoiceById: (formType: string, invoiceId: string, formJSON: object) => Promise<any>;
  fetchMetadataForQuoteLineItemsAndLinePriceDimension: (metadata: string) => Promise<any>;
  publishInvoiceTemplate: (formJSON: object, type?: string) => Promise<any>;
  unPublishInvoiceTemplate: (formJSON: object, type?: string) => Promise<any>;
  markDefaultInvoiceTemplate: (templateId: string, type?: string) => Promise<any>;
  getInvoiceTemplateById: (templateId: string, type?: string) => Promise<any>;
  saveInvoiceTemplateImage: (templateId: string, imageId: string, file: File) => Promise<any>;
  saveProfileImg: (file: File) => Promise<any>;
  setRefreshPdfUrl: (formType: string, invoiceIds: string[]) => Promise<any>;
  sendInvoiceEmail: (invoiceIds: string[]) => Promise<any>;
  getDownloadPdf: (token: string) => Promise<any>;
  getViewInvoiceToken: (invoiceId: string) => Promise<any>;
  updateInvoiceTemplate: (formJSON: object, type?: string) => Promise<any>;
  checkInvoiceTokenByInvoiceId: (invoiceId: object) => Promise<any>;
  deleteGroupingAttributes: (attributeIds: string[]) => Promise<any>;
  getCustomFieldsOfAsset: () => Promise<any>;
}

export interface RevenueRuleSettingsService {
  getTenantRevenueRuleSettings: () => Promise<RevenueRulesSettings>;
  getTenantRevenueRuleSetting: (priceModel: string) => Promise<RevenueRuleMapping>;
  restoreTenantRevenueRuleSetting: (
    settings: Record<string, any>,
    priceModel: string,
    applicationLevel: string,
    productSku: string | null,
  ) => Promise<Map<string, string>>;
  saveTenantRevenueRuleSettings: (
    settings: Record<string, any>,
    priceModel: string,
    applicationLevel: string,
    productSku: string | null,
  ) => Promise<Map<string, string>>;
  getEventDefinitions: () => Promise<EventDefinition[]>;
  getRevenueSettings: () => Promise<RevenueSettings>;
}

export const KEY_BILL_ON_ORDER_ACTIVATION: string = 'bill-on-order-activation';
export const KEY_ACTIVATE_INVOICES_ON_ORDER_ACTIVATION: string = 'activate-on-order-activation';
export const KEY_BILL_ON_ORDER_ACTIVATION_FILTERS: string = 'bill-on-order-activation-filters';
export const KEY_ENABLE_ERR_BILLING_JOB_EMAIL_NOTIFY: string =
  'enable-err-billing-job-email-notify';

export const BillingSettingsContext = React.createContext<BillingSettingsService | null>(null);

export const RevenueRulesSettingsContext = React.createContext<RevenueRuleSettingsService | null>(
  null,
);

export const QuickBooksSettingsContext = React.createContext<QuickBooksConnectionService | null>(
  null,
);

export const PricingAttributeServiceContext = React.createContext<PricingAttributeService | null>(
  null,
);

export const WebhookAttributeServiceContext = React.createContext<WebhookAttributeService | null>(
  null,
);

export const QuantityTierAttributeServiceContext =
  React.createContext<QuantityTierAttributeService | null>(null);

interface SalesforceSourceSyncDetails {
  connectingUserEmail: string;
  organizationId: string;
  username: string;
}

interface ConnectionStatus {
  setupState: 'incomplete' | 'connected' | 'broken';
  syncState: 'scheduled' | 'syncing' | 'paused' | 'rescheduled';
  updateState: 'on_schedule' | 'delayed';
}

export interface ConnectionDetails {
  id: string;
  groupId: string;
  syncFrequency: number;
  dailySyncTime: string;
  status: ConnectionStatus;
  sourceSyncDetails: SalesforceSourceSyncDetails;
  succeededAt: string;
}

type DataFlowJobStatus = 'Scheduled' | 'Completed' | 'InProgress' | 'Failed';

type SyncObjectCategory = 'SalesforceStandardObject' | 'SalesforceBigObject' | 'Custom';

export interface DataFlowJob {
  id: string;
  startTime: string;
  endTime?: string;
  scheduleType: string;
  status: DataFlowJobStatus;
  dataFlowConnectorId: string;
  syncObjectCategory: SyncObjectCategory;
  objectNames?: string[];
}

type ScheduleType = 'OnDemand' | 'Scheduled';

export interface SyncNowRequest {
  dataFlowConnectorId: string;
  objectNames?: string[];
  syncObjectCategory: SyncObjectCategory;
  startTime: string;
  scheduleType: ScheduleType;
}

export interface ScheduleSyncRequest {
  syncFrequency: string;
  dailySyncTime: string;
}

export interface DataFlowManagerService {
  connectorId: string;
  getConnectionDetails: () => Promise<ConnectionDetails>;
  scheduleSync: (request: ScheduleSyncRequest) => Promise<void>;
  fullSync: () => Promise<void>;
  syncNow: () => Promise<void>;
  dataFlowJobMetadata: RubyObject;
  getDataFlowJobs: () => Promise<DataFlowJob[]>;
  getConnectorIds: () => Promise<string[]>;
  user: User;
  getIntegration: (
    appCategory: 'RevenueBuilder',
    integrationType: 'Salesforce' | 'Fivetran',
  ) => Promise<GetIntegrationResponse>;
  getIntegrationSecrets: (
    appCategory: 'RevenueBuilder',
    integrationType: 'Salesforce',
  ) => Promise<GetIntegrationSecretResponse>;
}

export const DataFlowManagerContext = React.createContext<DataFlowManagerService | null>(null);

export interface FivetranIntegration {
  apiKey: string;
  apiSecret: string;
}

export type DefaultCreditPool = 'Default Credit Pool';

export type RolloverUnit = 'Month';

export type CreditType = {
  defaultCreditType: DefaultCreditType;
  isCash: boolean;
};

export type DefaultCreditType = 'None' | 'Cash' | 'Credit';

export const getCreditType = () => {
  const x: CreditType = {
    defaultCreditType: 'None',
    isCash: false,
  };
  return x;
};

export interface CreditTypeProps {
  open: boolean;
  onClose: () => void;
  creditType: DefaultCreditType;
  creditTypeName: string;
  handleConfirm: any;
  creditTypeId: string;
  status: CreditStatus;
}

export interface CreditPoolProps {
  open: boolean;
  onClose: () => void;
  handleConfirm: any;
  creditPool: CreditPools;
  creditTypes: CreditTypes[];
  isEditCreditPool: boolean;
  isDeleteCreditPool: boolean;
}

export type UsageWindowSizeType = 'DAY' | 'HOUR';

export interface RevenueRuleModalProps {
  openRevenueRuleDialog: boolean;
  handleSetOpenRevenueRuleDialog: (openRevenueRuleDialog: boolean) => void;
  loadingRevenueRuleDialog: boolean;
  initValue: Record<string, any>;
  handleSubmit: (values: Record<string, any>) => Promise<void>;
  handleRestoreDefault: (values: Record<string, any>) => Promise<void>;
  hiddenFields: string[];
  setHiddenFields: React.Dispatch<React.SetStateAction<string[]>>;
  validationSchema: yup.ObjectSchema;
}

const createable = true;

export const fields: RubyField[] = [
  {
    name: 'Name',
    type: 'String',
    apiName: 'name',
    required: true,
    xs: 6,
    creatable: createable,
    showTooltip: true,
    toolTipText: 'Name of revenue rule',
  },
  {
    name: 'Revenue Trigger',
    type: 'String',
    apiName: 'revenueTrigger',
    xs: 6,
    creatable: createable,
  },
  {
    name: 'User Defined Events',
    type: 'String',
    apiName: 'userDefinedEvents',
    xs: 6,
    creatable: createable,
  },
  {
    name: 'Distribution Method',
    type: 'String',
    apiName: 'distributionMethod',
    xs: 6,
    creatable: createable,
  },
  {
    name: 'Prior Period Revenue Method',
    type: 'String',
    apiName: 'priorPeriodRevenueMethod',
    xs: 6,
    creatable: createable,
  },
  {
    name: 'Prorate Method',
    type: 'String',
    apiName: 'prorateMethod',
    xs: 6,
    creatable: createable,
  },
];

export const values = {
  name: 'Usage',
  revenueTrigger: 'youknow',
  userDefinedEvents: 'haHA',
  distributionMethod: 'whatawwhat',
  priorPeriodRevenueMethod: 'skrill',
};

export interface CardActionProps {
  objectMetadata: RubyObject;
  initValues: Record<string, any>;
  value?: string;
  handleSetValue?: (newValue: string) => void;
  InfoSection?: React.ReactNode | string;
  afterFieldChange?: AfterFieldChangeListener;
}

export interface ChangeModalProps {
  cardAction: CardAction;
  objectMetadata: RubyObject;
  initValues: Record<string, any>;
  defaultValues: Record<string, any>;
  handleOpen: (newOpen: boolean) => void;
  validationSchema: yup.ObjectSchema;
  subscriptions: Subscription[];
}

export type SessionTimeoutEnum = 15 | 30 | 60 | 90 | 120;

export type SecuritySettings = {
  sessionTimeout: SessionTimeoutEnum;
  mfaEnforcement: 'Required' | 'Optional' | 'Disabled';
};

export interface SecuritySettingsService {
  getTenantSecuritySettings: () => Promise<SecuritySettings>;
  saveTenantSecuritySettings: (settings: SecuritySettings) => Promise<string>;
  enableTenantMFA: () => Promise<void>;
  disableTenantMFA: () => Promise<void>;
  handleUserMFAToggle?: (id: string, enabled: boolean) => Promise<void>;
}

export const SecuritySettingsContext = React.createContext<SecuritySettingsService | null>(null);

export const getDefaultSecuritySettings = () => {
  const x: SecuritySettings = {
    sessionTimeout: 15,
    mfaEnforcement: 'Disabled',
  };
  return x;
};

export interface ValueSet {
  alphabeticallyOrdered?: boolean;
  apiName?: string;
  defaultValue?: string;
  name?: string;
  values: ValuePair[];
}

export const KEY_RENEWAL_NOTIFICATION_FILTER: string = 'RenewalNotificationFilter';

// ANALYTICS setting
export type AnalyticsDashboard = {
  id?: string; // only non-default dashboards have an id
  default: boolean;
  externalId?: string; // Embed code
  name: string; // Unique
  label: string; // Display name
};

export const AnalyticsSystemDashboardName = {
  BOOKING: 'BookingDashboard',
  PIPELINE: 'PipelineDashboard',
  BILLING_AND_COLLECTIONS: 'BillingAndCollectionsDashboard',
} as const;

export interface AnalyticsSettingsService {
  dashboards: AnalyticsDashboard[];
  permissionToManage: boolean;
  isSigmaProvisioned: boolean;
  createCustomDashboard: ({
    name,
    label,
    externalId,
  }: {
    name: string;
    label: string;
    externalId: string;
  }) => Promise<unknown>;
  createCustomSystemDashboard: ({
    name,
    label,
    externalId,
  }: {
    name: string;
    label: string;
    externalId: string;
  }) => Promise<unknown>;
  updateCustomDashboard: ({
    name,
    payload,
  }: {
    name: string;
    payload: Partial<{
      label: string;
      externalId: string;
    }>;
  }) => Promise<unknown>;
  restoreDashboardsToDefault: () => Promise<unknown>;
  deleteCustomDashboard: ({ id }: { id: string }) => Promise<unknown>;
}

export const AnalyticsSettingsContext = React.createContext<AnalyticsSettingsService | null>(null);

export type AutoSetBcdMode = {
  mode: string | null;
  setting: string | null;
};
export type GroupingAttribute = {
  conditions: GroupingConditionItem[];
  description: string;
  id: string;
  name: string;
  groupingField: string;
  status: 'Active' | 'Inactive';
  isStandard: true | false;
};

export interface TemplateListViewSectionProps {
  getAllInvoiceTemplateList: any;
  handleEditInvoiceTemplate: any;
  handlePreviewInvoiceTemplate: any;
  invoiceList: any;
  getInvoiceById: any;
  checkInvoiceTokenByInvoiceId: any;
  handleMarkTemplateAsDefault: any;
  handlePublishInvoiceTemplate: any;
  handleDeleteInvoiceTemplate: any;
  handleUnPublishInvoiceTemplate: any;
  handleCopyInvoiceTemplate: any;
  invoiceTemplateEdit: any;
  showInvoiceTemplatePreview: any;
  setShowInvoiceTemplatePreview: any;
  refreshFlag: any;
  setRefreshFlag: any;
  showInvoiceTemplateEdit: any;
  setShowInvoiceTemplateEdit: any;
  label: string;
  rootStyle?: React.CSSProperties;
  editInvoiceTemplate: boolean;
  formTemplateMetadata: RubyObject;
  mode?: string;
  showDivider?: boolean;
}

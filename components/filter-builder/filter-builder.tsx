//@ts-nocheck
import React, { Component } from 'react';
import DateFnsUtils from '@date-io/date-fns';
import Checkbox from '@material-ui/core/Checkbox';
import FormControl from '@material-ui/core/FormControl';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Typography from '@material-ui/core/Typography';
import { withStyles } from '@material-ui/core/styles';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import dayjs from 'dayjs';
import _ from 'lodash';
import DialogComponent from '../dialog-component';
import type {
  Condition,
  FilterBuilderCondition,
  Operator,
  OrderByField,
  RubyFilter,
} from '../graph-ql-query-constructor';
import GraphQLQueryConstructor from '../graph-ql-query-constructor';
import type { RubyField } from '../metadata/interface';
import { RubyButtonBar } from '../ruby-button/ruby-button-bar';
import ConditionList from './condition-list';
import FilterBuilderStyles from './filter-builder-styles';
import { GraphQLConverter } from './graphql-query-converter';
import type { Props, States } from './interface';
import { operandTypes, operatorTypes } from './operator-map';
import { RubyButtonProps } from '../ruby-button';

/**
 * Note: much of this code was taken from https://github.com/logipro/logi-filter-builder and refractored for our case to construct filters into GraphQL
 * TODO: Refractor to make this into a functional component and clean up code
 */
class FilterBuilder extends Component<Props, States> {
  constructor(props: Props) {
    super(props);
    this.handleChange = this.handleChange.bind(this);
    if (props.preLoadConditions) {
      this.state = {
        conditions: props.preLoadConditions,
        openWarningDialog: false,
        pendingAction: null,
        openWarningBeforeUpdate: false,
      };
    } else {
      this.state = {
        conditions: [
          {
            error: '',
            type: 'Simple',
            field: null,
            operator: null,
            value: null,
            operand: operandTypes.get(' _and '),
            nestedConditions: [],
            updatedFilterName: '',
          },
        ],
        saveFilterResponse: '',
        saveFilterError: '',
        openWarningDialog: false,
        pendingAction: null,
        openWarningBeforeUpdate: false,
      };
    }
  }

  componentDidMount() {
    const { savedFilters, temporaryFilter, warningBeforeUpdate } = this.props;
    // find the first applied filter and open it in the Filter Builder
    if (savedFilters) {
      const firstAppliedFilter = savedFilters.find((filter: RubyFilter) => filter.isApplied);
      this.applySavedFilters(firstAppliedFilter);
    }
    if (temporaryFilter && temporaryFilter.length > 0) {
      this.setState({
        conditions: temporaryFilter,
      });
    }
    this.loadDontAskAgain();
  }

  async loadDontAskAgain() {
    if (this.props.warningBeforeUpdate?.show) {
      const val: boolean = await this.props.warningBeforeUpdate.getDontAskAgain();
      this.setState({
        warningDontAskAgain: val,
      });
    }
  }

  // eslint-disable-next-line class-methods-use-this
  setupDefaultValueFilters(operatorId: string, type?: string) {
    if (!type) {
      return {
        value: '',
        translateValue: '',
      };
    }
    if (operatorId === ' _is_null ') {
      return {
        value: true,
        translateValue: 'true',
      };
    }

    if (operatorId === ' _is_not_null ') {
      return {
        value: true,
        translateValue: 'true',
      };
    }

    // I want to set default values for the filter so that my default value for the filter gets populated
    const today = dayjs();
    switch (type) {
      case 'boolean':
      case 'Boolean':
        return {
          value: 'false',
          translateValue: 'false',
        };
      case 'number':
        return {
          value: 0,
          translateValue: 0,
        };
      case 'dateTime':
        return {
          value: today.format('YYYY-MM-DDTHH:mm:ssZ'),
          translateValue: `"${today.format('YYYY-MM-DDTHH:mm:ssZ')}"`,
        };
      case 'Date':
      case 'date':
        return {
          value: today,
          translateValue: `"${today.format('YYYY-MM-DD')}"`,
        };
      case 'integer':
      case 'Decimal':
      case 'decimal':
      case 'Double':
      case 'phone':
      case 'text':
      case 'string':
      case 'String':
      case 'email':
      case 'currency':
      case 'bLookup':
      case 'lookup':
      case 'bMasterDetail':
      case 'Reference':
      case 'id':
        return {
          value: '',
          translateValue: '',
        };
      case 'Picklist':
      case 'pickList':
        return {
          value: '',
          translateValue: '',
        };
      default:
        return {
          value: [],
          translateValue: '[]',
        };
    }
    return {
      value: '',
      translateValue: '',
    };
  }

  convertConditionFromFilter = (condition: Condition, isNested: boolean) => {
    const { objectMetadata } = this.props;

    const generatedConditions: FilterBuilderCondition[] = [];
    const operators = operatorTypes.get(condition.nestedType || condition.type);
    if (!operators) {
      return generatedConditions;
    }
    const operator = operators.filter(
      (currentOperator: Operator) => currentOperator.id === condition.operator,
    );
    let operand;
    let value;

    // we have to remove the beginning and end quotations because in graphql expects quotation marks around the value when sending strings
    if (
      condition.operator === ' _is_null ' ||
      condition.operator === ' _is_not_null ' ||
      condition.type === 'boolean' ||
      condition.type === 'Boolean' ||
      condition.type === 'date' ||
      condition.type === 'Date' ||
      condition.type === 'number' ||
      condition.type === 'integer' ||
      condition.type === 'Decimal' ||
      condition.type === 'decimal' ||
      condition.type === 'Double' ||
      condition.type === 'dateTime' ||
      (condition.type === 'bLookup' &&
        (condition.nestedType === 'boolean' ||
          condition.nestedType === 'Boolean' ||
          condition.nestedType === 'date' ||
          condition.nestedType === 'Date' ||
          condition.nestedType === 'number' ||
          condition.nestedType === 'integer' ||
          condition.nestedType === 'Decimal' ||
          condition.nestedType === 'decimal' ||
          condition.nestedType === 'Double' ||
          condition.nestedType === 'dateTime'))
    ) {
      value = condition.value;
    } else if (
      (condition.type === 'pickList' ||
        condition.type === 'Picklist' ||
        condition.type === 'multiPickList' ||
        condition.type === 'MultiPickList' ||
        (condition.nestedType && condition.nestedType === 'pickList') ||
        (condition.nestedType && condition.nestedType === 'multiPickList')) &&
      operator[0].id === ' _in '
    ) {
      value = condition.value
        ?.slice(1, -1)
        .split(', ')
        .map((conditionValue: string) => conditionValue.substring(1, conditionValue.length - 1));
    } else {
      value = condition.value?.slice(1, -1);
    }

    for (const [k] of operandTypes) {
      if (operandTypes.get(k).id === condition.operand) {
        operand = operandTypes.get(k);
      }
    }
    const filteredCondition: FilterBuilderCondition = {
      error: '',
      type: 'Simple',
      field: {
        name: condition.name,
        type: condition.type,
        apiName: condition.apiName,
        nestedApiName: condition.nestedApiName,
        nestedType: condition.nestedType,
        relation: condition.relation ? condition.relation : null,
        valueSet:
          condition.valueSet ||
          condition.field?.valueSet ||
          objectMetadata.fields.filter(
            (field: RubyField) =>
              field.apiName === condition.apiName || field.apiName === condition.field?.apiName,
          )[0]?.valueSet ||
          (this.props.allMetadatas &&
            condition.nestedApiName &&
            this.props.allMetadatas
              .find(
                (m) =>
                  m.apiName ===
                  objectMetadata.fields?.find((metadata) => metadata.apiName === condition.apiName)
                    ?.lookupRelation?.referenceTo,
              )
              ?.fields?.filter((f) => f.apiName === condition.nestedApiName)[0]?.valueSet) ||
          [],
      },
      operator: operator[0],
      value: {
        value,
        translateValue: GraphQLConverter.get(condition.nestedType || condition.type)(value),
      },
      operand,
      isRequired: condition.isRequired,
    };
    if (isNested) {
      filteredCondition.type = 'Nested';
    }
    if (condition.nestedConditions && condition.nestedConditions.length > 0) {
      filteredCondition.nestedConditions = [];
      condition.nestedConditions.forEach((nestedCondition: Condition) => {
        filteredCondition.nestedConditions = filteredCondition.nestedConditions?.concat(
          this.convertConditionFromFilter(nestedCondition, true),
        );
      });
    }
    generatedConditions.push(filteredCondition);
    return generatedConditions;
  };

  convertFiltersToConditions = (conditions: Condition[]) => {
    let generatedConditions: FilterBuilderCondition[] = [];
    conditions.forEach((condition) => {
      generatedConditions = generatedConditions.concat(
        this.convertConditionFromFilter(condition, false),
      );
    });
    return generatedConditions;
  };

  applySavedFilters = async (filter: RubyFilter) => {
    // on component did mount or component did update
    if (filter && filter.conditions) {
      const savedConditions = this.convertFiltersToConditions(filter.conditions);
      this.setState({
        conditions: savedConditions,
      });
    } else {
      this.setState({
        conditions: [
          {
            error: '',
            type: 'Simple',
            field: null,
            operator: null,
            value: null,
            operand: operandTypes.get(' _and '),
            nestedConditions: [],
          },
        ],
      });
    }
  };

  componentDidUpdate = (prevProps: Props, prevState) => {
    const { referencedFilter, temporaryFilter, setTemporaryFilter } = this.props;
    if (prevProps.referencedFilter !== referencedFilter) {
      this.applySavedFilters(referencedFilter);
    }
    if (temporaryFilter?.length === 0 && prevProps.temporaryFilter?.length !== 0) {
      this.setState({
        conditions: [
          {
            error: '',
            type: 'Simple',
            field: null,
            operator: null,
            value: null,
            operand: operandTypes.get(' _and '),
            nestedConditions: [],
          },
        ],
      });
    }

    if (setTemporaryFilter) {
      setTemporaryFilter(this.state.conditions);
    }
  };

  saveFilter = async (objectApiName: string, filterName: string, conditions: Condition[]) => {
    const filterResponse = await this.props.saveFilter(objectApiName, filterName, conditions);
    return filterResponse;
    // const filterResponse = await MetadataApi.filters(objectApiName).save(filterName, conditions);
    // return filterResponse;
  };

  updateSavedFilter = (savedFiltersCopy: RubyFilter[], filterId: string, newFilter: RubyFilter) => {
    const previousFilterIndex = savedFiltersCopy.findIndex((filter) => filter.id === filterId);
    if (previousFilterIndex !== -1) {
      // replace previous filter with this new filter
      savedFiltersCopy.splice(previousFilterIndex, 1);
      savedFiltersCopy.splice(0, 0, newFilter);
    } else {
      savedFiltersCopy.unshift(newFilter);
    }
    if (this.props.updateListOfSavedFilters) {
      this.props.updateListOfSavedFilters(savedFiltersCopy);
    }
    return savedFiltersCopy;
  };

  updateSavedFiltersList = (
    newFilter: RubyFilter,
    referenceFilterId: string,
    existingFilter: RubyFilter | null,
  ) => {
    const {
      updateListOfSavedFilters,
      savedFilters,
      handleUpdateReferencedFilter,
      handleUpdateFilterNameWithValue,
    } = this.props;
    const filtersCopy = [...savedFilters];
    const updatedFilterIndex = filtersCopy.findIndex((filter) => filter.id === referenceFilterId);
    if (existingFilter && existingFilter.id.includes('appliedFilter')) {
      const appliedFilterIndex = filtersCopy.findIndex((filter) => filter.id === existingFilter.id);
      filtersCopy[appliedFilterIndex] = newFilter;
    } else if (updatedFilterIndex === -1 || (existingFilter && !existingFilter?.isUpdatable)) {
      filtersCopy.unshift(newFilter);
    } else {
      filtersCopy[updatedFilterIndex] = newFilter;
    }
    updateListOfSavedFilters(filtersCopy);
    handleUpdateReferencedFilter(newFilter);
    handleUpdateFilterNameWithValue('');
  };

  saveOrUpdateFilter = async () => {
    const { objectMetadata, referencedFilter, filterName } = this.props;
    const existingFilter =
      referencedFilter && Object.keys(referencedFilter).length > 0 && referencedFilter.id !== 'none'
        ? referencedFilter
        : undefined;
    let updatedOrNewFilter;
    const conditions = this.validateAllConditions();
    if (conditions.length === 0) {
      return;
    }

    let filterNameCopy = filterName;
    if (
      filterName === '' &&
      referencedFilter &&
      JSON.stringify(referencedFilter.isUpdatable) !== 'false'
    ) {
      filterNameCopy = referencedFilter.name;
    }

    if (!this.validateDefaultConditions(referencedFilter, conditions, true)) {
      return;
    }

    // here we must persist a new filter if the filter was updated or if there is a new name for the filter
    if (
      !existingFilter ||
      (existingFilter && filterNameCopy !== '' && filterNameCopy !== existingFilter.name)
    ) {
      updatedOrNewFilter = await this.saveFilter(
        objectMetadata.apiName,
        filterNameCopy,
        conditions,
      );
      this.setState({
        updatedFilterId: updatedOrNewFilter.id,
        updatedFilterName: filterNameCopy,
        saveFilterResponse: `The filter '${filterNameCopy}' was created.`,
      });
      this.updateSavedFiltersList(updatedOrNewFilter, updatedOrNewFilter.id, existingFilter);
    } else {
      // here we know the filters have been updated
      const hasUpdatedConditions = this.findIfConditionsWereUpdated(conditions, referencedFilter);
      if (!hasUpdatedConditions) {
        // there are no updates to the name and the filter has not changed
        return;
      }
      if (this.state.updatedFilterName) {
        updatedOrNewFilter = await this.props.updateFilters(
          objectMetadata.apiName,
          referencedFilter.id,
          this.state.updatedFilterName,
          conditions,
        );
      } else {
        updatedOrNewFilter = await this.props.updateFilters(
          objectMetadata.apiName,
          referencedFilter.id,
          referencedFilter.filterName ? referencedFilter.filterName : filterNameCopy,
          conditions,
        );
      }

      const updatedFilterName =
        updatedOrNewFilter.name !== filterNameCopy && filterNameCopy !== ''
          ? filterNameCopy
          : updatedOrNewFilter.name;
      if (updatedOrNewFilter) {
        this.setState({
          updatedFilterId: updatedOrNewFilter.id,
          saveFilterResponse: `The '${updatedFilterName}' filter was updated.`,
          updatedFilterName,
        });
      }
      // update the filter so that the new name is reflected in the list when you click "save" then "apply"
      this.updateSavedFiltersList(updatedOrNewFilter, referencedFilter.id, null);
    }

    this.onCloseWarningDialog();
  };

  getConditionRelation = (condition: FilterBuilderCondition) => {
    if (condition.field?.lookupRelation) {
      return condition.field?.lookupRelation.relationName;
    }
    if (condition.field?.masterDetailRelation) {
      return condition.field?.masterDetailRelation.relationName;
    }
    return null;
  };

  // need to translate the conditions into values to be store into our database
  deconstructConditions = (conditions: FilterBuilderCondition[]) => {
    const newConditions: Condition[] = [];
    conditions.forEach((condition: FilterBuilderCondition) => {
      let value: any;
      if (typeof condition.value?.translateValue === 'number') {
        value = condition.value?.translateValue.toString();
      } else {
        value = condition.value?.translateValue;
      }
      const newCondition: Condition = {
        apiName: condition.field?.apiName || '',
        nestedApiName: condition.field?.field?.apiName || condition.field?.nestedApiName || '',
        nestedType: condition.field?.field?.type || condition.field?.nestedType || '',
        lookupRelation: condition.field?.lookupRelation,
        operator: condition.operator?.id,
        value: value,
        operand: condition.operand?.id,
        type: condition.field?.type || '',
        name: condition.field?.field?.name
          ? condition.field?.field?.name
          : condition.field?.name || '',
        relation: condition.field?.relation,
        error: '',
        isRequired: condition?.isRequired,
      };
      if (condition.field?.field?.valueSet) {
        newCondition.valueSet = condition.field?.field?.valueSet;
      }
      if (condition.valueSet) {
        newCondition.valueSet = condition.valueSet;
      }
      if (condition.field?.valueSet) {
        newCondition.valueSet = condition.field?.valueSet;
      }
      if (condition.nestedConditions) {
        const newNestedConditions = this.deconstructConditions(condition.nestedConditions);
        newCondition.nestedConditions = newNestedConditions;
      }
      newConditions.push(newCondition);
    });
    return newConditions;
  };

  updateConditions = (conditionsCopy: FilterBuilderCondition[]) => {
    // we should only update our conditions when there is a condition with length > 0, otherwise we will remove all conditions
    if (conditionsCopy.length > 0) {
      this.setState({
        conditions: conditionsCopy,
      });
    } else {
      this.setState({
        conditions: [
          {
            error: '',
            type: 'Simple',
            field: null,
            operator: null,
            value: null,
            operand: operandTypes.get(' _and '),
            nestedConditions: [],
          },
        ],
      });
    }
  };

  validateAllConditions = () => {
    let conditions: Condition[] = [];
    if (this.state.conditions.length === 1 && this.state.conditions[0].field === null) {
      this.setState({
        saveFilterError:
          (this.props.requireAtLeastOneCondition ?? true)
            ? 'Please add a condition before applying a filter.'
            : '',
      });
      return conditions;
    }
    this.setState({
      // eslint-disable-next-line react/no-access-state-in-setstate
      conditions: this.validateConditions(this.state.conditions),
    });
    // TODO: optimize this because we have to loop through conditions twice just to see if there are any errors so we don't construct & run a query
    if (!this.hasValidConditions(this.state.conditions)) {
      return conditions;
    }
    conditions = this.deconstructConditions(this.state.conditions);
    return conditions;
  };

  validateDefaultConditions = (
    referencedFilter: Condition[],
    conditions: Condition[],
    isSaveFilter: boolean,
  ) => {
    const { filterName } = this.props;
    const diffConditionsRef = [];

    if (referencedFilter === null) {
      return true;
    }

    referencedFilter.conditions?.map((condition) => {
      diffConditionsRef.push({
        apiName: condition.apiName,
        name: condition.name,
        operand: condition.operand,
        operator: condition.operator,
        type: condition.type,
        value: condition.value,
      });
    });

    const diffConditions = [];
    conditions?.map((condition) => {
      diffConditions.push({
        apiName: condition.apiName,
        name: condition.name,
        operand: condition.operand,
        operator: condition.operator,
        type: condition.type,
        value: condition.value,
      });
    });

    if (
      referencedFilter &&
      JSON.stringify(referencedFilter.isUpdatable) === 'false' &&
      JSON.stringify(diffConditionsRef) !== JSON.stringify(diffConditions)
    ) {
      if (!isSaveFilter || !filterName || filterName.trim() === '') {
        this.setState({
          saveFilterResponse: '',
          saveFilterError:
            'Standard filter cannot be updated, try Save As to create a custom filter.',
        });
        return false;
      }
      return true;
    } else {
      this.setState({
        saveFilterError: '',
      });
      return true;
    }
  };

  findIfConditionsWereUpdated = (conditions: Condition[], filter: RubyFilter) => {
    for (let j = 0; j < conditions.length; j += 1) {
      if (
        Object.keys(filter).length > 0 &&
        JSON.stringify(conditions[j]) !== JSON.stringify(filter.conditions[j])
      ) {
        // check to see if there is any change in terms of ordering. if there is, we know that this condition
        // is updated and these changes need to be reflected.
        return true;
      }
    }

    return false;
  };

  handleApply = async () => {
    const {
      handleClose,
      handleSearch,
      referencedFilter,
      searchResult,
      savedFilters,
      filterName,
      updateListViewLocalState,
      updateListOfSavedFilters,
    } = this.props;
    let newSavedFiltersCopy = [...savedFilters];
    let updatedFilter;
    const conditions = this.validateAllConditions();
    if (conditions.length === 0) {
      return;
    }

    if (!this.validateDefaultConditions(referencedFilter, conditions, false)) {
      return;
    }

    let isUpdatable = true;
    let isExclusive = false;
    if (
      referencedFilter &&
      referencedFilter.isUpdatable !== null &&
      typeof referencedFilter.isUpdatable !== undefined
    ) {
      isUpdatable = referencedFilter.isUpdatable;
    }

    if (
      referencedFilter &&
      referencedFilter.isExclusive !== null &&
      typeof referencedFilter.isExclusive !== undefined
    ) {
      isExclusive = referencedFilter.isExclusive;
    }

    // first, check to see if you're applying a filter that has been clicked on or not
    // precedence for filter name: 1. referencedFilter, then if the filter name has been recently updated, then if not get the selected filter's name
    let newFilterName;
    if (this.state.updatedFilterName) {
      newFilterName = this.state.updatedFilterName;
    } else if (referencedFilter && Object.keys(referencedFilter).length > 0) {
      newFilterName = referencedFilter.name;
    } else {
      newFilterName = filterName;
    }
    // check to see if a filter from the list of saved filters has been selected
    if (this.state.updatedFilterId) {
      const filter: RubyFilter = newSavedFiltersCopy.find(
        (savedFilter: RubyFilter) => savedFilter.id === this.state.updatedFilterId,
      );
      updatedFilter = { ...filter, isApplied: true };
    } else if (this.isExistingFilter(referencedFilter)) {
      updatedFilter = {
        id: referencedFilter.id,
        name: newFilterName,
        conditions,
        isApplied: true,
        isUpdatable: isUpdatable,
        isExclusive: isExclusive,
      };
    } else {
      const numUnsavedFilters =
        savedFilters.filter((filter: RubyFilter) => filter.isUnsavedFilter).length + 1;
      updatedFilter = {
        id: `appliedFilter${numUnsavedFilters}`,
        name: `Applied Filter ${numUnsavedFilters}`,
        conditions,
        isApplied: true,
        isUnsavedFilter: true,
        isUpdatable: isUpdatable,
        isExclusive: isExclusive,
      };
    }

    if (updatedFilter.isExclusive) {
      const appliedAndExclusiveFilters = newSavedFiltersCopy.filter(
        (filter) => filter.isApplied === true && filter.isExclusive === true,
      );
      let excludeIds = [];
      if (appliedAndExclusiveFilters && appliedAndExclusiveFilters.length > 0) {
        excludeIds = appliedAndExclusiveFilters
          .filter((filter) => filter.id !== updatedFilter.id)
          .map((filter) => filter.id);
      }

      if (excludeIds && excludeIds.length > 0) {
        newSavedFiltersCopy = newSavedFiltersCopy.map((filter) => {
          const item = excludeIds.find((e) => e === filter.id);
          if (item) {
            return { ...filter, isApplied: false };
          }
          return filter;
        });
      }
    }

    // you may not be applying a saved filter, so we simply append this to the list of applied filters
    newSavedFiltersCopy = this.updateSavedFilter(
      newSavedFiltersCopy,
      updatedFilter.id,
      updatedFilter,
    );

    const appliedFilters = newSavedFiltersCopy.filter((filter) => filter.isApplied === true);
    if (updateListViewLocalState) {
      await updateListViewLocalState({ filtersToApply: appliedFilters });
    }
    this.closeAndExecute(searchResult, appliedFilters, handleClose, handleSearch);
  };

  handleSaveFilterWithoutClose = async () => {
    const {
      handleClose,
      handleSearch,
      referencedFilter,
      searchResult,
      savedFilters,
      filterName,
      updateListViewLocalState,
      updateListOfSavedFilters,
    } = this.props;
    let newSavedFiltersCopy = [...savedFilters];
    let updatedFilter;
    const conditions = this.validateAllConditions();
    if (conditions.length === 0) {
      return;
    }

    if (!this.validateDefaultConditions(referencedFilter, conditions, false)) {
      return;
    }

    let isUpdatable = true;
    let isExclusive = false;
    if (
      referencedFilter &&
      referencedFilter.isUpdatable !== null &&
      typeof referencedFilter.isUpdatable !== undefined
    ) {
      isUpdatable = referencedFilter.isUpdatable;
    }

    if (
      referencedFilter &&
      referencedFilter.isExclusive !== null &&
      typeof referencedFilter.isExclusive !== undefined
    ) {
      isExclusive = referencedFilter.isExclusive;
    }

    // first, check to see if you're applying a filter that has been clicked on or not
    // precedence for filter name: 1. referencedFilter, then if the filter name has been recently updated, then if not get the selected filter's name
    let newFilterName;
    if (this.state.updatedFilterName) {
      newFilterName = this.state.updatedFilterName;
    } else if (referencedFilter && Object.keys(referencedFilter).length > 0) {
      newFilterName = referencedFilter.name;
    } else {
      newFilterName = filterName;
    }
    // check to see if a filter from the list of saved filters has been selected
    if (this.state.updatedFilterId) {
      const filter: RubyFilter = newSavedFiltersCopy.find(
        (savedFilter: RubyFilter) => savedFilter.id === this.state.updatedFilterId,
      );
      updatedFilter = { ...filter, isApplied: true };
    } else if (this.isExistingFilter(referencedFilter)) {
      updatedFilter = {
        id: referencedFilter.id,
        name: newFilterName,
        conditions,
        isApplied: true,
        isUpdatable: isUpdatable,
        isExclusive: isExclusive,
      };
    } else {
      const numUnsavedFilters =
        savedFilters.filter((filter: RubyFilter) => filter.isUnsavedFilter).length + 1;
      updatedFilter = {
        id: `appliedFilter${numUnsavedFilters}`,
        name: `Applied Filter ${numUnsavedFilters}`,
        conditions,
        isApplied: true,
        isUnsavedFilter: true,
        isUpdatable: isUpdatable,
        isExclusive: isExclusive,
      };
    }

    if (updatedFilter.isExclusive) {
      const appliedAndExclusiveFilters = newSavedFiltersCopy.filter(
        (filter) => filter.isApplied === true && filter.isExclusive === true,
      );
      let excludeIds = [];
      if (appliedAndExclusiveFilters && appliedAndExclusiveFilters.length > 0) {
        excludeIds = appliedAndExclusiveFilters
          .filter((filter) => filter.id !== updatedFilter.id)
          .map((filter) => filter.id);
      }

      if (excludeIds && excludeIds.length > 0) {
        newSavedFiltersCopy = newSavedFiltersCopy.map((filter) => {
          const item = excludeIds.find((e) => e === filter.id);
          if (item) {
            return { ...filter, isApplied: false };
          }
          return filter;
        });
      }
    }

    // you may not be applying a saved filter, so we simply append this to the list of applied filters
    newSavedFiltersCopy = this.updateSavedFilter(
      newSavedFiltersCopy,
      updatedFilter.id,
      updatedFilter,
    );

    const appliedFilters = newSavedFiltersCopy.filter((filter) => filter.isApplied === true);
    if (updateListViewLocalState) {
      await updateListViewLocalState({ filtersToApply: appliedFilters });
    }
    this.closeAndExecute(searchResult, appliedFilters, () => {}, handleSearch);
  };

  graphqlGenerator = () => {
    const { setGraphqlGeneratorResult } = this.props;
    const conditions = this.validateAllConditions();
    if (conditions.length === 0) {
      return;
    }

    const filter = [
      {
        id: '1',
        name: 'newFilterName',
        conditions,
        isApplied: true,
      },
    ];

    const whereCondition = GraphQLQueryConstructor.construct().conditionsFromFilters(filter);
    this.setState({
      saveFilterError: '',
    });
    if (setGraphqlGeneratorResult) {
      setGraphqlGeneratorResult(whereCondition);
    }
    return whereCondition;
  };

  isExistingFilter = (filter: RubyFilter) => {
    return filter && Object.keys(filter).length > 0 && filter.id !== 'none';
  };

  closeAndExecute = async (
    searchResult: string,
    filtersToApply: RubyFilter[],
    handleClose: () => void,
    handleSearch: (
      searchResult: string,
      filters: RubyFilter[],
      orderByFields?: OrderByField[],
    ) => Promise<void>,
  ) => {
    handleClose();
    await handleSearch(searchResult, filtersToApply);
  };

  hasValidConditions = (conditions?: Condition[]) => {
    let validFlag = true;
    if (!conditions) {
      return validFlag;
    }
    for (let i = 0; i < conditions.length; i += 1) {
      if (conditions[i].error) {
        validFlag = false;
        break;
      }
      if (conditions[i].nestedConditions) {
        const areNestedConditionsValid = this.hasValidConditions(conditions[i].nestedConditions);
        if (areNestedConditionsValid) {
          // eslint-disable-next-line no-continue
          continue;
        } else {
          validFlag = false;
        }
      }
    }
    return validFlag;
  };

  validateSaveNameAndEmptyFilter = () => {
    const { filterName, referencedFilter, disableSaveButton, currentUserId } = this.props;
    this.setState({
      saveFilterError: '',
    });
    if ((!filterName || filterName.trim() === '') && referencedFilter?.isUpdatable === false) {
      this.setState({
        saveFilterResponse: '',
        saveFilterError:
          'Standard filter cannot be updated, try Save As to create a custom filter.',
      });
      return false;
    }

    if (
      disableSaveButton &&
      referencedFilter.ownerId &&
      referencedFilter.ownerId !== currentUserId
    ) {
      this.setState({
        saveFilterResponse: '',
        saveFilterError:
          'You cannot edit this filter as it is owned by another user. Please use the "Save As" option to save it as a new filter before making any edits. ',
      });
      return false;
    }

    if (
      filterName === '' &&
      (!referencedFilter ||
        Object.keys(referencedFilter).length === 0 ||
        referencedFilter.id === 'none' ||
        referencedFilter.id.includes('appliedFilter'))
    ) {
      this.setState({
        saveFilterError: 'A Filter Name is required.',
      });
      return false;
    }

    if (this.state.conditions.length === 1 && this.state.conditions[0].field === null) {
      this.setState({
        saveFilterError: 'Create a Filter Condition before saving this filter.',
      });
      return false;
    }
    return true;
  };

  openWarningDialogBeforeProceedAction = (pendingAction: 'delete' | 'save') => {
    this.setState({
      openWarningDialog: true,
      pendingAction: pendingAction,
    });
  };

  handleSaveFilter = () => {
    if (!this.validateSaveNameAndEmptyFilter()) {
      return;
    }
    const { referencedFilter, filterName } = this.props;
    const isUpdating =
      referencedFilter &&
      Object.keys(referencedFilter).length > 0 &&
      referencedFilter.id !== 'none' &&
      _.isEmpty(filterName);

    if (
      referencedFilter &&
      referencedFilter.ownerId &&
      referencedFilter.ownerId !== this.props.currentUserId &&
      _.isEmpty(filterName)
    ) {
      this.openWarningDialogBeforeProceedAction('save');
      return;
    }

    if (isUpdating && this.props.warningBeforeUpdate?.show && !this.state.warningDontAskAgain) {
      this.openWarningBeforeUpdateDialog();
    } else {
      this.saveOrUpdateFilter();
    }
  };

  openWarningBeforeUpdateDialog = () => this.setState({ openWarningBeforeUpdate: true });

  closeWarningBeforeUpdateDialog = () => this.setState({ openWarningBeforeUpdate: false });

  handleOpenDeleteFilterWarning = () => {
    const { referencedFilter } = this.props;
    if (
      !referencedFilter ||
      Object.keys(referencedFilter).length === 0 ||
      referencedFilter.id === 'none'
    ) {
      this.setState({
        saveFilterResponse: '',
        saveFilterError: `Please select a saved filter to delete.`,
      });
    } else {
      this.setState({
        openWarningDialog: true,
        pendingAction: 'delete',
      });
    }
  };

  onCloseWarningDialog = () => {
    this.setState({
      openWarningDialog: false,
      pendingAction: null,
    });
  };

  onSubmitWarningDialog = async () => {
    if (this.state.pendingAction === 'delete') {
      await this.handleDeleteFilter();
      return;
    } else if (this.state.pendingAction === 'save') {
      await this.saveOrUpdateFilter();
      return;
    }
  };
  handleDeleteFilter = async () => {
    const {
      referencedFilter,
      objectMetadata,
      handleUpdateReferencedFilter,
      savedFilters,
      deleteFilter,
    } = this.props;
    const existingFilter =
      Object.keys(referencedFilter).length > 0 && referencedFilter.id !== 'none'
        ? referencedFilter
        : undefined;
    if (!existingFilter) {
      return;
    }
    if (existingFilter.id.includes('appliedFilter')) {
      this.removeFilter(savedFilters, existingFilter);
      handleUpdateReferencedFilter({});
      this.onCloseWarningDialog();
      return;
    }
    const deleteSuccess = await deleteFilter(objectMetadata.apiName, existingFilter.id);
    if (!deleteSuccess.error) {
      this.setState({
        saveFilterResponse: `'${referencedFilter.name}' filter has successfully been deleted.`,
        updatedFilterId: '',
        updatedFilterName: '',
      });
      handleUpdateReferencedFilter({});
      this.removeFilter(savedFilters, existingFilter);
    } else {
      this.setState({
        saveFilterError: `There was an error deleting this filter: ${deleteSuccess.error}`,
      });
    }
    this.onCloseWarningDialog();
  };

  buildWarningDialogMessage = () => {
    const { referencedFilter } = this.props;
    if (this.state.pendingAction === 'delete') {
      return `Are you sure you want to delete '${referencedFilter.name}'? This action cannot be undone.`;
    } else if (this.state.pendingAction === 'save') {
      return `The filter ${referencedFilter?.name} is owned by another user.  Are you sure you want to proceed?`;
    }
  };

  removeFilter = (filters: RubyFilter[], filterToRemove: RubyFilter) => {
    const filtersCopy = [...filters];
    const updatedFilterIndex = filtersCopy.findIndex((filter) => filter.id === filterToRemove.id);
    filtersCopy.splice(updatedFilterIndex, 1);
    this.props.updateListOfSavedFilters(filtersCopy);
  };

  // eslint-disable-next-line class-methods-use-this
  validateCondition(condition: FilterBuilderCondition) {
    if (!condition.field) {
      condition.error = 'Please enter a valid field.';
    } else if (!condition.operator) {
      condition.error = 'Please enter a valid operator.';
    } else if (
      condition.operator.id !== ' _is_null ' &&
      condition.operator.id !== ' _is_not_null ' &&
      (condition.value === null ||
        condition.value === undefined ||
        condition.value.value === null ||
        condition.value.value === undefined ||
        condition.value.value.length === 0)
    ) {
      console.log('condition.operator.id:' + condition.operator.id);
      console.log('condition.value:' + condition.value);
      console.debug('Invalid condition value', condition);
      condition.error = 'Please enter a valid value.';
    } else if (
      condition.field.type === 'date' &&
      condition.operator.id !== ' _is_null ' &&
      condition.operator.id !== ' _is_not_null ' &&
      // @ts-ignore
      isNaN(new Date(condition.value.value)) &&
      !condition.value.value.includes('$')
    ) {
      condition.error = 'Please enter a valid date.';
    }
    return condition;
  }

  validateConditions(conditions: FilterBuilderCondition[]) {
    const conditionsCopy = [...conditions];
    for (let i = 0; i < conditionsCopy.length; i += 1) {
      conditionsCopy[i].error = null;
      conditionsCopy[i] = this.validateCondition(conditionsCopy[i]);
      if (conditionsCopy[i].nestedConditions) {
        conditionsCopy[i].nestedConditions = this.validateConditions(
          conditionsCopy[i].nestedConditions || [],
        );
      }
    }
    return conditionsCopy;
  }

  handleChange(
    selectedField: FilterBuilderCondition,
    index: number,
    value: string,
    changeType: string,
    translateValue: string | null,
    conditions: FilterBuilderCondition[],
    updateConditions: (newConditions: FilterBuilderCondition[]) => void,
    fieldValue?: RubyField,
  ) {
    const { dispatch, objectMetadata, object, graphqlGenerator } = this.props;
    const conditionsCopy = [...conditions];
    let condition;
    let isOperandChange = false;
    if (changeType === 'field') {
      const field = objectMetadata.fields.filter(
        (metadataField: RubyField) => metadataField.apiName === value,
      )[0];
      condition = selectedField;
      if (field) {
        condition = {
          ...condition,
          field: {
            ...field,
            // eslint-disable-next-line no-nested-ternary
            relation: field.lookupRelation
              ? field.lookupRelation.relationName
              : field.masterDetailRelation
                ? field.masterDetailRelation.relationName
                : null,
          },
        };
      }
      if (fieldValue) {
        condition.field.field = fieldValue;
      }
      if (condition?.value) {
        // reset the value so that an invalid field does not populate
        condition.value = null;
      }
      if (condition?.operator && !condition?.value) {
        // if you change the where field to 'none', you should also change the operator to undefined
        condition.operator = undefined;
      }
      // do validation check
      // setQuery(dispatch, "");
    } else if (changeType === 'field.field') {
      condition = selectedField;
      condition.field.field = value;
      if (condition?.value) {
        // reset the value so that an invalid field does not populate
        condition.value = null;
      }
      if (condition?.operator && !condition?.value) {
        // if you change the where field to 'none', you should also change the operator to undefined
        condition.operator = undefined;
      }
    } else if (changeType === 'operator') {
      condition = {
        ...selectedField,
        operator: operatorTypes
          .get(
            selectedField.field?.field?.type
              ? selectedField.field?.field?.type
              : selectedField.field?.nestedType || selectedField.field?.type,
          )
          .filter((opt: Operator) => {
            return opt.id === value;
          })[0],
      };
      if (
        !condition.value ||
        (condition.operator?.id === ' _in ' &&
          (selectedField.field?.type === 'pickList' ||
            selectedField.field?.nestedType === 'pickList'))
      ) {
        // RIM-12180: https://rubyio.atlassian.net/browse/RIM-12180
        // For now we have an issue whenever user switch the equals/contains/is null, the value sometimes has ""xxx"" format, which makes the graphql not correct
        // ->>>>> the solution is that whenever user switch the operator, we reset the value, that's why I hide the logic below.
        // condition.value = this.setupDefaultValueFilters(
        //   condition.operator?.id,
        //   selectedField.field?.field?.type
        //     ? selectedField.field?.field?.type
        //     : selectedField.field?.type || selectedField.field?.nestedType,
        // );
        condition.value = '';
      } else if (selectedField.operator?.id === ' _is_null ') {
        condition.value = {
          value: true,
          translateValue: 'true',
        };
      } else if (selectedField.operator?.id === ' _is_not_null ') {
        condition.value = {
          value: false,
          translateValue: 'false',
        };
      }
    } else if (changeType === 'value') {
      condition = {
        ...selectedField,
        value: { value, translateValue },
      };
    } else {
      // if changeType === "operand"
      condition = {
        ...selectedField,
        operand: operandTypes.get(value),
      };
      isOperandChange = true;
    }
    if (condition && condition.error) {
      condition.error = '';
    }
    if (isOperandChange && condition?.type !== 'Nested') {
      // here we need to update the previous condition so that the operand change is reflected properly
      conditionsCopy[index - 1] = condition;
    } else {
      conditionsCopy[index] = condition;
    }
    updateConditions(conditionsCopy);
  }

  render() {
    const {
      classes,
      objectMetadata,
      handleClose,
      referencedFilter,
      hiddenButton,
      invokeObject,
      disabled,
      setTemporaryFilter,
      hideNestedCondition,
      disableSaveButton = false,
      disableDeleteButton = false,
      currentUserId,
      allMetadatas,
      disableApplyButton = false,
      disableSave = false,
      specifyRelatedObjects,
    } = this.props;
    const sortedFilterableFields = objectMetadata.fields
      .filter(({ filterable, type }: RubyField) => filterable === true && operatorTypes.has(type))
      .sort((a: RubyFilter, b: RubyFilter) => {
        return a.name.localeCompare(b.name);
      });
    if (invokeObject) {
      invokeObject.validateAllConditions = () => {
        return this.validateAllConditions();
      };
      invokeObject.graphqlGenerator = () => {
        return this.graphqlGenerator();
      };
    }

    const rightButtons: RubyButtonProps[] = disableApplyButton
      ? disableSave
        ? [
            {
              text: 'Close',
              onClick: this.props.handleClose,
            },
          ]
        : [
            {
              text: 'Save and Close',
              onClick: this.handleApply,
            },
            {
              text: 'Save',
              onClick: this.handleSaveFilterWithoutClose,
            },
          ]
      : [
          {
            text: 'Apply',
            onClick: this.handleApply,
          },
          {
            text: 'Save',
            onClick: this.handleSaveFilter,
          },
        ];
    if (referencedFilter && referencedFilter.isUpdatable !== false && !disableDeleteButton) {
      rightButtons.push({
        text: 'Delete Filter',
        onClick: this.handleOpenDeleteFilterWarning,
      });
    }
    return (
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        {/* <MuiThemeProvider theme={theme}> */}
        <ConditionList
          conditions={this.state.conditions}
          fields={sortedFilterableFields}
          handleChange={this.handleChange}
          updateConditions={this.updateConditions}
          objectMetadata={objectMetadata}
          allMetadatas={allMetadatas}
          isFirst
          disabled={disabled}
          hideNestedCondition={hideNestedCondition}
          specifyRelatedObjects={specifyRelatedObjects}
        />

        {/* Hide when no text is set */}
        {Boolean(this.state.saveFilterResponse || this.state.saveFilterError) && (
          <section className={classes.conditionSection}>
            <FormControl className={classes.formControl} style={{ marginBottom: '10px' }}>
              <div
                className={
                  this.state.saveFilterResponse ? classes.saveFilterText : classes.errorFilterText
                }
              >
                {this.state.saveFilterResponse
                  ? this.state.saveFilterResponse
                  : this.state.saveFilterError}
              </div>
            </FormControl>
          </section>
        )}

        {!hiddenButton && (
          <RubyButtonBar
            variant="inPlace"
            style={{ marginTop: '30px' }}
            leftButtons={[
              {
                text: 'Cancel',
                onClick: handleClose,
              },
            ]}
            rightButtons={rightButtons}
            customButtonBarStyles={{
              paddingLeft: '12px',
              paddingRight: '12px',
            }}
          />
        )}

        <DialogComponent
          width="sm"
          open={this.state.openWarningDialog}
          handleClose={this.onCloseWarningDialog}
          handleSubmit={this.onSubmitWarningDialog}
          title={this.state.pendingAction === 'delete' ? 'Delete Filter' : 'Save Filter'}
        >
          {referencedFilter && (
            <p className={classes.dialogContent}>{this.buildWarningDialogMessage()}</p>
          )}
        </DialogComponent>
        {this.props.warningBeforeUpdate?.show && (
          <DialogComponent
            open={this.state.openWarningBeforeUpdate}
            title={this.props.warningBeforeUpdate.title}
            width={this.props.warningBeforeUpdate.width || 'md'}
            submitButtonText={'Yes'}
            cancelButtonText={'No'}
            handleSubmit={async () => {
              try {
                await this.saveOrUpdateFilter();
              } finally {
                this.closeWarningBeforeUpdateDialog();
              }
            }}
            handleClose={() => {
              this.closeWarningBeforeUpdateDialog();
            }}
          >
            <p>{this.props.warningBeforeUpdate.message}</p>

            <FormControlLabel
              control={
                <Checkbox
                  checked={this.state.warningDontAskAgain}
                  name="dont-ask-again"
                  onChange={(event, value) => {
                    this.setState({
                      warningDontAskAgain: value,
                    });
                    this.props.warningBeforeUpdate.storeDontAskAgain(value);
                  }}
                  disableRipple
                  className={classes.checkBox}
                />
              }
              label={<Typography className={classes.label}>Do not ask me again</Typography>}
              className={classes.labelSpacing}
            />
          </DialogComponent>
        )}
        {/* </MuiThemeProvider> */}
      </MuiPickersUtilsProvider>
    );
  }
}

// @ts-ignore
const styledFilter = withStyles(FilterBuilderStyles, { withTheme: true })(FilterBuilder);
export default styledFilter;

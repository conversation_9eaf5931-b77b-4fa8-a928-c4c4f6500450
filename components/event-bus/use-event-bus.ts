const registry = new Map<string, Map<string, Function>>();

export type EventType = 'openActionDialog' | 'closeActionDialog';
export const useEventBus = () => {
  const subscribeEvent = (eventName: string, subscriberId: string, callback: Function) => {
    if (!registry.has(eventName)) {
      registry.set(eventName, new Map<string, Function>());
    }
    registry.get(eventName)?.set(subscriberId, callback);
  };

  const unsubscribeEvent = (eventName: string, subscriber: string, callback: Function) => {
    if (!registry.has(eventName)) {
      return;
    }
    registry.get(eventName)?.delete(subscriber);
  };

  const publishEvent = (eventName: string, ...args: any) => {
    if (!registry.has(eventName)) {
      return;
    }
    registry.get(eventName)?.forEach((callback, subscriberId) => {
      console.debug('handle event ' + eventName, subscriberId, callback);
      callback(...args);
    });
  };

  return {
    subscribeEvent,
    unsubscribeEvent,
    publishEvent,
  };
};

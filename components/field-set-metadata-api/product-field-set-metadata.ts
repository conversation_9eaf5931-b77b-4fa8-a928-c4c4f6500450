export default [
  {
    apiName: 'name',
    name: 'Name',
    type: 'text',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    minLength: 0,
    maxLength: 200,
  },
  {
    apiName: 'priceModel',
    name: 'Revenue Model',
    type: 'pickList',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    defaultValue: 'Recurring',
    required: false,
    unique: false,
    valueSet: {
      apiName: 'PriceModel',
      name: 'Price Model',
      defaultValue: 'Recurring',
      alphabeticallyOrdered: true,
      valuePairs: [
        { apiName: 'OneTime', name: 'One Time', orderNumber: 0 },
        { apiName: 'Recurring', name: 'Recurring', orderNumber: 0 },
        { apiName: 'Usage', name: 'Usage', orderNumber: 0 },
      ],
    },
  },
  {
    apiName: 'defaultUomId',
    name: 'Default UOM Id',
    type: 'bLookup',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: false,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    lookupRelation: {
      referenceTo: 'UOM',
      referenceField: 'id',
      relationName: 'defaultUom',
      relationLabel: 'Default UOM',
    },
  },
  {
    apiName: 'sku',
    name: 'Sku',
    type: 'text',
    creatable: true,
    updatable: true,
    filterable: true,
    sortable: true,
    customField: false,
    encrypted: false,
    required: true,
    unique: false,
    minLength: 0,
    maxLength: 200,
  },
];

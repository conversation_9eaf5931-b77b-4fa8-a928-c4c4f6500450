import * as React from 'react';
import { MenuItem, FormControl, ListItemText, Checkbox, Select } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import SelectInputBase from '../select-input-base';
import { Props } from './interface';

const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: 280,
      width: 200,
    },
  },
};

const useStyles = makeStyles({
  root: {},
  selectBorderColor: {
    borderColor: 'red',
  },
  titleLabel: {
    textTransform: 'uppercase',
    color: '#9c9c9c',
    fontWeight: 'bold',
    fontSize: '.80rem',
    marginTop: '4px',
  },
  formControl: {
    flexDirection: 'row',
    width: '100%',
  },
  optionLabel: {
    '& > span': {
      color: '#6e6e6e',
      fontSize: '.9rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
  },
  container: {
    display: 'flex',
    alignItems: 'center',
  },
});

export const MultipleSelectCheckmarks: React.FC<Props> = (userProps) => {
  const props = { ...userProps };
  const {
    options,
    value,
    selectBorderColor = false,
    style,
    name,
    label,
    fullWidth = false,
    placeholder,
    onChange,
  } = props;
  const classes = useStyles();

  return (
    <div className={classes.container} style={style}>
      <FormControl fullWidth={fullWidth}>
        <Select
          classes={{
            root: selectBorderColor ? classes.selectBorderColor : classes.root,
          }}
          multiple
          value={value}
          displayEmpty
          name={name}
          fullWidth={fullWidth}
          label={label}
          onChange={onChange}
          placeholder={placeholder}
          input={<SelectInputBase placeholder={placeholder} disabled={false} />}
          renderValue={(selected) => {
            if (Array.isArray(selected)) {
              return selected.join(', ');
            }
          }}
          MenuProps={MenuProps}
        >
          {options.map((name, index) => (
            <MenuItem key={index} value={name}>
              <Checkbox checked={value.indexOf(name) > -1} />
              <ListItemText primary={name} />
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </div>
  );
};

export default MultipleSelectCheckmarks;

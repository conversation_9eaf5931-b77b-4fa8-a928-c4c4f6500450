import React from 'react';

import DateFnsUtils from '@date-io/date-fns';
import { FormControl, ThemeProvider } from '@material-ui/core';
import { KeyboardDatePicker, MuiPickersUtilsProvider } from '@material-ui/pickers';
import { KeyboardDatePickerProps } from '@material-ui/pickers/DatePicker/DatePicker';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import { CalendarIcon } from '../icons';
import LabelWithTooltip from '../label-with-tooltip';
import { MuiPickersTheme } from '../theme/theme';
import useDateInputStyles from './date-input-style';
import { Props } from './interface';

const defaultProps = {
  required: false,
  readOnly: false,
  value: null,
  dateFormat: 'yyyy-MM-dd',
};

dayjs.extend(utc);

export const StyledKeyboardDatePicker: React.FC<KeyboardDatePickerProps> = ({
  className,
  ...otherProps
}) => {
  const classes = useDateInputStyles();
  return (
    <ThemeProvider theme={MuiPickersTheme}>
      <KeyboardDatePicker
        className={className || classes.root}
        keyboardIcon={<CalendarIcon />}
        {...otherProps}
      />
    </ThemeProvider>
  );
};

const DateInput: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const {
    value,
    label,
    field,
    name,
    disabled,
    handleInputChange,
    className,
    readOnly,
    required,
    dateFormat,
    showTooltip,
    toolTipText,
    disableLabel,
    minDate,
    clearable,
    formControlClassName,
    minDateMessage,
  } = props;
  //todo make persisted value format a prop
  let parsedDate: dayjs.Dayjs | null;
  if (value === '' || value === null) {
    parsedDate = null;
  } else if (value) {
    parsedDate = dayjs(dayjs(value).format('YYYY-MM-DD'));
  } else if (field?.defaultValue) {
    parsedDate = dayjs(field.defaultValue, 'YYYY-MM-DD');
  } else {
    parsedDate = null;
  }
  const _label = label || field.name;
  const isRequired = required || field.required;
  const isClearable = clearable || field?.clearable;
  return (
    <FormControl style={{ width: '100%' }} className={formControlClassName}>
      <MuiPickersUtilsProvider utils={DateFnsUtils}>
        {!disableLabel && (
          <LabelWithTooltip
            label={_label}
            required={isRequired}
            shrink
            showTooltip={showTooltip}
            tooltipText={toolTipText}
            htmlFor={name || field.apiName}
          />
        )}
        <StyledKeyboardDatePicker
          format={dateFormat}
          disabled={disabled || readOnly}
          inputVariant="outlined"
          error={false}
          name={name || field.apiName}
          placeholder={_label}
          className={className}
          autoOk
          label={label}
          value={parsedDate}
          style={{ flexGrow: 1 }}
          required={isRequired}
          readOnly={readOnly}
          minDate={minDate}
          minDateMessage={minDateMessage}
          keyboardIcon={<CalendarIcon />}
          clearable={isClearable}
          onChange={(date, value) => {
            if (handleInputChange && date !== null && value !== null) {
              const formattedDate = dayjs(date).format('YYYY-MM-DD');
              if (dayjs(date).isValid()) {
                // only trigger updates to pricing calculator when date is valid
                handleInputChange(formattedDate);
              }
            } else if (handleInputChange && isClearable && !value) {
              handleInputChange(value);
            }
          }}
        />
      </MuiPickersUtilsProvider>
    </FormControl>
  );
};

export default DateInput;

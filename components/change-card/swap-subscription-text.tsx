import React from 'react';
import { Typography, makeStyles } from '@material-ui/core';
import dayjs from 'dayjs';
import type { ChangeItemText } from './interface';
import type { SwapRequest } from '../change-cart';

const useStyles = makeStyles((theme) => ({
  itemText: {
    color: theme.palette.grey[900],
    fontWeight: 500,
    fontSize: '.85rem',
  },
  textHighlight: {
    color: '#6239EB',
    fontWeight: 'bold',
    fontSize: '.85rem',
  },
  textUnderline: {
    opacity: 0.8,
    textDecorationColor: '#6239EB',
    textDecorationStyle: 'dotted',
    textDecorationLine: 'underline',
    fontWeight: 500,
    cursor: 'pointer',
    fontSize: '.85rem',
  },
}));

const SwapSubscriptionText: React.FC<ChangeItemText> = (props) => {
  const classes = useStyles();

  const { changeItem } = props;

  // Ensure the request is of the expected type
  if (!changeItem.request || changeItem.request.changeType !== 'Swap') {
    console.error('Expected Swap request type but received:', changeItem.request);
  }

  const request = changeItem.request as SwapRequest;

  const { objectType, assetName, toProduct, startDate } = request;
  const { product } = changeItem.asset;

  const formattedStartDate = dayjs(startDate).format('MM/DD/YYYY');

  return (
    <Typography variant="body2" component="p" display="inline" className={classes.itemText}>
      The {objectType} <span className={classes.textUnderline}>{assetName}</span> of product{' '}
      <span className={classes.textHighlight}>{product?.name || 'Unknown'}</span> is swapped with{' '}
      <span className={classes.textHighlight}>{toProduct?.name || 'Unknown'}</span> on{' '}
      <span className={classes.textHighlight}>{formattedStartDate}</span>
    </Typography>
  );
};

export default SwapSubscriptionText;

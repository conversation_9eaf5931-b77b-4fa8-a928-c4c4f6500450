import React, { useMemo } from 'react';

import { Checkbox, FormControlLabel, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';

import HelpIcon from '../help-icon';
import { Props } from './interface';

const useStyles = makeStyles({
  checkBox: {
    padding: '0px',
    borderRadius: '0px',
    marginRight: '16px',
    color: '#9f9f9f',
    '&:hover': {
      fill: '#9f9f9f',
      backgroundColor: 'transparent',
    },
  },
  label: {
    marginLeft: '0px',
    color: '#6e6e6e',
    fontSize: '.9rem',
    fontWeight: 400,
    lineHeight: 1.5,
    letterSpacing: '0.00938em',
  },
  container: {
    display: 'flex',
    alignItems: 'center',
  },
});

const defaultProps = {};

export const RubyCheckbox: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const {
    value,
    field,
    label,
    disabled,
    handleInputChange,
    className,
    required,
    readOnly,
    labelClassName,
    showTooltip,
    toolTipText,
    labelStyles,
    toolTipStyles,
    style,
    hideLabel,
  } = props;
  const classes = useStyles();
  let checked: boolean;
  if (value !== undefined) {
    checked = !!value;
  } else {
    checked = field.defaultValue !== undefined ? !!field.defaultValue : false;
  }

  return (
    <div className={classes.container} style={style}>
      <FormControlLabel
        control={
          <Checkbox
            checked={checked}
            value={checked}
            name={field.apiName}
            required={required === undefined ? required : field.required}
            onChange={(event: React.ChangeEvent<HTMLInputElement>, newValue: boolean) => {
              if (handleInputChange) {
                handleInputChange(newValue);
              }
            }}
            disabled={disabled || readOnly}
            disableRipple={true}
            className={className ? `${className} ${classes.checkBox}` : `${classes.checkBox}`}
          />
        }
        label={
          hideLabel ? (
            ''
          ) : labelStyles ? (
            <Typography style={labelStyles}>{label || field.name}</Typography>
          ) : (
            label || field.name
          )
        }
        className={labelClassName ? labelClassName : classes.label}
      />
      {showTooltip && (
        <HelpIcon style={toolTipStyles} toolTipText={toolTipText || ''} toolTipPlacement="bottom" />
      )}
    </div>
  );
};

export default RubyCheckbox;

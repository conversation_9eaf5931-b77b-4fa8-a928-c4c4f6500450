import { makeStyles } from '@material-ui/core/styles';

export const useStyles = makeStyles((theme) => ({
  dialogHeader: {
    paddingBottom: theme.spacing(3),
  },
  dialogTitle: {
    padding: 0,
  },
  section: {
    marginBottom: theme.spacing(3),
  },
  mainTitle: {
    fontSize: '20px',
    fontWeight: 500,
    marginBottom: theme.spacing(1),
  },
  subTitle: {
    fontSize: '16px',
    fontWeight: 400,
    marginBottom: theme.spacing(3),
    color: theme.palette.text.secondary,
  },
  formControl: {
    marginBottom: theme.spacing(2),
    '& .MuiOutlinedInput-root': {
      backgroundColor: '#fff',
      borderRadius: '4px',
      '& fieldset': {
        borderColor: '#e0e0e0',
      },
    },
    '& .MuiOutlinedInput-input': {
      padding: '14px',
    },
  },
  addButton: {
    backgroundColor: '#e0e0e0',
    color: '#000',
    textTransform: 'none',
    padding: '6px 16px',
    borderRadius: '4px',
    '&:hover': {
      backgroundColor: '#d5d5d5',
    },
  },
  actionList: {
    marginTop: theme.spacing(3),
  },
}));

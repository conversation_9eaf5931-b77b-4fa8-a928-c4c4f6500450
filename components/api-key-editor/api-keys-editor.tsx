import React, { useContext } from 'react';
import { <PERSON>rid, Toolt<PERSON>, Typography, makeStyles } from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import DeleteOutlineIcon from '@material-ui/icons/DeleteOutline';
import EditIcon from '@material-ui/icons/Edit';
import HistoryOutlinedIcon from '@material-ui/icons/HistoryOutlined';
import { GridActions, RubyButton, RubyGrid } from '@nue-apps/ruby-ui-component';
import { ViewIcon } from '@nue-apps/ruby-ui-component';
import dayjs from 'dayjs';
import produce from 'immer';
import ApiKeyPasswordDialog from '../api-key-password-dialog/api-key-password-dialog';
import ApiKeysLog from '../api-keys-log/api-keys-log';
import { MuiGridButton } from '../grid-actions/grid-actions';
import { RollApiKeyDialog } from '../roll-api-key-dialog/roll-api-key-dialog';
import { RubySettingEditorPanel } from '../ruby-settings/ruby-setting-editor-panel';
import { UpsertApiKeyDialog } from '../upsert-api-key-dialog/upsert-api-key-dialog';
import ApiKeysContext from './api-keys-context';

const useStyles = makeStyles({
  sectionTitle: {
    opacity: 0.7,
    color: '#000000',
    fontSize: '16px',
    fontWeight: 400,
    letterSpacing: '-0.77px',
    lineHeight: '20px',
    marginBottom: '16px',
  },
  blurText: {
    backgroundSize: '100% 100%',
    height: '30px',
    width: '100%',
    filter: 'blur(3px)',
  },
  analyticsFlowTitle: {
    opacity: '0.7',
    color: '#000000',
    fontSize: '26px',
    letterSpacing: '-0.3px',
    lineHeight: '32px',
    fontWeight: 500,
    marginBottom: '12px',
  },
  statusSubtitle: {
    height: '15px',
    width: '53px',
    opacity: '0.4',
    color: '#000000',
    fontSize: '12px',
    fontWeight: 'bold',
    letterSpacing: 0,
    lineHeight: '15px',
    marginBottom: '10px',
    textTransform: 'uppercase',
    whiteSpace: 'nowrap',
  },
  statusChip: {
    color: '#6239eb',
    backgroundColor: '#f2ecfe !important',
    border: '1px solid #f2ecfe',
    borderRadius: '4px',
  },
  gridContainer: {
    marginTop: '16px',
  },
  btnStyles: {
    minWidth: '150px',
    minHeight: '47px',
    color: '#6239EB',
    backgroundColor: '#F2ECFE',
    textTransform: 'none',
    '&:hover': {
      backgroundColor: '#F2ECFE',
    },
  },
  btnContainer: {
    display: 'flex',
    justifyContent: 'flex-end',
  },
  statusSection: {
    display: 'flex',
  },
  nextTime: {
    marginTop: '14px',
  },
  statusChipLabel: {
    fontSize: '0.875rem',
    fontWeight: 400,
    lineHeight: 1.43,
  },
  textContainer: {
    marginLeft: '24px',
  },
  closeBtn: {
    height: 30,
  },
  revealBtn: {
    height: 30,
    position: 'absolute',
    top: 5,
    left: '50%',
    right: '50%',
    transform: 'translate(-50%, -50%)',
    '&:hover': {
      opacity: 1,
      backgroundColor: '#6239EB',
    },
  },
});

const ApiKeyField = ({
  row,
  apiKey,
  onGetKey,
  authUser,
  revealApiKey,
  isCopied,
  setCopied,
}: any) => {
  const classes = useStyles();
  const [requirePwd, setRequirePwd] = React.useState(false);
  const [show, setShow] = React.useState(false);
  const [showRevealButton, setShowRevealButton] = React.useState(false);
  React.useEffect(() => {
    setShow(!!apiKey);
  }, [apiKey]);
  if (!show) {
    return (
      <>
        <ApiKeyPasswordDialog
          open={requirePwd}
          title="Reveal the Key"
          description="For security purposes, use your password to reveal the API key."
          handleClose={() => {
            setRequirePwd(!requirePwd);
          }}
          handleSubmitResult={async (value: any, onError: any) => {
            const authResult = await authUser(value.password);

            if (!authResult?.error) {
              const revealResult = await revealApiKey(row.id, value.password);
              if (revealResult) {
                onGetKey(revealResult.apiKey);
                setRequirePwd(!requirePwd);
                setShow(!show);
              } else {
                onError('Reveal Api Failed');
              }
            } else {
              onError('The password you entered is incorrect. Please try again.');
            }
          }}
        />
        <div
          onMouseOver={() => {
            setShowRevealButton(true);
          }}
          onMouseLeave={() => {
            setShowRevealButton(false);
          }}
          style={{
            position: 'relative',
            marginTop: 8,
          }}
        >
          <div className={classes.blurText}>this is blur text, should not able to see</div>
          {showRevealButton && (
            <RubyButton
              text="Reveal"
              classNames={classes.revealBtn}
              onClick={() => {
                setRequirePwd(!requirePwd);
              }}
            />
          )}
        </div>
      </>
    );
  } else {
    return (
      <div
        onMouseLeave={() => {
          setCopied({});
        }}
      >
        <Tooltip title={isCopied ? 'Copied' : 'Click to copy'} arrow placement="bottom">
          <div
            onClick={() => {
              navigator.clipboard.writeText(apiKey);
              setCopied(row.id, true);
            }}
            style={{
              wordWrap: 'break-word',
              whiteSpace: 'break-spaces',
              cursor: 'pointer',
            }}
          >
            {apiKey}
          </div>
        </Tooltip>
        <RubyButton
          text="Hide"
          classNames={classes.closeBtn}
          onClick={() => {
            setShow(!show);
          }}
        />
      </div>
    );
  }
};

export const ApiKeysEditor = ({ category, onSwithCategory }: any) => {
  const classes = useStyles();
  const [requirePwd, setRequirePwd] = React.useState(false);
  const [showRotateDialog, setShowRotateDialog] = React.useState(false);
  const [selectedRow, setSelectedRow] = React.useState<any>(null);
  const apiKeysContext = useContext(ApiKeysContext);
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = React.useState<any>(null);
  const [copied, seCopied] = React.useState<any>({});
  const [openUpsert, setOpenUpsert] = React.useState(false);
  const [tenantRoles, setTenantRoles] = React.useState<any>([]);
  const [openApiKeyLogs, setOpenApiKeyLogs] = React.useState(false);
  const [dialogType, setDialogType] = React.useState('');

  if (!apiKeysContext) {
    throw Error('Api keys requires apiKeysContext to be declared in context provider');
  }

  const loadData = async () => {
    try {
      const apiKeysData = await apiKeysContext.getApiKeysData();
      setData(apiKeysData);
      const tenantRolesResult = await apiKeysContext.fetchTenantRoles();
      setTenantRoles(tenantRolesResult);
    } catch {
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    loadData();
  }, []);

  const roles = apiKeysContext.user?.roles;
  const userRole = roles?.find((x: { effective: boolean }) => x.effective);
  const roleFunctions = userRole?.functions;
  const canRotate = !!roleFunctions?.find((x: string) => x === 'ManageAPIKeys');
  const getDialogContent = () => {
    switch (dialogType) {
      case 'rotate':
        return {
          title: 'Rotate the Key',
          description: 'For security purposes, use your password to rotate the API key.',
        };
      case 'delete':
        return {
          title: 'Delete the Key',
          description: 'For security purposes, use your password to delete the API key.',
        };
      default:
        return {
          title: '',
          description: '',
        };
    }
  };
  return (
    <>
      {openApiKeyLogs && (
        <ApiKeysLog
          onClose={() => {
            setOpenApiKeyLogs(false);
            setSelectedRow(null);
          }}
          open={openApiKeyLogs}
          selectedRow={selectedRow}
          getEventRequestDetails={async (id: string) => {
            return await apiKeysContext.getEventRequestDetails(id);
          }}
          loadApiKeyLog={async (
            apiKeyIds: string[],
            page: number,
            pageSize: number,
            statusCode?: number,
            accessDate?: string,
            endPoint?: string,
            method?: string,
          ) => {
            return await apiKeysContext.loadApiKeyLog(
              apiKeyIds,
              page,
              pageSize,
              statusCode,
              accessDate,
              endPoint,
              method,
            );
          }}
        />
      )}

      {openUpsert && (
        <UpsertApiKeyDialog
          open={openUpsert}
          tenantRoles={tenantRoles?.map((x: any) => {
            return {
              ...x,
              value: x.id,
            };
          })}
          handleClose={() => {
            setOpenUpsert(false);
          }}
          selectedRow={selectedRow}
          onSettingsButtonClick={() => {
            onSwithCategory('Roles');
            setOpenUpsert(false);
          }}
          handleSubmitApiKeysResult={async (submitData: any) => {
            if (selectedRow) {
              await apiKeysContext.updateApiKey(
                selectedRow.id,
                submitData.title,
                submitData.selectedRole.id,
              );
            } else {
              await apiKeysContext.createApiKey(submitData.title, submitData.selectedRole.id);
            }
            await loadData();
            setOpenUpsert(false);
          }}
        />
      )}

      <ApiKeyPasswordDialog
        open={requirePwd}
        title={getDialogContent().title}
        description={getDialogContent().description}
        loading={loading}
        handleClose={() => {
          setRequirePwd(!requirePwd);
          setSelectedRow(null);
          setDialogType('');
        }}
        handleSubmitResult={async (value: any, onError: any) => {
          const authResult = await apiKeysContext.authUser({
            userName: apiKeysContext.user.userName,
            password: value.password,
          });

          if (!authResult?.error) {
            if (dialogType === 'delete') {
              setLoading(true);
              await apiKeysContext.deleteApiKey(selectedRow.id);
              await loadData();
              setSelectedRow(null);
              setLoading(false);
            }
            if (dialogType === 'rotate') {
              setShowRotateDialog(true);
            }
            setRequirePwd(!requirePwd);
          } else {
            onError('The password you entered is incorrect. Please try again.');
          }
        }}
      />
      {showRotateDialog && (
        <RollApiKeyDialog
          open={showRotateDialog}
          row={selectedRow}
          handleClose={() => {
            setShowRotateDialog(false);
          }}
          handleSubmitResult={async (value: any, pwd: string) => {
            if (!apiKeysContext.user.userName) return;
            if (selectedRow) {
              const result = await apiKeysContext.rollApiKey(
                selectedRow.id,
                value.dateOption === 'nowDate',
                dayjs(value.expireDate).format('YYYY-MM-DD'),
              );
              if (result) {
                await loadData();
                setShowRotateDialog(false);
              }
            }
          }}
        />
      )}

      <RubySettingEditorPanel title={category.name}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography component="h3" className={classes.sectionTitle}>
              These API keys enable authentication for API requests. For enhanced security, you have
              the option to limit access and permissions for various resources by restricting the
              API keys
            </Typography>
          </Grid>
        </Grid>
        <div style={{ width: '100%' }}>
          <div style={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
            <RubyButton
              icon={<AddIcon />}
              text="New API Key"
              onClick={() => {
                setOpenUpsert(true);
              }}
            />
          </div>
          <RubyGrid
            columnReordering
            enablePaging
            columns={[
              {
                name: 'actions',
                title: 'Actions',
                apiName: 'actions',
                type: 'actions',
                cellRenderer: (props) => {
                  const { row } = props;
                  const actions: any = [];
                  if (canRotate) {
                    if (!row.expiredTime) {
                      actions.push(
                        MuiGridButton({
                          onClickHandler: () => {
                            setSelectedRow(row);
                            setRequirePwd(true);
                            setDialogType('rotate');
                          },
                          Icon: HistoryOutlinedIcon,
                          tooltipText: 'Rotate Api Key',
                        }),
                      );
                    }

                    actions.push(
                      MuiGridButton({
                        onClickHandler: () => {
                          setSelectedRow(row);
                          setOpenUpsert(true);
                        },
                        Icon: EditIcon,
                        tooltipText: 'Update Api Key',
                      }),
                    );

                    actions.push(
                      MuiGridButton({
                        onClickHandler: () => {
                          setSelectedRow(row);
                          setOpenApiKeyLogs(true);
                        },
                        tooltipText: 'View Log',
                        //@ts-ignore
                        Icon: ViewIcon,
                      }),
                    );
                    if (!row.masterKey) {
                      actions.push(
                        MuiGridButton({
                          onClickHandler: () => {
                            setSelectedRow(row);
                            setRequirePwd(true);
                            setDialogType('delete');
                          },
                          tooltipText: 'Delete',
                          //@ts-ignore
                          Icon: DeleteOutlineIcon,
                        }),
                      );
                    }
                  }

                  return <GridActions actions={actions} actionHelpers={{ row }} />;
                },
              },
              {
                apiName: 'name',
                name: 'name',
                title: 'Name',
                type: 'text',
                cellRenderer: (props) => {
                  const { row } = props;
                  let text = '';
                  if (row.expiredTime) {
                    const today = dayjs();
                    const diff = -1 * today.diff(row.expiredTime, 'd');
                    if (diff <= 2 && diff >= 0) {
                      text = `Expired in ${diff + 1} days`;
                    } else if (diff > 2) {
                      text = `Expired on ${dayjs(row.expiredTime).format('YYYY-MM-DD')}`;
                    } else if (diff < 0) {
                      text = `Expired`;
                    }
                  }

                  return (
                    <>
                      <div>{row.name}</div>
                      {text && <div style={{ color: '#FF3939' }}>{text}</div>}
                    </>
                  );
                },
              },
              {
                apiName: 'apiKey',
                name: 'apiKey',
                title: 'Keys',
                type: 'text',
                cellRenderer: (props) => {
                  const { row } = props;
                  return (
                    <ApiKeyField
                      apiKey={row.apiKey}
                      row={row}
                      onGetKey={(key: string) => {
                        const newData = produce(data, (draft: any) => {
                          const newItem = draft.find((x: any) => x.id === row.id);
                          if (newItem) {
                            newItem.apiKey = key;
                          }
                          return draft;
                        });
                        setData(newData);
                      }}
                      isCopied={copied[row.id]}
                      setCopied={(id: string, value: boolean) => {
                        seCopied({
                          [id]: value,
                        });
                      }}
                      authUser={async (pwd: string) => {
                        if (!apiKeysContext.user.userName) return;
                        return await apiKeysContext.authUser({
                          userName: apiKeysContext.user.userName,
                          password: pwd,
                        });
                      }}
                      revealApiKey={async (id: string, pwd: string) => {
                        if (!apiKeysContext.user.userName) return;
                        return await apiKeysContext.revealApiKey(
                          id,
                          apiKeysContext.user.userName,
                          pwd,
                        );
                      }}
                    />
                  );
                },
              },
              {
                apiName: 'createdDate',
                name: 'createdDate',
                title: 'Created On',
                type: 'dateTime',
              },
              {
                apiName: 'lastUsedDate',
                name: 'lastUsedDate',
                title: 'Last Used On',
                type: 'dateTime',
              },
            ]}
            columnWidths={[
              { columnName: 'apiKey', width: 220 },
              { columnName: 'name', width: 180 },
              { columnName: 'actions', width: 150 },
              { columnName: 'lastUsedDate', width: 180 },
              { columnName: 'createdDate', width: 180 },
            ]}
            pageSize={20}
            rows={data}
            columnOrder={['createdDate']}
          />
        </div>
      </RubySettingEditorPanel>
    </>
  );
};

import React, { useEffect, useRef, useState } from 'react';

import { Column } from '@devexpress/dx-react-grid';
import { Button, Grid, Paper, TextField } from '@material-ui/core';
import Checkbox from '@material-ui/core/Checkbox';
import List from '@material-ui/core/List';
import ListItem from '@material-ui/core/ListItem';
import ListItemText from '@material-ui/core/ListItemText';
import Popover from '@material-ui/core/Popover';
import Tooltip from '@material-ui/core/Tooltip';
import { makeStyles } from '@material-ui/core/styles';
import { Autocomplete } from '@material-ui/lab';

import { ColumnChooserIcon } from '../icons';
import { Props } from './interface';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    alignItems: 'flex-end',
  },
  checkBox: {
    padding: '6px',
    '&:hover': {
      fill: '#9f9f9f',
      backgroundColor: 'transparent',
    },
  },
  toolTip: {
    paddingBottom: 0,
  },
  listItemRoot: {
    maxHeight: '200px',
  },
  gutters: {
    padding: '4px 8px',
  },
  listStylesRoot: {
    maxHeight: '250px',
  },
  option: {
    '&[aria-selected="true"]': {
      backgroundColor: '#EDEDED',
    },
    '&[data-focus="true"]': {
      backgroundColor: '#E4E4E4',
    },
  },
});

export const ColumnChooser: React.FC<Props> = ({
  options,
  hiddenColumns,
  handleHiddenColumns,
  onColumnChooserClose,
}) => {
  const classes = useStyles();

  const [anchorEl, setAnchorEl] = useState(null);
  const textEl = useRef();

  const onToggle = (e: any) => {
    setAnchorEl(e.currentTarget);

    setTimeout(() => {
      if (textEl?.current) {
        //@ts-ignore
        textEl.current?.focus?.();
      }
    }, 200);
  };

  const handleClose = () => {
    setAnchorEl(null);
    if (onColumnChooserClose) {
      onColumnChooserClose();
    }
  };

  const hideColumn = (toggledColumn: string) => {
    const hiddenColumnsCopy = [...hiddenColumns];
    if (hiddenColumns.indexOf(toggledColumn) > -1) {
      hiddenColumnsCopy.splice(hiddenColumns.indexOf(toggledColumn), 1);
    } else {
      hiddenColumnsCopy.push(toggledColumn);
    }
    handleHiddenColumns(hiddenColumnsCopy, toggledColumn);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'column-chooser-popover' : undefined;
  const useColumns = options.filter(
    (x) =>
      x.apiName !== '_transactionhub' &&
      x.name !== 'actions' &&
      x.apiName !== '_drag' &&
      x.name !== 'drag' &&
      x.type !== 'drag',
  );
  const [inputValue, setInputValue] = useState('');

  return (
    <div className={classes.root}>
      <Tooltip arrow title="Column Chooser" placement="bottom" enterDelay={300}>
        <Grid container alignItems="center" style={{ color: '#999999' }}>
          <Grid>
            <Button onClick={onToggle} size="small" startIcon={<ColumnChooserIcon />}>
              <span style={{ fontSize: 12, textTransform: 'none', color: '#999999' }}>
                Configure
              </span>
            </Button>
          </Grid>
        </Grid>
      </Tooltip>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Autocomplete
          multiple
          size="small"
          renderTags={() => {
            return null;
          }}
          classes={{
            option: classes.option,
          }}
          openOnFocus
          inputValue={inputValue}
          onInputChange={(event, value) => {
            //@ts-ignore
            if (event?.currentTarget?.type === 'text') {
              //@ts-ignore
              setInputValue(value);
            }
          }}
          PaperComponent={(props) => <Paper {...props} className={classes.option} />}
          options={useColumns}
          defaultValue={useColumns.filter((x) => hiddenColumns.indexOf(x.name) === -1)}
          disableCloseOnSelect
          noOptionsText={true}
          disableClearable={true}
          onChange={(event: any, newValue: any) => {
            const defaultOptions = useColumns.filter((x) => hiddenColumns.indexOf(x.name) === -1);
            let changedItem: any = null;
            if (defaultOptions.length < newValue.length) {
              //add new
              changedItem = newValue.find(
                (x: any) => !defaultOptions.find((n) => n.name === x.name),
              );
            } else {
              changedItem = useColumns.find(
                (x: any) =>
                  !newValue.find((n: any) => n.name === x.name) &&
                  hiddenColumns.indexOf(x.name) === -1,
              );
            }

            if (changedItem) {
              hideColumn(changedItem?.name);
            }
          }}
          //@ts-ignore
          getOptionLabel={(option) => option.title || option.name}
          renderOption={(option, { selected }) => (
            <div>
              <Checkbox
                classes={{
                  root: classes.checkBox,
                }}
                readOnly
                checked={selected}
                disableRipple
              />
              {option.title || option.name}
            </div>
          )}
          style={{ width: 300 }}
          renderInput={(params) => (
            //@ts-ignore
            <TextField
              inputRef={(el) => {
                textEl.current = el;
              }}
              {...params}
              variant="outlined"
              onKeyDown={(event: any) => {
                if (event.keyCode === 8) {
                  event.stopPropagation();
                }
              }}
            />
          )}
        />
      </Popover>
    </div>
  );
};

export default ColumnChooser;

export default [
  {
    apiName: 'name',
    name: 'Name',
    type: 'id',
    creatable: false,
    updatable: false,
    filterable: true,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: true,
  },
  {
    apiName: 'customer',
    name: 'Customer',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'value',
    name: 'Contract Value',
    type: 'currency',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'stage',
    name: 'Stage',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'billed',
    name: 'Billed',
    type: 'currency',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'recognized',
    name: 'Recognized',
    type: 'currency',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'scheduled',
    name: 'Scheduled',
    type: 'currency',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
    precision: 16,
    scale: 2,
  },
  {
    apiName: 'created_period',
    name: 'Created Period',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'modified_period',
    name: 'Modified Period',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'version',
    name: 'Version',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'isonhold',
    name: 'Is On Hold',
    type: 'boolean',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'amendment_version',
    name: 'Amendment Version',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'prior_rc_version',
    name: 'Prior Version',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'created_date',
    name: 'Created Date',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'modified_date',
    name: 'Modified Date',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'id',
    name: 'Id',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: true,
  },
  {
    apiName: 'mje_id',
    name: 'MJE Id',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'allocation_type',
    name: 'Allocation Type',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'doc_type',
    name: 'Doc Type',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'transactional_currency_code',
    name: 'Transactional Currency Code',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
  {
    apiName: 'ca_cl_position',
    name: 'CA CL Position',
    type: 'text',
    creatable: false,
    updatable: false,
    filterable: false,
    sortable: false,
    customField: false,
    encrypted: false,
    required: false,
    unique: false,
  },
];

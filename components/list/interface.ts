import { Uom } from '../metadata/interface';

export interface Props {
  items: Array<Item>;
  summaryItem?: SummaryItem;
  currencyIsoCode?: string;
  locale?: string;
  link?: string;
  handleOnClickLink?: (event: React.MouseEvent<HTMLElement, MouseEvent>) => Promise<void>;
  mouseOverPopupAria?: string;
  PopoverComponent?: any;
  shouldDisplayNegativeNumberStyling?: (type: string, itemApiName: string) => boolean;
  uom?: Uom;
}

export interface SummaryItem extends Item {
  position?: 'top-left' | 'bottom-right';
}

export interface ItemProps {
  item: Item;
}

export interface SummaryItemProps {
  summaryItem: SummaryItem;
  uom?: Uom;
}

export interface SummaryTitleProps {
  summaryItem: SummaryItem;
}

export interface Item {
  apiName: string;
  name: string;
  value: any;
  type: string;
  toolTipText?: string;
  toolTipIcon?: () => JSX.Element;
  xs?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | null;
  isLoading?: boolean;
}

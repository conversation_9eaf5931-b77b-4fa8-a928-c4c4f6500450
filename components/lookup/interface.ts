import React from 'react';

import { RubyFormControlProps } from '../form-field';

export type LookupOption = {
  name: string;
  optionName?: string;
  id: any;
};

interface LookupProps {
  showNone?: boolean;
  //todo remove this
  options?: Array<LookupOption>;

  withSearch?: boolean;
  inputStyle?: React.CSSProperties;
}

export interface Props extends RubyFormControlProps, LookupProps {
  usePopper?: boolean;
  poperStyle?: any;
  getFocusElement?: (event: any) => any;
}

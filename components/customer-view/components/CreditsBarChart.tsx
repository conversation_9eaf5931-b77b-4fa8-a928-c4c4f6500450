import React, { memo } from 'react';
import { CreditStatistics } from '../../revenue-builder-types/interface';
import { StatsBarChart } from '../../stats-bar-chart/stats-bar-chart';
import { calculateBarItemWidth } from '../utils/utils';
import { formatNumberForDisplay } from '../../list/list';

interface CreditsBarChartProps {
  creditsStatistics: CreditStatistics;
  locale: string | undefined;
  currencyIsoCode: string;
}

const CreditsBarChart = memo((props: CreditsBarChartProps) => {
  const { creditsStatistics, locale, currencyIsoCode } = props;

  if (!creditsStatistics) return <></>;

  const {
    expiredCredits,
    consumedCredits,
    canceledCredits,
    activeCredits,
    pendingCredits,
    isCash,
  } = creditsStatistics;
  const totalAmount =
    expiredCredits + consumedCredits + canceledCredits + activeCredits + pendingCredits;

  let defaultWidth = null;
  if (totalAmount === 0) {
    defaultWidth = '25%';
  }

  const items = [
    {
      text: {
        value: formatNumberForDisplay(
          expiredCredits,
          creditsStatistics.currencyIsoCode != null && creditsStatistics.currencyIsoCode != ''
            ? creditsStatistics.currencyIsoCode
            : currencyIsoCode,
          true,
          !isCash,
        ),
        locale,
      },
      desc: { value: 'EXPIRED' },
      width: defaultWidth || calculateBarItemWidth(expiredCredits, totalAmount),
      backgroundColor: '#D6D6D6',
    },
    {
      text: {
        value: formatNumberForDisplay(
          consumedCredits,
          creditsStatistics.currencyIsoCode != null && creditsStatistics.currencyIsoCode != ''
            ? creditsStatistics.currencyIsoCode
            : currencyIsoCode,
          true,
          !isCash,
        ),
        locale,
      },
      desc: { value: 'CONSUMED' },
      width: defaultWidth || calculateBarItemWidth(consumedCredits, totalAmount),
      backgroundColor: '#6239eb',
    },
    {
      text: {
        value: formatNumberForDisplay(
          canceledCredits,
          creditsStatistics.currencyIsoCode != null && creditsStatistics.currencyIsoCode != ''
            ? creditsStatistics.currencyIsoCode
            : currencyIsoCode,
          true,
          !isCash,
        ),
        locale,
      },
      desc: { value: 'CANCELED' },
      width: defaultWidth || calculateBarItemWidth(canceledCredits, totalAmount),
      backgroundColor: '#FA5454',
    },
    {
      text: {
        value: formatNumberForDisplay(
          activeCredits,
          creditsStatistics.currencyIsoCode != null && creditsStatistics.currencyIsoCode != ''
            ? creditsStatistics.currencyIsoCode
            : currencyIsoCode,
          true,
          !isCash,
        ),
        locale,
      },
      desc: { value: 'ACTIVE' },
      width: defaultWidth || calculateBarItemWidth(activeCredits, totalAmount),
      backgroundColor: '#0CB2D2',
    },
    {
      text: {
        value: formatNumberForDisplay(
          pendingCredits,
          creditsStatistics.currencyIsoCode != null && creditsStatistics.currencyIsoCode != ''
            ? creditsStatistics.currencyIsoCode
            : currencyIsoCode,
          true,
          !isCash,
        ),
        locale,
      },
      desc: { value: 'PENDING' },
      width: defaultWidth || calculateBarItemWidth(pendingCredits, totalAmount),
      backgroundColor: '#095393',
    },
  ];

  return <StatsBarChart items={items} />;
});

export default CreditsBarChart;

import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { Props } from './interface';

const defaultProps = {};

const ListIcon: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };

  return (
    <SvgIcon style={{fontSize: '1.4rem'}} viewBox={props.viewBox}>
      <svg
        width="16px"
        height="16px"
        viewBox="0 0 16 16"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
      >
        <title>358C498D-DA6D-462C-B808-01936FADF567</title>
        <g id="06-Quote-Builder" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="6-1-7-1-Product-Configurator" transform="translate(-1812.000000, -456.000000)">
            <g id="Group-8" transform="translate(1812.000000, 455.000000)">
              <g id="function-line" transform="translate(0.000000, 1.000000)">
                <polygon id="Path" points="0 0 16 0 16 16 0 16" />
                <path
                  d="M2,2 L7.33333333,2 L7.33333333,7.33333333 L2,7.33333333 L2,2 Z M2,8.66666667 L7.33333333,8.66666667 L7.33333333,14 L2,14 L2,8.66666667 Z M8.66666667,2 L14,2 L14,7.33333333 L8.66666667,7.33333333 L8.66666667,2 Z M8.66666667,8.66666667 L14,8.66666667 L14,14 L8.66666667,14 L8.66666667,8.66666667 Z M10,3.33333333 L10,6 L12.6666667,6 L12.6666667,3.33333333 L10,3.33333333 Z M10,10 L10,12.6666667 L12.6666667,12.6666667 L12.6666667,10 L10,10 Z M3.33333333,3.33333333 L3.33333333,6 L6,6 L6,3.33333333 L3.33333333,3.33333333 Z M3.33333333,10 L3.33333333,12.6666667 L6,12.6666667 L6,10 L3.33333333,10 Z"
                  id="Shape"
                  fill={props.styles && props.styles.color ? props.styles.color : '#000000'}
                  fillRule="nonzero"
                />
              </g>
            </g>
          </g>
        </g>
      </svg>
    </SvgIcon>
  );
};

export default ListIcon;

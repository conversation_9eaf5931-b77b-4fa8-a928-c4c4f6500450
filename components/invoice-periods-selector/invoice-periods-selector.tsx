import React from 'react';

import { ListSubheader, MenuItem, Select, makeStyles } from '@material-ui/core';
import dayjs from 'dayjs';

import { InvoicePeriods, Period } from '../revenue-builder-types';

interface Props {
  label: string;
  invoicePeriods: InvoicePeriods;
  onSelectPeriod: (startDate?: string, endDate?: string, id?: string) => void;
}

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  fieldLabel: {
    opacity: '0.4',
    color: '#000000',
    fontSize: '12px',
    fontWeight: 'bold',
    letterSpacing: 0,
    lineHeight: '15px',
    marginRight: '22px',
    textTransform: 'uppercase',
    whiteSpace: 'nowrap',
  },
});

const DEFAULTVALUEKEY = 'default';

const InvoiceListSelector: React.FC<Props> = ({ label, invoicePeriods, onSelectPeriod }: Props) => {
  const classes = useStyles();
  const [value, setValue] = React.useState<string>(DEFAULTVALUEKEY);
  return (
    <>
      <div className={classes.container}>
        <span className={classes.fieldLabel} role="label">
          {label}
        </span>
        <Select
          style={{ width: 280 }}
          value={value}
          onChange={(event: React.ChangeEvent<{ name?: string; value: unknown }>) => {
            const selectedValue = event.target.value as string;
            if (!selectedValue) return;
            if (selectedValue === DEFAULTVALUEKEY) {
              onSelectPeriod();
            } else {
              const isSelectedCurrentDate =
                invoicePeriods.currentPeriod?.startDate === selectedValue;
              if (isSelectedCurrentDate) {
                onSelectPeriod(selectedValue, dayjs().format('YYYY-MM-DD'));
              } else {
                const selectedPeriod = invoicePeriods.previousPeriods?.find(
                  (x: Period) => x.startDate === selectedValue,
                );
                if (selectedPeriod) {
                  onSelectPeriod(selectedPeriod.startDate, selectedPeriod.endDate);
                } else {
                  const selectedFuturePeriod = invoicePeriods.futurePeriods?.find(
                    (x: Period) => x.startDate === selectedValue,
                  );
                  if (selectedFuturePeriod) {
                    onSelectPeriod(
                      selectedFuturePeriod.startDate,
                      selectedFuturePeriod.endDate,
                      selectedFuturePeriod.id,
                    );
                  }
                }
              }
            }
            setValue(selectedValue);
          }}
        >
          <MenuItem value={DEFAULTVALUEKEY}>{'Select Period'}</MenuItem>
          <ListSubheader onClickCapture={(e: any) => e.stopPropagation()} disableSticky>
            Current Period
          </ListSubheader>
          <MenuItem
            value={invoicePeriods.currentPeriod?.startDate}
          >{`${invoicePeriods.currentPeriod?.startDate} - Present`}</MenuItem>

          <ListSubheader onClickCapture={(e: any) => e.stopPropagation()} disableSticky>
            Previous Periods
          </ListSubheader>
          {invoicePeriods.previousPeriods?.map((p: Period, index: number) => {
            return (
              <MenuItem key={index} value={p.startDate}>{`${p.startDate} - ${p.endDate}`}</MenuItem>
            );
          })}
          {invoicePeriods.futurePeriods?.length && (
            <ListSubheader onClickCapture={(e: any) => e.stopPropagation()} disableSticky>
              Future Periods (Preview)
            </ListSubheader>
          )}
          {invoicePeriods.futurePeriods?.map((p: Period, index: number) => {
            return (
              <MenuItem key={p.id} value={p.startDate}>{`${p.startDate} - ${p.endDate}`}</MenuItem>
            );
          })}
        </Select>
      </div>
    </>
  );
};

export default InvoiceListSelector;

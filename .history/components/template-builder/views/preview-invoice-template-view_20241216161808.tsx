import React, { useContext, useEffect, useState } from 'react';
import { Grid, IconButton, Paper, TextField, Tooltip } from '@material-ui/core';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import { MuiThemeProvider, createTheme, makeStyles } from '@material-ui/core/styles';
import LanguageIcon from '@material-ui/icons/Language';
import MailOutlineIcon from '@material-ui/icons/MailOutline';
import SearchIcon from '@material-ui/icons/Search';
import Autocomplete from '@material-ui/lab/Autocomplete';
import { ModalAppBar } from '@nue-apps/ruby-ui-component';
import { get } from 'lodash';
import { EvergreenIcon, RefreshTokenIcon, SendEmailIcon, SquareDownloadIcon } from '../../icons';
import Loading from '../../loading';
import MultiSelectionSearchBar, { SelectedObject } from '../../multi-selection-search-bar';
import { useRubySnackbar } from '../../ruby-notifier';
import { BillingSettingsContext } from '../../ruby-settings/interface';
import TemplateBuilderContext from '../../template-builder-context';
import {
  TemplateBuilderFacade,
  TemplateSchema,
} from '../../template-builder-context/template-builder-context';
import { Props } from '../interface';
import PreviewRecordList from '../record-list';
import templateClassicSchema from '../template-classic';
import TemplateDesign from '../template-design';
import templateFriendlySchema from '../template-friendly';
import { downloadHtmlPdf } from '../template-html2pdf';
import TemplatePreview from '../template-preview';
import { templateOrderProduct as OrderProduct, templateRecord as Record } from '../template-record';
import TemplateTheme from '../template-theme';

const defaultProps = {};

const useStyles = makeStyles({
  templateContainer: {
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
  },
  toolBarContainer: {
    height: '100%',
    width: '300px',
    overflow: 'scroll',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
  },
  designContainer: {
    height: '100%',
    overflow: 'hidden',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
    flex: 1,
  },
  previewContainer: {
    height: '100%',
    overflow: 'scroll',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
    flex: 1.5,
    borderRaidus: '0',
  },
  toolBarPaper: {
    height: '100%',
    overflow: 'scroll',
    padding: '15px 30px',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    background: '#F9F8FA',
    boxSizing: 'border-box',
  },
  designPaper: {
    height: '100%',
    padding: '20px 5px',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
    boxSizing: 'border-box',
  },
  previewPaper: {
    height: '100%',
    padding: '35px 30px',
    scrollbarWidth: 'none',
    overflowStyle: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
    scrollBehavior: 'smooth',
    boxSizing: 'border-box',
    background: 'white',
    boxShadow: '0px 2px 10px 0px rgba(200,200,200,0.5);',
    borderTop: '2px solid #6239EB',
    borderRadius: 0,
  },
  dialogTitle: {
    padding: 0,
  },
  dialogContent: {},
  tableWrapper: {
    maxWidth: '50%',
  },
  downloadPdf: {
    maxWidth: 'auto',
  },
  titleContainer: {
    marginLeft: 32,
    display: 'flex',
    alignItems: 'center',
    width: '90vw',
    justifyContent: 'flex-end',
  },
  titleWrapper: {
    display: 'flex',
    alignItems: 'center',
  },
  title: {
    color: '#6239eb',
    fontSize: '16px',
  },
  iconContainer: {
    color: '#8b66f4',
    height: 24,
    marginRight: 4,
    cursor: 'pointer',
    marginLeft: '20px',
  },
  inputRoot: {
    '&>div': {
      padding: '0!important',
    },
    '& input': {
      paddingLeft: '15px!important',
    },
    '& fieldset': {
      border: 0,
    },
  },
  autocompleteOption: {
    fontSize: '.875rem',
    color: '#4d4c50',
  },
});

export const PreviewInvoiceTemplateView: React.FC<Props> = (userProps: Props) => {
  const templateTheme = createTheme(TemplateTheme);
  const billingSettingsCtx = useContext(BillingSettingsContext);

  const {
    schema,
    invoice,
    getInvoiceById,
    invoiceList,
    setRefreshPdfUrl,
    sendInvoiceEmail,
    mode,
    getDownloadPdf,
    magicToken,
    tenant,
  } = userProps;

  const [config, setConfig] = useState<TemplateSchema>(templateFriendlySchema);
  const [templateOrderProduct, setTemplateOrderProduct] = useState<any>(OrderProduct);
  const [templateRecord, setTemplateRecord] = useState<any>(invoice || Record);
  const [activateComponent, setActivateComponent] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const { showSnackbar, Snackbar } = useRubySnackbar();
  const [refreshToolTip, setRefreshToolTip] = useState('123');
  const [loaded, setLoaded] = useState(false);

  const props = { ...defaultProps, ...userProps };

  const { open, setOpen } = props;

  const classes = useStyles();

  useEffect(() => {
    setOpen(open);
    setConfig(open);
    console.log('mode::', mode);
  }, [open]);

  useEffect(() => {
    if (schema) {
      setOpen(schema);
      setConfig(schema);
      if (invoiceList) {
        handleUpdateRecord(invoiceList[0], schema);
      }
    }
  }, [schema]);

  useEffect(() => {
    if (invoice) {
      setTemplateRecord(invoice);
    }
  }, [invoice]);

  const templateBuilderFacade: TemplateBuilderFacade = {
    templateBuilderService: {
      templateConfig: config,
      getTemplateBuilderConfig: () => {
        return {
          templateConfig: config,
        };
      },
      setTemplateBuilderConfig: (c: TemplateSchema) => {
        setConfig(c);
      },
      templateOrderProduct: templateOrderProduct,
      getTemplateOrderProduct: () => {
        return {
          templateOrderProduct: templateOrderProduct,
        };
      },
      setTemplateOrderProduct: (c: any) => {
        setTemplateOrderProduct(c);
      },
      templateRecord: templateRecord,
      getTemplateRecord: () => {
        return {
          templateRecord: templateRecord,
        };
      },
      setTemplateRecord: (c: any) => {
        setTemplateRecord(c);
      },
      activateComponent: activateComponent,
      getActivateComponent: () => {
        return {
          activateComponent: activateComponent,
        };
      },
      setActivateComponent: (c: any) => {
        setActivateComponent(c);
      },
    },
  };

  const handleUpdateRecord = async (invoice: any, template: any) => {
    console.log('Current mode in handleUpdateRecord:', mode);
    console.log('getInvoiceById:', getInvoiceById);
    if (getInvoiceById && invoice) {
      setLoading(true);
      const data = await getInvoiceById(mode, invoice.id, template);
      if (data.data.company && data.data.Invoice) {
        data.data.Invoice[0].company = data.data.company;
        setTemplateRecord(data.data.Invoice[0]);
      }
      if (data.data.company && data.data.Quote) {
        data.data.Quote[0].company = data.data.company;
        setTemplateRecord(data.data.Quote[0]);
      }
      setLoading(false);
    }
  };

  const previewPdf = () => {};

  const downloadPdf = () => {
    if (templateRecord) {
      setIsDownloading(true);
      const fileTitle =
        templateRecord.tenantId +
        '_' +
        (mode == 'invoice' ? templateRecord?.name : templateRecord?.QuoteNumber);
      console.log('fileTile', fileTitle);
      downloadHtmlPdf(
        fileTitle,
        'a4',
        document.getElementById('previewContainer')!,
        ['no2canvas'],
        true,
        setIsDownloading,
      );
    }
  };

  const handleSetRefreshPdfUrl = async () => {
    try {
      if (setRefreshPdfUrl) {
        const res = await setRefreshPdfUrl('invoices', [invoice.id]);
        if (res.success) {
          showSnackbar('confirm', 'Success', 'Successfully refresh the pdf url.');
        }
        setIsRefreshing(false);
      }
    } catch (e) {}
  };

  const handleSendInvoiceEmail = async () => {
    try {
      if (sendInvoiceEmail) {
        const res = await sendInvoiceEmail([invoice.id]);
        if (res.success) {
          showSnackbar('confirm', 'Success', 'Successfully send the email.');
        }
        setIsSending(false);
      }
    } catch (e) {}
  };

  const handleDownloadPdfFromBackend = async () => {
    try {
      if (getDownloadPdf) {
        const res = await getDownloadPdf(magicToken);
        if (res.success) {
          showSnackbar('confirm', 'Success', 'Successfully send the email.');
        }
      }
    } catch (e) {}
  };
  return (
    <MuiThemeProvider theme={templateTheme}>
      <TemplateBuilderContext.Provider value={templateBuilderFacade}>
        <Snackbar />
        <Dialog
          open={open}
          fullWidth
          fullScreen
          PaperProps={{
            style: {
              backgroundColor: '#F9F8FA',
            },
          }}
        >
          <DialogTitle className={classes.dialogTitle}>
            <ModalAppBar
              title={
                <div
                  className={classes.titleContainer}
                  style={{
                    justifyContent: invoice ? 'flex-end' : 'space-between',
                  }}
                >
                  {
                    <div
                      className={classes.titleWrapper}
                      style={{
                        visibility: invoice ? 'hidden' : 'visible',
                      }}
                    >
                      <div className={classes.title}>Previewing Record</div>
                      <div>
                        <Autocomplete
                          id="combo-box-demo"
                          options={invoiceList || []}
                          disableClearable={true}
                          getOptionLabel={(option) => option.name}
                          style={{
                            width: 200,
                            padding: 0,
                            marginLeft: '20px',
                            background: 'white',
                            borderRadius: '5px',
                          }}
                          classes={{ option: classes.autocompleteOption }}
                          onChange={(event: object, value: any, reason: string) => {
                            const newValue = value;
                            handleUpdateRecord(newValue, config);
                          }}
                          renderInput={(params: any) => (
                            <TextField
                              {...params}
                              label=""
                              variant="outlined"
                              className={classes.inputRoot}
                            />
                          )}
                          defaultValue={invoiceList && invoiceList[0]}
                        />
                      </div>
                    </div>
                  }
                  {loaded &&
                    (magicToken ? (
                      <>
                        <div
                          className={classes.iconContainer}
                          style={{
                            marginRight: invoice ? '4px' : '30px',
                            visibility:
                              mode == 'invoice' || mode === 'order' ? 'hidden' : 'visible',
                          }}
                          onClick={() => {
                            downloadPdf();
                          }}
                          id="magiclink-downloadpdf-hidden"
                          role-name={
                            templateRecord.tenantId +
                              '_' +
                              (mode == 'invoice'
                                ? templateRecord?.name
                                : templateRecord?.QuoteNumber) || 'invoice'
                          }
                        >
                          {isDownloading ? (
                            <Loading size="24px" />
                          ) : (
                            <Tooltip title="Download PDF" arrow placement="bottom">
                              <IconButton style={{ padding: '5px', marginTop: '-15px' }}>
                                <SquareDownloadIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                        </div>
                        <div
                          className={classes.iconContainer}
                          style={{
                            marginRight: invoice ? '4px' : '30px',
                            visibility:
                              mode == 'invoice' || mode === 'order' ? 'visible' : 'hidden',
                          }}
                          onClick={async () => {
                            await handleDownloadPdfFromBackend();
                          }}
                          id="maginlink-downloadpdf"
                        >
                          {isDownloading ? (
                            <Loading size="24px" />
                          ) : (
                            <Tooltip title="Download PDF" arrow placement="bottom">
                              <IconButton style={{ padding: '5px', marginTop: '-15px' }}>
                                <SquareDownloadIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                        </div>
                      </>
                    ) : (
                      <div
                        className={classes.iconContainer}
                        style={{
                          marginRight: invoice ? '4px' : '30px',
                          visibility: mode == 'invoice' || mode === 'order' ? 'visible' : 'hidden',
                        }}
                        onClick={() => {
                          downloadPdf();
                        }}
                        id="downloadpdf"
                      >
                        {isDownloading ? (
                          <Loading size="24px" />
                        ) : (
                          <Tooltip title="Download PDF" arrow placement="bottom">
                            <IconButton style={{ padding: '5px', marginTop: '-15px' }}>
                              <SquareDownloadIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </div>
                    ))}
                  {/* {invoice && (
                    <div
                      className={classes.iconContainer}
                      onClick={async () => {
                        setIsRefreshing(true);
                        await handleSetRefreshPdfUrl();
                      }}
                    >
                      {isRefreshing ? <Loading size="24px" /> : <Tooltip title={refreshToolTip} arrow placement='bottom'>
                        <IconButton style={{ padding: '5px', marginTop: '-15px'}}>
                          <RefreshTokenIcon />
                        </IconButton>
                      </Tooltip>}
                    </div>
                  )}
                  {invoice && (
                    <div
                      className={classes.iconContainer}
                      onClick={async () => {
                        setIsSending(true);
                        await handleSendInvoiceEmail();
                      }}
                    >
                      {isSending ? <Loading size="24px" /> : <Tooltip title="Send Email to Customer" arrow placement='bottom'>
                        <IconButton style={{ padding: '5px', marginTop: '-15px'}}>
                          <SendEmailIcon />
                        </IconButton>
                      </Tooltip>}
                    </div>
                  )} */}
                </div>
              }
              handleClose={() => {
                setOpen();
              }}
              showColoseButton={invoice ? false : true}
            />
          </DialogTitle>
          <DialogContent className={classes.dialogContent}>
            <div
              role="template-builder"
              style={{
                height: '100%',
              }}
            >
              <Grid container className={classes.templateContainer}>
                <Grid
                  item
                  style={{
                    height: '100%',
                    maxWidth: navigator.userAgent.match(
                      /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i,
                    )
                      ? '90%'
                      : '80%',
                  }}
                  id="tableWrapper"
                >
                  <Paper
                    style={{
                      padding: `${
                        get(config, 'details.settings.pageMargins').top === 'zeropx'
                          ? '0'
                          : get(config, 'details.settings.pageMargins').top
                      } ${
                        get(config, 'details.settings.pageMargins').right === 'zeropx'
                          ? '0'
                          : get(config, 'details.settings.pageMargins').right
                      } ${
                        get(config, 'details.settings.pageMargins').bottom === 'zeropx'
                          ? '0'
                          : get(config, 'details.settings.pageMargins').bottom
                      } ${
                        get(config, 'details.settings.pageMargins').left === 'zeropx'
                          ? '0'
                          : get(config, 'details.settings.pageMargins').left
                      }`,
                      background: '#FAFAFA',
                      height: '100%',
                      overflow: 'hidden',
                      borderRadius: '0',
                    }}
                  >
                    <Grid item className={classes.previewContainer}>
                      {loading ? (
                        <div
                          style={{
                            width: '50vw',
                          }}
                        >
                          <Loading />
                        </div>
                      ) : (
                        <TemplatePreview setLoaded={setLoaded} />
                      )}
                    </Grid>
                  </Paper>
                </Grid>
              </Grid>
            </div>
          </DialogContent>
        </Dialog>
      </TemplateBuilderContext.Provider>
    </MuiThemeProvider>
  );
};

export default PreviewInvoiceTemplateView;

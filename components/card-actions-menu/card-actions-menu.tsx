import React from 'react';

import Grid from '@material-ui/core/Grid';
import Typography from '@material-ui/core/Typography';
import { makeStyles } from '@material-ui/core/styles';
import CloseIcon from '@material-ui/icons/Close';

import { ActionListProps, Props } from './interface';

const defaultProps = {};

const useStyles = makeStyles({
  container: {
    width: '100%',
    display: 'flex',
    backgroundColor: 'rgba(255,255,255,0.8)',
    flexDirection: 'column',
    height: '100%',
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
    marginBottom: '40px',
    minHeight: 400,
  },
  transition: {
    top: '0',
    transition: 'top 2s ease',
  },
  closeIcon: {
    cursor: 'pointer',
    opacity: 0.7,
    fontSize: '1.125rem',
    marginRight: '16px',
  },
  title: {
    paddingLeft: '30px',
    fontSize: '.8rem',
    fontWeight: 'bold',
    opacity: '0.4',
    color: '#000000',
    paddingBottom: '16px',
  },
  containerPadding: {
    paddingLeft: '30px',
  },
  actionText: {
    paddingLeft: '16px',
    fontWeight: 500,
    fontSize: '.875rem',
  },
  actionTextContainer: {
    cursor: 'pointer',
    padding: '9px 0',
    color: '#6239EB',
    '&:hover': {
      color: '#6239EB',
      backgroundColor: '#f2ecfe',
    },
  },
  titleContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    paddingTop: 16,
  },
});

const ActionList = ({ actions, classes, handleClickAction }: ActionListProps) => (
  <Grid container direction="column">
    {actions.map((action) => (
      <Grid
        container
        direction="row"
        className={classes.actionTextContainer}
        onClick={() => handleClickAction(action)}
      >
        {action.icon && (
          <div className={classes.containerPadding}>
            <action.icon style={{ fontSize: '1.25rem' }} color="secondary" />
          </div>
        )}
        <div>
          <Typography className={classes.actionText} component="p" variant="body1">
            {action.description}
          </Typography>
        </div>
      </Grid>
    ))}
  </Grid>
);

const CardActionsMenu: React.FC<Props> = (userProps) => {
  const props = { ...defaultProps, ...userProps };
  const { handleClose, handleClickAction, actions } = props;
  const classes = useStyles();

  return (
    <div {...props} className={classes.container}>
      <div className={classes.titleContainer}>
        <Typography variant="body1" component="p" className={classes.title}>
          SELECT ACTION
        </Typography>
        <CloseIcon
          name="closeIcon"
          onClick={(event: any) => {
            handleClose();
          }}
          className={classes.closeIcon}
        />
      </div>
      <ActionList actions={actions} classes={classes} handleClickAction={handleClickAction} />
    </div>
  );
};

export default CardActionsMenu;

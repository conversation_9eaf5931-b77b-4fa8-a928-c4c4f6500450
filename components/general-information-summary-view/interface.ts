export interface SubscriptionData {
  startDate: string | null;
  endDate: string | null;
  data: {
    startDate: string;
    label: string;
    details: {
      [subscriptionId: string]: {
        quantity: number;
        cmrr: number;
        arr: number;
      };
    };
  }[];
}

export interface SubscriptionSteamData {
  data: { [billingAccountId: string]: SubscriptionData };
  accountHierarchy?: GeneralInformationAccount;
}

export interface GeneralInformationAccount {
  name: string;
  id: string;
  children?: GeneralInformationAccount[];
  billingAccounts?: GeneralInformationAccount[];
}

import ActiveIcon from '../active-icon';
import DraftIcon from '../draft-icon';
import { CreditCardIcon, PeopleAltIcon } from '../icons';
import { Customer } from '../revenue-builder-types/interface';

export default {
  cardHeaderMetadata: {
    title: 'name',
    // TODO: figure out a metadata-driven way to retrieve status and description
    // may need to do it dynamically since they depend on dynamic values
    description: (customer: Customer) => {
      let description = '';
      if (customer.billingStreet) {
        description += `${customer.billingStreet}, `;
      }
      if (customer.billingCity) {
        description += `${customer.billingCity}, `;
      }
      if (customer.billingStateCode) {
        description += `${customer.billingStateCode}, `;
      }
      if (customer.billingPostalCode) {
        description += `${customer.billingPostalCode}, `;
      }
      if (description.length > 0) {
        description = description.substring(0, description.length - 2);
      }
      return description;
    },
    // TODO when we implement orders, get status by orders active
    statusStyle: {
      Account: {
        color: 'white',
        backgroundColor: '#6239EB',
      },
      Subsidiary: {
        color: 'white',
        backgroundColor: '#2F62EF',
      },
      BillingAccount: {
        color: 'white',
        backgroundColor: '#0FB3D3',
      },
    },
    statusShadowStyle: {
      Account: {
        borderColor: 'transparent transparent #000000 transparent',
        boxShadow: 'none',
      },
      Subsidiary: {
        borderColor: 'transparent transparent #000000 transparent',
        boxShadow: 'none',
      },
      BillingAccount: {
        borderColor: 'transparent transparent #000000 transparent',
        boxShadow: 'none',
      },
    },
    statusHeaderStyle: {
      Account: {
        margin: '-30px -30px 0 -30px',
        padding: '30px 30px 0 30px',
        borderRadius: '10px 10px 0 0',
        backgroundColor: 'rgb(243, 238, 254)',
      },
      Subsidiary: {
        margin: '-30px -30px 0 -30px',
        padding: '30px 30px 0 30px',
        borderRadius: '10px 10px 0 0',
        backgroundColor: 'rgb(237, 241, 252)',
      },
      BillingAccount: {
        margin: '-30px -30px 0 -30px',
        padding: '30px 30px 0 30px',
        borderRadius: '10px 10px 0 0',
        backgroundColor: 'rgb(234, 250, 251)',
      },
    },
    statusIcon: {
      Account: PeopleAltIcon,
      Subsidiary: PeopleAltIcon,
      BillingAccount: CreditCardIcon,
    },
    status: (customer: Customer) => {
      return customer.type;
    },
    imageSignedUrl: 'imageSignedUrl',
  },
  cardBodyMetadata: {
    summaryItemPosition: 'top-left',
    actions: [],
    activeBorderStyle: {
      border: '1px solid',
    },
  },
};

import React from 'react';
import { UseFormMethods } from 'react-hook-form';
import { CSSProperties } from '@material-ui/core/styles/withStyles';
import { RubyFilter } from '../graph-ql-query-constructor/interface';

export type ValuePair = {
  apiName: string;
  name: string;
  orderNumber?: number;
  active?: boolean;
};

export interface RubyField {
  apiName: string;
  name: string;
  type: string;
  creatable?: boolean;
  customField?: boolean;
  defaultValue?: any;
  encrypted?: boolean;
  filterable?: boolean;
  required?: boolean;
  sortable?: boolean;
  unique?: boolean;
  updatable?: boolean;
  compoundFieldName?: string | null;
  compoundedFields?: RubyField[];
  minLength?: number;
  maxLength?: number;
  disableLabel?: boolean;
  xs?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | null;
  valueSet?: {
    alphabeticallyOrdered?: boolean;
    apiName?: string;
    defaultValue?: string;
    name?: string;
    valuePairs: ValuePair[];
  };
  lookupRelation?: {
    referenceField: 'id';
    referenceTo: string;
    relationLabel: string;
    relationName: string;
  };
  masterDetailRelation?: {
    referenceField: 'id';
    referenceTo: string;
    relationLabel: string;
    relationName: string;
  };
  afterFieldChangeThrottle?: number;
  showTooltip?: boolean;
  toolTipText?: string;
  currencyIsoCode?: string;
  showNone?: boolean;
  allowOnlyPositiveNumbers?: boolean;
  withClearIcon?: boolean;
  subType?: string;
  format?: string;
  hideInlineError?: boolean;
  shouldValidateOnChange?: boolean;
  customStyles?: CSSProperties;
  uom?: string;
  minDate?: any;
  nestedApiName?: string;
  nestedType?: string;
  clearable?: boolean;
  transformDisplayValue?: (value: any) => any;
  transformSavedValue?: (value: any) => any;
}

export interface RubyRelation {
  referenceObjectName: string;
  referenceFieldName: string;
  relationFieldName: string;
  relationName: string;
  relationLabel: string;
  relationType: string;
  detailAccessPolicy: {
    viewable: boolean;
    creatable: boolean;
    updatable: boolean;
  };
}

export interface RubyObject {
  apiName: string;
  label?: string;
  name: string;
  customizable?: boolean;
  feature?: string;
  objectType?: string;
  fields?: RubyField[];
  relations?: RubyRelation[];
}

export type LookupOptionsLoader = (
  lookupFieldApiName: string,
  query: string,
  values?: Record<string, any>,
) => Promise<object[]>;

export interface ExtendedFormContext extends UseFormMethods<any> {
  setFieldVisibility: (fieldApiName: string, visiblity: boolean) => void;
}

export type AfterFieldChangeListener = (
  fieldApiName: string,
  fieldNewValue: any,
  fieldOldValue: any,
  values: Record<string, any>,
  formContext: ExtendedFormContext,
) => void | Promise<void>;

export type GetLookupOptionByValue = (lookupFieldApiName: string, id: string) => Promise<object>;

export interface ListViewMetadata {
  columns: ColumnConfig[];
  predefinedFilters?: RubyFilter[];
}

export interface GridViewMetadata {
  cardHeaderMetadata: CardHeaderMetadata;
  cardBodyMetadata: CardBodyMetadata;
}

type ActionFunc = (args: { object: Record<string, any> }) => CardAction[];

interface CardBodyMetadata {
  summaryItemField?: string;
  summaryItemPosition?: 'top-left' | 'bottom-right';
  activeBorderStyle: React.CSSProperties;
  actions: Record<string, CardAction[] | ActionFunc>;
  moreActions?: CardAction[];
  popUpMoreActions?: CardAction[];
  numCardActionsPerRow?: number;
}

export interface SelectedSubscription {
  key: 'invoices' | 'usages' | 'orders';
  subscriptionName: string;
}

export interface CustomerViewPageMode {
  mode: 'Order' | 'Subscription' | 'Entitlement' | 'Asset';
  name: string;
}

export interface CardAction {
  id: string;
  name: string;
  description: string;
  icon?: React.ComponentType<any>;
  confirmation?: {
    title: string;
    message: string;
  };
  showTooltip?: boolean;
  toolTipText?: string;
  style?: CSSProperties;
  type?: string;
}

interface CardHeaderMetadata {
  title: string | ((object: any) => string);
  description: string | ((object: any) => string);
  subtitle: string | ((object: any) => string);
  status: string | ((status: string) => string);
  imageSignedUrl: string | ((object: any) => string);
  statusStyle: Record<string, any>;
  statusIcon: Record<string, React.ComponentType<any>>;
  statusShadowStyle: Record<string, any>;
  statusHeaderStyle?: Record<string, any>;
}

export interface ColumnConfig extends RubyField {
  apiName: string;
  title?: string;
  hidden?: boolean;
  width?: number;
  cellRenderer?: (row: any) => void;
  getCellValue?: (row: any) => void;
  valueSet?: {
    defaultValue?: string;
    valuePairs: ValuePair[];
  };
  style?: React.CSSProperties;
}

export interface FieldSetValue {
  apiName: string;
  name: string;
  value: any;
  valuePairs?: ValuePair[];
}

export interface GetDisplayFieldsFromFieldSet {
  objectFieldSet: RubyField[];
  objectMetadata: RubyObject;
  currencyIsoCode: string;
  object: Record<string, any>;

  locale: string;
}

export interface ViewMetadata {
  objects: any[];
  objectMetadata: RubyObject;
}

export interface CustomPermissions {
  deletePriceOrDiscountTags?: boolean;
  addPriceOrDiscountTags?: boolean;
  editPriceOrDiscountTags?: boolean;
  activateInvoice?: boolean;
  cancelInvoice?: boolean;
  deleteInvoice?: boolean;
  activateCreditMemos?: boolean;
  cancelCreditMemos?: boolean;
  createCustomFilter?: boolean;
  editCustomFilter?: boolean;
  deleteCustomFilter?: boolean;
}

export type UserRole = {
  id: string;
  name: string;
  effective?: boolean;
  functions: string[];
  features: string[];
};

export interface User {
  name: string;
  firstName?: string;
  lastName?: string;
  id: string;
  email: string;
  userName?: string;
  currencyIsoCode: string;
  salesforceUserName?: string;
  permissions?: CustomPermissions;
  createdDate?: Date;
  imageSignedUrl?: string;
  locale?: string;
  status?: string;
  roles?: any;
}

export interface UserProfile extends Omit<User, 'createdDate' | 'currencyIsoCode' | 'permissions'> {
  timeZone: string;
  currency: string;
  effectiveRoleId: string;
  roles: UserRole[];
  tenantId: string;
  lastLoginTime: string;
  mfaEnabled: boolean;
  createdDate?: string;
}

export interface Uom {
  id: string;
  decimalScale: number;
  name: string;
  quantityDimension: string;
  termDimension: string;
  roundingMode: 'Up' | 'Down';
}

export interface PriceBook {
  id: string;
  name: string;
  isStandard?: boolean;
}

export interface PricingPriceTier extends PriceTier {
  applied: boolean;
}

export interface PriceTier {
  id: string;
  amount?: number;
  chargeModel?: 'FlatFee' | 'PerUnit';
  discountPercentage?: number;
  endUnit?: number;
  endUnitDimension?: string;
  priceDimensionId: string;
  startUnit: number | string;
  startUnitDimension?: string;
  tierNumber: number;
  applied?: boolean;
  currencyIsoCode?: string;
}

export interface PriceDimension {
  id?: string;
  active?: boolean;
  description: string;
  endTime?: string;
  name: string;
  objectType?: string;
  objectId?: string;
  priceBookId?: string;
  priceDimensionType: 'Quantity' | 'Term';
  priceType: 'Tiered' | 'Volume';
  recordType: 'DiscountDimension' | 'PriceDimension';
  startTime: Date;
  uomId?: string;
  priceTiers: PriceTier[];
  uom?: Uom;
  source?: 'ProductCatalog' | 'LineItem' | 'Plugin' | 'IncludedUnits';
  ownerId?: string;
  code?: string;
  uomDimension: string;
  isFromLastVersion?: boolean;
  currencyIsoCode?: string;
  quantityPriceTierAttribute?: string;
  dimensionNumber?: number;
  copiedFrom?: string;
  publishStatus?: 'Published' | 'Unpublished';
  needGrandfatherLock?: boolean; // temp mark field for auto unlock and lock grandfather tier
}

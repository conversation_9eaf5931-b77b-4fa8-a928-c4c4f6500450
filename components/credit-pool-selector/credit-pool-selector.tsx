import React from 'react';

import { ListSubheader, MenuItem, Select, makeStyles } from '@material-ui/core';
import dayjs from 'dayjs';

import { InvoicePeriods, Period } from '../revenue-builder-types';

interface Props {
  label: string;
  creditPool: {
    id: string;
    name: string;
  }[];
  onSelectCreditPool: (id: string) => void;
}

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  fieldLabel: {
    opacity: '0.4',
    color: '#000000',
    fontSize: '12px',
    fontWeight: 'bold',
    letterSpacing: 0,
    lineHeight: '15px',
    marginRight: '22px',
    textTransform: 'uppercase',
    whiteSpace: 'nowrap',
  },
});

const CreditPoolSelector: React.FC<Props> = ({ label, creditPool, onSelectCreditPool }: Props) => {
  const classes = useStyles();
  const [value, setValue] = React.useState<string>('');

  React.useEffect(() => {
    if (creditPool && creditPool.length) {
      setValue(creditPool[0]?.id);
    }
  }, [creditPool]);

  if (!creditPool) {
    return null;
  }

  return (
    <>
      <div className={classes.container}>
        <span className={classes.fieldLabel} role="label">
          {label}
        </span>
        <Select
          style={{ width: 280 }}
          value={value}
          onChange={(event) => {
            const selectedValue = event.target.value as string;
            setValue(selectedValue);
            onSelectCreditPool(selectedValue);
          }}
        >
          {creditPool.map(
            (
              p: {
                id: string;
                name: string;
              },
              index: number,
            ) => {
              return (
                <MenuItem key={index} value={p.id}>
                  {p.name}
                </MenuItem>
              );
            },
          )}
        </Select>
      </div>
    </>
  );
};

export default CreditPoolSelector;

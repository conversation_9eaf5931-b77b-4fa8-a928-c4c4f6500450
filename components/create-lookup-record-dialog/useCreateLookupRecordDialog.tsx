import React, { useEffect, useState } from 'react';
import CreateLookupRecordDialog from './create-lookup-record-dialog';
import {
  GetLookupOptionByValue,
  LookupOptionsLoader,
  RubyField,
  RubyObject,
} from '../metadata/interface';
import { GetLookupObjectMetadata } from './interface';

export const useCreateLookupRecordDialog = (args: {
  getLookupObjectMetadata: GetLookupObjectMetadata;
  lookupOptionsLoader: LookupOptionsLoader;
  getLookupOptionByValue: GetLookupOptionByValue;
}) => {
  const [open, setOpen] = useState(false);

  const [originObject, setOriginObject] = useState<RubyObject>();
  const [lookupField, setLookupField] = useState<RubyField>();
  const [lookupObject, setLookupObject] = useState<RubyObject>();

  const showCreateLookupRecordDialog = (args: {
    originObject: RubyObject;
    lookupField: <PERSON>Field;
  }) => {
    setOriginObject(args.originObject);
    setLookupField(args.lookupField);
    setOpen(true);
  };

  const LookupRecordDialog = () => {
    useEffect(() => {
      setUp();
    });

    const setUp = async () => {
      if (originObject && lookupField) {
        const lookupObjectMetadata = await args.getLookupObjectMetadata(originObject, lookupField);
        setLookupObject(lookupObjectMetadata);
      }
    };

    if (!originObject || !lookupField || !lookupObject) {
      return null;
    }

    return (
      <CreateLookupRecordDialog
        open={open}
        originObject={originObject}
        lookupField={lookupField}
        onClose={() => setOpen(false)}
        lookupObject={lookupObject}
        lookupOptionsLoader={args.lookupOptionsLoader}
        getLookupOptionByValue={args.getLookupOptionByValue}
      />
    );
  };

  return {
    showCreateLookupRecordDialog,
    LookupRecordDialog,
  };
};

import React from 'react';

import FormControl from '@material-ui/core/FormControl';
// import '../../views/App/App.css'
import Tooltip from '@material-ui/core/Tooltip';
import { makeStyles } from '@material-ui/core/styles';
import InfoIcon from '@material-ui/icons/Info';
import PropTypes from 'prop-types';

import InputBaseComponent from '../input-base-component';
import InputLabelComponent from '../input-label-component';

const MetadataTextInput = (props: any) => {
  const {
    value,
    name,
    id,
    field,
    disabled,
    label,
    handleInputChange,
    placeholder,
    readOnly,
    toolTipPlacement,
    textCenterStyle = false,
  } = props;

  const showTooltip = props.showTooltip || !!field?.inlineHelpText;
  const toolTipText = props.toolTipText || field?.inlineHelpText;
  const useStyles = makeStyles({
    root: {},
    item: {
      display: 'flex',
    },
    inputWrapper: {
      '& input': {
        textAlign: 'center',
        fontSize: '.875rem',
        fontWeight: 800,
        letterSpacing: '1em',
      },
    },
  });
  const classes = useStyles();
  return (
    <FormControl style={{ width: '100%' }}>
      <InputLabelComponent
        required={field ? field.required : false}
        shrink
        htmlFor={field ? field.apiName : ''}
      >
        <span style={{ display: 'inline', alignItems: 'center' }}>
          {label || field.name}
          {showTooltip && (
            <Tooltip title={toolTipText} arrow placement={toolTipPlacement}>
              <InfoIcon />
            </Tooltip>
          )}
        </span>
      </InputLabelComponent>
      <InputBaseComponent
        style={{ paddingTop: '0px', paddingBottom: '0px' }}
        className={textCenterStyle ? classes.inputWrapper : ''}
        value={value}
        placeholder={placeholder ? placeholder : label}
        name={field ? field.apiName : name}
        type="text"
        required={field ? field.required : false}
        onChange={handleInputChange}
        fullWidth={true}
        disabled={disabled}
        readOnly={readOnly}
      />
    </FormControl>
  );
};

export default MetadataTextInput;
